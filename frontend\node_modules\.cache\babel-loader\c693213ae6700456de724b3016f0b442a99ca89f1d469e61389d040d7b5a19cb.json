{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport EyeOutlined from \"@ant-design/icons/es/icons/EyeOutlined\";\nimport classNames from 'classnames';\nimport RcImage from 'rc-image';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { useLocale } from '../locale';\nimport PreviewGroup, { icons } from './PreviewGroup';\nimport useStyle from './style';\nconst Image = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      preview,\n      className,\n      rootClassName,\n      style\n    } = props,\n    otherProps = __rest(props, [\"prefixCls\", \"preview\", \"className\", \"rootClassName\", \"style\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Image');\n    warning.deprecated(!(preview && typeof preview === 'object' && 'destroyOnClose' in preview), 'destroyOnClose', 'destroyOnHidden');\n  }\n  const {\n    getPrefixCls,\n    getPopupContainer: getContextPopupContainer,\n    className: contextClassName,\n    style: contextStyle,\n    preview: contextPreview\n  } = useComponentConfig('image');\n  const [imageLocale] = useLocale('Image');\n  const prefixCls = getPrefixCls('image', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  // Style\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const mergedRootClassName = classNames(rootClassName, hashId, cssVarCls, rootCls);\n  const mergedClassName = classNames(className, hashId, contextClassName);\n  const [zIndex] = useZIndex('ImagePreview', typeof preview === 'object' ? preview.zIndex : undefined);\n  const mergedPreview = React.useMemo(() => {\n    if (preview === false) {\n      return preview;\n    }\n    const _preview = typeof preview === 'object' ? preview : {};\n    const {\n        getContainer,\n        closeIcon,\n        rootClassName,\n        destroyOnClose,\n        destroyOnHidden\n      } = _preview,\n      restPreviewProps = __rest(_preview, [\"getContainer\", \"closeIcon\", \"rootClassName\", \"destroyOnClose\", \"destroyOnHidden\"]);\n    return Object.assign(Object.assign({\n      mask: (/*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-mask-info`\n      }, /*#__PURE__*/React.createElement(EyeOutlined, null), imageLocale === null || imageLocale === void 0 ? void 0 : imageLocale.preview)),\n      icons\n    }, restPreviewProps), {\n      // TODO: In the future, destroyOnClose in rc-image needs to be upgrade to destroyOnHidden\n      destroyOnClose: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyOnClose,\n      rootClassName: classNames(mergedRootClassName, rootClassName),\n      getContainer: getContainer !== null && getContainer !== void 0 ? getContainer : getContextPopupContainer,\n      transitionName: getTransitionName(rootPrefixCls, 'zoom', _preview.transitionName),\n      maskTransitionName: getTransitionName(rootPrefixCls, 'fade', _preview.maskTransitionName),\n      zIndex,\n      closeIcon: closeIcon !== null && closeIcon !== void 0 ? closeIcon : contextPreview === null || contextPreview === void 0 ? void 0 : contextPreview.closeIcon\n    });\n  }, [preview, imageLocale, contextPreview === null || contextPreview === void 0 ? void 0 : contextPreview.closeIcon]);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RcImage, Object.assign({\n    prefixCls: prefixCls,\n    preview: mergedPreview,\n    rootClassName: mergedRootClassName,\n    className: mergedClassName,\n    style: mergedStyle\n  }, otherProps)));\n};\nImage.PreviewGroup = PreviewGroup;\nif (process.env.NODE_ENV !== 'production') {\n  Image.displayName = 'Image';\n}\nexport default Image;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}