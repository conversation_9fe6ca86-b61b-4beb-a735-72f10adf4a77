{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Item } from 'rc-menu';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { cloneElement } from '../_util/reactNode';\nimport { SiderContext } from '../layout/Sider';\nimport Tooltip from '../tooltip';\nimport MenuContext from './MenuContext';\nconst MenuItem = props => {\n  var _a;\n  const {\n    className,\n    children,\n    icon,\n    title,\n    danger,\n    extra\n  } = props;\n  const {\n    prefixCls,\n    firstLevel,\n    direction,\n    disableMenuItemTitleTooltip,\n    inlineCollapsed: isInlineCollapsed\n  } = React.useContext(MenuContext);\n  const renderItemChildren = inlineCollapsed => {\n    const label = children === null || children === void 0 ? void 0 : children[0];\n    const wrapNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(`${prefixCls}-title-content`, {\n        [`${prefixCls}-title-content-with-extra`]: !!extra || extra === 0\n      })\n    }, children);\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    if (!icon || /*#__PURE__*/React.isValidElement(children) && children.type === 'span') {\n      if (children && inlineCollapsed && firstLevel && typeof label === 'string') {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: `${prefixCls}-inline-collapsed-noicon`\n        }, label.charAt(0));\n      }\n    }\n    return wrapNode;\n  };\n  const {\n    siderCollapsed\n  } = React.useContext(SiderContext);\n  let tooltipTitle = title;\n  if (typeof title === 'undefined') {\n    tooltipTitle = firstLevel ? children : '';\n  } else if (title === false) {\n    tooltipTitle = '';\n  }\n  const tooltipProps = {\n    title: tooltipTitle\n  };\n  if (!siderCollapsed && !isInlineCollapsed) {\n    tooltipProps.title = null;\n    // Reset `open` to fix control mode tooltip display not correct\n    // ref: https://github.com/ant-design/ant-design/issues/16742\n    tooltipProps.open = false;\n  }\n  const childrenLength = toArray(children).length;\n  let returnNode = /*#__PURE__*/React.createElement(Item, Object.assign({}, omit(props, ['title', 'icon', 'danger']), {\n    className: classNames({\n      [`${prefixCls}-item-danger`]: danger,\n      [`${prefixCls}-item-only-child`]: (icon ? childrenLength + 1 : childrenLength) === 1\n    }, className),\n    title: typeof title === 'string' ? title : undefined\n  }), cloneElement(icon, {\n    className: classNames(/*#__PURE__*/React.isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', `${prefixCls}-item-icon`)\n  }), renderItemChildren(isInlineCollapsed));\n  if (!disableMenuItemTitleTooltip) {\n    returnNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps, {\n      placement: direction === 'rtl' ? 'left' : 'right',\n      classNames: {\n        root: `${prefixCls}-inline-collapsed-tooltip`\n      }\n    }), returnNode);\n  }\n  return returnNode;\n};\nexport default MenuItem;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}