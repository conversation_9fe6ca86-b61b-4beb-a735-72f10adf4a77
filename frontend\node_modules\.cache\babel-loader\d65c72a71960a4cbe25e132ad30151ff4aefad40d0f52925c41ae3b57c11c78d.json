{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport JavaOutlinedSvg from \"@ant-design/icons-svg/es/asn/JavaOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar JavaOutlined = function JavaOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: JavaOutlinedSvg\n  }));\n};\n\n/**![java](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMzk0LjY4IDc1Ni45OXMtMzQuMzMgMTkuOTUgMjQuMzQgMjYuNmM3MS4xIDguMDUgMTA3LjM1IDcgMTg1LjY0LTcuODcgMCAwIDIwLjY2IDEyLjk0IDQ5LjM4IDI0LjE0LTE3NS40NyA3NS4wOC0zOTcuMTgtNC4zNy0yNTkuMzYtNDIuODdtLTIxLjM3LTk4LjE3cy0zOC4zNSAyOC4zNSAyMC4zMiAzNC40N2M3NS44MyA3Ljg4IDEzNS45IDguNCAyMzkuNTctMTEuNTUgMCAwIDE0LjM2IDE0LjUzIDM2Ljk1IDIyLjQtMjEyLjQzIDYyLjEzLTQ0OC44NCA1LjA4LTI5Ni44NC00NS4zMm0xODAuNzMtMTY2LjQzYzQzLjI2IDQ5LjctMTEuMzggOTQuNS0xMS4zOCA5NC41czEwOS44LTU2LjcgNTkuMzctMTI3LjU3Yy00Ny4xMS02Ni4xNS04My4xOS05OS4wNSAxMTIuMjUtMjEyLjI3LjE4IDAtMzA2LjgyIDc2LjY1LTE2MC4yNCAyNDUuMzVtMjMyLjIyIDMzNy4wNHMyNS40IDIwLjgyLTI3Ljg1IDM3LjFjLTEwMS40IDMwLjYyLTQyMS43IDM5LjktNTEwLjY2IDEuMjItMzIuMDUtMTMuODIgMjguMDItMzMuMjUgNDYuOTMtMzcuMjcgMTkuNjItNC4yIDMxLTMuNSAzMS0zLjUtMzUuNTUtMjUuMDMtMjI5Ljk0IDQ5LjE3LTk4Ljc3IDcwLjM1IDM1Ny42IDU4LjEgNjUyLjE2LTI2LjA4IDU1OS4zNS02Ny45bS0zNzUuMTItMjcyLjNzLTE2My4wNCAzOC42OC01Ny43OSA1Mi42OGM0NC40OCA1Ljk1IDEzMy4xIDQuNTUgMjE1LjU4LTIuMjggNjcuNDItNS42IDEzNS4yLTE3Ljg1IDEzNS4yLTE3Ljg1cy0yMy44MiAxMC4xNS00MC45OCAyMS44OGMtMTY1LjUgNDMuNTctNDg1LjEgMjMuMjctMzkzLjE2LTIxLjE4IDc3LjkzLTM3LjQ1IDE0MS4xNS0zMy4yNSAxNDEuMTUtMzMuMjVNNzAzLjYgNzIwLjQyYzE2OC4zLTg3LjMzIDkwLjM3LTE3MS4zMyAzNi4wOC0xNTkuOTUtMTMuMzEgMi44LTE5LjI3IDUuMjUtMTkuMjcgNS4yNXM0LjktNy43IDE0LjM2LTExLjAzQzg0Mi4xMiA1MTYuOSA5MjQuNzggNjY2IDcwMC4xIDcyNC45N2MwLS4xOCAyLjYzLTIuNDUgMy41LTQuNTVNNjAyLjAzIDY0czkzLjE2IDkzLjEtODguNDQgMjM2LjI1Yy0xNDUuNTMgMTE0LjgtMzMuMjcgMTgwLjQyIDAgMjU1LjE0LTg0Ljk0LTc2LjY1LTE0Ny4yOC0xNDQuMDItMTA1LjQyLTIwNi44NEM0NjkuNjMgMjU2LjY3IDYzOS42OCAyMTEuODcgNjAyLjAzIDY0TTQyNy43OCA5NTcuMTlDNTg5LjI0IDk2Ny41IDgzNy4yMiA5NTEuNCA4NDMgODc1LjFjMCAwLTExLjIgMjguODgtMTMzLjQ0IDUxLjk4LTEzNy44MyAyNS45LTMwNy44NyAyMi45Mi00MDguNTcgNi4zIDAtLjE4IDIwLjY2IDE2Ljk3IDEyNi43OSAyMy44IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(JavaOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'JavaOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}