{"ast": null, "code": "import { genStyleHooks, mergeToken } from '../../theme/internal';\nimport { alignItemsValues, flexWrapValues, justifyContentValues } from '../utils';\nconst genFlexStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'flex',\n      margin: 0,\n      padding: 0,\n      '&-vertical': {\n        flexDirection: 'column'\n      },\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&:empty': {\n        display: 'none'\n      }\n    }\n  };\n};\nconst genFlexGapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-gap-small': {\n        gap: token.flexGapSM\n      },\n      '&-gap-middle': {\n        gap: token.flexGap\n      },\n      '&-gap-large': {\n        gap: token.flexGapLG\n      }\n    }\n  };\n};\nconst genFlexWrapStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const wrapStyle = {};\n  flexWrapValues.forEach(value => {\n    wrapStyle[`${componentCls}-wrap-${value}`] = {\n      flexWrap: value\n    };\n  });\n  return wrapStyle;\n};\nconst genAlignItemsStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const alignStyle = {};\n  alignItemsValues.forEach(value => {\n    alignStyle[`${componentCls}-align-${value}`] = {\n      alignItems: value\n    };\n  });\n  return alignStyle;\n};\nconst genJustifyContentStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const justifyStyle = {};\n  justifyContentValues.forEach(value => {\n    justifyStyle[`${componentCls}-justify-${value}`] = {\n      justifyContent: value\n    };\n  });\n  return justifyStyle;\n};\nexport const prepareComponentToken = () => ({});\nexport default genStyleHooks('Flex', token => {\n  const {\n    paddingXS,\n    padding,\n    paddingLG\n  } = token;\n  const flexToken = mergeToken(token, {\n    flexGapSM: paddingXS,\n    flexGap: padding,\n    flexGapLG: paddingLG\n  });\n  return [genFlexStyle(flexToken), genFlexGapStyle(flexToken), genFlexWrapStyle(flexToken), genAlignItemsStyle(flexToken), genJustifyContentStyle(flexToken)];\n}, prepareComponentToken, {\n  // Flex component don't apply extra font style\n  // https://github.com/ant-design/ant-design/issues/46403\n  resetStyle: false\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}