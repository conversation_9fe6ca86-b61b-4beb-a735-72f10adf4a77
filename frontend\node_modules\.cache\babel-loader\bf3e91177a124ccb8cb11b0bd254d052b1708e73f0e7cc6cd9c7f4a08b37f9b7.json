{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Shared ==============================\nconst genSharedBackTopStyle = token => {\n  const {\n    componentCls,\n    backTopFontSize,\n    backTopSize,\n    zIndexPopup\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'fixed',\n      insetInlineEnd: token.backTopInlineEnd,\n      insetBlockEnd: token.backTopBlockEnd,\n      zIndex: zIndexPopup,\n      width: 40,\n      height: 40,\n      cursor: 'pointer',\n      '&:empty': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        width: backTopSize,\n        height: backTopSize,\n        overflow: 'hidden',\n        color: token.backTopColor,\n        textAlign: 'center',\n        backgroundColor: token.backTopBackground,\n        borderRadius: backTopSize,\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.backTopHoverBackground,\n          transition: `all ${token.motionDurationMid}`\n        }\n      },\n      // change to .backtop .backtop-icon\n      [`${componentCls}-icon`]: {\n        fontSize: backTopFontSize,\n        lineHeight: unit(backTopSize)\n      }\n    })\n  };\n};\nconst genMediaBackTopStyle = token => {\n  const {\n    componentCls,\n    screenMD,\n    screenXS,\n    backTopInlineEndMD,\n    backTopInlineEndXS\n  } = token;\n  return {\n    [`@media (max-width: ${unit(screenMD)})`]: {\n      [componentCls]: {\n        insetInlineEnd: backTopInlineEndMD\n      }\n    },\n    [`@media (max-width: ${unit(screenXS)})`]: {\n      [componentCls]: {\n        insetInlineEnd: backTopInlineEndXS\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexBase + 10\n});\n// ============================== Export ==============================\nexport default genStyleHooks('BackTop', token => {\n  const {\n    fontSizeHeading3,\n    colorTextDescription,\n    colorTextLightSolid,\n    colorText,\n    controlHeightLG,\n    calc\n  } = token;\n  const backTopToken = mergeToken(token, {\n    backTopBackground: colorTextDescription,\n    backTopColor: colorTextLightSolid,\n    backTopHoverBackground: colorText,\n    backTopFontSize: fontSizeHeading3,\n    backTopSize: controlHeightLG,\n    backTopBlockEnd: calc(controlHeightLG).mul(1.25).equal(),\n    backTopInlineEnd: calc(controlHeightLG).mul(2.5).equal(),\n    backTopInlineEndMD: calc(controlHeightLG).mul(1.5).equal(),\n    backTopInlineEndXS: calc(controlHeightLG).mul(0.5).equal()\n  });\n  return [genSharedBackTopStyle(backTopToken), genMediaBackTopStyle(backTopToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}