{"ast": null, "code": "/**\n * While micromark is a lexer/tokenizer, the common case of going from markdown\n * to html is currently built in as this module, even though the parts can be\n * used separately to build ASTs, CSTs, or many other output formats.\n *\n * Having an HTML compiler built in is useful because it allows us to check for\n * compliancy to CommonMark, the de facto norm of markdown, specified in roughly\n * 600 input/output cases.\n *\n * This module has an interface that accepts lists of events instead of the\n * whole at once, however, because markdown can’t be truly streaming, we buffer\n * events before processing and outputting the final result.\n */\n\n/**\n * @import {\n *   CompileContext,\n *   CompileData,\n *   CompileOptions,\n *   Compile,\n *   Definition,\n *   Event,\n *   Handle,\n *   HtmlExtension,\n *   LineEnding,\n *   NormalizedHtmlExtension,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef Media\n * @property {boolean | undefined} [image]\n * @property {string | undefined} [labelId]\n * @property {string | undefined} [label]\n * @property {string | undefined} [referenceId]\n * @property {string | undefined} [destination]\n * @property {string | undefined} [title]\n */\n\nimport { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { ok as assert } from 'devlop';\nimport { push } from 'micromark-util-chunked';\nimport { combineHtmlExtensions } from 'micromark-util-combine-extensions';\nimport { decodeNumericCharacterReference } from 'micromark-util-decode-numeric-character-reference';\nimport { encode as _encode } from 'micromark-util-encode';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { sanitizeUri } from 'micromark-util-sanitize-uri';\nimport { codes, constants, types } from 'micromark-util-symbol';\nconst hasOwnProperty = {}.hasOwnProperty;\n\n/**\n * These two are allowlists of safe protocols for full URLs in respectively the\n * `href` (on `<a>`) and `src` (on `<img>`) attributes.\n * They are based on what is allowed on GitHub,\n * <https://github.com/syntax-tree/hast-util-sanitize/blob/9275b21/lib/github.json#L31>\n */\nconst protocolHref = /^(https?|ircs?|mailto|xmpp)$/i;\nconst protocolSource = /^https?$/i;\n\n/**\n * @param {CompileOptions | null | undefined} [options]\n * @returns {Compile}\n */\nexport function compile(options) {\n  const settings = options || {};\n\n  /**\n   * Tags is needed because according to markdown, links and emphasis and\n   * whatnot can exist in images, however, as HTML doesn’t allow content in\n   * images, the tags are ignored in the `alt` attribute, but the content\n   * remains.\n   *\n   * @type {boolean | undefined}\n   */\n  let tags = true;\n\n  /**\n   * An object to track identifiers to media (URLs and titles) defined with\n   * definitions.\n   *\n   * @type {Record<string, Definition>}\n   */\n  const definitions = {};\n\n  /**\n   * A lot of the handlers need to capture some of the output data, modify it\n   * somehow, and then deal with it.\n   * We do that by tracking a stack of buffers, that can be opened (with\n   * `buffer`) and closed (with `resume`) to access them.\n   *\n   * @type {Array<Array<string>>}\n   */\n  const buffers = [[]];\n\n  /**\n   * As we can have links in images and the other way around, where the deepest\n   * ones are closed first, we need to track which one we’re in.\n   *\n   * @type {Array<Media>}\n   */\n  const mediaStack = [];\n\n  /**\n   * Same as `mediaStack` for tightness, which is specific to lists.\n   * We need to track if we’re currently in a tight or loose container.\n   *\n   * @type {Array<boolean>}\n   */\n  const tightStack = [];\n\n  /** @type {HtmlExtension} */\n  const defaultHandlers = {\n    enter: {\n      blockQuote: onenterblockquote,\n      codeFenced: onentercodefenced,\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: onentercodeindented,\n      codeText: onentercodetext,\n      content: onentercontent,\n      definition: onenterdefinition,\n      definitionDestinationString: onenterdefinitiondestinationstring,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: onenteremphasis,\n      htmlFlow: onenterhtmlflow,\n      htmlText: onenterhtml,\n      image: onenterimage,\n      label: buffer,\n      link: onenterlink,\n      listItemMarker: onenterlistitemmarker,\n      listItemValue: onenterlistitemvalue,\n      listOrdered: onenterlistordered,\n      listUnordered: onenterlistunordered,\n      paragraph: onenterparagraph,\n      reference: buffer,\n      resource: onenterresource,\n      resourceDestinationString: onenterresourcedestinationstring,\n      resourceTitleString: buffer,\n      setextHeading: onentersetextheading,\n      strong: onenterstrong\n    },\n    exit: {\n      atxHeading: onexitatxheading,\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: onexitblockquote,\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: onexitflowcode,\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onresumedrop,\n      codeFlowValue: onexitcodeflowvalue,\n      codeIndented: onexitflowcode,\n      codeText: onexitcodetext,\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: onexitdefinition,\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: onexitemphasis,\n      hardBreakEscape: onexithardbreak,\n      hardBreakTrailing: onexithardbreak,\n      htmlFlow: onexithtml,\n      htmlFlowData: onexitdata,\n      htmlText: onexithtml,\n      htmlTextData: onexitdata,\n      image: onexitmedia,\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: onexitmedia,\n      listOrdered: onexitlistordered,\n      listUnordered: onexitlistunordered,\n      paragraph: onexitparagraph,\n      reference: onresumedrop,\n      referenceString: onexitreferencestring,\n      resource: onresumedrop,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      setextHeading: onexitsetextheading,\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: onexitstrong,\n      thematicBreak: onexitthematicbreak\n    }\n  };\n\n  /**\n   * Combine the HTML extensions with the default handlers.\n   * An HTML extension is an object whose fields are either `enter` or `exit`\n   * (reflecting whether a token is entered or exited).\n   * The values at such objects are names of tokens mapping to handlers.\n   * Handlers are called, respectively when a token is opener or closed, with\n   * that token, and a context as `this`.\n   */\n  const handlers = /** @type {NormalizedHtmlExtension} */\n  combineHtmlExtensions([defaultHandlers, ...(settings.htmlExtensions || [])]);\n\n  /**\n   * Handlers do often need to keep track of some state.\n   * That state is provided here as a key-value store (an object).\n   *\n   * @type {CompileData}\n   */\n  const data = {\n    definitions,\n    tightStack\n  };\n\n  /**\n   * The context for handlers references a couple of useful functions.\n   * In handlers from extensions, those can be accessed at `this`.\n   * For the handlers here, they can be accessed directly.\n   *\n   * @type {Omit<CompileContext, 'sliceSerialize'>}\n   */\n  const context = {\n    buffer,\n    encode,\n    getData,\n    lineEndingIfNeeded,\n    options: settings,\n    raw,\n    resume,\n    setData,\n    tag\n  };\n\n  /**\n   * Generally, micromark copies line endings (`'\\r'`, `'\\n'`, `'\\r\\n'`) in the\n   * markdown document over to the compiled HTML.\n   * In some cases, such as `> a`, CommonMark requires that extra line endings\n   * are added: `<blockquote>\\n<p>a</p>\\n</blockquote>`.\n   * This variable hold the default line ending when given (or `undefined`),\n   * and in the latter case will be updated to the first found line ending if\n   * there is one.\n   */\n  let lineEndingStyle = settings.defaultLineEnding;\n\n  // Return the function that handles a slice of events.\n  return compile;\n\n  /**\n   * Deal w/ a slice of events.\n   * Return either the empty string if there’s nothing of note to return, or the\n   * result when done.\n   *\n   * @param {ReadonlyArray<Event>} events\n   * @returns {string}\n   */\n  function compile(events) {\n    let index = -1;\n    let start = 0;\n    /** @type {Array<number>} */\n    const listStack = [];\n    // As definitions can come after references, we need to figure out the media\n    // (urls and titles) defined by them before handling the references.\n    // So, we do sort of what HTML does: put metadata at the start (in head), and\n    // then put content after (`body`).\n    /** @type {Array<Event>} */\n    let head = [];\n    /** @type {Array<Event>} */\n    let body = [];\n    while (++index < events.length) {\n      // Figure out the line ending style used in the document.\n      if (!lineEndingStyle && (events[index][1].type === types.lineEnding || events[index][1].type === types.lineEndingBlank)) {\n        lineEndingStyle = /** @type {LineEnding} */\n        events[index][2].sliceSerialize(events[index][1]);\n      }\n\n      // Preprocess lists to infer whether the list is loose or not.\n      if (events[index][1].type === types.listOrdered || events[index][1].type === types.listUnordered) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index);\n        } else {\n          prepareList(events.slice(listStack.pop(), index));\n        }\n      }\n\n      // Move definitions to the front.\n      if (events[index][1].type === types.definition) {\n        if (events[index][0] === 'enter') {\n          body = push(body, events.slice(start, index));\n          start = index;\n        } else {\n          head = push(head, events.slice(start, index + 1));\n          start = index + 1;\n        }\n      }\n    }\n    head = push(head, body);\n    head = push(head, events.slice(start));\n    index = -1;\n    const result = head;\n\n    // Handle the start of the document, if defined.\n    if (handlers.enter.null) {\n      handlers.enter.null.call(context);\n    }\n\n    // Handle all events.\n    while (++index < events.length) {\n      const handles = handlers[result[index][0]];\n      const kind = result[index][1].type;\n      const handle = handles[kind];\n      if (hasOwnProperty.call(handles, kind) && handle) {\n        handle.call({\n          sliceSerialize: result[index][2].sliceSerialize,\n          ...context\n        }, result[index][1]);\n      }\n    }\n\n    // Handle the end of the document, if defined.\n    if (handlers.exit.null) {\n      handlers.exit.null.call(context);\n    }\n    return buffers[0].join('');\n  }\n\n  /**\n   * Figure out whether lists are loose or not.\n   *\n   * @param {ReadonlyArray<Event>} slice\n   * @returns {undefined}\n   */\n  function prepareList(slice) {\n    const length = slice.length;\n    let index = 0; // Skip open.\n    let containerBalance = 0;\n    let loose = false;\n    /** @type {boolean | undefined} */\n    let atMarker;\n    while (++index < length) {\n      const event = slice[index];\n      if (event[1]._container) {\n        atMarker = undefined;\n        if (event[0] === 'enter') {\n          containerBalance++;\n        } else {\n          containerBalance--;\n        }\n      } else switch (event[1].type) {\n        case types.listItemPrefix:\n          {\n            if (event[0] === 'exit') {\n              atMarker = true;\n            }\n            break;\n          }\n        case types.linePrefix:\n          {\n            // Ignore\n\n            break;\n          }\n        case types.lineEndingBlank:\n          {\n            if (event[0] === 'enter' && !containerBalance) {\n              if (atMarker) {\n                atMarker = undefined;\n              } else {\n                loose = true;\n              }\n            }\n            break;\n          }\n        default:\n          {\n            atMarker = undefined;\n          }\n      }\n    }\n    slice[0][1]._loose = loose;\n  }\n\n  /**\n   * @type {CompileContext['setData']}\n   */\n  function setData(key, value) {\n    // @ts-expect-error: assume `value` is omitted (`undefined` is passed) only\n    // if allowed.\n    data[key] = value;\n  }\n\n  /**\n   * @type {CompileContext['getData']}\n   */\n  function getData(key) {\n    return data[key];\n  }\n\n  /** @type {CompileContext['buffer']} */\n  function buffer() {\n    buffers.push([]);\n  }\n\n  /** @type {CompileContext['resume']} */\n  function resume() {\n    const buf = buffers.pop();\n    assert(buf !== undefined, 'Cannot resume w/o buffer');\n    return buf.join('');\n  }\n\n  /** @type {CompileContext['tag']} */\n  function tag(value) {\n    if (!tags) return;\n    setData('lastWasTag', true);\n    buffers[buffers.length - 1].push(value);\n  }\n\n  /** @type {CompileContext['raw']} */\n  function raw(value) {\n    setData('lastWasTag');\n    buffers[buffers.length - 1].push(value);\n  }\n\n  /**\n   * Output an extra line ending.\n   *\n   * @returns {undefined}\n   */\n  function lineEnding() {\n    raw(lineEndingStyle || '\\n');\n  }\n\n  /** @type {CompileContext['lineEndingIfNeeded']} */\n  function lineEndingIfNeeded() {\n    const buffer = buffers[buffers.length - 1];\n    const slice = buffer[buffer.length - 1];\n    const previous = slice ? slice.charCodeAt(slice.length - 1) : codes.eof;\n    if (previous === codes.lf || previous === codes.cr || previous === codes.eof) {\n      return;\n    }\n    lineEnding();\n  }\n\n  /** @type {CompileContext['encode']} */\n  function encode(value) {\n    return getData('ignoreEncode') ? value : _encode(value);\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @returns {undefined}\n   */\n  function onresumedrop() {\n    resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered(token) {\n    tightStack.push(!token._loose);\n    lineEndingIfNeeded();\n    tag('<ol');\n    setData('expectFirstItem', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistunordered(token) {\n    tightStack.push(!token._loose);\n    lineEndingIfNeeded();\n    tag('<ul');\n    setData('expectFirstItem', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectFirstItem')) {\n      const value = Number.parseInt(this.sliceSerialize(token), constants.numericBaseDecimal);\n      if (value !== 1) {\n        tag(' start=\"' + encode(String(value)) + '\"');\n      }\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterlistitemmarker() {\n    if (getData('expectFirstItem')) {\n      tag('>');\n    } else {\n      onexitlistitem();\n    }\n    lineEndingIfNeeded();\n    tag('<li>');\n    setData('expectFirstItem');\n    // “Hack” to prevent a line ending from showing up if the item is empty.\n    setData('lastWasTag');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistordered() {\n    onexitlistitem();\n    tightStack.pop();\n    lineEnding();\n    tag('</ol>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistunordered() {\n    onexitlistitem();\n    tightStack.pop();\n    lineEnding();\n    tag('</ul>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistitem() {\n    if (getData('lastWasTag') && !getData('slurpAllLineEndings')) {\n      lineEndingIfNeeded();\n    }\n    tag('</li>');\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterblockquote() {\n    tightStack.push(false);\n    lineEndingIfNeeded();\n    tag('<blockquote>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitblockquote() {\n    tightStack.pop();\n    lineEndingIfNeeded();\n    tag('</blockquote>');\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterparagraph() {\n    if (!tightStack[tightStack.length - 1]) {\n      lineEndingIfNeeded();\n      tag('<p>');\n    }\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitparagraph() {\n    if (tightStack[tightStack.length - 1]) {\n      setData('slurpAllLineEndings', true);\n    } else {\n      tag('</p>');\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodefenced() {\n    lineEndingIfNeeded();\n    tag('<pre><code');\n    setData('fencesCount', 0);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const value = resume();\n    tag(' class=\"language-' + value + '\"');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    const count = getData('fencesCount') || 0;\n    if (!count) {\n      tag('>');\n      setData('slurpOneLineEnding', true);\n    }\n    setData('fencesCount', count + 1);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodeindented() {\n    lineEndingIfNeeded();\n    tag('<pre><code>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitflowcode() {\n    const count = getData('fencesCount');\n\n    // One special case is if we are inside a container, and the fenced code was\n    // not closed (meaning it runs to the end).\n    // In that case, the following line ending, is considered *outside* the\n    // fenced code and block quote by micromark, but CM wants to treat that\n    // ending as part of the code.\n    if (count !== undefined && count < 2 && data.tightStack.length > 0 && !getData('lastWasTag')) {\n      lineEnding();\n    }\n\n    // But in most cases, it’s simpler: when we’ve seen some data, emit an extra\n    // line ending when needed.\n    if (getData('flowCodeSeenData')) {\n      lineEndingIfNeeded();\n    }\n    tag('</code></pre>');\n    if (count !== undefined && count < 2) lineEndingIfNeeded();\n    setData('flowCodeSeenData');\n    setData('fencesCount');\n    setData('slurpOneLineEnding');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterimage() {\n    mediaStack.push({\n      image: true\n    });\n    tags = undefined; // Disallow tags.\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlink() {\n    mediaStack.push({});\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabeltext(token) {\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabel() {\n    mediaStack[mediaStack.length - 1].label = resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitreferencestring(token) {\n    mediaStack[mediaStack.length - 1].referenceId = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresource() {\n    buffer(); // We can have line endings in the resource, ignore them.\n    mediaStack[mediaStack.length - 1].destination = '';\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresourcedestinationstring() {\n    buffer();\n    // Ignore encoding the result, as we’ll first percent encode the url and\n    // encode manually after.\n    setData('ignoreEncode', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcedestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume();\n    setData('ignoreEncode');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcetitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitmedia() {\n    let index = mediaStack.length - 1; // Skip current.\n    const media = mediaStack[index];\n    const id = media.referenceId || media.labelId;\n    assert(id !== undefined, 'media should have `referenceId` or `labelId`');\n    assert(media.label !== undefined, 'media should have `label`');\n    const context = media.destination === undefined ? definitions[normalizeIdentifier(id)] : media;\n    tags = true;\n    while (index--) {\n      if (mediaStack[index].image) {\n        tags = undefined;\n        break;\n      }\n    }\n    if (media.image) {\n      tag('<img src=\"' + sanitizeUri(context.destination, settings.allowDangerousProtocol ? undefined : protocolSource) + '\" alt=\"');\n      raw(media.label);\n      tag('\"');\n    } else {\n      tag('<a href=\"' + sanitizeUri(context.destination, settings.allowDangerousProtocol ? undefined : protocolHref) + '\"');\n    }\n    tag(context.title ? ' title=\"' + context.title + '\"' : '');\n    if (media.image) {\n      tag(' />');\n    } else {\n      tag('>');\n      raw(media.label);\n      tag('</a>');\n    }\n    mediaStack.pop();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinition() {\n    buffer();\n    mediaStack.push({});\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    // Discard label, use the source content instead.\n    resume();\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinitiondestinationstring() {\n    buffer();\n    setData('ignoreEncode', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume();\n    setData('ignoreEncode');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinition() {\n    const media = mediaStack[mediaStack.length - 1];\n    assert(media.labelId !== undefined, 'media should have `labelId`');\n    const id = normalizeIdentifier(media.labelId);\n    resume();\n    if (!hasOwnProperty.call(definitions, id)) {\n      definitions[id] = mediaStack[mediaStack.length - 1];\n    }\n    mediaStack.pop();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercontent() {\n    setData('slurpAllLineEndings', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    // Exit for further sequences.\n    if (getData('headingRank')) return;\n    setData('headingRank', this.sliceSerialize(token).length);\n    lineEndingIfNeeded();\n    tag('<h' + getData('headingRank') + '>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentersetextheading() {\n    buffer();\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('slurpAllLineEndings', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheading() {\n    tag('</h' + getData('headingRank') + '>');\n    setData('headingRank');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    setData('headingRank', this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    const value = resume();\n    lineEndingIfNeeded();\n    tag('<h' + getData('headingRank') + '>');\n    raw(value);\n    tag('</h' + getData('headingRank') + '>');\n    setData('slurpAllLineEndings');\n    setData('headingRank');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdata(token) {\n    raw(encode(this.sliceSerialize(token)));\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlineending(token) {\n    if (getData('slurpAllLineEndings')) {\n      return;\n    }\n    if (getData('slurpOneLineEnding')) {\n      setData('slurpOneLineEnding');\n      return;\n    }\n    if (getData('inCodeText')) {\n      raw(' ');\n      return;\n    }\n    raw(encode(this.sliceSerialize(token)));\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeflowvalue(token) {\n    raw(encode(this.sliceSerialize(token)));\n    setData('flowCodeSeenData', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexithardbreak() {\n    tag('<br />');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtmlflow() {\n    lineEndingIfNeeded();\n    onenterhtml();\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexithtml() {\n    setData('ignoreEncode');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtml() {\n    if (settings.allowDangerousHtml) {\n      setData('ignoreEncode', true);\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenteremphasis() {\n    tag('<em>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterstrong() {\n    tag('<strong>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onentercodetext() {\n    setData('inCodeText', true);\n    tag('<code>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitcodetext() {\n    setData('inCodeText');\n    tag('</code>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitemphasis() {\n    tag('</em>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitstrong() {\n    tag('</strong>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitthematicbreak() {\n    lineEndingIfNeeded();\n    tag('<hr />');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @param {Token} token\n   * @returns {undefined}\n   */\n  function onexitcharacterreferencemarker(token) {\n    setData('characterReferenceType', token.type);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const value = this.sliceSerialize(token);\n    const decoded = getData('characterReferenceType') ? decodeNumericCharacterReference(value, getData('characterReferenceType') === types.characterReferenceMarkerNumeric ? constants.numericBaseDecimal : constants.numericBaseHexadecimal) : decodeNamedCharacterReference(value);\n\n    // `decodeNamedCharacterReference` can return `false` for invalid named\n    // character references,\n    // but everything we’ve tokenized is valid.\n    raw(encode(/** @type {string} */decoded));\n    setData('characterReferenceType');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    const uri = this.sliceSerialize(token);\n    tag('<a href=\"' + sanitizeUri(uri, settings.allowDangerousProtocol ? undefined : protocolHref) + '\">');\n    raw(encode(uri));\n    tag('</a>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    const uri = this.sliceSerialize(token);\n    tag('<a href=\"' + sanitizeUri('mailto:' + uri) + '\">');\n    raw(encode(uri));\n    tag('</a>');\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}