{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n};\n\n/** @type {Construct} */\nexport const codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this;\n  /** @type {Construct} */\n  const closeStart = {\n    partial: true,\n    tokenize: tokenizeCloseStart\n  };\n  let initialPrefix = 0;\n  let sizeOpen = 0;\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code);\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(code === codes.graveAccent || code === codes.tilde, 'expected `` ` `` or `~`');\n    const tail = self.events[self.events.length - 1];\n    initialPrefix = tail && tail[1].type === types.linePrefix ? tail[2].sliceSerialize(tail[1], true).length : 0;\n    marker = code;\n    effects.enter(types.codeFenced);\n    effects.enter(types.codeFencedFence);\n    effects.enter(types.codeFencedFenceSequence);\n    return sequenceOpen(code);\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++;\n      effects.consume(code);\n      return sequenceOpen;\n    }\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code);\n    }\n    effects.exit(types.codeFencedFenceSequence);\n    return markdownSpace(code) ? factorySpace(effects, infoBefore, types.whitespace)(code) : infoBefore(code);\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence);\n      return self.interrupt ? ok(code) : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code);\n    }\n    effects.enter(types.codeFencedFenceInfo);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return info(code);\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceInfo);\n      return infoBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceInfo);\n      return factorySpace(effects, metaBefore, types.whitespace)(code);\n    }\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return info;\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code);\n    }\n    effects.enter(types.codeFencedFenceMeta);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return meta(code);\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceMeta);\n      return infoBefore(code);\n    }\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return meta;\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    return effects.attempt(closeStart, after, contentBefore)(code);\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return contentStart;\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code) ? factorySpace(effects, beforeContentChunk, types.linePrefix, initialPrefix + 1)(code) : beforeContentChunk(code);\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code);\n    }\n    effects.enter(types.codeFlowValue);\n    return contentChunk(code);\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue);\n      return beforeContentChunk(code);\n    }\n    effects.consume(code);\n    return contentChunk;\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced);\n    return ok(code);\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0;\n    return startBefore;\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol');\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return start;\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence);\n      return markdownSpace(code) ? factorySpace(effects, beforeSequenceClose, types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code) : beforeSequenceClose(code);\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence);\n        return sequenceClose(code);\n      }\n      return nok(code);\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++;\n        effects.consume(code);\n        return sequenceClose;\n      }\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence);\n        return markdownSpace(code) ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code) : sequenceCloseAfter(code);\n      }\n      return nok(code);\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence);\n        return ok(code);\n      }\n      return nok(code);\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return lineStart;\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}