{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nconst BreadcrumbSeparator = ({\n  children\n}) => {\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('breadcrumb');\n  return /*#__PURE__*/React.createElement(\"li\", {\n    className: `${prefixCls}-separator`,\n    \"aria-hidden\": \"true\"\n  }, children === '' ? children : children || '/');\n};\nBreadcrumbSeparator.__ANT_BREADCRUMB_SEPARATOR = true;\nexport default BreadcrumbSeparator;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}