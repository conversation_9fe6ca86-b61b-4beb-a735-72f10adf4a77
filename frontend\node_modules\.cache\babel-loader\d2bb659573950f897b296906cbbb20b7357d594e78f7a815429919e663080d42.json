{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { SpaceContext } from './context';\nconst Item = ({\n  className,\n  index,\n  children,\n  split,\n  style\n}) => {\n  const {\n    latestIndex\n  } = React.useContext(SpaceContext);\n  if (children === null || children === undefined) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: style\n  }, children), index < latestIndex && split && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${className}-split`\n  }, split));\n};\nexport default Item;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}