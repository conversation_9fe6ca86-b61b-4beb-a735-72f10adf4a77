{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RollbackOutlinedSvg from \"@ant-design/icons-svg/es/asn/RollbackOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RollbackOutlined = function RollbackOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RollbackOutlinedSvg\n  }));\n};\n\n/**![rollback](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc5MyAyNDJIMzY2di03NGMwLTYuNy03LjctMTAuNC0xMi45LTYuM2wtMTQyIDExMmE4IDggMCAwMDAgMTIuNmwxNDIgMTEyYzUuMiA0LjEgMTIuOS40IDEyLjktNi4zdi03NGg0MTV2NDcwSDE3NWMtNC40IDAtOCAzLjYtOCA4djYwYzAgNC40IDMuNiA4IDggOGg2MThjMzUuMyAwIDY0LTI4LjcgNjQtNjRWMzA2YzAtMzUuMy0yOC43LTY0LTY0LTY0eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RollbackOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RollbackOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}