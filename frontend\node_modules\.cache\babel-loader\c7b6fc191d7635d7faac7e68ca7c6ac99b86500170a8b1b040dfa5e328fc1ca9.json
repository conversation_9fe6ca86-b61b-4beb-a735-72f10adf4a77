{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FunnelPlotFilledSvg from \"@ant-design/icons-svg/es/asn/FunnelPlotFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FunnelPlotFilled = function FunnelPlotFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FunnelPlotFilledSvg\n  }));\n};\n\n/**![funnel-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMzNi43IDU4NmgzNTAuNmw4NC45LTE0OEgyNTEuOHptNTQzLjQtNDMySDE0My45Yy0yNC41IDAtMzkuOCAyNi43LTI3LjUgNDhMMjE1IDM3NGg1OTRsOTguNy0xNzJjMTIuMi0yMS4zLTMuMS00OC0yNy42LTQ4ek0zNDkgODM4YzAgMTcuNyAxNC4yIDMyIDMxLjggMzJoMjYyLjRjMTcuNiAwIDMxLjgtMTQuMyAzMS44LTMyVjY1MEgzNDl2MTg4eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FunnelPlotFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FunnelPlotFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}