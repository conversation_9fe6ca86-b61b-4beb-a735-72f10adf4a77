{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport { useCacheToken } from '@ant-design/cssinjs';\nimport version from '../version';\nimport { defaultTheme, DesignTokenContext } from './context';\nimport defaultSeedToken from './themes/seed';\nimport formatToken from './util/alias';\nexport const unitless = {\n  lineHeight: true,\n  lineHeightSM: true,\n  lineHeightLG: true,\n  lineHeightHeading1: true,\n  lineHeightHeading2: true,\n  lineHeightHeading3: true,\n  lineHeightHeading4: true,\n  lineHeightHeading5: true,\n  opacityLoading: true,\n  fontWeightStrong: true,\n  zIndexPopupBase: true,\n  zIndexBase: true,\n  opacityImage: true\n};\nexport const ignore = {\n  size: true,\n  sizeSM: true,\n  sizeLG: true,\n  sizeMD: true,\n  sizeXS: true,\n  sizeXXS: true,\n  sizeMS: true,\n  sizeXL: true,\n  sizeXXL: true,\n  sizeUnit: true,\n  sizeStep: true,\n  motionBase: true,\n  motionUnit: true\n};\nconst preserve = {\n  screenXS: true,\n  screenXSMin: true,\n  screenXSMax: true,\n  screenSM: true,\n  screenSMMin: true,\n  screenSMMax: true,\n  screenMD: true,\n  screenMDMin: true,\n  screenMDMax: true,\n  screenLG: true,\n  screenLGMin: true,\n  screenLGMax: true,\n  screenXL: true,\n  screenXLMin: true,\n  screenXLMax: true,\n  screenXXL: true,\n  screenXXLMin: true\n};\nexport const getComputedToken = (originToken, overrideToken, theme) => {\n  const derivativeToken = theme.getDerivativeToken(originToken);\n  const {\n      override\n    } = overrideToken,\n    components = __rest(overrideToken, [\"override\"]);\n  // Merge with override\n  let mergedDerivativeToken = Object.assign(Object.assign({}, derivativeToken), {\n    override\n  });\n  // Format if needed\n  mergedDerivativeToken = formatToken(mergedDerivativeToken);\n  if (components) {\n    Object.entries(components).forEach(([key, value]) => {\n      const {\n          theme: componentTheme\n        } = value,\n        componentTokens = __rest(value, [\"theme\"]);\n      let mergedComponentToken = componentTokens;\n      if (componentTheme) {\n        mergedComponentToken = getComputedToken(Object.assign(Object.assign({}, mergedDerivativeToken), componentTokens), {\n          override: componentTokens\n        }, componentTheme);\n      }\n      mergedDerivativeToken[key] = mergedComponentToken;\n    });\n  }\n  return mergedDerivativeToken;\n};\n// ================================== Hook ==================================\nexport default function useToken() {\n  const {\n    token: rootDesignToken,\n    hashed,\n    theme,\n    override,\n    cssVar\n  } = React.useContext(DesignTokenContext);\n  const salt = `${version}-${hashed || ''}`;\n  const mergedTheme = theme || defaultTheme;\n  const [token, hashId, realToken] = useCacheToken(mergedTheme, [defaultSeedToken, rootDesignToken], {\n    salt,\n    override,\n    getComputedToken,\n    // formatToken will not be consumed after 1.15.0 with getComputedToken.\n    // But token will break if @ant-design/cssinjs is under 1.15.0 without it\n    formatToken,\n    cssVar: cssVar && {\n      prefix: cssVar.prefix,\n      key: cssVar.key,\n      unitless,\n      ignore,\n      preserve\n    }\n  });\n  return [mergedTheme, realToken, hashed ? hashId : '', token, cssVar];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}