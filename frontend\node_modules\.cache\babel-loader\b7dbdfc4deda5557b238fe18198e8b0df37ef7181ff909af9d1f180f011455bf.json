{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\HomeworkAnalysis\\\\QuestionAnalysis.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, Progress, Tag, Space, Typography, Alert, Divider, List, Avatar, Tooltip } from 'antd';\nimport api from '../../utils/api';\nimport { CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined, EditOutlined, BarChartOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst QuestionAnalysis = ({\n  assignmentId,\n  user,\n  onLoading\n}) => {\n  _s();\n  const [questionsData, setQuestionsData] = useState(null);\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\n  const [error, setError] = useState(null);\n\n  // 获取题目分析数据\n  const fetchQuestionsData = async () => {\n    console.log('🔍 QuestionAnalysis fetchQuestionsData called, assignmentId:', assignmentId);\n    if (!assignmentId) {\n      console.log('❌ No assignmentId provided');\n      return;\n    }\n    try {\n      onLoading(true);\n      console.log('📡 Requesting questions data for assignment:', assignmentId);\n      const response = await api.get(`/homework-analysis/questions/${assignmentId}`);\n      console.log('📥 Questions API response:', response);\n\n      // 注意：api拦截器已经返回了response.data，所以response就是API的响应体\n      if (response && response.success) {\n        console.log('✅ Questions data received:', response.data);\n        setQuestionsData(response.data);\n\n        // 默认选择第一题\n        if (response.data && response.data.questions && response.data.questions.length > 0) {\n          console.log('🎯 Setting first question as selected:', response.data.questions[0]);\n          setSelectedQuestion(response.data.questions[0]);\n        } else {\n          console.log('⚠️ No questions found in response data');\n        }\n        setError(null);\n      } else {\n        console.log('❌ API returned failure:', (response === null || response === void 0 ? void 0 : response.message) || 'Unknown error');\n        throw new Error((response === null || response === void 0 ? void 0 : response.message) || '获取数据失败');\n      }\n    } catch (err) {\n      console.error('❌ 获取题目分析失败:', err);\n      setError(err.message || '获取题目分析数据失败');\n    } finally {\n      onLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchQuestionsData();\n  }, [assignmentId]);\n\n  // 获取状态颜色和图标\n  const getStatusConfig = status => {\n    switch (status) {\n      case 'excellent':\n        return {\n          color: '#52c41a',\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 42\n          }, this),\n          text: '优秀'\n        };\n      case 'warning':\n        return {\n          color: '#faad14',\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 42\n          }, this),\n          text: '注意'\n        };\n      case 'error':\n        return {\n          color: '#ff4d4f',\n          icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 42\n          }, this),\n          text: '重点'\n        };\n      default:\n        return {\n          color: '#d9d9d9',\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 42\n          }, this),\n          text: '未知'\n        };\n    }\n  };\n\n  // 渲染题目导航\n  const renderQuestionNavigation = () => {\n    if (!questionsData) return null;\n    const {\n      questions\n    } = questionsData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9898\\u76EE\\u5BFC\\u822A\",\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: questions.map(question => {\n          const statusConfig = getStatusConfig(question.status);\n          const isSelected = selectedQuestion && selectedQuestion.question_number === question.question_number;\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `第${question.question_number}题 - 正确率: ${question.accuracy_rate}%`,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: isSelected ? 'primary' : 'default',\n              style: {\n                borderColor: statusConfig.color,\n                color: isSelected ? '#fff' : statusConfig.color\n              },\n              icon: statusConfig.icon,\n              onClick: () => setSelectedQuestion(question),\n              children: question.question_number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this)\n          }, question.question_number, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n            style: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), \"\\u4F18\\u79C0 (\\u6B63\\u786E\\u7387\\u226580%): \", questionsData.summary.excellent_questions, \"\\u9898\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n            style: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), \"\\u6CE8\\u610F (\\u6B63\\u786E\\u738760-80%): \", questionsData.summary.warning_questions, \"\\u9898\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n            style: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), \"\\u91CD\\u70B9 (\\u6B63\\u786E\\u7387<60%): \", questionsData.summary.error_questions, \"\\u9898\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染题目详情\n  const renderQuestionDetail = () => {\n    if (!selectedQuestion) return null;\n    const statusConfig = getStatusConfig(selectedQuestion.status);\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: [\"\\u7B2C\", selectedQuestion.question_number, \"\\u9898\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: statusConfig.color,\n          icon: statusConfig.icon,\n          children: selectedQuestion.question_type || '题目'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [\"\\u5F97\\u5206\\u7387: \", selectedQuestion.accuracy_rate, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: statusConfig.color,\n          children: statusConfig.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this),\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 27\n          }, this),\n          size: \"small\",\n          children: \"\\u4FEE\\u6539\\u7B54\\u6848\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 27\n          }, this),\n          size: \"small\",\n          children: \"\\u7B54\\u9898\\u7EDF\\u8BA1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this),\n      children: [selectedQuestion.question_content && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u9898\\u76EE\\u5185\\u5BB9\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            marginTop: 8,\n            padding: 12,\n            background: '#f5f5f5',\n            borderRadius: 4\n          },\n          children: selectedQuestion.question_content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u9009\\u9879\\u5206\\u5E03\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: renderOptionDistribution()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: \"\\u5B66\\u751F\\u7B54\\u9898\\u60C5\\u51B5\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: renderStudentAnswers()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), selectedQuestion.correct_answer && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n            style: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: [\"\\u6B63\\u786E\\u7B54\\u6848\\uFF1A\", selectedQuestion.correct_answer]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染选项分布\n  const renderOptionDistribution = () => {\n    if (!selectedQuestion || !selectedQuestion.student_answers) return null;\n\n    // 统计选项分布\n    const optionStats = {};\n    selectedQuestion.student_answers.forEach(answer => {\n      var _answer$student_answe;\n      const option = (_answer$student_answe = answer.student_answer) === null || _answer$student_answe === void 0 ? void 0 : _answer$student_answe.trim().toUpperCase();\n      if (option) {\n        optionStats[option] = (optionStats[option] || 0) + 1;\n      }\n    });\n    const totalAnswers = selectedQuestion.total_answers;\n    return /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      style: {\n        width: '100%'\n      },\n      children: Object.entries(optionStats).map(([option, count]) => {\n        var _selectedQuestion$cor;\n        const percentage = totalAnswers > 0 ? count / totalAnswers * 100 : 0;\n        const isCorrect = option === ((_selectedQuestion$cor = selectedQuestion.correct_answer) === null || _selectedQuestion$cor === void 0 ? void 0 : _selectedQuestion$cor.trim().toUpperCase());\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'space-between'\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                color: isCorrect ? '#52c41a' : '#000'\n              },\n              children: [\"\\u3010\", option, \"\\u3011 \", count, \"\\u4EBA / \\u5360\", percentage.toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: percentage,\n            strokeColor: isCorrect ? '#52c41a' : '#1890ff',\n            showInfo: false,\n            style: {\n              marginTop: 4\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, option, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染学生答题情况\n  const renderStudentAnswers = () => {\n    if (!selectedQuestion || !selectedQuestion.student_answers) return null;\n    const correctAnswers = selectedQuestion.student_answers.filter(a => a.is_correct);\n    const wrongAnswers = selectedQuestion.student_answers.filter(a => !a.is_correct);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: [correctAnswers.slice(0, 10).map((answer, index) => /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 63\n          }, this),\n          children: answer.student_name || `学生${index + 1}`\n        }, `correct-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)), correctAnswers.length > 10 && /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          children: [\"+\", correctAnswers.length - 10, \"\\u4EBA\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), wrongAnswers.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          type: \"vertical\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          wrap: true,\n          children: [wrongAnswers.slice(0, 10).map((answer, index) => /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"red\",\n            icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 63\n            }, this),\n            children: answer.student_name || `学生${index + 1}`\n          }, `wrong-${index}`, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this)), wrongAnswers.length > 10 && /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"red\",\n            children: [\"+\", wrongAnswers.length - 10, \"\\u4EBA\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"\\u6B63\\u786E: \", correctAnswers.length, \"\\u4EBA\\uFF0C\\u9519\\u8BEF: \", wrongAnswers.length, \"\\u4EBA\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this);\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u52A0\\u8F7D\\u5931\\u8D25\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      action: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        onClick: fetchQuestionsData,\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this);\n  }\n  if (!questionsData) {\n    console.log('🔄 QuestionAnalysis rendering loading state, questionsData:', questionsData);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u9010\\u9898\\u5206\\u6790\\u6570\\u636E...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '10px',\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: [\"Assignment ID: \", assignmentId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u9010\\u9898\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"large\",\n      style: {\n        width: '100%'\n      },\n      children: [renderQuestionNavigation(), renderQuestionDetail()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 323,\n    columnNumber: 5\n  }, this);\n};\n_s(QuestionAnalysis, \"2Uosy8fJl8e08HxnYWGj3h1lQXQ=\");\n_c = QuestionAnalysis;\nexport default QuestionAnalysis;\nvar _c;\n$RefreshReg$(_c, \"QuestionAnalysis\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "Progress", "Tag", "Space", "Typography", "<PERSON><PERSON>", "Divider", "List", "Avatar", "<PERSON><PERSON><PERSON>", "api", "CheckCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "EditOutlined", "BarChartOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "QuestionAnalysis", "assignmentId", "user", "onLoading", "_s", "questionsData", "setQuestionsData", "selectedQuestion", "setSelectedQuestion", "error", "setError", "fetchQuestionsData", "console", "log", "response", "get", "success", "data", "questions", "length", "message", "Error", "err", "getStatusConfig", "status", "color", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "renderQuestionNavigation", "title", "style", "marginBottom", "children", "wrap", "map", "question", "statusConfig", "isSelected", "question_number", "accuracy_rate", "type", "borderColor", "onClick", "summary", "excellent_questions", "warning_questions", "error_questions", "renderQuestionDetail", "strong", "question_type", "extra", "size", "question_content", "marginTop", "padding", "background", "borderRadius", "renderOptionDistribution", "renderStudentAnswers", "correct_answer", "student_answers", "optionStats", "for<PERSON>ach", "answer", "_answer$student_answe", "option", "student_answer", "trim", "toUpperCase", "totalAnswers", "total_answers", "direction", "width", "Object", "entries", "count", "_selectedQuestion$cor", "percentage", "isCorrect", "justifyContent", "toFixed", "percent", "strokeColor", "showInfo", "correctAnswers", "filter", "a", "is_correct", "wrongAnswers", "slice", "index", "student_name", "description", "showIcon", "action", "textAlign", "fontSize", "level", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/HomeworkAnalysis/QuestionAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Card, Row, Col, Button, Progress, Tag, Space, Typography,\r\n  Alert, Divider, List, Avatar, Tooltip\r\n} from 'antd';\r\nimport api from '../../utils/api';\r\nimport {\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  EditOutlined,\r\n  BarChartOutlined\r\n} from '@ant-design/icons';\r\n\r\nconst { Title, Text, Paragraph } = Typography;\r\n\r\nconst QuestionAnalysis = ({ assignmentId, user, onLoading }) => {\r\n  const [questionsData, setQuestionsData] = useState(null);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [error, setError] = useState(null);\r\n\r\n  // 获取题目分析数据\r\n  const fetchQuestionsData = async () => {\r\n    console.log('🔍 QuestionAnalysis fetchQuestionsData called, assignmentId:', assignmentId);\r\n\r\n    if (!assignmentId) {\r\n      console.log('❌ No assignmentId provided');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      onLoading(true);\r\n      console.log('📡 Requesting questions data for assignment:', assignmentId);\r\n\r\n      const response = await api.get(`/homework-analysis/questions/${assignmentId}`);\r\n      console.log('📥 Questions API response:', response);\r\n\r\n      // 注意：api拦截器已经返回了response.data，所以response就是API的响应体\r\n      if (response && response.success) {\r\n        console.log('✅ Questions data received:', response.data);\r\n        setQuestionsData(response.data);\r\n\r\n        // 默认选择第一题\r\n        if (response.data && response.data.questions && response.data.questions.length > 0) {\r\n          console.log('🎯 Setting first question as selected:', response.data.questions[0]);\r\n          setSelectedQuestion(response.data.questions[0]);\r\n        } else {\r\n          console.log('⚠️ No questions found in response data');\r\n        }\r\n        setError(null);\r\n      } else {\r\n        console.log('❌ API returned failure:', response?.message || 'Unknown error');\r\n        throw new Error(response?.message || '获取数据失败');\r\n      }\r\n    } catch (err) {\r\n      console.error('❌ 获取题目分析失败:', err);\r\n      setError(err.message || '获取题目分析数据失败');\r\n    } finally {\r\n      onLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchQuestionsData();\r\n  }, [assignmentId]);\r\n\r\n  // 获取状态颜色和图标\r\n  const getStatusConfig = (status) => {\r\n    switch (status) {\r\n      case 'excellent':\r\n        return { color: '#52c41a', icon: <CheckCircleOutlined />, text: '优秀' };\r\n      case 'warning':\r\n        return { color: '#faad14', icon: <ExclamationCircleOutlined />, text: '注意' };\r\n      case 'error':\r\n        return { color: '#ff4d4f', icon: <CloseCircleOutlined />, text: '重点' };\r\n      default:\r\n        return { color: '#d9d9d9', icon: <CheckCircleOutlined />, text: '未知' };\r\n    }\r\n  };\r\n\r\n  // 渲染题目导航\r\n  const renderQuestionNavigation = () => {\r\n    if (!questionsData) return null;\r\n    \r\n    const { questions } = questionsData;\r\n    \r\n    return (\r\n      <Card title=\"题目导航\" style={{ marginBottom: 16 }}>\r\n        <Space wrap>\r\n          {questions.map((question) => {\r\n            const statusConfig = getStatusConfig(question.status);\r\n            const isSelected = selectedQuestion && \r\n              selectedQuestion.question_number === question.question_number;\r\n            \r\n            return (\r\n              <Tooltip \r\n                key={question.question_number}\r\n                title={`第${question.question_number}题 - 正确率: ${question.accuracy_rate}%`}\r\n              >\r\n                <Button\r\n                  type={isSelected ? 'primary' : 'default'}\r\n                  style={{ \r\n                    borderColor: statusConfig.color,\r\n                    color: isSelected ? '#fff' : statusConfig.color\r\n                  }}\r\n                  icon={statusConfig.icon}\r\n                  onClick={() => setSelectedQuestion(question)}\r\n                >\r\n                  {question.question_number}\r\n                </Button>\r\n              </Tooltip>\r\n            );\r\n          })}\r\n        </Space>\r\n        \r\n        <Divider />\r\n        \r\n        <Space>\r\n          <Text>\r\n            <CheckCircleOutlined style={{ color: '#52c41a' }} /> \r\n            优秀 (正确率≥80%): {questionsData.summary.excellent_questions}题\r\n          </Text>\r\n          <Text>\r\n            <ExclamationCircleOutlined style={{ color: '#faad14' }} /> \r\n            注意 (正确率60-80%): {questionsData.summary.warning_questions}题\r\n          </Text>\r\n          <Text>\r\n            <CloseCircleOutlined style={{ color: '#ff4d4f' }} />\r\n            重点 (正确率&lt;60%): {questionsData.summary.error_questions}题\r\n          </Text>\r\n        </Space>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  // 渲染题目详情\r\n  const renderQuestionDetail = () => {\r\n    if (!selectedQuestion) return null;\r\n    \r\n    const statusConfig = getStatusConfig(selectedQuestion.status);\r\n    \r\n    return (\r\n      <Card \r\n        title={\r\n          <Space>\r\n            <Text strong>第{selectedQuestion.question_number}题</Text>\r\n            <Tag color={statusConfig.color} icon={statusConfig.icon}>\r\n              {selectedQuestion.question_type || '题目'}\r\n            </Tag>\r\n            <Text>得分率: {selectedQuestion.accuracy_rate}%</Text>\r\n            <Tag color={statusConfig.color}>{statusConfig.text}</Tag>\r\n          </Space>\r\n        }\r\n        extra={\r\n          <Space>\r\n            <Button icon={<EditOutlined />} size=\"small\">\r\n              修改答案\r\n            </Button>\r\n            <Button icon={<BarChartOutlined />} size=\"small\">\r\n              答题统计\r\n            </Button>\r\n          </Space>\r\n        }\r\n      >\r\n        {/* 题目内容 */}\r\n        {selectedQuestion.question_content && (\r\n          <div style={{ marginBottom: 16 }}>\r\n            <Text strong>题目内容：</Text>\r\n            <Paragraph style={{ marginTop: 8, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>\r\n              {selectedQuestion.question_content}\r\n            </Paragraph>\r\n          </div>\r\n        )}\r\n        \r\n        {/* 选项分布 */}\r\n        <div style={{ marginBottom: 16 }}>\r\n          <Text strong>选项分布：</Text>\r\n          <div style={{ marginTop: 8 }}>\r\n            {renderOptionDistribution()}\r\n          </div>\r\n        </div>\r\n        \r\n        {/* 学生答题情况 */}\r\n        <div style={{ marginBottom: 16 }}>\r\n          <Text strong>学生答题情况：</Text>\r\n          <div style={{ marginTop: 8 }}>\r\n            {renderStudentAnswers()}\r\n          </div>\r\n        </div>\r\n        \r\n        {/* 正确答案 */}\r\n        {selectedQuestion.correct_answer && (\r\n          <div>\r\n            <Space>\r\n              <CheckCircleOutlined style={{ color: '#52c41a' }} />\r\n              <Text strong>正确答案：{selectedQuestion.correct_answer}</Text>\r\n            </Space>\r\n          </div>\r\n        )}\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  // 渲染选项分布\r\n  const renderOptionDistribution = () => {\r\n    if (!selectedQuestion || !selectedQuestion.student_answers) return null;\r\n    \r\n    // 统计选项分布\r\n    const optionStats = {};\r\n    selectedQuestion.student_answers.forEach(answer => {\r\n      const option = answer.student_answer?.trim().toUpperCase();\r\n      if (option) {\r\n        optionStats[option] = (optionStats[option] || 0) + 1;\r\n      }\r\n    });\r\n    \r\n    const totalAnswers = selectedQuestion.total_answers;\r\n    \r\n    return (\r\n      <Space direction=\"vertical\" style={{ width: '100%' }}>\r\n        {Object.entries(optionStats).map(([option, count]) => {\r\n          const percentage = totalAnswers > 0 ? (count / totalAnswers * 100) : 0;\r\n          const isCorrect = option === selectedQuestion.correct_answer?.trim().toUpperCase();\r\n          \r\n          return (\r\n            <div key={option} style={{ width: '100%' }}>\r\n              <Space style={{ width: '100%', justifyContent: 'space-between' }}>\r\n                <Text strong style={{ color: isCorrect ? '#52c41a' : '#000' }}>\r\n                  【{option}】 {count}人 / 占{percentage.toFixed(1)}%\r\n                </Text>\r\n              </Space>\r\n              <Progress \r\n                percent={percentage}\r\n                strokeColor={isCorrect ? '#52c41a' : '#1890ff'}\r\n                showInfo={false}\r\n                style={{ marginTop: 4 }}\r\n              />\r\n            </div>\r\n          );\r\n        })}\r\n      </Space>\r\n    );\r\n  };\r\n\r\n  // 渲染学生答题情况\r\n  const renderStudentAnswers = () => {\r\n    if (!selectedQuestion || !selectedQuestion.student_answers) return null;\r\n\r\n    const correctAnswers = selectedQuestion.student_answers.filter(a => a.is_correct);\r\n    const wrongAnswers = selectedQuestion.student_answers.filter(a => !a.is_correct);\r\n\r\n    return (\r\n      <div>\r\n        <Space wrap>\r\n          {correctAnswers.slice(0, 10).map((answer, index) => (\r\n            <Tag key={`correct-${index}`} color=\"green\" icon={<CheckCircleOutlined />}>\r\n              {answer.student_name || `学生${index + 1}`}\r\n            </Tag>\r\n          ))}\r\n          {correctAnswers.length > 10 && (\r\n            <Tag color=\"green\">\r\n              +{correctAnswers.length - 10}人\r\n            </Tag>\r\n          )}\r\n        </Space>\r\n\r\n        {wrongAnswers.length > 0 && (\r\n          <>\r\n            <Divider type=\"vertical\" />\r\n            <Space wrap>\r\n              {wrongAnswers.slice(0, 10).map((answer, index) => (\r\n                <Tag key={`wrong-${index}`} color=\"red\" icon={<CloseCircleOutlined />}>\r\n                  {answer.student_name || `学生${index + 1}`}\r\n                </Tag>\r\n              ))}\r\n              {wrongAnswers.length > 10 && (\r\n                <Tag color=\"red\">\r\n                  +{wrongAnswers.length - 10}人\r\n                </Tag>\r\n              )}\r\n            </Space>\r\n          </>\r\n        )}\r\n\r\n        <div style={{ marginTop: 8 }}>\r\n          <Text type=\"secondary\">\r\n            正确: {correctAnswers.length}人，错误: {wrongAnswers.length}人\r\n          </Text>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert\r\n        message=\"加载失败\"\r\n        description={error}\r\n        type=\"error\"\r\n        showIcon\r\n        action={\r\n          <Button size=\"small\" onClick={fetchQuestionsData}>\r\n            重试\r\n          </Button>\r\n        }\r\n      />\r\n    );\r\n  }\r\n\r\n  if (!questionsData) {\r\n    console.log('🔄 QuestionAnalysis rendering loading state, questionsData:', questionsData);\r\n    return (\r\n      <div style={{ textAlign: 'center', padding: '50px' }}>\r\n        <div>正在加载逐题分析数据...</div>\r\n        <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>\r\n          Assignment ID: {assignmentId}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <Title level={2}>逐题分析</Title>\r\n      \r\n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\r\n        {/* 题目导航 */}\r\n        {renderQuestionNavigation()}\r\n        \r\n        {/* 题目详情 */}\r\n        {renderQuestionDetail()}\r\n      </Space>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionAnalysis;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EACxDC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAChC,MAAM;AACb,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,mBAAmB,EACnBC,YAAY,EACZC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGlB,UAAU;AAE7C,MAAMmB,gBAAgB,GAAGA,CAAC;EAAEC,YAAY;EAAEC,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMuC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCC,OAAO,CAACC,GAAG,CAAC,8DAA8D,EAAEZ,YAAY,CAAC;IAEzF,IAAI,CAACA,YAAY,EAAE;MACjBW,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC;IACF;IAEA,IAAI;MACFV,SAAS,CAAC,IAAI,CAAC;MACfS,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEZ,YAAY,CAAC;MAEzE,MAAMa,QAAQ,GAAG,MAAM3B,GAAG,CAAC4B,GAAG,CAAC,gCAAgCd,YAAY,EAAE,CAAC;MAC9EW,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,CAAC;;MAEnD;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,OAAO,EAAE;QAChCJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,CAACG,IAAI,CAAC;QACxDX,gBAAgB,CAACQ,QAAQ,CAACG,IAAI,CAAC;;QAE/B;QACA,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,SAAS,IAAIJ,QAAQ,CAACG,IAAI,CAACC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UAClFP,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,QAAQ,CAACG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;UACjFV,mBAAmB,CAACM,QAAQ,CAACG,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,MAAM;UACLN,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD;QACAH,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACLE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,CAAAC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,OAAO,KAAI,eAAe,CAAC;QAC5E,MAAM,IAAIC,KAAK,CAAC,CAAAP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,OAAO,KAAI,QAAQ,CAAC;MAChD;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZV,OAAO,CAACH,KAAK,CAAC,aAAa,EAAEa,GAAG,CAAC;MACjCZ,QAAQ,CAACY,GAAG,CAACF,OAAO,IAAI,YAAY,CAAC;IACvC,CAAC,SAAS;MACRjB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED9B,SAAS,CAAC,MAAM;IACdsC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACV,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMsB,eAAe,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO;UAAEC,KAAK,EAAE,SAAS;UAAEC,IAAI,eAAEhC,OAAA,CAACN,mBAAmB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC;MACxE,KAAK,SAAS;QACZ,OAAO;UAAEN,KAAK,EAAE,SAAS;UAAEC,IAAI,eAAEhC,OAAA,CAACL,yBAAyB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC;MAC9E,KAAK,OAAO;QACV,OAAO;UAAEN,KAAK,EAAE,SAAS;UAAEC,IAAI,eAAEhC,OAAA,CAACJ,mBAAmB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC;MACxE;QACE,OAAO;UAAEN,KAAK,EAAE,SAAS;UAAEC,IAAI,eAAEhC,OAAA,CAACN,mBAAmB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC;IAC1E;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC3B,aAAa,EAAE,OAAO,IAAI;IAE/B,MAAM;MAAEa;IAAU,CAAC,GAAGb,aAAa;IAEnC,oBACEX,OAAA,CAACpB,IAAI;MAAC2D,KAAK,EAAC,0BAAM;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAC,QAAA,gBAC7C1C,OAAA,CAACd,KAAK;QAACyD,IAAI;QAAAD,QAAA,EACRlB,SAAS,CAACoB,GAAG,CAAEC,QAAQ,IAAK;UAC3B,MAAMC,YAAY,GAAGjB,eAAe,CAACgB,QAAQ,CAACf,MAAM,CAAC;UACrD,MAAMiB,UAAU,GAAGlC,gBAAgB,IACjCA,gBAAgB,CAACmC,eAAe,KAAKH,QAAQ,CAACG,eAAe;UAE/D,oBACEhD,OAAA,CAACR,OAAO;YAEN+C,KAAK,EAAE,IAAIM,QAAQ,CAACG,eAAe,YAAYH,QAAQ,CAACI,aAAa,GAAI;YAAAP,QAAA,eAEzE1C,OAAA,CAACjB,MAAM;cACLmE,IAAI,EAAEH,UAAU,GAAG,SAAS,GAAG,SAAU;cACzCP,KAAK,EAAE;gBACLW,WAAW,EAAEL,YAAY,CAACf,KAAK;gBAC/BA,KAAK,EAAEgB,UAAU,GAAG,MAAM,GAAGD,YAAY,CAACf;cAC5C,CAAE;cACFC,IAAI,EAAEc,YAAY,CAACd,IAAK;cACxBoB,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAAC+B,QAAQ,CAAE;cAAAH,QAAA,EAE5CG,QAAQ,CAACG;YAAe;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC,GAbJS,QAAQ,CAACG,eAAe;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OActB,CAAC;QAEd,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAERpC,OAAA,CAACX,OAAO;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXpC,OAAA,CAACd,KAAK;QAAAwD,QAAA,gBACJ1C,OAAA,CAACI,IAAI;UAAAsC,QAAA,gBACH1C,OAAA,CAACN,mBAAmB;YAAC8C,KAAK,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gDACtC,EAACzB,aAAa,CAAC0C,OAAO,CAACC,mBAAmB,EAAC,QAC3D;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpC,OAAA,CAACI,IAAI;UAAAsC,QAAA,gBACH1C,OAAA,CAACL,yBAAyB;YAAC6C,KAAK,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6CAC1C,EAACzB,aAAa,CAAC0C,OAAO,CAACE,iBAAiB,EAAC,QAC3D;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPpC,OAAA,CAACI,IAAI;UAAAsC,QAAA,gBACH1C,OAAA,CAACJ,mBAAmB;YAAC4C,KAAK,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2CACnC,EAACzB,aAAa,CAAC0C,OAAO,CAACG,eAAe,EAAC,QAC1D;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEX,CAAC;;EAED;EACA,MAAMqB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC5C,gBAAgB,EAAE,OAAO,IAAI;IAElC,MAAMiC,YAAY,GAAGjB,eAAe,CAAChB,gBAAgB,CAACiB,MAAM,CAAC;IAE7D,oBACE9B,OAAA,CAACpB,IAAI;MACH2D,KAAK,eACHvC,OAAA,CAACd,KAAK;QAAAwD,QAAA,gBACJ1C,OAAA,CAACI,IAAI;UAACsD,MAAM;UAAAhB,QAAA,GAAC,QAAC,EAAC7B,gBAAgB,CAACmC,eAAe,EAAC,QAAC;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDpC,OAAA,CAACf,GAAG;UAAC8C,KAAK,EAAEe,YAAY,CAACf,KAAM;UAACC,IAAI,EAAEc,YAAY,CAACd,IAAK;UAAAU,QAAA,EACrD7B,gBAAgB,CAAC8C,aAAa,IAAI;QAAI;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNpC,OAAA,CAACI,IAAI;UAAAsC,QAAA,GAAC,sBAAK,EAAC7B,gBAAgB,CAACoC,aAAa,EAAC,GAAC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDpC,OAAA,CAACf,GAAG;UAAC8C,KAAK,EAAEe,YAAY,CAACf,KAAM;UAAAW,QAAA,EAAEI,YAAY,CAACT;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACR;MACDwB,KAAK,eACH5D,OAAA,CAACd,KAAK;QAAAwD,QAAA,gBACJ1C,OAAA,CAACjB,MAAM;UAACiD,IAAI,eAAEhC,OAAA,CAACH,YAAY;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACyB,IAAI,EAAC,OAAO;UAAAnB,QAAA,EAAC;QAE7C;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpC,OAAA,CAACjB,MAAM;UAACiD,IAAI,eAAEhC,OAAA,CAACF,gBAAgB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACyB,IAAI,EAAC,OAAO;UAAAnB,QAAA,EAAC;QAEjD;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAM,QAAA,GAGA7B,gBAAgB,CAACiD,gBAAgB,iBAChC9D,OAAA;QAAKwC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAC,QAAA,gBAC/B1C,OAAA,CAACI,IAAI;UAACsD,MAAM;UAAAhB,QAAA,EAAC;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzBpC,OAAA,CAACK,SAAS;UAACmC,KAAK,EAAE;YAAEuB,SAAS,EAAE,CAAC;YAAEC,OAAO,EAAE,EAAE;YAAEC,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAxB,QAAA,EACrF7B,gBAAgB,CAACiD;QAAgB;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CACN,eAGDpC,OAAA;QAAKwC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAC,QAAA,gBAC/B1C,OAAA,CAACI,IAAI;UAACsD,MAAM;UAAAhB,QAAA,EAAC;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzBpC,OAAA;UAAKwC,KAAK,EAAE;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAArB,QAAA,EAC1ByB,wBAAwB,CAAC;QAAC;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNpC,OAAA;QAAKwC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAC,QAAA,gBAC/B1C,OAAA,CAACI,IAAI;UAACsD,MAAM;UAAAhB,QAAA,EAAC;QAAO;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3BpC,OAAA;UAAKwC,KAAK,EAAE;YAAEuB,SAAS,EAAE;UAAE,CAAE;UAAArB,QAAA,EAC1B0B,oBAAoB,CAAC;QAAC;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLvB,gBAAgB,CAACwD,cAAc,iBAC9BrE,OAAA;QAAA0C,QAAA,eACE1C,OAAA,CAACd,KAAK;UAAAwD,QAAA,gBACJ1C,OAAA,CAACN,mBAAmB;YAAC8C,KAAK,EAAE;cAAET,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDpC,OAAA,CAACI,IAAI;YAACsD,MAAM;YAAAhB,QAAA,GAAC,gCAAK,EAAC7B,gBAAgB,CAACwD,cAAc;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEX,CAAC;;EAED;EACA,MAAM+B,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAACtD,gBAAgB,IAAI,CAACA,gBAAgB,CAACyD,eAAe,EAAE,OAAO,IAAI;;IAEvE;IACA,MAAMC,WAAW,GAAG,CAAC,CAAC;IACtB1D,gBAAgB,CAACyD,eAAe,CAACE,OAAO,CAACC,MAAM,IAAI;MAAA,IAAAC,qBAAA;MACjD,MAAMC,MAAM,IAAAD,qBAAA,GAAGD,MAAM,CAACG,cAAc,cAAAF,qBAAA,uBAArBA,qBAAA,CAAuBG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC1D,IAAIH,MAAM,EAAE;QACVJ,WAAW,CAACI,MAAM,CAAC,GAAG,CAACJ,WAAW,CAACI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;MACtD;IACF,CAAC,CAAC;IAEF,MAAMI,YAAY,GAAGlE,gBAAgB,CAACmE,aAAa;IAEnD,oBACEhF,OAAA,CAACd,KAAK;MAAC+F,SAAS,EAAC,UAAU;MAACzC,KAAK,EAAE;QAAE0C,KAAK,EAAE;MAAO,CAAE;MAAAxC,QAAA,EAClDyC,MAAM,CAACC,OAAO,CAACb,WAAW,CAAC,CAAC3B,GAAG,CAAC,CAAC,CAAC+B,MAAM,EAAEU,KAAK,CAAC,KAAK;QAAA,IAAAC,qBAAA;QACpD,MAAMC,UAAU,GAAGR,YAAY,GAAG,CAAC,GAAIM,KAAK,GAAGN,YAAY,GAAG,GAAG,GAAI,CAAC;QACtE,MAAMS,SAAS,GAAGb,MAAM,OAAAW,qBAAA,GAAKzE,gBAAgB,CAACwD,cAAc,cAAAiB,qBAAA,uBAA/BA,qBAAA,CAAiCT,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAElF,oBACE9E,OAAA;UAAkBwC,KAAK,EAAE;YAAE0C,KAAK,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBACzC1C,OAAA,CAACd,KAAK;YAACsD,KAAK,EAAE;cAAE0C,KAAK,EAAE,MAAM;cAAEO,cAAc,EAAE;YAAgB,CAAE;YAAA/C,QAAA,eAC/D1C,OAAA,CAACI,IAAI;cAACsD,MAAM;cAAClB,KAAK,EAAE;gBAAET,KAAK,EAAEyD,SAAS,GAAG,SAAS,GAAG;cAAO,CAAE;cAAA9C,QAAA,GAAC,QAC5D,EAACiC,MAAM,EAAC,SAAE,EAACU,KAAK,EAAC,iBAAK,EAACE,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC,EAAC,GAChD;YAAA;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRpC,OAAA,CAAChB,QAAQ;YACP2G,OAAO,EAAEJ,UAAW;YACpBK,WAAW,EAAEJ,SAAS,GAAG,SAAS,GAAG,SAAU;YAC/CK,QAAQ,EAAE,KAAM;YAChBrD,KAAK,EAAE;cAAEuB,SAAS,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA,GAXMuC,MAAM;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEZ,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACvD,gBAAgB,IAAI,CAACA,gBAAgB,CAACyD,eAAe,EAAE,OAAO,IAAI;IAEvE,MAAMwB,cAAc,GAAGjF,gBAAgB,CAACyD,eAAe,CAACyB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC;IACjF,MAAMC,YAAY,GAAGrF,gBAAgB,CAACyD,eAAe,CAACyB,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,UAAU,CAAC;IAEhF,oBACEjG,OAAA;MAAA0C,QAAA,gBACE1C,OAAA,CAACd,KAAK;QAACyD,IAAI;QAAAD,QAAA,GACRoD,cAAc,CAACK,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACvD,GAAG,CAAC,CAAC6B,MAAM,EAAE2B,KAAK,kBAC7CpG,OAAA,CAACf,GAAG;UAA0B8C,KAAK,EAAC,OAAO;UAACC,IAAI,eAAEhC,OAAA,CAACN,mBAAmB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAM,QAAA,EACvE+B,MAAM,CAAC4B,YAAY,IAAI,KAAKD,KAAK,GAAG,CAAC;QAAE,GADhC,WAAWA,KAAK,EAAE;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvB,CACN,CAAC,EACD0D,cAAc,CAACrE,MAAM,GAAG,EAAE,iBACzBzB,OAAA,CAACf,GAAG;UAAC8C,KAAK,EAAC,OAAO;UAAAW,QAAA,GAAC,GAChB,EAACoD,cAAc,CAACrE,MAAM,GAAG,EAAE,EAAC,QAC/B;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAEP8D,YAAY,CAACzE,MAAM,GAAG,CAAC,iBACtBzB,OAAA,CAAAE,SAAA;QAAAwC,QAAA,gBACE1C,OAAA,CAACX,OAAO;UAAC6D,IAAI,EAAC;QAAU;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3BpC,OAAA,CAACd,KAAK;UAACyD,IAAI;UAAAD,QAAA,GACRwD,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACvD,GAAG,CAAC,CAAC6B,MAAM,EAAE2B,KAAK,kBAC3CpG,OAAA,CAACf,GAAG;YAAwB8C,KAAK,EAAC,KAAK;YAACC,IAAI,eAAEhC,OAAA,CAACJ,mBAAmB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAM,QAAA,EACnE+B,MAAM,CAAC4B,YAAY,IAAI,KAAKD,KAAK,GAAG,CAAC;UAAE,GADhC,SAASA,KAAK,EAAE;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACN,CAAC,EACD8D,YAAY,CAACzE,MAAM,GAAG,EAAE,iBACvBzB,OAAA,CAACf,GAAG;YAAC8C,KAAK,EAAC,KAAK;YAAAW,QAAA,GAAC,GACd,EAACwD,YAAY,CAACzE,MAAM,GAAG,EAAE,EAAC,QAC7B;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA,eACR,CACH,eAEDpC,OAAA;QAAKwC,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAE,CAAE;QAAArB,QAAA,eAC3B1C,OAAA,CAACI,IAAI;UAAC8C,IAAI,EAAC,WAAW;UAAAR,QAAA,GAAC,gBACjB,EAACoD,cAAc,CAACrE,MAAM,EAAC,4BAAM,EAACyE,YAAY,CAACzE,MAAM,EAAC,QACxD;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAIrB,KAAK,EAAE;IACT,oBACEf,OAAA,CAACZ,KAAK;MACJsC,OAAO,EAAC,0BAAM;MACd4E,WAAW,EAAEvF,KAAM;MACnBmC,IAAI,EAAC,OAAO;MACZqD,QAAQ;MACRC,MAAM,eACJxG,OAAA,CAACjB,MAAM;QAAC8E,IAAI,EAAC,OAAO;QAACT,OAAO,EAAEnC,kBAAmB;QAAAyB,QAAA,EAAC;MAElD;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEN;EAEA,IAAI,CAACzB,aAAa,EAAE;IAClBO,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAER,aAAa,CAAC;IACzF,oBACEX,OAAA;MAAKwC,KAAK,EAAE;QAAEiE,SAAS,EAAE,QAAQ;QAAEzC,OAAO,EAAE;MAAO,CAAE;MAAAtB,QAAA,gBACnD1C,OAAA;QAAA0C,QAAA,EAAK;MAAa;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxBpC,OAAA;QAAKwC,KAAK,EAAE;UAAEuB,SAAS,EAAE,MAAM;UAAE2C,QAAQ,EAAE,MAAM;UAAE3E,KAAK,EAAE;QAAO,CAAE;QAAAW,QAAA,GAAC,iBACnD,EAACnC,YAAY;MAAA;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEpC,OAAA;IAAA0C,QAAA,gBACE1C,OAAA,CAACG,KAAK;MAACwG,KAAK,EAAE,CAAE;MAAAjE,QAAA,EAAC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE7BpC,OAAA,CAACd,KAAK;MAAC+F,SAAS,EAAC,UAAU;MAACpB,IAAI,EAAC,OAAO;MAACrB,KAAK,EAAE;QAAE0C,KAAK,EAAE;MAAO,CAAE;MAAAxC,QAAA,GAE/DJ,wBAAwB,CAAC,CAAC,EAG1BmB,oBAAoB,CAAC,CAAC;IAAA;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA9TIJ,gBAAgB;AAAAsG,EAAA,GAAhBtG,gBAAgB;AAgUtB,eAAeA,gBAAgB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}