{"ast": null, "code": "import { isValidElement } from 'react';\nfunction convertToTooltipProps(tooltip) {\n  // isNil\n  if (tooltip === undefined || tooltip === null) {\n    return null;\n  }\n  if (typeof tooltip === 'object' && ! /*#__PURE__*/isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nexport default convertToTooltipProps;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}