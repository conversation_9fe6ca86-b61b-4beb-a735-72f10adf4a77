#!/usr/bin/env python3
"""
文件管理服务 - 统一管理上传文件的组织和存储
支持多学校、多年级、多班级的分层管理和新的命名规范
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from pathlib import Path
import shutil
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)

class HomeworkConfig:
    """作业配置类"""
    MAX_PAGES_PER_HOMEWORK = 4              # 最大页数
    MIN_PAGES_PER_HOMEWORK = 1              # 最小页数
    SUPPORTED_IMAGE_FORMATS = [             # 支持的图片格式
        '.jpg', '.jpeg', '.png', '.bmp', '.webp'
    ]
    MAX_FILE_SIZE_MB = 10                   # 单个文件最大10MB
    MAX_TOTAL_SIZE_MB = 40                  # 单次上传总大小最大40MB

class FileManager:
    """文件管理器 - 负责文件的组织、存储和管理"""

    def __init__(self, base_upload_dir: str = "uploads"):
        self.base_upload_dir = Path(base_upload_dir)
        self.ensure_base_directories()
    
    def ensure_base_directories(self):
        """确保基础目录结构存在 - 按照新的命名规范"""
        directories = [
            "schools",                          # 学校根目录
            "system/super_admin_exports",       # 超级管理员导出
            "system/backups",                   # 系统备份
            "temp"                              # 临时文件
        ]

        for dir_path in directories:
            full_path = self.base_upload_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)

    def ensure_school_directories(self, school_id: int):
        """确保学校目录结构存在"""
        school_dir = f"schools/school_{school_id:03d}"
        directories = [
            f"{school_dir}/exports/teacher_exports",
            f"{school_dir}/exports/admin_exports",
            f"{school_dir}/exports/temp"
        ]

        for dir_path in directories:
            full_path = self.base_upload_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)

    def ensure_grade_class_directories(self, school_id: int, grade_class_code: str):
        """确保年级班级目录结构存在"""
        base_dir = f"schools/school_{school_id:03d}/grade_{grade_class_code}"

        # 支持的科目列表
        subjects = ['math', 'chinese', 'english', 'physics', 'chemistry',
                   'biology', 'history', 'geography']

        directories = []
        for subject in subjects:
            directories.extend([
                f"{base_dir}/subjects/{subject}/assignments"
            ])

        for dir_path in directories:
            full_path = self.base_upload_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)

    def ensure_assignment_directories(self, school_id: int, grade_class_code: str,
                                    subject: str, assignment_id: int, assignment_name: str):
        """确保作业任务目录结构存在"""
        assignment_dir = (f"schools/school_{school_id:03d}/grade_{grade_class_code}/"
                         f"subjects/{subject}/assignments/assignment_{assignment_id:03d}_{assignment_name}")

        directories = [
            f"{assignment_dir}/original",
            f"{assignment_dir}/annotated"
        ]

        for dir_path in directories:
            full_path = self.base_upload_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)

        return assignment_dir
    
    def generate_homework_filename(self, user_id: int, homework_id: int,
                                 page_number: int, file_extension: str = ".jpg") -> str:
        """
        生成作业图片文件名
        格式: user_{用户ID}_{时间戳}_hw{作业ID}_p{页码}.{扩展名}
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"user_{user_id}_{timestamp}_hw{homework_id}_p{page_number}{file_extension}"

    def generate_annotated_filename(self, original_filename: str) -> str:
        """
        生成批注图片文件名
        在原始文件名基础上添加_annotated后缀
        """
        name, ext = os.path.splitext(original_filename)
        return f"{name}_annotated{ext}"

    def generate_homework_file_path(self, school_id: int, grade_class_code: str,
                                  subject: str, assignment_id: int, assignment_name: str,
                                  user_id: int, homework_id: int, page_number: int,
                                  file_extension: str = ".jpg", is_annotated: bool = False) -> Dict[str, str]:
        """
        生成作业文件的完整路径信息

        Args:
            school_id: 学校ID
            grade_class_code: 年级班级代码 (如: 701, 702, 801)
            subject: 科目名称
            assignment_id: 作业任务ID
            assignment_name: 作业任务名称
            user_id: 用户ID
            homework_id: 作业ID
            page_number: 页码 (1-4)
            file_extension: 文件扩展名
            is_annotated: 是否为批注文件

        Returns:
            包含文件路径信息的字典
        """
        # 确保目录结构存在
        assignment_dir = self.ensure_assignment_directories(
            school_id, grade_class_code, subject, assignment_id, assignment_name
        )

        # 生成文件名
        if is_annotated:
            original_filename = self.generate_homework_filename(
                user_id, homework_id, page_number, file_extension
            )
            filename = self.generate_annotated_filename(original_filename)
            subdir = "annotated"
        else:
            filename = self.generate_homework_filename(
                user_id, homework_id, page_number, file_extension
            )
            subdir = "original"

        # 构建完整路径
        dir_path = self.base_upload_dir / assignment_dir / subdir
        full_path = dir_path / filename
        relative_path = full_path.relative_to(self.base_upload_dir)
        url_path = f"/uploads/{relative_path.as_posix()}"

        return {
            "filename": filename,
            "full_path": str(full_path),
            "relative_path": str(relative_path),
            "url_path": url_path,
            "directory": str(dir_path),
            "assignment_dir": assignment_dir
        }
    
    def generate_export_filename(self, export_type: str, school_name: str,
                               grade_class: str, subject: str, assignment_name: str) -> str:
        """
        生成导出文件名
        格式: {导出类型}_{学校名称}_{年级班级}_{科目}_{作业任务名称}_{导出时间}.zip
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{export_type}_{school_name}_{grade_class}_{subject}_{assignment_name}_{timestamp}.zip"

    def get_grade_class_code(self, grade: str, class_name: str) -> str:
        """
        生成年级班级代码
        例如: 7年级1班 -> 701, 8年级2班 -> 802
        """
        # 提取年级数字
        grade_num = ''.join(filter(str.isdigit, grade))
        # 提取班级数字
        class_num = ''.join(filter(str.isdigit, class_name))

        if grade_num and class_num:
            return f"{grade_num}{class_num:0>2}"  # 班级号补零到2位
        else:
            # 如果无法提取数字，使用默认格式
            return f"{grade}_{class_name}".replace(' ', '_')

    def save_file(self, file_content: bytes, file_info: Dict[str, str]) -> bool:
        """
        保存文件到指定路径

        Args:
            file_content: 文件内容
            file_info: 文件信息字典

        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_info["full_path"]), exist_ok=True)

            with open(file_info["full_path"], "wb") as f:
                f.write(file_content)
            logger.info(f"文件保存成功: {file_info['filename']}")
            return True
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False
    
    def move_temp_file(self, temp_path: str, target_info: Dict[str, str]) -> bool:
        """
        将临时文件移动到目标位置

        Args:
            temp_path: 临时文件路径
            target_info: 目标文件信息

        Returns:
            是否移动成功
        """
        try:
            # 确保目标目录存在
            os.makedirs(os.path.dirname(target_info["full_path"]), exist_ok=True)
            shutil.move(temp_path, target_info["full_path"])
            logger.info(f"文件移动成功: {temp_path} -> {target_info['full_path']}")
            return True
        except Exception as e:
            logger.error(f"移动文件失败: {e}")
            return False

    def validate_homework_file(self, file_size: int, file_extension: str) -> Tuple[bool, str]:
        """
        验证作业文件

        Args:
            file_size: 文件大小（字节）
            file_extension: 文件扩展名

        Returns:
            (是否有效, 错误信息)
        """
        # 检查文件格式
        if file_extension.lower() not in HomeworkConfig.SUPPORTED_IMAGE_FORMATS:
            return False, f"不支持的文件格式: {file_extension}"

        # 检查文件大小
        max_size_bytes = HomeworkConfig.MAX_FILE_SIZE_MB * 1024 * 1024
        if file_size > max_size_bytes:
            return False, f"文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {HomeworkConfig.MAX_FILE_SIZE_MB}MB"

        return True, ""
    
    def delete_file(self, file_path: str) -> bool:
        """
        删除文件

        Args:
            file_path: 文件路径

        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"文件删除成功: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def get_homework_files_by_assignment(self, school_id: int, grade_class_code: str,
                                       subject: str, assignment_id: int, assignment_name: str,
                                       file_type: str = "original") -> List[Path]:
        """
        获取指定作业任务的所有文件

        Args:
            school_id: 学校ID
            grade_class_code: 年级班级代码
            subject: 科目
            assignment_id: 作业任务ID
            assignment_name: 作业任务名称
            file_type: 文件类型 ("original" 或 "annotated")

        Returns:
            文件路径列表
        """
        assignment_dir = (f"schools/school_{school_id:03d}/grade_{grade_class_code}/"
                         f"subjects/{subject}/assignments/assignment_{assignment_id:03d}_{assignment_name}")

        file_dir = self.base_upload_dir / assignment_dir / file_type

        if not file_dir.exists():
            return []

        files = []
        for item in file_dir.glob("*"):
            if item.is_file():
                files.append(item)

        return sorted(files)  # 按文件名排序
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            文件信息字典或None
        """
        try:
            if not os.path.exists(file_path):
                return None

            stat = os.stat(file_path)
            return {
                "size": stat.st_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "exists": True
            }
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return None

    def parse_homework_filename(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        解析作业文件名，提取信息

        Args:
            filename: 文件名

        Returns:
            解析出的信息字典或None
        """
        try:
            # 移除文件扩展名
            name_without_ext = os.path.splitext(filename)[0]

            # 检查是否为批注文件
            is_annotated = name_without_ext.endswith('_annotated')
            if is_annotated:
                name_without_ext = name_without_ext[:-10]  # 移除'_annotated'

            # 解析格式: user_{用户ID}_{时间戳}_hw{作业ID}_p{页码}
            parts = name_without_ext.split('_')
            if len(parts) >= 5 and parts[0] == 'user':
                return {
                    "user_id": int(parts[1]),
                    "timestamp": f"{parts[2]}_{parts[3]}",
                    "homework_id": int(parts[4][2:]),  # 移除'hw'前缀
                    "page_number": int(parts[5][1:]),  # 移除'p'前缀
                    "is_annotated": is_annotated
                }
        except (ValueError, IndexError) as e:
            logger.warning(f"无法解析文件名 {filename}: {e}")

        return None
    
    def cleanup_temp_files(self, older_than_hours: int = 24):
        """
        清理临时文件

        Args:
            older_than_hours: 清理多少小时前的临时文件
        """
        # 清理全局temp目录
        temp_dir = self.base_upload_dir / "temp"
        if temp_dir.exists():
            self._cleanup_temp_in_directory(temp_dir, older_than_hours)

        # 清理各学校的导出临时目录
        schools_dir = self.base_upload_dir / "schools"
        if schools_dir.exists():
            for school_dir in schools_dir.iterdir():
                if school_dir.is_dir():
                    export_temp_dir = school_dir / "exports" / "temp"
                    if export_temp_dir.exists():
                        self._cleanup_temp_in_directory(export_temp_dir, older_than_hours)

    def _cleanup_temp_in_directory(self, temp_dir: Path, older_than_hours: int):
        """清理指定目录中的临时文件"""
        cutoff_time = datetime.now().timestamp() - (older_than_hours * 3600)

        for item in temp_dir.rglob("*"):
            if item.is_file():
                try:
                    if item.stat().st_mtime < cutoff_time:
                        item.unlink()
                        logger.info(f"清理临时文件: {item}")
                except Exception as e:
                    logger.error(f"清理临时文件失败 {item}: {e}")

    def get_subject_mapping(self) -> Dict[str, str]:
        """
        获取科目映射表

        Returns:
            科目英文名到中文名的映射
        """
        return {
            'math': '数学',
            'chinese': '语文',
            'english': '英语',
            'physics': '物理',
            'chemistry': '化学',
            'biology': '生物',
            'history': '历史',
            'geography': '地理'
        }

    def get_school_storage_info(self, school_id: int) -> Dict[str, Any]:
        """
        获取学校存储信息

        Args:
            school_id: 学校ID

        Returns:
            存储信息字典
        """
        school_dir = self.base_upload_dir / f"schools/school_{school_id:03d}"

        if not school_dir.exists():
            return {
                "total_size": 0,
                "file_count": 0,
                "assignment_count": 0,
                "grade_classes": []
            }

        total_size = 0
        file_count = 0
        assignment_count = 0
        grade_classes = []

        # 遍历年级班级目录
        for grade_class_dir in school_dir.glob("grade_*"):
            if grade_class_dir.is_dir():
                grade_class_code = grade_class_dir.name[6:]  # 移除'grade_'前缀
                grade_classes.append(grade_class_code)

                # 统计该年级班级的文件
                for file_path in grade_class_dir.rglob("*"):
                    if file_path.is_file():
                        try:
                            total_size += file_path.stat().st_size
                            file_count += 1
                        except Exception:
                            continue

                # 统计作业任务数量
                subjects_dir = grade_class_dir / "subjects"
                if subjects_dir.exists():
                    for subject_dir in subjects_dir.iterdir():
                        if subject_dir.is_dir():
                            assignments_dir = subject_dir / "assignments"
                            if assignments_dir.exists():
                                assignment_count += len([d for d in assignments_dir.iterdir()
                                                       if d.is_dir() and d.name.startswith("assignment_")])

        return {
            "total_size": total_size,
            "file_count": file_count,
            "assignment_count": assignment_count,
            "grade_classes": sorted(grade_classes)
        }

    def get_directory_size(self, directory_path: str) -> int:
        """
        获取目录大小（字节）

        Args:
            directory_path: 目录路径

        Returns:
            目录大小
        """
        total_size = 0
        dir_path = self.base_upload_dir / directory_path

        if not dir_path.exists():
            return 0

        for item in dir_path.rglob("*"):
            if item.is_file():
                try:
                    total_size += item.stat().st_size
                except Exception:
                    continue

        return total_size

    def create_export_directory(self, user_role: str, school_id: Optional[int] = None) -> str:
        """
        创建导出目录

        Args:
            user_role: 用户角色 ("teacher", "school_admin", "super_admin")
            school_id: 学校ID（超级管理员可为None）

        Returns:
            导出目录路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if user_role == "super_admin":
            export_dir = self.base_upload_dir / "system" / "super_admin_exports" / timestamp
        elif user_role in ["teacher", "school_admin"] and school_id:
            export_dir = (self.base_upload_dir / f"schools/school_{school_id:03d}" /
                         "exports" / f"{user_role}_exports" / timestamp)
        else:
            # 默认使用临时目录
            export_dir = self.base_upload_dir / "temp" / timestamp

        export_dir.mkdir(parents=True, exist_ok=True)
        return str(export_dir)

    def get_page_labels(self, subject: str) -> List[str]:
        """
        获取科目特定的页面标签

        Args:
            subject: 科目名称

        Returns:
            页面标签列表
        """
        page_labels = {
            'math': ['第1页', '第2页', '第3页', '第4页'],
            'english': ['听力部分', '阅读部分', '写作部分', '第4页'],
            'physics': ['实验步骤', '实验数据', '实验结果', '实验总结'],
            'chemistry': ['实验过程', '化学方程式', '实验现象', '实验结论'],
            'biology': ['观察记录', '实验步骤', '实验结果', '分析总结']
        }

        return page_labels.get(subject, ['第1页', '第2页', '第3页', '第4页'])

# 全局文件管理器实例
file_manager = FileManager()
