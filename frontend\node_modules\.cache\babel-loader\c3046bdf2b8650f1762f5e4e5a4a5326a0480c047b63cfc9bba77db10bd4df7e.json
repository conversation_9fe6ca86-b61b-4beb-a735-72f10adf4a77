{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\SuperSchoolManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, message, Modal, Form, Input, Select, Tabs, Spin, Space, Tag, Divider, Upload, Checkbox, Row, Col } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, TeamOutlined, SettingOutlined, UploadOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons';\nimport { getSchools, createSchool, updateSchool, deleteSchool, getRegions, getSchoolDetail, getClassesBySchool, createClassForSchool, updateClassInfo, deleteClassById, getSchoolRoles, assignRole, revokeRole, getSchoolUsers, getUsers } from '../utils/api';\nimport { useAuth } from '../utils/auth';\nimport { getUserRoleTags } from '../utils/roleUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Option\n} = Select;\n\n// 定义所有角色类型\nconst USER_ROLES = {\n  // 基础角色\n  SUPER_ADMIN: {\n    value: \"super_admin\",\n    label: \"超级管理员\"\n  },\n  SCHOOL_ADMIN: {\n    value: \"school_admin\",\n    label: \"学校管理员\"\n  },\n  PRINCIPAL: {\n    value: \"principal\",\n    label: \"校长\"\n  },\n  VICE_PRINCIPAL: {\n    value: \"vice_principal\",\n    label: \"副校长\"\n  },\n  ACADEMIC_DIRECTOR: {\n    value: \"academic_director\",\n    label: \"教务处主任\"\n  },\n  GRADE_DIRECTOR: {\n    value: \"grade_director\",\n    label: \"年级组长\"\n  },\n  SUBJECT_LEADER: {\n    value: \"subject_leader\",\n    label: \"教研组长\"\n  },\n  LESSON_PLANNER: {\n    value: \"lesson_planner\",\n    label: \"备课组长\"\n  },\n  CLASS_TEACHER: {\n    value: \"class_teacher\",\n    label: \"班主任\"\n  },\n  TEACHER: {\n    value: \"teacher\",\n    label: \"教师\"\n  },\n  STUDENT: {\n    value: \"student\",\n    label: \"学生\"\n  },\n  PARENT: {\n    value: \"parent\",\n    label: \"家长\"\n  }\n};\nconst SuperSchoolManagement = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [schools, setSchools] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [currentSchool, setCurrentSchool] = useState(null);\n  const [selectedSchool, setSelectedSchool] = useState(null);\n  const [form] = Form.useForm();\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n\n  // 添加搜索相关状态\n  const [searchForm] = Form.useForm();\n  const [filteredSchools, setFilteredSchools] = useState([]);\n  const [isSearching, setIsSearching] = useState(false);\n\n  // 学校详情管理相关状态\n  const [schoolDetail, setSchoolDetail] = useState(null);\n  const [classes, setClasses] = useState([]);\n  const [roles, setRoles] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [allUsers, setAllUsers] = useState([]);\n  const [detailActiveTab, setDetailActiveTab] = useState('1');\n  const [classModalVisible, setClassModalVisible] = useState(false);\n  const [roleModalVisible, setRoleModalVisible] = useState(false);\n  const [newUserModalVisible, setNewUserModalVisible] = useState(false);\n  const [currentClass, setCurrentClass] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [classForm] = Form.useForm();\n  const [roleForm] = Form.useForm();\n  const [userForm] = Form.useForm();\n  const [batchStudentForm] = Form.useForm();\n  const [batchStudentModalVisible, setBatchStudentModalVisible] = useState(false);\n  const [selectedClassId, setSelectedClassId] = useState(null);\n  const [classStudentsModalVisible, setClassStudentsModalVisible] = useState(false);\n  const [currentClassStudents, setCurrentClassStudents] = useState([]);\n  const [currentClassInfo, setCurrentClassInfo] = useState(null);\n  const [importStudentModalVisible, setImportStudentModalVisible] = useState(false);\n  const [importStudentForm] = Form.useForm();\n  const [importFileList, setImportFileList] = useState([]);\n  const [schoolDetailForm] = Form.useForm(); // 添加学校详情表单引用\n\n  // 获取学校列表和地区数据\n  useEffect(() => {\n    if (user && user.is_admin) {\n      // 先获取地区数据，确保省份下拉菜单有数据\n      fetchRegions().then(() => {\n        // 再获取学校列表\n        fetchSchools();\n      });\n    }\n  }, [user]);\n\n  // 获取学校列表\n  const fetchSchools = async () => {\n    setLoading(true);\n    try {\n      const data = await getSchools();\n      console.log(\"获取到的学校列表:\", data);\n\n      // 确保数据是数组\n      let schoolsData = Array.isArray(data) ? data : [];\n\n      // 如果数据是对象且有items属性，则使用items\n      if (!Array.isArray(data) && data && Array.isArray(data.items)) {\n        schoolsData = data.items;\n      }\n\n      // 确保每个学校对象都有完整的字段，特别是id字段\n      const processedSchools = schoolsData.map((school, index) => {\n        // 检查是否有id字段，如果没有则使用索引+1作为ID\n        let schoolId = school.id;\n        if (schoolId === undefined || schoolId === null) {\n          console.warn('学校记录缺少ID，使用索引作为ID:', index + 1);\n          schoolId = index + 1;\n        }\n\n        // 尝试将ID转换为数字\n        const numericId = parseInt(schoolId, 10);\n        if (!isNaN(numericId)) {\n          schoolId = numericId;\n        }\n        return {\n          ...school,\n          id: schoolId,\n          // 确保id字段存在\n          province: school.province || '',\n          city: school.city || '',\n          district: school.district || '',\n          class_count: school.class_count || 0\n        };\n      });\n      setSchools(processedSchools);\n      setFilteredSchools(processedSchools); // 初始化过滤后的学校列表\n    } catch (error) {\n      console.error(\"获取学校列表失败:\", error);\n      message.error('获取学校列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取地区数据\n  const fetchRegions = async () => {\n    try {\n      console.log('开始获取全国省份数据...');\n      const data = await getRegions();\n      if (data && data.provinces) {\n        console.log(`成功获取 ${data.provinces.length} 个省份数据`);\n        setProvinces(data.provinces);\n      } else {\n        console.error('获取省份数据失败: 返回数据格式不正确', data);\n        // 尝试使用备用方法获取省份数据\n        try {\n          // 从数据库中查询所有已有的省份\n          const schoolsData = await getSchools();\n          if (Array.isArray(schoolsData)) {\n            const uniqueProvinces = [...new Set(schoolsData.map(school => school.province).filter(Boolean))];\n            if (uniqueProvinces.length > 0) {\n              console.log(`从学校数据中提取了 ${uniqueProvinces.length} 个省份`);\n              setProvinces(uniqueProvinces);\n            }\n          }\n        } catch (backupError) {\n          console.error('备用方法获取省份数据失败:', backupError);\n        }\n      }\n    } catch (error) {\n      console.error('获取地区数据失败:', error);\n    }\n  };\n\n  // 处理省份变化\n  const handleProvinceChange = async value => {\n    form.setFieldsValue({\n      city: undefined,\n      district: undefined\n    });\n    setCities([]);\n    setDistricts([]);\n    if (!value) return;\n    try {\n      console.log(`获取省份 ${value} 的城市数据...`);\n      const data = await getRegions({\n        province: value\n      });\n      if (data && data.cities && data.cities.length > 0) {\n        console.log(`成功获取 ${data.cities.length} 个城市数据`);\n        setCities(data.cities);\n      } else {\n        console.error('获取城市数据失败或城市列表为空:', data);\n        // 尝试使用备用方法获取城市数据\n        try {\n          // 从学校数据中提取该省份的城市\n          const schoolsData = await getSchools();\n          if (Array.isArray(schoolsData)) {\n            const uniqueCities = [...new Set(schoolsData.filter(school => school.province === value).map(school => school.city).filter(Boolean))];\n            if (uniqueCities.length > 0) {\n              console.log(`从学校数据中提取了 ${uniqueCities.length} 个城市`);\n              setCities(uniqueCities);\n            }\n          }\n        } catch (backupError) {\n          console.error('备用方法获取城市数据失败:', backupError);\n        }\n      }\n    } catch (error) {\n      console.error('获取城市数据失败:', error);\n    }\n  };\n\n  // 处理城市变化\n  const handleCityChange = async value => {\n    form.setFieldsValue({\n      district: undefined\n    });\n    setDistricts([]);\n    if (!value) return;\n    const province = form.getFieldValue('province');\n    if (!province) return;\n    try {\n      console.log(`获取省份 ${province} 城市 ${value} 的区县数据...`);\n      const data = await getRegions({\n        province: province,\n        city: value\n      });\n      if (data && data.districts && data.districts.length > 0) {\n        console.log(`成功获取 ${data.districts.length} 个区县数据`);\n        setDistricts(data.districts);\n      } else {\n        console.error('获取区县数据失败或区县列表为空:', data);\n        // 尝试使用备用方法获取区县数据\n        try {\n          // 从学校数据中提取该省份城市的区县\n          const schoolsData = await getSchools();\n          if (Array.isArray(schoolsData)) {\n            const uniqueDistricts = [...new Set(schoolsData.filter(school => school.province === province && school.city === value).map(school => school.district).filter(Boolean))];\n            if (uniqueDistricts.length > 0) {\n              console.log(`从学校数据中提取了 ${uniqueDistricts.length} 个区县`);\n              setDistricts(uniqueDistricts);\n            }\n          }\n        } catch (backupError) {\n          console.error('备用方法获取区县数据失败:', backupError);\n        }\n      }\n    } catch (error) {\n      console.error('获取区县数据失败:', error);\n    }\n  };\n\n  // 打开学校编辑模态框\n  const showModal = (school = null) => {\n    setCurrentSchool(school);\n    form.resetFields();\n    if (school) {\n      form.setFieldsValue({\n        name: school.name,\n        province: school.province,\n        city: school.city,\n        district: school.district\n      });\n\n      // 如果有省份，加载城市\n      if (school.province) {\n        handleProvinceChange(school.province);\n      }\n\n      // 如果有城市，加载区县\n      if (school.province && school.city) {\n        handleCityChange(school.city);\n      }\n    }\n    setModalVisible(true);\n  };\n\n  // 处理学校表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n      if (currentSchool) {\n        // 更新学校\n        console.log(`准备更新学校，ID: ${currentSchool.id}，数据:`, values);\n        try {\n          // 使用 updateSchoolInfo API 而不是 updateSchool\n          const {\n            updateSchoolInfo\n          } = await import('../utils/api');\n          await updateSchoolInfo(currentSchool.id, values);\n          message.success('学校更新成功');\n        } catch (updateError) {\n          console.error('更新学校失败，尝试备用方法:', updateError);\n\n          // 如果 updateSchoolInfo 失败，尝试使用 updateSchool\n          await updateSchool(currentSchool.id, values);\n          message.success('学校更新成功（使用备用方法）');\n        }\n      } else {\n        // 创建学校\n        console.log('准备创建新学校:', values);\n        await createSchool(values);\n        message.success('学校创建成功');\n      }\n      setModalVisible(false);\n      fetchSchools();\n    } catch (error) {\n      console.error('保存学校信息失败:', error);\n      message.error(`保存学校信息失败: ${error.message || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理学校删除\n  const handleDelete = id => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这个学校吗？删除后无法恢复，且会影响所有关联的班级和用户。',\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        setLoading(true);\n        try {\n          await deleteSchool(id);\n          message.success('学校删除成功');\n\n          // 如果删除的是当前选中的学校，清除选择\n          if (selectedSchool && selectedSchool.id === id) {\n            setSelectedSchool(null);\n          }\n          fetchSchools();\n        } catch (error) {\n          message.error('学校删除失败');\n          console.error('学校删除失败:', error);\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n\n  // 获取学校详情数据\n  const fetchSchoolDetail = async schoolId => {\n    setLoading(true);\n    try {\n      console.log(`获取学校详情，ID: ${schoolId}`);\n      if (!schoolId && schoolId !== 0) {\n        console.error('未提供有效的学校ID，无法获取学校详情');\n        message.error('未提供有效的学校ID');\n        setLoading(false);\n        return;\n      }\n\n      // 确保ID是数字类型\n      const numericSchoolId = parseInt(schoolId, 10);\n      if (isNaN(numericSchoolId)) {\n        console.error('无效的学校ID:', schoolId);\n        message.error('无效的学校ID');\n        setLoading(false);\n        return;\n      }\n      const data = await getSchoolDetail(numericSchoolId);\n      console.log('获取到学校详情:', data);\n      if (!data) {\n        console.error('获取学校详情失败：服务器未返回数据');\n        message.error('获取学校详情失败：服务器未返回数据');\n        setLoading(false);\n        return;\n      }\n      setSchoolDetail(data);\n\n      // 获取班级和用户数据\n      fetchClasses(numericSchoolId);\n      if (detailActiveTab === '3') {\n        fetchRoles();\n        fetchUsers(numericSchoolId);\n        fetchAllUsers();\n      }\n    } catch (error) {\n      console.error('获取学校详情失败:', error);\n      message.error(`获取学校详情失败: ${error.message || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取班级数据\n  const fetchClasses = async schoolId => {\n    setLoading(true);\n    try {\n      console.log(`获取学校班级，学校ID: ${schoolId}，类型: ${typeof schoolId}`);\n      if (!schoolId && schoolId !== 0) {\n        console.error('未提供学校ID，无法获取班级列表');\n        setLoading(false);\n        return;\n      }\n\n      // 尝试将schoolId转换为数字，但如果失败也继续使用原值\n      let numericSchoolId;\n      try {\n        numericSchoolId = parseInt(schoolId, 10);\n        if (isNaN(numericSchoolId)) {\n          console.warn('学校ID不是数字，使用原始值:', schoolId);\n          numericSchoolId = schoolId;\n        }\n      } catch (e) {\n        console.warn('转换学校ID失败，使用原始值:', schoolId);\n        numericSchoolId = schoolId;\n      }\n      console.log(`调用API获取班级数据，学校ID: ${numericSchoolId}`);\n      const data = await getClassesBySchool(numericSchoolId);\n      console.log('获取到班级数据:', data);\n\n      // 处理不同格式的返回数据\n      let classesList = [];\n      if (Array.isArray(data)) {\n        classesList = data;\n        console.log('班级数据是数组格式，长度:', classesList.length);\n      } else if (data && Array.isArray(data.items)) {\n        classesList = data.items;\n        console.log('班级数据是对象.items格式，长度:', classesList.length);\n      } else if (data && typeof data === 'object') {\n        classesList = [data]; // 单个对象转为数组\n        console.log('班级数据是单个对象，转为数组');\n      } else {\n        console.warn('班级数据格式不正确:', data);\n      }\n\n      // 确保每个班级对象都有id字段\n      classesList = classesList.map((cls, index) => {\n        if (!cls.id && cls.id !== 0) {\n          console.warn(`班级记录缺少ID:`, cls);\n          cls.id = `temp_class_${Date.now()}_${index}`;\n        }\n        return cls;\n      });\n      console.log('最终处理后的班级列表:', classesList);\n      setClasses(classesList);\n    } catch (error) {\n      console.error('获取班级信息失败:', error);\n      message.error('获取班级信息失败');\n      // 设置空数组避免前端崩溃\n      setClasses([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取角色数据\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const data = await getSchoolRoles();\n      setRoles(data);\n    } catch (error) {\n      console.error('获取角色信息失败:', error);\n      message.error('获取角色信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取用户数据\n  const fetchUsers = async schoolId => {\n    setLoading(true);\n    try {\n      const data = await getSchoolUsers(schoolId);\n      setUsers(data);\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      message.error('获取用户信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取所有用户\n  const fetchAllUsers = async () => {\n    try {\n      const data = await getUsers();\n      if (data && typeof data === 'object' && 'items' in data) {\n        setAllUsers(data.items || []);\n      } else {\n        setAllUsers(data || []);\n      }\n    } catch (error) {\n      console.error('获取所有用户失败:', error);\n    }\n  };\n\n  // 处理学校详情标签页切换\n  const handleDetailTabChange = key => {\n    console.log(`详情Tab切换到: ${key}，之前的Tab: ${detailActiveTab}`);\n    setDetailActiveTab(key);\n    if (selectedSchool && selectedSchool.id) {\n      // 确保ID是有效的\n      const schoolId = selectedSchool.id;\n      console.log(`详情Tab切换到: ${key}，学校ID: ${schoolId}`);\n      if (key === '2') {\n        // 获取班级数据\n        console.log(`切换到班级管理标签页，获取班级数据，学校ID: ${schoolId}`);\n        fetchClasses(schoolId);\n      } else if (key === '3') {\n        // 获取角色和用户数据\n        fetchRoles();\n        fetchUsers(schoolId);\n        fetchAllUsers();\n      }\n    }\n  };\n\n  // 更新学校信息\n  const handleUpdateSchoolDetail = async values => {\n    if (!schoolDetail) {\n      message.error('未找到学校详情，无法更新');\n      return;\n    }\n    console.log('准备更新学校信息:', values);\n\n    // 确保学校ID是有效的\n    const schoolId = schoolDetail.id;\n    if (!schoolId && schoolId !== 0) {\n      message.error('无效的学校ID，无法更新');\n      return;\n    }\n    setLoading(true);\n    try {\n      // 使用 updateSchoolInfo API 而不是 updateSchool\n      // 因为 updateSchoolInfo 专门用于更新学校详情\n      const {\n        updateSchoolInfo\n      } = await import('../utils/api');\n\n      // 记录请求详情\n      console.log(`发送更新请求，学校ID: ${schoolId}，数据:`, values);\n\n      // 发送请求\n      await updateSchoolInfo(schoolId, values);\n      message.success('学校信息更新成功');\n\n      // 刷新学校详情\n      fetchSchoolDetail(schoolId);\n\n      // 刷新学校列表\n      fetchSchools();\n    } catch (error) {\n      console.error('学校信息更新失败:', error);\n      message.error(`学校信息更新失败: ${error.message || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 打开班级编辑模态框\n  const showClassModal = (record = null) => {\n    setCurrentClass(record);\n    classForm.resetFields();\n    if (record) {\n      classForm.setFieldsValue({\n        name: record.name,\n        grade: record.grade\n      });\n    }\n    setClassModalVisible(true);\n  };\n\n  // 关闭班级编辑模态框\n  const handleClassCancel = () => {\n    setClassModalVisible(false);\n    setCurrentClass(null);\n    classForm.resetFields();\n  };\n\n  // 保存班级信息\n  const handleClassSubmit = async () => {\n    try {\n      const values = await classForm.validateFields();\n      setLoading(true);\n      if (!selectedSchool || !selectedSchool.id) {\n        message.error('未选择学校，无法保存班级');\n        setLoading(false);\n        return;\n      }\n      if (currentClass) {\n        // 更新班级\n        await updateClassInfo(currentClass.id, {\n          ...values,\n          school_id: selectedSchool.id\n        });\n        message.success('班级更新成功');\n      } else {\n        // 创建班级\n        await createClassForSchool({\n          ...values,\n          school_id: selectedSchool.id\n        });\n        message.success('班级创建成功');\n      }\n      setClassModalVisible(false);\n      fetchClasses(selectedSchool.id);\n    } catch (error) {\n      console.error('保存班级信息失败:', error);\n      message.error('保存班级信息失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 删除班级\n  const handleDeleteClass = async classId => {\n    Modal.confirm({\n      title: '确认删除',\n      content: '确定要删除这个班级吗？删除后无法恢复。',\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        setLoading(true);\n        try {\n          await deleteClassById(classId);\n          message.success('班级删除成功');\n          if (selectedSchool && selectedSchool.id) {\n            fetchClasses(selectedSchool.id);\n          }\n        } catch (error) {\n          console.error('班级删除失败:', error);\n          message.error('班级删除失败');\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n\n  // 打开角色分配模态框\n  const showRoleModal = record => {\n    setCurrentUser(record);\n    roleForm.resetFields();\n    setRoleModalVisible(true);\n  };\n\n  // 打开添加用户模态框\n  const showAddUserModal = () => {\n    userForm.resetFields();\n    userForm.setFieldsValue({\n      activeTab: '1'\n    });\n    setNewUserModalVisible(true);\n  };\n\n  // 关闭角色分配模态框\n  const handleRoleCancel = () => {\n    setRoleModalVisible(false);\n    setCurrentUser(null);\n    roleForm.resetFields();\n  };\n\n  // 分配角色\n  const handleRoleSubmit = async () => {\n    try {\n      const values = await roleForm.validateFields();\n      setLoading(true);\n      if (!selectedSchool || !selectedSchool.id) {\n        message.error('未选择学校，无法分配角色');\n        setLoading(false);\n        return;\n      }\n      await assignRole({\n        user_id: currentUser.id,\n        role_id: values.role_id,\n        school_id: selectedSchool.id\n      });\n      message.success('角色分配成功');\n      setRoleModalVisible(false);\n      fetchUsers(selectedSchool.id);\n    } catch (error) {\n      console.error('角色分配失败:', error);\n      message.error('角色分配失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加用户并分配角色\n  const handleAddUser = async () => {\n    try {\n      const values = await userForm.validateFields();\n      setLoading(true);\n      if (!selectedSchool || !selectedSchool.id) {\n        message.error('未选择学校，无法添加用户');\n        setLoading(false);\n        return;\n      }\n\n      // 判断是选择现有用户还是创建新用户\n      const activeTab = userForm.getFieldValue('activeTab') || '1';\n      let userId = values.user_id;\n      if (activeTab === '2' || !userId) {\n        // 创建新用户\n        if (!values.username || !values.password) {\n          message.error('请填写用户名和密码');\n          setLoading(false);\n          return;\n        }\n        try {\n          const {\n            createUser\n          } = await import('../utils/api');\n          const newUser = await createUser({\n            username: values.username,\n            password: values.password,\n            full_name: values.full_name,\n            email: values.email,\n            phone: values.phone,\n            is_teacher: values.is_teacher || false,\n            is_admin: values.is_admin || false,\n            school_id: selectedSchool.id\n          });\n          userId = newUser.id;\n          message.success('用户创建成功');\n        } catch (error) {\n          console.error('创建用户失败:', error);\n          message.error(`创建用户失败: ${error.message || '未知错误'}`);\n          setLoading(false);\n          return;\n        }\n      }\n\n      // 分配角色\n      await assignRole({\n        user_id: userId,\n        role_id: values.role_id,\n        school_id: selectedSchool.id\n      });\n      message.success('用户角色分配成功');\n      setNewUserModalVisible(false);\n      userForm.resetFields();\n      fetchUsers(selectedSchool.id);\n      fetchAllUsers(); // 刷新所有用户列表\n    } catch (error) {\n      console.error('添加用户失败:', error);\n      message.error(`添加用户失败: ${error.message || '未知错误'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 显示批量添加学生模态框\n  const showBatchStudentModal = () => {\n    setBatchStudentModalVisible(true);\n    batchStudentForm.resetFields();\n  };\n\n  // 下载学生导入模板\n  const downloadStudentTemplate = () => {\n    // 创建CSV内容\n    const headers = ['姓名(full_name)', '用户名(username)', '密码(password)', '手机号(phone)', '邮箱(email)', '性别(gender)', '备注(note)'];\n    const exampleData = ['张三', 'zhangsan', '123456', '13800138000', '<EMAIL>', '男', '班长', '李四', 'lisi', '123456', '13800138001', '<EMAIL>', '女', ''];\n    let csvContent = headers.join(',') + '\\n';\n    csvContent += exampleData.join(',') + '\\n';\n\n    // 创建Blob对象\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n\n    // 创建下载链接\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', '学生导入模板.csv');\n    link.style.visibility = 'hidden';\n\n    // 添加到DOM并触发下载\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    message.success('模板下载成功');\n  };\n\n  // 处理导入学生名册\n  const handleImportStudents = async () => {\n    try {\n      // 验证表单\n      const values = await importStudentForm.validateFields();\n      const classId = values.class_id;\n      if (importFileList.length === 0) {\n        message.error('请上传学生名册文件');\n        return;\n      }\n      setLoading(true);\n\n      // 创建FormData对象\n      const formData = new FormData();\n      formData.append('file', importFileList[0]);\n      formData.append('class_id', classId);\n\n      // 导入API函数\n      const {\n        batchCreateStudents,\n        addStudentToClass\n      } = await import('../utils/api');\n\n      // 读取文件内容\n      const reader = new FileReader();\n      reader.onload = async e => {\n        try {\n          const content = e.target.result;\n          let students = [];\n          let successCount = 0;\n          let failCount = 0;\n\n          // 解析CSV文件\n          const lines = content.split(/\\\\r?\\\\n/);\n          const headers = lines[0].split(',');\n\n          // 检查必要的字段\n          const fullNameIndex = headers.findIndex(h => h.includes('姓名') || h.includes('full_name'));\n          const usernameIndex = headers.findIndex(h => h.includes('用户名') || h.includes('username'));\n          const passwordIndex = headers.findIndex(h => h.includes('密码') || h.includes('password'));\n          if (fullNameIndex === -1 || usernameIndex === -1 || passwordIndex === -1) {\n            message.error('文件格式错误：缺少必要的字段（姓名、用户名或密码）');\n            setLoading(false);\n            return;\n          }\n\n          // 解析数据行\n          for (let i = 1; i < lines.length; i++) {\n            var _values$fullNameIndex, _values$usernameIndex, _values$passwordIndex;\n            if (!lines[i].trim()) continue;\n            const values = lines[i].split(',');\n            const student = {\n              full_name: (_values$fullNameIndex = values[fullNameIndex]) === null || _values$fullNameIndex === void 0 ? void 0 : _values$fullNameIndex.trim(),\n              username: (_values$usernameIndex = values[usernameIndex]) === null || _values$usernameIndex === void 0 ? void 0 : _values$usernameIndex.trim(),\n              password: (_values$passwordIndex = values[passwordIndex]) === null || _values$passwordIndex === void 0 ? void 0 : _values$passwordIndex.trim(),\n              is_student: true\n            };\n\n            // 添加可选字段\n            const phoneIndex = headers.findIndex(h => h.includes('手机') || h.includes('phone'));\n            if (phoneIndex !== -1 && values[phoneIndex]) {\n              student.phone = values[phoneIndex].trim();\n            }\n            const emailIndex = headers.findIndex(h => h.includes('邮箱') || h.includes('email'));\n            if (emailIndex !== -1 && values[emailIndex]) {\n              student.email = values[emailIndex].trim();\n            }\n            const genderIndex = headers.findIndex(h => h.includes('性别') || h.includes('gender'));\n            if (genderIndex !== -1 && values[genderIndex]) {\n              student.gender = values[genderIndex].trim();\n            }\n            const noteIndex = headers.findIndex(h => h.includes('备注') || h.includes('note'));\n            if (noteIndex !== -1 && values[noteIndex]) {\n              student.note = values[noteIndex].trim();\n            }\n\n            // 验证必要字段\n            if (!student.full_name || !student.username || !student.password) {\n              console.warn(`第${i}行数据不完整，跳过`, student);\n              failCount++;\n              continue;\n            }\n            students.push(student);\n          }\n          if (students.length === 0) {\n            message.error('没有有效的学生数据');\n            setLoading(false);\n            return;\n          }\n\n          // 批量创建学生\n          console.log(`准备创建 ${students.length} 名学生`);\n\n          // 逐个创建学生账号并添加到班级\n          for (const student of students) {\n            try {\n              const {\n                createUser\n              } = await import('../utils/api');\n              const newStudent = await createUser(student);\n\n              // 添加到班级\n              await addStudentToClass(classId, newStudent.id);\n              successCount++;\n            } catch (error) {\n              console.error(`创建学生失败: ${student.username}`, error);\n              failCount++;\n            }\n          }\n\n          // 显示结果\n          if (successCount > 0) {\n            message.success(`成功导入 ${successCount} 名学生到班级`);\n            setImportStudentModalVisible(false);\n            setImportFileList([]);\n            importStudentForm.resetFields();\n\n            // 刷新班级数据\n            fetchClasses(selectedSchool.id);\n          } else {\n            message.error('未能成功导入任何学生');\n          }\n          if (failCount > 0) {\n            message.warning(`${failCount} 名学生导入失败，可能是用户名已存在或数据格式错误`);\n          }\n        } catch (error) {\n          console.error('解析文件失败:', error);\n          message.error('解析文件失败');\n        } finally {\n          setLoading(false);\n        }\n      };\n      reader.onerror = () => {\n        message.error('读取文件失败');\n        setLoading(false);\n      };\n      reader.readAsText(importFileList[0]);\n    } catch (error) {\n      console.error('导入学生名册失败:', error);\n      message.error('导入学生名册失败');\n      setLoading(false);\n    }\n  };\n\n  // 显示班级学生\n  const showClassStudents = async classRecord => {\n    setLoading(true);\n    setCurrentClassInfo(classRecord);\n    try {\n      // 导入API函数\n      const {\n        getClass\n      } = await import('../utils/api');\n\n      // 获取班级详情，包括学生列表\n      const classDetails = await getClass(classRecord.id, true);\n\n      // 设置学生列表\n      let students = [];\n      if (classDetails && classDetails.students) {\n        students = Array.isArray(classDetails.students) ? classDetails.students : [];\n      }\n      setCurrentClassStudents(students);\n      setClassStudentsModalVisible(true);\n    } catch (error) {\n      console.error('获取班级学生失败:', error);\n      message.error('获取班级学生失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理批量添加学生\n  const handleBatchAddStudents = async () => {\n    try {\n      const values = await batchStudentForm.validateFields();\n      setLoading(true);\n      const classId = values.class_id;\n      const studentsData = values.students_data.trim();\n      if (!studentsData) {\n        message.error('请输入学生数据');\n        setLoading(false);\n        return;\n      }\n\n      // 解析学生数据\n      const studentLines = studentsData.split('\\n');\n      const students = [];\n      const errors = [];\n      studentLines.forEach((line, index) => {\n        const parts = line.trim().split(',');\n        if (parts.length < 3) {\n          errors.push(`第${index + 1}行格式错误: ${line}`);\n          return;\n        }\n        const [fullName, username, password] = parts;\n        if (!fullName || !username || !password) {\n          errors.push(`第${index + 1}行数据不完整: ${line}`);\n          return;\n        }\n        students.push({\n          full_name: fullName.trim(),\n          username: username.trim(),\n          password: password.trim(),\n          is_student: true\n        });\n      });\n      if (errors.length > 0) {\n        message.error(`数据格式错误:\\n${errors.join('\\n')}`);\n        setLoading(false);\n        return;\n      }\n\n      // 创建学生账号\n      const {\n        createUser,\n        addStudentToClass\n      } = await import('../utils/api');\n      const createdStudents = [];\n      let successCount = 0;\n      let failCount = 0;\n\n      // 逐个创建学生账号并添加到班级\n      for (const student of students) {\n        try {\n          const newStudent = await createUser(student);\n          createdStudents.push(newStudent);\n\n          // 添加到班级\n          await addStudentToClass(classId, newStudent.id);\n          successCount++;\n        } catch (error) {\n          console.error(`创建学生失败: ${student.username}`, error);\n          failCount++;\n        }\n      }\n\n      // 显示结果\n      if (successCount > 0) {\n        message.success(`成功添加 ${successCount} 名学生到班级`);\n        setBatchStudentModalVisible(false);\n        batchStudentForm.resetFields();\n\n        // 刷新班级数据\n        fetchClasses(selectedSchool.id);\n      } else {\n        message.error('未能成功添加任何学生');\n      }\n      if (failCount > 0) {\n        message.warning(`${failCount} 名学生添加失败，可能是用户名已存在`);\n      }\n    } catch (error) {\n      console.error('批量添加学生失败:', error);\n      message.error('批量添加学生失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 撤销角色\n  const handleRevokeRole = async (userId, roleId) => {\n    Modal.confirm({\n      title: '确认撤销',\n      content: '确定要撤销这个用户的角色吗？',\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        setLoading(true);\n        try {\n          if (!selectedSchool || !selectedSchool.id) {\n            message.error('未选择学校，无法撤销角色');\n            setLoading(false);\n            return;\n          }\n          await revokeRole({\n            user_id: userId,\n            role_id: roleId,\n            school_id: selectedSchool.id\n          });\n          message.success('角色撤销成功');\n          fetchUsers(selectedSchool.id);\n        } catch (error) {\n          console.error('角色撤销失败:', error);\n          message.error('角色撤销失败');\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n\n  // 处理管理按钮点击\n  const handleManageSchool = record => {\n    console.log(`点击管理按钮，学校记录:`, record);\n    console.log(`学校ID类型: ${typeof record.id}, 值: ${record.id}`);\n\n    // 直接使用记录中的数据，不再重新获取学校详情\n    let schoolData = {\n      ...record\n    };\n\n    // 确保学校数据包含必要的字段\n    if (!schoolData.name) {\n      schoolData.name = '未命名学校';\n    }\n\n    // 确保ID是数字类型\n    if (typeof schoolData.id === 'string') {\n      const numericId = parseInt(schoolData.id, 10);\n      if (!isNaN(numericId)) {\n        schoolData.id = numericId;\n      }\n    }\n    console.log(`处理后的学校数据:`, schoolData);\n\n    // 设置学校详情数据，直接使用当前记录\n    setSchoolDetail(schoolData);\n\n    // 设置选中的学校\n    setSelectedSchool(schoolData);\n\n    // 获取班级数据\n    if (schoolData.id) {\n      console.log(`主动获取班级数据，学校ID: ${schoolData.id}`);\n      fetchClasses(schoolData.id);\n      fetchRoles();\n      fetchUsers(schoolData.id);\n      fetchAllUsers();\n\n      // 设置详情标签页为1（基本信息）\n      setDetailActiveTab('1');\n    }\n\n    // 切换到学校管理标签页\n    console.log(`已设置选中学校，ID: ${schoolData.id}，准备切换到Tab 2`);\n\n    // 直接设置activeKey状态\n    setActiveKey(\"2\");\n\n    // 使用setTimeout确保状态更新\n    setTimeout(() => {\n      console.log('延时执行检查，确保状态已更新，schoolId:', schoolData.id);\n\n      // 再次确认activeKey已设置为2\n      if (activeKey !== \"2\") {\n        console.warn('activeKey不是2，再次设置为2');\n        setActiveKey(\"2\");\n      }\n    }, 100);\n  };\n\n  // 学校表格列定义\n  const columns = [{\n    title: '序号',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '学校名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '省份',\n    dataIndex: 'province',\n    key: 'province',\n    render: text => text || '未设置'\n  }, {\n    title: '城市',\n    dataIndex: 'city',\n    key: 'city',\n    render: text => text || '未设置'\n  }, {\n    title: '区县',\n    dataIndex: 'district',\n    key: 'district',\n    render: text => text || '未设置'\n  }, {\n    title: '班级数量',\n    dataIndex: 'class_count',\n    key: 'class_count',\n    render: text => text || 0\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleManageSchool(record),\n        children: \"\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1208,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 19\n        }, this),\n        onClick: () => showModal(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1224,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1221,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }];\n\n  // 处理Tab切换\n  const handleTabChange = key => {\n    console.log('Tab切换到:', key);\n\n    // 更新activeKey状态\n    setActiveKey(key);\n    if (key === \"1\") {\n      // 切换到学校列表，清除选中的学校\n      setSelectedSchool(null);\n      setSchoolDetail(null);\n    } else if (key === \"2\" && selectedSchool) {\n      console.log(`Tab切换到学校管理，学校ID: ${selectedSchool.id}`);\n\n      // 如果没有学校详情数据，则使用selectedSchool的数据\n      if (!schoolDetail) {\n        setSchoolDetail(selectedSchool);\n      }\n\n      // 获取班级数据\n      if (selectedSchool.id) {\n        fetchClasses(selectedSchool.id);\n      }\n    } else if (key === \"2\" && !selectedSchool) {\n      // 如果切换到Tab 2但没有选中的学校，显示错误消息\n      console.error('尝试切换到学校管理标签页，但没有选中的学校');\n      message.error('请先选择一个学校');\n      // 切换回Tab 1\n      setActiveKey(\"1\");\n    }\n  };\n\n  // 添加状态来控制当前激活的标签页\n  const [activeKey, setActiveKey] = useState(\"1\");\n\n  // 当selectedSchool变化时更新activeKey\n  useEffect(() => {\n    if (selectedSchool) {\n      console.log('selectedSchool已更新，切换到Tab 2');\n      setActiveKey(\"2\");\n\n      // 确保加载班级数据\n      if (selectedSchool.id) {\n        console.log(`selectedSchool变化，自动获取班级数据，学校ID: ${selectedSchool.id}`);\n        fetchClasses(selectedSchool.id);\n      }\n    } else {\n      setActiveKey(\"1\");\n    }\n  }, [selectedSchool]);\n\n  // 添加搜索学校的方法 - 实时搜索\n  const handleSearch = (changedValues, allValues) => {\n    const {\n      name,\n      province,\n      city,\n      district\n    } = allValues;\n\n    // 只要有任何搜索条件，就设置为搜索状态\n    const hasSearchCriteria = name || province || city || district;\n    setIsSearching(hasSearchCriteria);\n\n    // 过滤学校列表\n    const filtered = schools.filter(school => {\n      // 名称搜索 - 如果提供了名称，检查学校名称是否包含搜索词\n      const nameMatch = !name || school.name && school.name.toLowerCase().includes(name.toLowerCase());\n\n      // 省份搜索\n      const provinceMatch = !province || school.province === province;\n\n      // 城市搜索\n      const cityMatch = !city || school.city === city;\n\n      // 区县搜索\n      const districtMatch = !district || school.district === district;\n\n      // 所有条件都必须满足\n      return nameMatch && provinceMatch && cityMatch && districtMatch;\n    });\n    setFilteredSchools(filtered);\n\n    // 不再显示消息提示，避免频繁弹出\n  };\n\n  // 重置搜索\n  const handleResetSearch = () => {\n    searchForm.resetFields();\n    setFilteredSchools(schools);\n    setIsSearching(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"super-school-management system-school-page\",\n    \"data-component\": \"SuperSchoolManagement\",\n    children: /*#__PURE__*/_jsxDEV(Tabs, {\n      activeKey: activeKey,\n      onChange: handleTabChange,\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"\\u5B66\\u6821\\u5217\\u8868\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7CFB\\u7EDF\\u5B66\\u6821\\u7BA1\\u7406\",\n          extra: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 23\n            }, this),\n            onClick: () => showModal(),\n            children: \"\\u6DFB\\u52A0\\u5B66\\u6821\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1333,\n            columnNumber: 15\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Form, {\n            form: searchForm,\n            layout: \"inline\",\n            style: {\n              marginBottom: 16\n            },\n            onValuesChange: handleSearch,\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5B66\\u6821\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B66\\u6821\\u540D\\u79F0\",\n                allowClear: true,\n                onChange: e => {\n                  // 当清除输入框时，确保触发搜索\n                  if (!e.target.value) {\n                    handleSearch({}, searchForm.getFieldsValue());\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1350,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"province\",\n              label: \"\\u7701\\u4EFD\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7701\\u4EFD\",\n                style: {\n                  width: 150\n                },\n                allowClear: true,\n                showSearch: true,\n                optionFilterProp: \"children\",\n                filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                onChange: value => {\n                  searchForm.setFieldsValue({\n                    city: undefined,\n                    district: undefined\n                  });\n                  if (value) {\n                    // 获取城市数据\n                    (async () => {\n                      try {\n                        const data = await getRegions({\n                          province: value\n                        });\n                        if (data && data.cities) {\n                          setCities(data.cities);\n                        }\n                      } catch (error) {\n                        console.error('获取城市数据失败:', error);\n                      } finally {\n                        // 省份变化后立即触发搜索\n                        handleSearch({}, searchForm.getFieldsValue());\n                      }\n                    })();\n                  } else {\n                    setCities([]);\n                    setDistricts([]);\n                    // 清空省份后立即触发搜索\n                    handleSearch({}, searchForm.getFieldsValue());\n                  }\n                },\n                children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n                  value: province,\n                  children: province\n                }, province, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1398,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1362,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"city\",\n              label: \"\\u57CE\\u5E02\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u57CE\\u5E02\",\n                style: {\n                  width: 120\n                },\n                allowClear: true,\n                disabled: !searchForm.getFieldValue('province'),\n                onChange: value => {\n                  searchForm.setFieldsValue({\n                    district: undefined\n                  });\n                  if (value) {\n                    // 获取区县数据\n                    (async () => {\n                      try {\n                        const data = await getRegions({\n                          province: searchForm.getFieldValue('province'),\n                          city: value\n                        });\n                        if (data && data.districts) {\n                          setDistricts(data.districts);\n                        }\n                      } catch (error) {\n                        console.error('获取区县数据失败:', error);\n                      } finally {\n                        // 城市变化后立即触发搜索\n                        handleSearch({}, searchForm.getFieldsValue());\n                      }\n                    })();\n                  } else {\n                    setDistricts([]);\n                    // 清空城市后立即触发搜索\n                    handleSearch({}, searchForm.getFieldsValue());\n                  }\n                },\n                children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n                  value: city,\n                  children: city\n                }, city, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1403,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"district\",\n              label: \"\\u533A\\u53BF\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u53BF\",\n                style: {\n                  width: 120\n                },\n                allowClear: true,\n                disabled: !searchForm.getFieldValue('city'),\n                onChange: value => {\n                  // 区县变化后立即触发搜索\n                  handleSearch({}, searchForm.getFieldsValue());\n                },\n                children: districts.map(district => /*#__PURE__*/_jsxDEV(Option, {\n                  value: district,\n                  children: district\n                }, district, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1452,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1441,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleResetSearch,\n                icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1457,\n                  columnNumber: 59\n                }, this),\n                children: \"\\u91CD\\u7F6E\\u7B5B\\u9009\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1457,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1456,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              columns: columns,\n              dataSource: isSearching ? filteredSchools : schools,\n              rowKey: record => `school_${record.id}`,\n              pagination: {\n                showSizeChanger: true,\n                showQuickJumper: true,\n                pageSizeOptions: ['10', '20', '50', '100'],\n                showTotal: total => `共 ${total} 所学校`\n              },\n              footer: () => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'right'\n                },\n                children: isSearching ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u641C\\u7D22\\u7ED3\\u679C: \\u627E\\u5230 \", filteredSchools.length, \" \\u6240\\u5B66\\u6821\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1477,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u663E\\u793A\\u6240\\u6709\\u5B66\\u6821: \\u5171 \", schools.length, \" \\u6240\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1479,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1464,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Modal, {\n            title: currentSchool ? \"编辑学校\" : \"添加学校\",\n            open: modalVisible,\n            onOk: handleSubmit,\n            onCancel: () => setModalVisible(false),\n            confirmLoading: loading,\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"name\",\n                label: \"\\u5B66\\u6821\\u540D\\u79F0\",\n                rules: [{\n                  required: true,\n                  message: '请输入学校名称'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B66\\u6821\\u540D\\u79F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1503,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"province\",\n                label: \"\\u7701\\u4EFD\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u7701\\u4EFD\",\n                  onChange: handleProvinceChange,\n                  showSearch: true,\n                  optionFilterProp: \"children\",\n                  filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                  children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n                    value: province,\n                    children: province\n                  }, province, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1520,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1509,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"city\",\n                label: \"\\u57CE\\u5E02\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u57CE\\u5E02\",\n                  onChange: handleCityChange,\n                  disabled: !form.getFieldValue('province'),\n                  showSearch: true,\n                  optionFilterProp: \"children\",\n                  filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                  children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n                    value: city,\n                    children: city\n                  }, city, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1540,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1528,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1524,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"district\",\n                label: \"\\u533A\\u53BF\",\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u53BF\",\n                  disabled: !form.getFieldValue('city'),\n                  showSearch: true,\n                  optionFilterProp: \"children\",\n                  filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                  children: districts.map(district => /*#__PURE__*/_jsxDEV(Option, {\n                    value: district,\n                    children: district\n                  }, district, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1559,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1548,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1544,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1494,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1487,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1330,\n          columnNumber: 11\n        }, this)\n      }, \"1\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: selectedSchool ? `${selectedSchool.name || '学校'}管理` : '学校管理',\n        children: selectedSchool ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"school-detail-management\",\n          \"data-school-id\": selectedSchool.id,\n          \"data-school-name\": selectedSchool.name || '未命名学校',\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: `${selectedSchool.name || '学校'}详情管理`,\n            children: /*#__PURE__*/_jsxDEV(Spin, {\n              spinning: loading && !classModalVisible && !roleModalVisible,\n              children: /*#__PURE__*/_jsxDEV(Tabs, {\n                activeKey: detailActiveTab,\n                onChange: handleDetailTabChange,\n                children: [/*#__PURE__*/_jsxDEV(TabPane, {\n                  tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1582,\n                      columnNumber: 27\n                    }, this), \"\\u5B66\\u6821\\u57FA\\u672C\\u4FE1\\u606F\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1581,\n                    columnNumber: 25\n                  }, this),\n                  children: schoolDetail ? /*#__PURE__*/_jsxDEV(Form, {\n                    form: schoolDetailForm,\n                    layout: \"vertical\",\n                    initialValues: {\n                      name: schoolDetail.name || (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.name) || '',\n                      province: schoolDetail.province || (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.province) || '',\n                      city: schoolDetail.city || (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.city) || '',\n                      district: schoolDetail.district || (selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.district) || ''\n                    },\n                    onFinish: handleUpdateSchoolDetail,\n                    children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"name\",\n                      label: \"\\u5B66\\u6821\\u540D\\u79F0\",\n                      rules: [{\n                        required: true,\n                        message: '请输入学校名称'\n                      }],\n                      children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1605,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1600,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"province\",\n                      label: \"\\u7701\\u4EFD\",\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        placeholder: \"\\u8BF7\\u9009\\u62E9\\u7701\\u4EFD\",\n                        showSearch: true,\n                        optionFilterProp: \"children\",\n                        filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                        onChange: value => {\n                          // 省份变化时，清空城市和区县\n                          const currentForm = form.current;\n                          if (currentForm) {\n                            currentForm.setFieldsValue({\n                              city: undefined,\n                              district: undefined\n                            });\n                          }\n                          handleProvinceChange(value);\n                        },\n                        children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n                          value: province,\n                          children: province\n                        }, province, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1629,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1611,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1607,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"city\",\n                      label: \"\\u57CE\\u5E02\",\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        placeholder: \"\\u8BF7\\u9009\\u62E9\\u57CE\\u5E02\",\n                        showSearch: true,\n                        optionFilterProp: \"children\",\n                        filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                        onChange: value => handleCityChange(value),\n                        disabled: !(schoolDetail !== null && schoolDetail !== void 0 && schoolDetail.province),\n                        children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n                          value: city,\n                          children: city\n                        }, city, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1649,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1637,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1633,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"district\",\n                      label: \"\\u533A\\u53BF\",\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u53BF\",\n                        showSearch: true,\n                        optionFilterProp: \"children\",\n                        filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                        disabled: !(schoolDetail !== null && schoolDetail !== void 0 && schoolDetail.city),\n                        children: districts.map(district => /*#__PURE__*/_jsxDEV(Option, {\n                          value: district,\n                          children: district\n                        }, district, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1668,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1657,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1653,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        type: \"primary\",\n                        htmlType: \"submit\",\n                        children: \"\\u4FDD\\u5B58\\u4FEE\\u6539\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1673,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1672,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1589,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Spin, {\n                    tip: \"\\u52A0\\u8F7D\\u5B66\\u6821\\u4FE1\\u606F\\u4E2D...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1679,\n                    columnNumber: 25\n                  }, this)\n                }, \"1\", false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1579,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                  tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1687,\n                      columnNumber: 27\n                    }, this), \"\\u73ED\\u7EA7\\u7BA1\\u7406\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1686,\n                    columnNumber: 25\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 16,\n                      display: 'flex',\n                      gap: '10px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1696,\n                        columnNumber: 33\n                      }, this),\n                      onClick: () => showClassModal(),\n                      children: \"\\u6DFB\\u52A0\\u73ED\\u7EA7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1694,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1703,\n                        columnNumber: 33\n                      }, this),\n                      onClick: () => showBatchStudentModal(),\n                      children: \"\\u6279\\u91CF\\u6DFB\\u52A0\\u5B66\\u751F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1701,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1710,\n                        columnNumber: 33\n                      }, this),\n                      onClick: () => setImportStudentModalVisible(true),\n                      children: \"\\u5BFC\\u5165\\u5B66\\u751F\\u540D\\u518C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1708,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"default\",\n                      icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1717,\n                        columnNumber: 33\n                      }, this),\n                      onClick: () => downloadStudentTemplate(),\n                      children: \"\\u4E0B\\u8F7D\\u5BFC\\u5165\\u6A21\\u677F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1715,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1693,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Table, {\n                    columns: [{\n                      title: '班级名称',\n                      dataIndex: 'name',\n                      key: 'name'\n                    }, {\n                      title: '年级',\n                      dataIndex: 'grade',\n                      key: 'grade'\n                    }, {\n                      title: '学生数量',\n                      dataIndex: 'student_count',\n                      key: 'student_count',\n                      render: text => text || 0\n                    }, {\n                      title: '操作',\n                      key: 'action',\n                      render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n                        size: \"middle\",\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          type: \"primary\",\n                          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1748,\n                            columnNumber: 41\n                          }, this),\n                          size: \"small\",\n                          onClick: () => showClassModal(record),\n                          children: \"\\u7F16\\u8F91\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1746,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          type: \"default\",\n                          icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1756,\n                            columnNumber: 41\n                          }, this),\n                          size: \"small\",\n                          onClick: () => showClassStudents(record),\n                          children: \"\\u67E5\\u770B\\u5B66\\u751F\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1754,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          danger: true,\n                          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1764,\n                            columnNumber: 41\n                          }, this),\n                          size: \"small\",\n                          onClick: () => handleDeleteClass(record.id),\n                          children: \"\\u5220\\u9664\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1762,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1745,\n                        columnNumber: 31\n                      }, this)\n                    }],\n                    dataSource: classes,\n                    rowKey: record => `class_${record.id}`,\n                    loading: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1723,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Modal, {\n                    title: currentClass ? \"编辑班级\" : \"添加班级\",\n                    open: classModalVisible,\n                    onOk: handleClassSubmit,\n                    onCancel: handleClassCancel,\n                    confirmLoading: loading,\n                    children: /*#__PURE__*/_jsxDEV(Form, {\n                      form: classForm,\n                      layout: \"vertical\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"name\",\n                        label: \"\\u73ED\\u7EA7\\u540D\\u79F0\",\n                        rules: [{\n                          required: true,\n                          message: '请输入班级名称'\n                        }],\n                        children: /*#__PURE__*/_jsxDEV(Input, {\n                          placeholder: \"\\u4F8B\\u5982\\uFF1A\\u4E03\\u5E74\\u7EA71\\u73ED\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1796,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1791,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"grade\",\n                        label: \"\\u5E74\\u7EA7\",\n                        rules: [{\n                          required: true,\n                          message: '请输入年级'\n                        }],\n                        children: /*#__PURE__*/_jsxDEV(Select, {\n                          placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E74\\u7EA7\",\n                          children: [/*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u4E00\\u5E74\\u7EA7\",\n                            children: \"\\u4E00\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1804,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u4E8C\\u5E74\\u7EA7\",\n                            children: \"\\u4E8C\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1805,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u4E09\\u5E74\\u7EA7\",\n                            children: \"\\u4E09\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1806,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u56DB\\u5E74\\u7EA7\",\n                            children: \"\\u56DB\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1807,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u4E94\\u5E74\\u7EA7\",\n                            children: \"\\u4E94\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1808,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u516D\\u5E74\\u7EA7\",\n                            children: \"\\u516D\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1809,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u4E03\\u5E74\\u7EA7\",\n                            children: \"\\u4E03\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1810,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u516B\\u5E74\\u7EA7\",\n                            children: \"\\u516B\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1811,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u4E5D\\u5E74\\u7EA7\",\n                            children: \"\\u4E5D\\u5E74\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1812,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u9AD8\\u4E00\",\n                            children: \"\\u9AD8\\u4E00\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1813,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u9AD8\\u4E8C\",\n                            children: \"\\u9AD8\\u4E8C\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1814,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Option, {\n                            value: \"\\u9AD8\\u4E09\",\n                            children: \"\\u9AD8\\u4E09\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1815,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1803,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1798,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1787,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1780,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Modal, {\n                    title: \"\\u6279\\u91CF\\u6DFB\\u52A0\\u5B66\\u751F\",\n                    open: batchStudentModalVisible,\n                    onOk: handleBatchAddStudents,\n                    onCancel: () => setBatchStudentModalVisible(false),\n                    confirmLoading: loading,\n                    width: 700,\n                    children: /*#__PURE__*/_jsxDEV(Form, {\n                      form: batchStudentForm,\n                      layout: \"vertical\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"class_id\",\n                        label: \"\\u9009\\u62E9\\u73ED\\u7EA7\",\n                        rules: [{\n                          required: true,\n                          message: '请选择班级'\n                        }],\n                        children: /*#__PURE__*/_jsxDEV(Select, {\n                          placeholder: \"\\u8BF7\\u9009\\u62E9\\u73ED\\u7EA7\",\n                          onChange: value => setSelectedClassId(value),\n                          children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                            value: cls.id,\n                            children: [cls.name, \" (\", cls.grade || '未设置年级', \")\"]\n                          }, cls.id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1844,\n                            columnNumber: 33\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1839,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1834,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"students_data\",\n                        label: \"\\u5B66\\u751F\\u6570\\u636E\",\n                        rules: [{\n                          required: true,\n                          message: '请输入学生数据'\n                        }],\n                        extra: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u5B66\\u751F\\uFF0C\\u683C\\u5F0F\\uFF1A\\u59D3\\u540D,\\u7528\\u6237\\u540D,\\u5BC6\\u7801\\u3002\\u4F8B\\u5982\\uFF1A\\u5F20\\u4E09,zhangsan,123456\",\n                        children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                          placeholder: \"\\u5F20\\u4E09,zhangsan,123456\\n\\u674E\\u56DB,lisi,123456\\n\\u738B\\u4E94,wangwu,123456\",\n                          rows: 10,\n                          style: {\n                            fontFamily: 'monospace'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1856,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1850,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1830,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1822,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Modal, {\n                    title: currentClassInfo ? `${currentClassInfo.name} 学生列表` : '班级学生',\n                    open: classStudentsModalVisible,\n                    onCancel: () => setClassStudentsModalVisible(false),\n                    footer: [/*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => setClassStudentsModalVisible(false),\n                      children: \"\\u5173\\u95ED\"\n                    }, \"close\", false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1871,\n                      columnNumber: 27\n                    }, this)],\n                    width: 800,\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      columns: [{\n                        title: '学号',\n                        dataIndex: 'id',\n                        key: 'id',\n                        width: 80\n                      }, {\n                        title: '姓名',\n                        dataIndex: 'full_name',\n                        key: 'full_name'\n                      }, {\n                        title: '用户名',\n                        dataIndex: 'username',\n                        key: 'username'\n                      }, {\n                        title: '操作',\n                        key: 'action',\n                        render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n                          size: \"middle\",\n                          children: /*#__PURE__*/_jsxDEV(Button, {\n                            danger: true,\n                            size: \"small\",\n                            onClick: () => {\n                              if (currentClassInfo) {\n                                Modal.confirm({\n                                  title: '确认移除',\n                                  content: `确定要将学生 ${record.full_name || record.username} 从班级中移除吗？`,\n                                  onOk: async () => {\n                                    try {\n                                      const {\n                                        removeStudentFromClass\n                                      } = await import('../utils/api');\n                                      await removeStudentFromClass(currentClassInfo.id, record.id);\n                                      message.success('学生已从班级移除');\n                                      showClassStudents(currentClassInfo);\n                                    } catch (error) {\n                                      console.error('移除学生失败:', error);\n                                      message.error('移除学生失败');\n                                    }\n                                  }\n                                });\n                              }\n                            },\n                            children: \"\\u79FB\\u51FA\\u73ED\\u7EA7\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1900,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1899,\n                          columnNumber: 33\n                        }, this)\n                      }],\n                      dataSource: currentClassStudents,\n                      rowKey: \"id\",\n                      pagination: false,\n                      locale: {\n                        emptyText: '班级暂无学生'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1877,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1866,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Modal, {\n                    title: \"\\u5BFC\\u5165\\u5B66\\u751F\\u540D\\u518C\",\n                    open: importStudentModalVisible,\n                    onOk: handleImportStudents,\n                    onCancel: () => {\n                      setImportStudentModalVisible(false);\n                      setImportFileList([]);\n                      importStudentForm.resetFields();\n                    },\n                    confirmLoading: loading,\n                    width: 600,\n                    children: [/*#__PURE__*/_jsxDEV(Form, {\n                      form: importStudentForm,\n                      layout: \"vertical\",\n                      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"class_id\",\n                        label: \"\\u9009\\u62E9\\u73ED\\u7EA7\",\n                        rules: [{\n                          required: true,\n                          message: '请选择班级'\n                        }],\n                        children: /*#__PURE__*/_jsxDEV(Select, {\n                          placeholder: \"\\u8BF7\\u9009\\u62E9\\u73ED\\u7EA7\",\n                          children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                            value: cls.id,\n                            children: [cls.name, \" (\", cls.grade || '未设置年级', \")\"]\n                          }, cls.id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1960,\n                            columnNumber: 33\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1958,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1953,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"file\",\n                        label: \"\\u5B66\\u751F\\u540D\\u518C\\u6587\\u4EF6\",\n                        rules: [{\n                          required: true,\n                          message: '请上传学生名册文件'\n                        }],\n                        extra: \"\\u652F\\u6301\\u7684\\u6587\\u4EF6\\u683C\\u5F0F\\uFF1A.xlsx, .xls, .csv\\u3002\\u6587\\u4EF6\\u7B2C\\u4E00\\u884C\\u5FC5\\u987B\\u662F\\u8868\\u5934\\uFF0C\\u5305\\u542B\\uFF1A\\u59D3\\u540D,\\u7528\\u6237\\u540D,\\u5BC6\\u7801,\\u624B\\u673A\\u53F7\\u7B49\\u5B57\\u6BB5\",\n                        children: /*#__PURE__*/_jsxDEV(Upload, {\n                          accept: \".xlsx,.xls,.csv\",\n                          fileList: importFileList,\n                          beforeUpload: file => {\n                            setImportFileList([file]);\n                            return false;\n                          },\n                          onRemove: () => {\n                            setImportFileList([]);\n                          },\n                          maxCount: 1,\n                          children: /*#__PURE__*/_jsxDEV(Button, {\n                            icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1984,\n                              columnNumber: 45\n                            }, this),\n                            children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1984,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1972,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1966,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1949,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: 16\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: \"\\u5BFC\\u5165\\u8BF4\\u660E\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1989,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u8BF7\\u5148\\u4E0B\\u8F7D\\u5BFC\\u5165\\u6A21\\u677F\\uFF0C\\u6309\\u7167\\u6A21\\u677F\\u683C\\u5F0F\\u586B\\u5199\\u5B66\\u751F\\u4FE1\\u606F\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1991,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u59D3\\u540D\\u3001\\u7528\\u6237\\u540D\\u3001\\u5BC6\\u7801\\u4E3A\\u5FC5\\u586B\\u5B57\\u6BB5\\uFF0C\\u5176\\u4ED6\\u5B57\\u6BB5\\u53EF\\u9009\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1992,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u7528\\u6237\\u540D\\u5FC5\\u987B\\u552F\\u4E00\\uFF0C\\u5982\\u679C\\u5B58\\u5728\\u91CD\\u590D\\u5C06\\u5BFC\\u81F4\\u5BFC\\u5165\\u5931\\u8D25\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1993,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                          children: \"\\u5BFC\\u5165\\u6210\\u529F\\u540E\\uFF0C\\u5B66\\u751F\\u5C06\\u81EA\\u52A8\\u6DFB\\u52A0\\u5230\\u9009\\u62E9\\u7684\\u73ED\\u7EA7\\u4E2D\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1994,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1990,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1988,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1937,\n                    columnNumber: 23\n                  }, this)]\n                }, \"2\", true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1684,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                  tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2004,\n                      columnNumber: 27\n                    }, this), \"\\u89D2\\u8272\\u7BA1\\u7406\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2003,\n                    columnNumber: 25\n                  }, this),\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 16\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2013,\n                        columnNumber: 33\n                      }, this),\n                      onClick: showAddUserModal,\n                      children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2011,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2010,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Table, {\n                    columns: [{\n                      title: '用户名',\n                      dataIndex: 'username',\n                      key: 'username'\n                    }, {\n                      title: '姓名',\n                      dataIndex: 'full_name',\n                      key: 'full_name'\n                    }, {\n                      title: '身份',\n                      key: 'identity',\n                      render: (_, record) => getUserRoleTags(record)\n                    }, {\n                      title: '主要角色',\n                      dataIndex: 'primary_role',\n                      key: 'primary_role',\n                      render: (_, record) => record.primary_role ? /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"blue\",\n                        children: record.primary_role.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2041,\n                        columnNumber: 53\n                      }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"default\",\n                        children: \"\\u672A\\u5206\\u914D\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2041,\n                        columnNumber: 106\n                      }, this)\n                    }, {\n                      title: '所有角色',\n                      dataIndex: 'roles',\n                      key: 'roles',\n                      render: roles => /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: roles && roles.map(role => /*#__PURE__*/_jsxDEV(Tag, {\n                          color: \"green\",\n                          children: role.name\n                        }, role.id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2051,\n                          columnNumber: 35\n                        }, this))\n                      }, void 0, false)\n                    }, {\n                      title: '操作',\n                      key: 'action',\n                      render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n                        size: \"middle\",\n                        children: [/*#__PURE__*/_jsxDEV(Button, {\n                          type: \"primary\",\n                          icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2065,\n                            columnNumber: 41\n                          }, this),\n                          size: \"small\",\n                          onClick: () => showRoleModal(record),\n                          children: \"\\u5206\\u914D\\u89D2\\u8272\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2063,\n                          columnNumber: 33\n                        }, this), record.roles && record.roles.length > 0 && record.roles.map(role => /*#__PURE__*/_jsxDEV(Button, {\n                          danger: true,\n                          size: \"small\",\n                          onClick: () => handleRevokeRole(record.id, role.id),\n                          children: [\"\\u64A4\\u9500 \", role.name]\n                        }, `${record.id}_${role.id}`, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2072,\n                          columnNumber: 35\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2062,\n                        columnNumber: 31\n                      }, this)\n                    }],\n                    dataSource: users,\n                    rowKey: record => `user_${record.id}`,\n                    loading: loading\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2019,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Modal, {\n                    title: \"\\u5206\\u914D\\u89D2\\u8272\",\n                    open: roleModalVisible,\n                    onOk: handleRoleSubmit,\n                    onCancel: handleRoleCancel,\n                    confirmLoading: loading,\n                    children: currentUser && /*#__PURE__*/_jsxDEV(Form, {\n                      form: roleForm,\n                      layout: \"vertical\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [\"\\u4E3A\\u7528\\u6237 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: currentUser.full_name || currentUser.username\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2103,\n                          columnNumber: 36\n                        }, this), \" \\u5206\\u914D\\u89D2\\u8272\\uFF1A\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2103,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"role_id\",\n                        label: \"\\u89D2\\u8272\",\n                        rules: [{\n                          required: true,\n                          message: '请选择角色'\n                        }],\n                        children: /*#__PURE__*/_jsxDEV(Select, {\n                          placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n                          children: [roles.map(role => /*#__PURE__*/_jsxDEV(Option, {\n                            value: role.id,\n                            children: [role.name, \" (\", role.description || role.code, \")\"]\n                          }, role.id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2112,\n                            columnNumber: 35\n                          }, this)), (!roles || roles.length === 0) && Object.values(USER_ROLES).map(role => /*#__PURE__*/_jsxDEV(Option, {\n                            value: role.value,\n                            children: role.label\n                          }, role.value, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2118,\n                            columnNumber: 35\n                          }, this))]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2109,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2104,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2099,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2091,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Modal, {\n                    title: \"\\u6DFB\\u52A0\\u7528\\u6237\",\n                    open: newUserModalVisible,\n                    onOk: handleAddUser,\n                    onCancel: () => setNewUserModalVisible(false),\n                    confirmLoading: loading,\n                    width: 600,\n                    children: /*#__PURE__*/_jsxDEV(Form, {\n                      form: userForm,\n                      layout: \"vertical\",\n                      initialValues: {\n                        activeTab: '1'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"activeTab\",\n                        hidden: true,\n                        children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2143,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2142,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n                        defaultActiveKey: \"1\",\n                        onChange: key => userForm.setFieldsValue({\n                          activeTab: key\n                        }),\n                        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n                          tab: \"\\u9009\\u62E9\\u73B0\\u6709\\u7528\\u6237\",\n                          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"user_id\",\n                            label: \"\\u9009\\u62E9\\u7528\\u6237\",\n                            rules: [{\n                              required: true,\n                              message: '请选择用户'\n                            }],\n                            children: /*#__PURE__*/_jsxDEV(Select, {\n                              placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\",\n                              showSearch: true,\n                              filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                              children: allUsers.filter(user => !users.some(existingUser => existingUser.id === user.id)).map(user => /*#__PURE__*/_jsxDEV(Option, {\n                                value: user.id,\n                                children: [user.full_name || user.username, \" (\", user.username, \")\"]\n                              }, user.id, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2166,\n                                columnNumber: 39\n                              }, this))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2155,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2150,\n                            columnNumber: 31\n                          }, this)\n                        }, \"1\", false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2149,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                          tab: \"\\u521B\\u5EFA\\u65B0\\u7528\\u6237\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"username\",\n                            label: \"\\u7528\\u6237\\u540D\",\n                            rules: [{\n                              required: true,\n                              message: '请输入用户名'\n                            }],\n                            children: /*#__PURE__*/_jsxDEV(Input, {\n                              placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2180,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2175,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"password\",\n                            label: \"\\u5BC6\\u7801\",\n                            rules: [{\n                              required: true,\n                              message: '请输入密码'\n                            }],\n                            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2187,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2182,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"full_name\",\n                            label: \"\\u59D3\\u540D\",\n                            rules: [{\n                              required: true,\n                              message: '请输入姓名'\n                            }],\n                            children: /*#__PURE__*/_jsxDEV(Input, {\n                              placeholder: \"\\u8BF7\\u8F93\\u5165\\u59D3\\u540D\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2194,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2189,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"email\",\n                            label: \"\\u90AE\\u7BB1\",\n                            children: /*#__PURE__*/_jsxDEV(Input, {\n                              placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2200,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2196,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"phone\",\n                            label: \"\\u7535\\u8BDD\",\n                            children: /*#__PURE__*/_jsxDEV(Input, {\n                              placeholder: \"\\u8BF7\\u8F93\\u5165\\u7535\\u8BDD\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2206,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2202,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"is_teacher\",\n                            valuePropName: \"checked\",\n                            children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                              children: \"\\u662F\\u6559\\u5E08\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2209,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2208,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                            name: \"is_admin\",\n                            valuePropName: \"checked\",\n                            children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                              children: \"\\u662F\\u7BA1\\u7406\\u5458\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2212,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2211,\n                            columnNumber: 31\n                          }, this)]\n                        }, \"2\", true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2174,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2145,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2216,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                        name: \"role_id\",\n                        label: \"\\u5206\\u914D\\u89D2\\u8272\",\n                        rules: [{\n                          required: true,\n                          message: '请选择角色'\n                        }],\n                        children: /*#__PURE__*/_jsxDEV(Select, {\n                          placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n                          children: [roles.map(role => /*#__PURE__*/_jsxDEV(Option, {\n                            value: role.id,\n                            children: [role.name, \" (\", role.description || role.code, \")\"]\n                          }, role.id, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2225,\n                            columnNumber: 33\n                          }, this)), (!roles || roles.length === 0) && Object.values(USER_ROLES).map(role => /*#__PURE__*/_jsxDEV(Option, {\n                            value: role.value,\n                            children: role.label\n                          }, role.value, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2231,\n                            columnNumber: 33\n                          }, this))]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2222,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2217,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2137,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2129,\n                    columnNumber: 23\n                  }, this)]\n                }, \"3\", true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2001,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1577,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1576,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1575,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1570,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-school-selected\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u8BF7\\u5148\\u9009\\u62E9\\u4E00\\u4E2A\\u5B66\\u6821\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: () => setActiveKey(\"1\"),\n            children: \"\\u8FD4\\u56DE\\u5B66\\u6821\\u5217\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2245,\n          columnNumber: 13\n        }, this)\n      }, \"2\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 1568,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1325,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1324,\n    columnNumber: 5\n  }, this);\n};\n_s(SuperSchoolManagement, \"WeGjVG2RF/1rey0TyeD9abDzE3k=\", false, function () {\n  return [useAuth, Form.useForm, Form.useForm, Form.useForm, Form.useForm, Form.useForm, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = SuperSchoolManagement;\nexport default SuperSchoolManagement;\nvar _c;\n$RefreshReg$(_c, \"SuperSchoolManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "message", "Modal", "Form", "Input", "Select", "Tabs", "Spin", "Space", "Tag", "Divider", "Upload", "Checkbox", "Row", "Col", "PlusOutlined", "EditOutlined", "DeleteOutlined", "UserOutlined", "TeamOutlined", "SettingOutlined", "UploadOutlined", "DownloadOutlined", "SearchOutlined", "getSchools", "createSchool", "updateSchool", "deleteSchool", "getRegions", "getSchoolDetail", "getClassesBySchool", "createClassForSchool", "updateClassInfo", "deleteClassById", "getSchoolRoles", "assignRole", "revokeRole", "getSchoolUsers", "getUsers", "useAuth", "getUserRoleTags", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TabPane", "Option", "USER_ROLES", "SUPER_ADMIN", "value", "label", "SCHOOL_ADMIN", "PRINCIPAL", "VICE_PRINCIPAL", "ACADEMIC_DIRECTOR", "GRADE_DIRECTOR", "SUBJECT_LEADER", "LESSON_PLANNER", "CLASS_TEACHER", "TEACHER", "STUDENT", "PARENT", "SuperSchoolManagement", "_s", "user", "schools", "setSchools", "loading", "setLoading", "modalVisible", "setModalVisible", "currentSchool", "setCurrentSchool", "selectedSchool", "setSelectedSchool", "form", "useForm", "provinces", "setProvinces", "cities", "setCities", "districts", "setDistricts", "searchForm", "filteredSchools", "setFilteredSchools", "isSearching", "setIsSearching", "schoolDetail", "setSchoolDetail", "classes", "setClasses", "roles", "setRoles", "users", "setUsers", "allUsers", "setAllUsers", "detailActiveTab", "setDetailActiveTab", "classModalVisible", "setClassModalVisible", "roleModalVisible", "setRoleModalVisible", "newUserModalVisible", "setNewUserModalVisible", "currentClass", "setCurrentClass", "currentUser", "setCurrentUser", "classForm", "roleForm", "userForm", "batchStudentForm", "batchStudentModalVisible", "setBatchStudentModalVisible", "selectedClassId", "setSelectedClassId", "classStudentsModalVisible", "setClassStudentsModalVisible", "currentClassStudents", "setCurrentClassStudents", "currentClassInfo", "setCurrentClassInfo", "importStudentModalVisible", "setImportStudentModalVisible", "importStudentForm", "importFileList", "setImportFileList", "schoolDetailForm", "is_admin", "fetchRegions", "then", "fetchSchools", "data", "console", "log", "schoolsData", "Array", "isArray", "items", "processedSchools", "map", "school", "index", "schoolId", "id", "undefined", "warn", "numericId", "parseInt", "isNaN", "province", "city", "district", "class_count", "error", "length", "uniqueProvinces", "Set", "filter", "Boolean", "backup<PERSON><PERSON>r", "handleProvinceChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uniqueCities", "handleCityChange", "getFieldValue", "uniqueDistricts", "showModal", "resetFields", "name", "handleSubmit", "values", "validateFields", "updateSchoolInfo", "success", "updateError", "handleDelete", "confirm", "title", "content", "okText", "cancelText", "onOk", "fetchSchoolDetail", "numericSchoolId", "fetchClasses", "fetchRoles", "fetchUsers", "fetchAllUsers", "e", "classesList", "cls", "Date", "now", "handleDetailTabChange", "key", "handleUpdateSchoolDetail", "showClassModal", "record", "grade", "handleClassCancel", "handleClassSubmit", "school_id", "handleDeleteClass", "classId", "showRoleModal", "showAddUserModal", "activeTab", "handleRoleCancel", "handleRoleSubmit", "user_id", "role_id", "handleAddUser", "userId", "username", "password", "createUser", "newUser", "full_name", "email", "phone", "is_teacher", "showBatchStudentModal", "downloadStudentTemplate", "headers", "exampleData", "csv<PERSON><PERSON>nt", "join", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleImportStudents", "class_id", "formData", "FormData", "append", "batchCreateStudents", "addStudentToClass", "reader", "FileReader", "onload", "target", "result", "students", "successCount", "failCount", "lines", "split", "fullNameIndex", "findIndex", "h", "includes", "usernameIndex", "passwordIndex", "i", "_values$fullNameIndex", "_values$usernameIndex", "_values$passwordIndex", "trim", "student", "is_student", "phoneIndex", "emailIndex", "genderIndex", "gender", "noteIndex", "note", "push", "newStudent", "warning", "onerror", "readAsText", "showClassStudents", "classRecord", "getClass", "classDetails", "handleBatchAddStudents", "studentsData", "students_data", "studentLines", "errors", "for<PERSON>ach", "line", "parts", "fullName", "createdStudents", "handleRevokeRole", "roleId", "handleManageSchool", "schoolData", "setActiveKey", "setTimeout", "active<PERSON><PERSON>", "columns", "dataIndex", "width", "render", "text", "_", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "danger", "handleTabChange", "handleSearch", "changedValues", "allValues", "hasSearchCriteria", "filtered", "nameMatch", "toLowerCase", "provinceMatch", "cityMatch", "districtMatch", "handleResetSearch", "className", "onChange", "tab", "extra", "layout", "marginBottom", "onValuesChange", "<PERSON><PERSON>", "placeholder", "allowClear", "getFieldsValue", "showSearch", "optionFilterProp", "filterOption", "input", "option", "indexOf", "disabled", "spinning", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "pageSizeOptions", "showTotal", "total", "footer", "textAlign", "open", "onCancel", "confirmLoading", "rules", "required", "initialValues", "onFinish", "currentForm", "current", "htmlType", "tip", "display", "gap", "size", "TextArea", "rows", "fontFamily", "removeStudentFromClass", "locale", "emptyText", "accept", "fileList", "beforeUpload", "file", "onRemove", "maxCount", "marginTop", "primary_role", "color", "role", "description", "code", "Object", "hidden", "defaultActiveKey", "some", "existingUser", "Password", "valuePropName", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/SuperSchoolManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Card, Table, Button, message, Modal, Form, Input, Select, Tabs, Spin, Space, Tag, Divider, Upload, Checkbox, Row, Col } from 'antd';\r\nimport { PlusOutlined, EditOutlined, DeleteOutlined, UserOutlined, TeamOutlined, SettingOutlined, UploadOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons';\r\nimport { getSchools, createSchool, updateSchool, deleteSchool, getRegions, getSchoolDetail, getClassesBySchool, createClassForSchool, updateClassInfo, deleteClassById, getSchoolRoles, assignRole, revokeRole, getSchoolUsers, getUsers } from '../utils/api';\r\nimport { useAuth } from '../utils/auth';\r\nimport { getUserRoleTags } from '../utils/roleUtils';\r\n\r\nconst { TabPane } = Tabs;\r\nconst { Option } = Select;\r\n\r\n// 定义所有角色类型\r\nconst USER_ROLES = {\r\n  // 基础角色\r\n  SUPER_ADMIN: { value: \"super_admin\", label: \"超级管理员\" },\r\n  SCHOOL_ADMIN: { value: \"school_admin\", label: \"学校管理员\" },\r\n  PRINCIPAL: { value: \"principal\", label: \"校长\" },\r\n  VICE_PRINCIPAL: { value: \"vice_principal\", label: \"副校长\" },\r\n  ACADEMIC_DIRECTOR: { value: \"academic_director\", label: \"教务处主任\" },\r\n  GRADE_DIRECTOR: { value: \"grade_director\", label: \"年级组长\" },\r\n  SUBJECT_LEADER: { value: \"subject_leader\", label: \"教研组长\" },\r\n  LESSON_PLANNER: { value: \"lesson_planner\", label: \"备课组长\" },\r\n  CLASS_TEACHER: { value: \"class_teacher\", label: \"班主任\" },\r\n  TEACHER: { value: \"teacher\", label: \"教师\" },\r\n  STUDENT: { value: \"student\", label: \"学生\" },\r\n  PARENT: { value: \"parent\", label: \"家长\" }\r\n};\r\n\r\nconst SuperSchoolManagement = () => {\r\n  const { user } = useAuth();\r\n  const [schools, setSchools] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [currentSchool, setCurrentSchool] = useState(null);\r\n  const [selectedSchool, setSelectedSchool] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [provinces, setProvinces] = useState([]);\r\n  const [cities, setCities] = useState([]);\r\n  const [districts, setDistricts] = useState([]);\r\n  \r\n  // 添加搜索相关状态\r\n  const [searchForm] = Form.useForm();\r\n  const [filteredSchools, setFilteredSchools] = useState([]);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  \r\n  // 学校详情管理相关状态\r\n  const [schoolDetail, setSchoolDetail] = useState(null);\r\n  const [classes, setClasses] = useState([]);\r\n  const [roles, setRoles] = useState([]);\r\n  const [users, setUsers] = useState([]);\r\n  const [allUsers, setAllUsers] = useState([]);\r\n  const [detailActiveTab, setDetailActiveTab] = useState('1');\r\n  const [classModalVisible, setClassModalVisible] = useState(false);\r\n  const [roleModalVisible, setRoleModalVisible] = useState(false);\r\n  const [newUserModalVisible, setNewUserModalVisible] = useState(false);\r\n  const [currentClass, setCurrentClass] = useState(null);\r\n  const [currentUser, setCurrentUser] = useState(null);\r\n  const [classForm] = Form.useForm();\r\n  const [roleForm] = Form.useForm();\r\n  const [userForm] = Form.useForm();\r\n  const [batchStudentForm] = Form.useForm();\r\n  const [batchStudentModalVisible, setBatchStudentModalVisible] = useState(false);\r\n  const [selectedClassId, setSelectedClassId] = useState(null);\r\n  const [classStudentsModalVisible, setClassStudentsModalVisible] = useState(false);\r\n  const [currentClassStudents, setCurrentClassStudents] = useState([]);\r\n  const [currentClassInfo, setCurrentClassInfo] = useState(null);\r\n  const [importStudentModalVisible, setImportStudentModalVisible] = useState(false);\r\n  const [importStudentForm] = Form.useForm();\r\n  const [importFileList, setImportFileList] = useState([]);\r\n  const [schoolDetailForm] = Form.useForm(); // 添加学校详情表单引用\r\n\r\n  // 获取学校列表和地区数据\r\n  useEffect(() => {\r\n    if (user && user.is_admin) {\r\n      // 先获取地区数据，确保省份下拉菜单有数据\r\n      fetchRegions().then(() => {\r\n        // 再获取学校列表\r\n        fetchSchools();\r\n      });\r\n    }\r\n  }, [user]);\r\n\r\n  // 获取学校列表\r\n  const fetchSchools = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const data = await getSchools();\r\n      console.log(\"获取到的学校列表:\", data);\r\n      \r\n      // 确保数据是数组\r\n      let schoolsData = Array.isArray(data) ? data : [];\r\n      \r\n      // 如果数据是对象且有items属性，则使用items\r\n      if (!Array.isArray(data) && data && Array.isArray(data.items)) {\r\n        schoolsData = data.items;\r\n      }\r\n      \r\n      // 确保每个学校对象都有完整的字段，特别是id字段\r\n      const processedSchools = schoolsData.map((school, index) => {\r\n        // 检查是否有id字段，如果没有则使用索引+1作为ID\r\n        let schoolId = school.id;\r\n        if (schoolId === undefined || schoolId === null) {\r\n          console.warn('学校记录缺少ID，使用索引作为ID:', index + 1);\r\n          schoolId = index + 1;\r\n        }\r\n        \r\n        // 尝试将ID转换为数字\r\n        const numericId = parseInt(schoolId, 10);\r\n        if (!isNaN(numericId)) {\r\n          schoolId = numericId;\r\n        }\r\n        \r\n        return {\r\n          ...school,\r\n          id: schoolId, // 确保id字段存在\r\n          province: school.province || '',\r\n          city: school.city || '',\r\n          district: school.district || '',\r\n          class_count: school.class_count || 0\r\n        };\r\n      });\r\n      \r\n      setSchools(processedSchools);\r\n      setFilteredSchools(processedSchools); // 初始化过滤后的学校列表\r\n    } catch (error) {\r\n      console.error(\"获取学校列表失败:\", error);\r\n      message.error('获取学校列表失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取地区数据\r\n  const fetchRegions = async () => {\r\n    try {\r\n      console.log('开始获取全国省份数据...');\r\n      const data = await getRegions();\r\n      if (data && data.provinces) {\r\n        console.log(`成功获取 ${data.provinces.length} 个省份数据`);\r\n        setProvinces(data.provinces);\r\n      } else {\r\n        console.error('获取省份数据失败: 返回数据格式不正确', data);\r\n        // 尝试使用备用方法获取省份数据\r\n        try {\r\n          // 从数据库中查询所有已有的省份\r\n          const schoolsData = await getSchools();\r\n          if (Array.isArray(schoolsData)) {\r\n            const uniqueProvinces = [...new Set(schoolsData.map(school => school.province).filter(Boolean))];\r\n            if (uniqueProvinces.length > 0) {\r\n              console.log(`从学校数据中提取了 ${uniqueProvinces.length} 个省份`);\r\n              setProvinces(uniqueProvinces);\r\n            }\r\n          }\r\n        } catch (backupError) {\r\n          console.error('备用方法获取省份数据失败:', backupError);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('获取地区数据失败:', error);\r\n    }\r\n  };\r\n\r\n  // 处理省份变化\r\n  const handleProvinceChange = async (value) => {\r\n    form.setFieldsValue({ city: undefined, district: undefined });\r\n    setCities([]);\r\n    setDistricts([]);\r\n    \r\n    if (!value) return;\r\n    \r\n    try {\r\n      console.log(`获取省份 ${value} 的城市数据...`);\r\n      const data = await getRegions({ province: value });\r\n      if (data && data.cities && data.cities.length > 0) {\r\n        console.log(`成功获取 ${data.cities.length} 个城市数据`);\r\n        setCities(data.cities);\r\n      } else {\r\n        console.error('获取城市数据失败或城市列表为空:', data);\r\n        // 尝试使用备用方法获取城市数据\r\n        try {\r\n          // 从学校数据中提取该省份的城市\r\n          const schoolsData = await getSchools();\r\n          if (Array.isArray(schoolsData)) {\r\n            const uniqueCities = [...new Set(\r\n              schoolsData\r\n                .filter(school => school.province === value)\r\n                .map(school => school.city)\r\n                .filter(Boolean)\r\n            )];\r\n            if (uniqueCities.length > 0) {\r\n              console.log(`从学校数据中提取了 ${uniqueCities.length} 个城市`);\r\n              setCities(uniqueCities);\r\n            }\r\n          }\r\n        } catch (backupError) {\r\n          console.error('备用方法获取城市数据失败:', backupError);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('获取城市数据失败:', error);\r\n    }\r\n  };\r\n\r\n  // 处理城市变化\r\n  const handleCityChange = async (value) => {\r\n    form.setFieldsValue({ district: undefined });\r\n    setDistricts([]);\r\n    \r\n    if (!value) return;\r\n    \r\n    const province = form.getFieldValue('province');\r\n    if (!province) return;\r\n    \r\n    try {\r\n      console.log(`获取省份 ${province} 城市 ${value} 的区县数据...`);\r\n      const data = await getRegions({ \r\n        province: province,\r\n        city: value \r\n      });\r\n      if (data && data.districts && data.districts.length > 0) {\r\n        console.log(`成功获取 ${data.districts.length} 个区县数据`);\r\n        setDistricts(data.districts);\r\n      } else {\r\n        console.error('获取区县数据失败或区县列表为空:', data);\r\n        // 尝试使用备用方法获取区县数据\r\n        try {\r\n          // 从学校数据中提取该省份城市的区县\r\n          const schoolsData = await getSchools();\r\n          if (Array.isArray(schoolsData)) {\r\n            const uniqueDistricts = [...new Set(\r\n              schoolsData\r\n                .filter(school => school.province === province && school.city === value)\r\n                .map(school => school.district)\r\n                .filter(Boolean)\r\n            )];\r\n            if (uniqueDistricts.length > 0) {\r\n              console.log(`从学校数据中提取了 ${uniqueDistricts.length} 个区县`);\r\n              setDistricts(uniqueDistricts);\r\n            }\r\n          }\r\n        } catch (backupError) {\r\n          console.error('备用方法获取区县数据失败:', backupError);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('获取区县数据失败:', error);\r\n    }\r\n  };\r\n\r\n  // 打开学校编辑模态框\r\n  const showModal = (school = null) => {\r\n    setCurrentSchool(school);\r\n    form.resetFields();\r\n    if (school) {\r\n      form.setFieldsValue({\r\n        name: school.name,\r\n        province: school.province,\r\n        city: school.city,\r\n        district: school.district,\r\n      });\r\n      \r\n      // 如果有省份，加载城市\r\n      if (school.province) {\r\n        handleProvinceChange(school.province);\r\n      }\r\n      \r\n      // 如果有城市，加载区县\r\n      if (school.province && school.city) {\r\n        handleCityChange(school.city);\r\n      }\r\n    }\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理学校表单提交\r\n  const handleSubmit = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      setLoading(true);\r\n      \r\n      if (currentSchool) {\r\n        // 更新学校\r\n        console.log(`准备更新学校，ID: ${currentSchool.id}，数据:`, values);\r\n        \r\n        try {\r\n          // 使用 updateSchoolInfo API 而不是 updateSchool\r\n          const { updateSchoolInfo } = await import('../utils/api');\r\n          await updateSchoolInfo(currentSchool.id, values);\r\n          message.success('学校更新成功');\r\n        } catch (updateError) {\r\n          console.error('更新学校失败，尝试备用方法:', updateError);\r\n          \r\n          // 如果 updateSchoolInfo 失败，尝试使用 updateSchool\r\n          await updateSchool(currentSchool.id, values);\r\n          message.success('学校更新成功（使用备用方法）');\r\n        }\r\n      } else {\r\n        // 创建学校\r\n        console.log('准备创建新学校:', values);\r\n        await createSchool(values);\r\n        message.success('学校创建成功');\r\n      }\r\n      \r\n      setModalVisible(false);\r\n      fetchSchools();\r\n    } catch (error) {\r\n      console.error('保存学校信息失败:', error);\r\n      message.error(`保存学校信息失败: ${error.message || '未知错误'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 处理学校删除\r\n  const handleDelete = (id) => {\r\n    Modal.confirm({\r\n      title: '确认删除',\r\n      content: '确定要删除这个学校吗？删除后无法恢复，且会影响所有关联的班级和用户。',\r\n      okText: '确认',\r\n      cancelText: '取消',\r\n      onOk: async () => {\r\n        setLoading(true);\r\n        try {\r\n          await deleteSchool(id);\r\n          message.success('学校删除成功');\r\n          \r\n          // 如果删除的是当前选中的学校，清除选择\r\n          if (selectedSchool && selectedSchool.id === id) {\r\n            setSelectedSchool(null);\r\n          }\r\n          \r\n          fetchSchools();\r\n        } catch (error) {\r\n          message.error('学校删除失败');\r\n          console.error('学校删除失败:', error);\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  // 获取学校详情数据\r\n  const fetchSchoolDetail = async (schoolId) => {\r\n    setLoading(true);\r\n    try {\r\n      console.log(`获取学校详情，ID: ${schoolId}`);\r\n      \r\n      if (!schoolId && schoolId !== 0) {\r\n        console.error('未提供有效的学校ID，无法获取学校详情');\r\n        message.error('未提供有效的学校ID');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 确保ID是数字类型\r\n      const numericSchoolId = parseInt(schoolId, 10);\r\n      if (isNaN(numericSchoolId)) {\r\n        console.error('无效的学校ID:', schoolId);\r\n        message.error('无效的学校ID');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      const data = await getSchoolDetail(numericSchoolId);\r\n      console.log('获取到学校详情:', data);\r\n      \r\n      if (!data) {\r\n        console.error('获取学校详情失败：服务器未返回数据');\r\n        message.error('获取学校详情失败：服务器未返回数据');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      setSchoolDetail(data);\r\n      \r\n      // 获取班级和用户数据\r\n      fetchClasses(numericSchoolId);\r\n      if (detailActiveTab === '3') {\r\n        fetchRoles();\r\n        fetchUsers(numericSchoolId);\r\n        fetchAllUsers();\r\n      }\r\n    } catch (error) {\r\n      console.error('获取学校详情失败:', error);\r\n      message.error(`获取学校详情失败: ${error.message || '未知错误'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 获取班级数据\r\n  const fetchClasses = async (schoolId) => {\r\n    setLoading(true);\r\n    try {\r\n      console.log(`获取学校班级，学校ID: ${schoolId}，类型: ${typeof schoolId}`);\r\n      \r\n      if (!schoolId && schoolId !== 0) {\r\n        console.error('未提供学校ID，无法获取班级列表');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 尝试将schoolId转换为数字，但如果失败也继续使用原值\r\n      let numericSchoolId;\r\n      try {\r\n        numericSchoolId = parseInt(schoolId, 10);\r\n        if (isNaN(numericSchoolId)) {\r\n          console.warn('学校ID不是数字，使用原始值:', schoolId);\r\n          numericSchoolId = schoolId;\r\n        }\r\n      } catch (e) {\r\n        console.warn('转换学校ID失败，使用原始值:', schoolId);\r\n        numericSchoolId = schoolId;\r\n      }\r\n      \r\n      console.log(`调用API获取班级数据，学校ID: ${numericSchoolId}`);\r\n      const data = await getClassesBySchool(numericSchoolId);\r\n      console.log('获取到班级数据:', data);\r\n      \r\n      // 处理不同格式的返回数据\r\n      let classesList = [];\r\n      if (Array.isArray(data)) {\r\n        classesList = data;\r\n        console.log('班级数据是数组格式，长度:', classesList.length);\r\n      } else if (data && Array.isArray(data.items)) {\r\n        classesList = data.items;\r\n        console.log('班级数据是对象.items格式，长度:', classesList.length);\r\n      } else if (data && typeof data === 'object') {\r\n        classesList = [data]; // 单个对象转为数组\r\n        console.log('班级数据是单个对象，转为数组');\r\n      } else {\r\n        console.warn('班级数据格式不正确:', data);\r\n      }\r\n      \r\n      // 确保每个班级对象都有id字段\r\n      classesList = classesList.map((cls, index) => {\r\n        if (!cls.id && cls.id !== 0) {\r\n          console.warn(`班级记录缺少ID:`, cls);\r\n          cls.id = `temp_class_${Date.now()}_${index}`;\r\n        }\r\n        return cls;\r\n      });\r\n      \r\n      console.log('最终处理后的班级列表:', classesList);\r\n      setClasses(classesList);\r\n    } catch (error) {\r\n      console.error('获取班级信息失败:', error);\r\n      message.error('获取班级信息失败');\r\n      // 设置空数组避免前端崩溃\r\n      setClasses([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 获取角色数据\r\n  const fetchRoles = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const data = await getSchoolRoles();\r\n      setRoles(data);\r\n    } catch (error) {\r\n      console.error('获取角色信息失败:', error);\r\n      message.error('获取角色信息失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 获取用户数据\r\n  const fetchUsers = async (schoolId) => {\r\n    setLoading(true);\r\n    try {\r\n      const data = await getSchoolUsers(schoolId);\r\n      setUsers(data);\r\n    } catch (error) {\r\n      console.error('获取用户信息失败:', error);\r\n      message.error('获取用户信息失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 获取所有用户\r\n  const fetchAllUsers = async () => {\r\n    try {\r\n      const data = await getUsers();\r\n      if (data && typeof data === 'object' && 'items' in data) {\r\n        setAllUsers(data.items || []);\r\n      } else {\r\n        setAllUsers(data || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取所有用户失败:', error);\r\n    }\r\n  };\r\n  \r\n  // 处理学校详情标签页切换\r\n  const handleDetailTabChange = (key) => {\r\n    console.log(`详情Tab切换到: ${key}，之前的Tab: ${detailActiveTab}`);\r\n    setDetailActiveTab(key);\r\n    \r\n    if (selectedSchool && selectedSchool.id) {\r\n      // 确保ID是有效的\r\n      const schoolId = selectedSchool.id;\r\n      console.log(`详情Tab切换到: ${key}，学校ID: ${schoolId}`);\r\n      \r\n      if (key === '2') {\r\n        // 获取班级数据\r\n        console.log(`切换到班级管理标签页，获取班级数据，学校ID: ${schoolId}`);\r\n        fetchClasses(schoolId);\r\n      } else if (key === '3') {\r\n        // 获取角色和用户数据\r\n        fetchRoles();\r\n        fetchUsers(schoolId);\r\n        fetchAllUsers();\r\n      }\r\n    }\r\n  };\r\n  \r\n  // 更新学校信息\r\n  const handleUpdateSchoolDetail = async (values) => {\r\n    if (!schoolDetail) {\r\n      message.error('未找到学校详情，无法更新');\r\n      return;\r\n    }\r\n    \r\n    console.log('准备更新学校信息:', values);\r\n    \r\n    // 确保学校ID是有效的\r\n    const schoolId = schoolDetail.id;\r\n    if (!schoolId && schoolId !== 0) {\r\n      message.error('无效的学校ID，无法更新');\r\n      return;\r\n    }\r\n    \r\n    setLoading(true);\r\n    try {\r\n      // 使用 updateSchoolInfo API 而不是 updateSchool\r\n      // 因为 updateSchoolInfo 专门用于更新学校详情\r\n      const { updateSchoolInfo } = await import('../utils/api');\r\n      \r\n      // 记录请求详情\r\n      console.log(`发送更新请求，学校ID: ${schoolId}，数据:`, values);\r\n      \r\n      // 发送请求\r\n      await updateSchoolInfo(schoolId, values);\r\n      \r\n      message.success('学校信息更新成功');\r\n      \r\n      // 刷新学校详情\r\n      fetchSchoolDetail(schoolId);\r\n      \r\n      // 刷新学校列表\r\n      fetchSchools();\r\n    } catch (error) {\r\n      console.error('学校信息更新失败:', error);\r\n      message.error(`学校信息更新失败: ${error.message || '未知错误'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 打开班级编辑模态框\r\n  const showClassModal = (record = null) => {\r\n    setCurrentClass(record);\r\n    classForm.resetFields();\r\n    if (record) {\r\n      classForm.setFieldsValue({\r\n        name: record.name,\r\n        grade: record.grade,\r\n      });\r\n    }\r\n    setClassModalVisible(true);\r\n  };\r\n  \r\n  // 关闭班级编辑模态框\r\n  const handleClassCancel = () => {\r\n    setClassModalVisible(false);\r\n    setCurrentClass(null);\r\n    classForm.resetFields();\r\n  };\r\n  \r\n  // 保存班级信息\r\n  const handleClassSubmit = async () => {\r\n    try {\r\n      const values = await classForm.validateFields();\r\n      setLoading(true);\r\n      \r\n      if (!selectedSchool || !selectedSchool.id) {\r\n        message.error('未选择学校，无法保存班级');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      if (currentClass) {\r\n        // 更新班级\r\n        await updateClassInfo(currentClass.id, {\r\n          ...values,\r\n          school_id: selectedSchool.id\r\n        });\r\n        message.success('班级更新成功');\r\n      } else {\r\n        // 创建班级\r\n        await createClassForSchool({\r\n          ...values,\r\n          school_id: selectedSchool.id\r\n        });\r\n        message.success('班级创建成功');\r\n      }\r\n      \r\n      setClassModalVisible(false);\r\n      fetchClasses(selectedSchool.id);\r\n    } catch (error) {\r\n      console.error('保存班级信息失败:', error);\r\n      message.error('保存班级信息失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 删除班级\r\n  const handleDeleteClass = async (classId) => {\r\n    Modal.confirm({\r\n      title: '确认删除',\r\n      content: '确定要删除这个班级吗？删除后无法恢复。',\r\n      okText: '确认',\r\n      cancelText: '取消',\r\n      onOk: async () => {\r\n        setLoading(true);\r\n        try {\r\n          await deleteClassById(classId);\r\n          message.success('班级删除成功');\r\n          \r\n          if (selectedSchool && selectedSchool.id) {\r\n            fetchClasses(selectedSchool.id);\r\n          }\r\n        } catch (error) {\r\n          console.error('班级删除失败:', error);\r\n          message.error('班级删除失败');\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    });\r\n  };\r\n  \r\n  // 打开角色分配模态框\r\n  const showRoleModal = (record) => {\r\n    setCurrentUser(record);\r\n    roleForm.resetFields();\r\n    setRoleModalVisible(true);\r\n  };\r\n  \r\n  // 打开添加用户模态框\r\n  const showAddUserModal = () => {\r\n    userForm.resetFields();\r\n    userForm.setFieldsValue({ activeTab: '1' });\r\n    setNewUserModalVisible(true);\r\n  };\r\n  \r\n  // 关闭角色分配模态框\r\n  const handleRoleCancel = () => {\r\n    setRoleModalVisible(false);\r\n    setCurrentUser(null);\r\n    roleForm.resetFields();\r\n  };\r\n  \r\n  // 分配角色\r\n  const handleRoleSubmit = async () => {\r\n    try {\r\n      const values = await roleForm.validateFields();\r\n      setLoading(true);\r\n      \r\n      if (!selectedSchool || !selectedSchool.id) {\r\n        message.error('未选择学校，无法分配角色');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      await assignRole({\r\n        user_id: currentUser.id,\r\n        role_id: values.role_id,\r\n        school_id: selectedSchool.id\r\n      });\r\n      \r\n      message.success('角色分配成功');\r\n      setRoleModalVisible(false);\r\n      fetchUsers(selectedSchool.id);\r\n    } catch (error) {\r\n      console.error('角色分配失败:', error);\r\n      message.error('角色分配失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 添加用户并分配角色\r\n  const handleAddUser = async () => {\r\n    try {\r\n      const values = await userForm.validateFields();\r\n      setLoading(true);\r\n      \r\n      if (!selectedSchool || !selectedSchool.id) {\r\n        message.error('未选择学校，无法添加用户');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 判断是选择现有用户还是创建新用户\r\n      const activeTab = userForm.getFieldValue('activeTab') || '1';\r\n      let userId = values.user_id;\r\n      \r\n      if (activeTab === '2' || !userId) {\r\n        // 创建新用户\r\n        if (!values.username || !values.password) {\r\n          message.error('请填写用户名和密码');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        \r\n        try {\r\n          const { createUser } = await import('../utils/api');\r\n          const newUser = await createUser({\r\n            username: values.username,\r\n            password: values.password,\r\n            full_name: values.full_name,\r\n            email: values.email,\r\n            phone: values.phone,\r\n            is_teacher: values.is_teacher || false,\r\n            is_admin: values.is_admin || false,\r\n            school_id: selectedSchool.id\r\n          });\r\n          \r\n          userId = newUser.id;\r\n          message.success('用户创建成功');\r\n        } catch (error) {\r\n          console.error('创建用户失败:', error);\r\n          message.error(`创建用户失败: ${error.message || '未知错误'}`);\r\n          setLoading(false);\r\n          return;\r\n        }\r\n      }\r\n      \r\n      // 分配角色\r\n      await assignRole({\r\n        user_id: userId,\r\n        role_id: values.role_id,\r\n        school_id: selectedSchool.id\r\n      });\r\n      \r\n      message.success('用户角色分配成功');\r\n      setNewUserModalVisible(false);\r\n      userForm.resetFields();\r\n      fetchUsers(selectedSchool.id);\r\n      fetchAllUsers(); // 刷新所有用户列表\r\n    } catch (error) {\r\n      console.error('添加用户失败:', error);\r\n      message.error(`添加用户失败: ${error.message || '未知错误'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 显示批量添加学生模态框\r\n  const showBatchStudentModal = () => {\r\n    setBatchStudentModalVisible(true);\r\n    batchStudentForm.resetFields();\r\n  };\r\n  \r\n  // 下载学生导入模板\r\n  const downloadStudentTemplate = () => {\r\n    // 创建CSV内容\r\n    const headers = ['姓名(full_name)', '用户名(username)', '密码(password)', '手机号(phone)', '邮箱(email)', '性别(gender)', '备注(note)'];\r\n    const exampleData = [\r\n      '张三', 'zhangsan', '123456', '13800138000', '<EMAIL>', '男', '班长',\r\n      '李四', 'lisi', '123456', '13800138001', '<EMAIL>', '女', ''\r\n    ];\r\n    \r\n    let csvContent = headers.join(',') + '\\n';\r\n    csvContent += exampleData.join(',') + '\\n';\r\n    \r\n    // 创建Blob对象\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    \r\n    // 创建下载链接\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', '学生导入模板.csv');\r\n    link.style.visibility = 'hidden';\r\n    \r\n    // 添加到DOM并触发下载\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    \r\n    message.success('模板下载成功');\r\n  };\r\n  \r\n  // 处理导入学生名册\r\n  const handleImportStudents = async () => {\r\n    try {\r\n      // 验证表单\r\n      const values = await importStudentForm.validateFields();\r\n      const classId = values.class_id;\r\n      \r\n      if (importFileList.length === 0) {\r\n        message.error('请上传学生名册文件');\r\n        return;\r\n      }\r\n      \r\n      setLoading(true);\r\n      \r\n      // 创建FormData对象\r\n      const formData = new FormData();\r\n      formData.append('file', importFileList[0]);\r\n      formData.append('class_id', classId);\r\n      \r\n      // 导入API函数\r\n      const { batchCreateStudents, addStudentToClass } = await import('../utils/api');\r\n      \r\n      // 读取文件内容\r\n      const reader = new FileReader();\r\n      \r\n      reader.onload = async (e) => {\r\n        try {\r\n          const content = e.target.result;\r\n          let students = [];\r\n          let successCount = 0;\r\n          let failCount = 0;\r\n          \r\n          // 解析CSV文件\r\n          const lines = content.split(/\\\\r?\\\\n/);\r\n          const headers = lines[0].split(',');\r\n          \r\n          // 检查必要的字段\r\n          const fullNameIndex = headers.findIndex(h => h.includes('姓名') || h.includes('full_name'));\r\n          const usernameIndex = headers.findIndex(h => h.includes('用户名') || h.includes('username'));\r\n          const passwordIndex = headers.findIndex(h => h.includes('密码') || h.includes('password'));\r\n          \r\n          if (fullNameIndex === -1 || usernameIndex === -1 || passwordIndex === -1) {\r\n            message.error('文件格式错误：缺少必要的字段（姓名、用户名或密码）');\r\n            setLoading(false);\r\n            return;\r\n          }\r\n          \r\n          // 解析数据行\r\n          for (let i = 1; i < lines.length; i++) {\r\n            if (!lines[i].trim()) continue;\r\n            \r\n            const values = lines[i].split(',');\r\n            const student = {\r\n              full_name: values[fullNameIndex]?.trim(),\r\n              username: values[usernameIndex]?.trim(),\r\n              password: values[passwordIndex]?.trim(),\r\n              is_student: true\r\n            };\r\n            \r\n            // 添加可选字段\r\n            const phoneIndex = headers.findIndex(h => h.includes('手机') || h.includes('phone'));\r\n            if (phoneIndex !== -1 && values[phoneIndex]) {\r\n              student.phone = values[phoneIndex].trim();\r\n            }\r\n            \r\n            const emailIndex = headers.findIndex(h => h.includes('邮箱') || h.includes('email'));\r\n            if (emailIndex !== -1 && values[emailIndex]) {\r\n              student.email = values[emailIndex].trim();\r\n            }\r\n            \r\n            const genderIndex = headers.findIndex(h => h.includes('性别') || h.includes('gender'));\r\n            if (genderIndex !== -1 && values[genderIndex]) {\r\n              student.gender = values[genderIndex].trim();\r\n            }\r\n            \r\n            const noteIndex = headers.findIndex(h => h.includes('备注') || h.includes('note'));\r\n            if (noteIndex !== -1 && values[noteIndex]) {\r\n              student.note = values[noteIndex].trim();\r\n            }\r\n            \r\n            // 验证必要字段\r\n            if (!student.full_name || !student.username || !student.password) {\r\n              console.warn(`第${i}行数据不完整，跳过`, student);\r\n              failCount++;\r\n              continue;\r\n            }\r\n            \r\n            students.push(student);\r\n          }\r\n          \r\n          if (students.length === 0) {\r\n            message.error('没有有效的学生数据');\r\n            setLoading(false);\r\n            return;\r\n          }\r\n          \r\n          // 批量创建学生\r\n          console.log(`准备创建 ${students.length} 名学生`);\r\n          \r\n          // 逐个创建学生账号并添加到班级\r\n          for (const student of students) {\r\n            try {\r\n              const { createUser } = await import('../utils/api');\r\n              const newStudent = await createUser(student);\r\n              \r\n              // 添加到班级\r\n              await addStudentToClass(classId, newStudent.id);\r\n              successCount++;\r\n            } catch (error) {\r\n              console.error(`创建学生失败: ${student.username}`, error);\r\n              failCount++;\r\n            }\r\n          }\r\n          \r\n          // 显示结果\r\n          if (successCount > 0) {\r\n            message.success(`成功导入 ${successCount} 名学生到班级`);\r\n            setImportStudentModalVisible(false);\r\n            setImportFileList([]);\r\n            importStudentForm.resetFields();\r\n            \r\n            // 刷新班级数据\r\n            fetchClasses(selectedSchool.id);\r\n          } else {\r\n            message.error('未能成功导入任何学生');\r\n          }\r\n          \r\n          if (failCount > 0) {\r\n            message.warning(`${failCount} 名学生导入失败，可能是用户名已存在或数据格式错误`);\r\n          }\r\n        } catch (error) {\r\n          console.error('解析文件失败:', error);\r\n          message.error('解析文件失败');\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      };\r\n      \r\n      reader.onerror = () => {\r\n        message.error('读取文件失败');\r\n        setLoading(false);\r\n      };\r\n      \r\n      reader.readAsText(importFileList[0]);\r\n      \r\n    } catch (error) {\r\n      console.error('导入学生名册失败:', error);\r\n      message.error('导入学生名册失败');\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 显示班级学生\r\n  const showClassStudents = async (classRecord) => {\r\n    setLoading(true);\r\n    setCurrentClassInfo(classRecord);\r\n    \r\n    try {\r\n      // 导入API函数\r\n      const { getClass } = await import('../utils/api');\r\n      \r\n      // 获取班级详情，包括学生列表\r\n      const classDetails = await getClass(classRecord.id, true);\r\n      \r\n      // 设置学生列表\r\n      let students = [];\r\n      if (classDetails && classDetails.students) {\r\n        students = Array.isArray(classDetails.students) ? classDetails.students : [];\r\n      }\r\n      \r\n      setCurrentClassStudents(students);\r\n      setClassStudentsModalVisible(true);\r\n    } catch (error) {\r\n      console.error('获取班级学生失败:', error);\r\n      message.error('获取班级学生失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 处理批量添加学生\r\n  const handleBatchAddStudents = async () => {\r\n    try {\r\n      const values = await batchStudentForm.validateFields();\r\n      setLoading(true);\r\n      \r\n      const classId = values.class_id;\r\n      const studentsData = values.students_data.trim();\r\n      \r\n      if (!studentsData) {\r\n        message.error('请输入学生数据');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 解析学生数据\r\n      const studentLines = studentsData.split('\\n');\r\n      const students = [];\r\n      const errors = [];\r\n      \r\n      studentLines.forEach((line, index) => {\r\n        const parts = line.trim().split(',');\r\n        if (parts.length < 3) {\r\n          errors.push(`第${index + 1}行格式错误: ${line}`);\r\n          return;\r\n        }\r\n        \r\n        const [fullName, username, password] = parts;\r\n        if (!fullName || !username || !password) {\r\n          errors.push(`第${index + 1}行数据不完整: ${line}`);\r\n          return;\r\n        }\r\n        \r\n        students.push({\r\n          full_name: fullName.trim(),\r\n          username: username.trim(),\r\n          password: password.trim(),\r\n          is_student: true\r\n        });\r\n      });\r\n      \r\n      if (errors.length > 0) {\r\n        message.error(`数据格式错误:\\n${errors.join('\\n')}`);\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 创建学生账号\r\n      const { createUser, addStudentToClass } = await import('../utils/api');\r\n      const createdStudents = [];\r\n      let successCount = 0;\r\n      let failCount = 0;\r\n      \r\n      // 逐个创建学生账号并添加到班级\r\n      for (const student of students) {\r\n        try {\r\n          const newStudent = await createUser(student);\r\n          createdStudents.push(newStudent);\r\n          \r\n          // 添加到班级\r\n          await addStudentToClass(classId, newStudent.id);\r\n          successCount++;\r\n        } catch (error) {\r\n          console.error(`创建学生失败: ${student.username}`, error);\r\n          failCount++;\r\n        }\r\n      }\r\n      \r\n      // 显示结果\r\n      if (successCount > 0) {\r\n        message.success(`成功添加 ${successCount} 名学生到班级`);\r\n        setBatchStudentModalVisible(false);\r\n        batchStudentForm.resetFields();\r\n        \r\n        // 刷新班级数据\r\n        fetchClasses(selectedSchool.id);\r\n      } else {\r\n        message.error('未能成功添加任何学生');\r\n      }\r\n      \r\n      if (failCount > 0) {\r\n        message.warning(`${failCount} 名学生添加失败，可能是用户名已存在`);\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('批量添加学生失败:', error);\r\n      message.error('批量添加学生失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 撤销角色\r\n  const handleRevokeRole = async (userId, roleId) => {\r\n    Modal.confirm({\r\n      title: '确认撤销',\r\n      content: '确定要撤销这个用户的角色吗？',\r\n      okText: '确认',\r\n      cancelText: '取消',\r\n      onOk: async () => {\r\n        setLoading(true);\r\n        try {\r\n          if (!selectedSchool || !selectedSchool.id) {\r\n            message.error('未选择学校，无法撤销角色');\r\n            setLoading(false);\r\n            return;\r\n          }\r\n          \r\n          await revokeRole({\r\n            user_id: userId,\r\n            role_id: roleId,\r\n            school_id: selectedSchool.id\r\n          });\r\n          \r\n          message.success('角色撤销成功');\r\n          fetchUsers(selectedSchool.id);\r\n        } catch (error) {\r\n          console.error('角色撤销失败:', error);\r\n          message.error('角色撤销失败');\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    });\r\n  };\r\n  \r\n  // 处理管理按钮点击\r\n  const handleManageSchool = (record) => {\r\n    console.log(`点击管理按钮，学校记录:`, record);\r\n    console.log(`学校ID类型: ${typeof record.id}, 值: ${record.id}`);\r\n    \r\n    // 直接使用记录中的数据，不再重新获取学校详情\r\n    let schoolData = { ...record };\r\n    \r\n    // 确保学校数据包含必要的字段\r\n    if (!schoolData.name) {\r\n      schoolData.name = '未命名学校';\r\n    }\r\n    \r\n    // 确保ID是数字类型\r\n    if (typeof schoolData.id === 'string') {\r\n      const numericId = parseInt(schoolData.id, 10);\r\n      if (!isNaN(numericId)) {\r\n        schoolData.id = numericId;\r\n      }\r\n    }\r\n    \r\n    console.log(`处理后的学校数据:`, schoolData);\r\n    \r\n    // 设置学校详情数据，直接使用当前记录\r\n    setSchoolDetail(schoolData);\r\n    \r\n    // 设置选中的学校\r\n    setSelectedSchool(schoolData);\r\n    \r\n    // 获取班级数据\r\n    if (schoolData.id) {\r\n      console.log(`主动获取班级数据，学校ID: ${schoolData.id}`);\r\n      fetchClasses(schoolData.id);\r\n      fetchRoles();\r\n      fetchUsers(schoolData.id);\r\n      fetchAllUsers();\r\n      \r\n      // 设置详情标签页为1（基本信息）\r\n      setDetailActiveTab('1');\r\n    }\r\n    \r\n    // 切换到学校管理标签页\r\n    console.log(`已设置选中学校，ID: ${schoolData.id}，准备切换到Tab 2`);\r\n    \r\n    // 直接设置activeKey状态\r\n    setActiveKey(\"2\");\r\n    \r\n    // 使用setTimeout确保状态更新\r\n    setTimeout(() => {\r\n      console.log('延时执行检查，确保状态已更新，schoolId:', schoolData.id);\r\n      \r\n      // 再次确认activeKey已设置为2\r\n      if (activeKey !== \"2\") {\r\n        console.warn('activeKey不是2，再次设置为2');\r\n        setActiveKey(\"2\");\r\n      }\r\n    }, 100);\r\n  };\r\n\r\n  // 学校表格列定义\r\n  const columns = [\r\n    {\r\n      title: '序号',\r\n      dataIndex: 'id',\r\n      key: 'id',\r\n      width: 80,\r\n    },\r\n    {\r\n      title: '学校名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n    },\r\n    {\r\n      title: '省份',\r\n      dataIndex: 'province',\r\n      key: 'province',\r\n      render: (text) => text || '未设置',\r\n    },\r\n    {\r\n      title: '城市',\r\n      dataIndex: 'city',\r\n      key: 'city',\r\n      render: (text) => text || '未设置',\r\n    },\r\n    {\r\n      title: '区县',\r\n      dataIndex: 'district',\r\n      key: 'district',\r\n      render: (text) => text || '未设置',\r\n    },\r\n    {\r\n      title: '班级数量',\r\n      dataIndex: 'class_count',\r\n      key: 'class_count',\r\n      render: (text) => text || 0,\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <>\r\n          <Button \r\n            type=\"link\" \r\n            onClick={() => handleManageSchool(record)}\r\n          >\r\n            管理\r\n          </Button>\r\n          <Button \r\n            type=\"link\" \r\n            icon={<EditOutlined />} \r\n            onClick={() => showModal(record)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Button \r\n            type=\"link\" \r\n            danger\r\n            icon={<DeleteOutlined />} \r\n            onClick={() => handleDelete(record.id)}\r\n          >\r\n            删除\r\n          </Button>\r\n        </>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 处理Tab切换\r\n  const handleTabChange = (key) => {\r\n    console.log('Tab切换到:', key);\r\n    \r\n    // 更新activeKey状态\r\n    setActiveKey(key);\r\n    \r\n    if (key === \"1\") {\r\n      // 切换到学校列表，清除选中的学校\r\n      setSelectedSchool(null);\r\n      setSchoolDetail(null);\r\n    } else if (key === \"2\" && selectedSchool) {\r\n      console.log(`Tab切换到学校管理，学校ID: ${selectedSchool.id}`);\r\n      \r\n      // 如果没有学校详情数据，则使用selectedSchool的数据\r\n      if (!schoolDetail) {\r\n        setSchoolDetail(selectedSchool);\r\n      }\r\n      \r\n      // 获取班级数据\r\n      if (selectedSchool.id) {\r\n        fetchClasses(selectedSchool.id);\r\n      }\r\n    } else if (key === \"2\" && !selectedSchool) {\r\n      // 如果切换到Tab 2但没有选中的学校，显示错误消息\r\n      console.error('尝试切换到学校管理标签页，但没有选中的学校');\r\n      message.error('请先选择一个学校');\r\n      // 切换回Tab 1\r\n      setActiveKey(\"1\");\r\n    }\r\n  };\r\n\r\n  // 添加状态来控制当前激活的标签页\r\n  const [activeKey, setActiveKey] = useState(\"1\");\r\n  \r\n  // 当selectedSchool变化时更新activeKey\r\n  useEffect(() => {\r\n    if (selectedSchool) {\r\n      console.log('selectedSchool已更新，切换到Tab 2');\r\n      setActiveKey(\"2\");\r\n      \r\n      // 确保加载班级数据\r\n      if (selectedSchool.id) {\r\n        console.log(`selectedSchool变化，自动获取班级数据，学校ID: ${selectedSchool.id}`);\r\n        fetchClasses(selectedSchool.id);\r\n      }\r\n    } else {\r\n      setActiveKey(\"1\");\r\n    }\r\n  }, [selectedSchool]);\r\n  \r\n  // 添加搜索学校的方法 - 实时搜索\r\n  const handleSearch = (changedValues, allValues) => {\r\n    const { name, province, city, district } = allValues;\r\n    \r\n    // 只要有任何搜索条件，就设置为搜索状态\r\n    const hasSearchCriteria = name || province || city || district;\r\n    setIsSearching(hasSearchCriteria);\r\n    \r\n    // 过滤学校列表\r\n    const filtered = schools.filter(school => {\r\n      // 名称搜索 - 如果提供了名称，检查学校名称是否包含搜索词\r\n      const nameMatch = !name || (school.name && school.name.toLowerCase().includes(name.toLowerCase()));\r\n      \r\n      // 省份搜索\r\n      const provinceMatch = !province || school.province === province;\r\n      \r\n      // 城市搜索\r\n      const cityMatch = !city || school.city === city;\r\n      \r\n      // 区县搜索\r\n      const districtMatch = !district || school.district === district;\r\n      \r\n      // 所有条件都必须满足\r\n      return nameMatch && provinceMatch && cityMatch && districtMatch;\r\n    });\r\n    \r\n    setFilteredSchools(filtered);\r\n    \r\n    // 不再显示消息提示，避免频繁弹出\r\n  };\r\n  \r\n  // 重置搜索\r\n  const handleResetSearch = () => {\r\n    searchForm.resetFields();\r\n    setFilteredSchools(schools);\r\n    setIsSearching(false);\r\n  };\r\n  \r\n  return (\r\n    <div className=\"super-school-management system-school-page\" data-component=\"SuperSchoolManagement\">\r\n      <Tabs \r\n        activeKey={activeKey}\r\n        onChange={handleTabChange}\r\n      >\r\n        <TabPane tab=\"学校列表\" key=\"1\">\r\n          <Card \r\n            title=\"系统学校管理\" \r\n            extra={\r\n              <Button \r\n                type=\"primary\" \r\n                icon={<PlusOutlined />} \r\n                onClick={() => showModal()}\r\n              >\r\n                添加学校\r\n              </Button>\r\n            }\r\n          >\r\n            {/* 添加搜索表单 - 实时搜索 */}\r\n            <Form\r\n              form={searchForm}\r\n              layout=\"inline\"\r\n              style={{ marginBottom: 16 }}\r\n              onValuesChange={handleSearch}\r\n            >\r\n              <Form.Item name=\"name\" label=\"学校名称\">\r\n                <Input \r\n                  placeholder=\"请输入学校名称\" \r\n                  allowClear \r\n                  onChange={(e) => {\r\n                    // 当清除输入框时，确保触发搜索\r\n                    if (!e.target.value) {\r\n                      handleSearch({}, searchForm.getFieldsValue());\r\n                    }\r\n                  }}\r\n                />\r\n              </Form.Item>\r\n              <Form.Item name=\"province\" label=\"省份\">\r\n                <Select \r\n                  placeholder=\"请选择省份\" \r\n                  style={{ width: 150 }}\r\n                  allowClear\r\n                  showSearch\r\n                  optionFilterProp=\"children\"\r\n                  filterOption={(input, option) =>\r\n                    option.children && typeof option.children === 'string' ? \r\n                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                  }\r\n                  onChange={(value) => {\r\n                    searchForm.setFieldsValue({ city: undefined, district: undefined });\r\n                    if (value) {\r\n                      // 获取城市数据\r\n                      (async () => {\r\n                        try {\r\n                          const data = await getRegions({ province: value });\r\n                          if (data && data.cities) {\r\n                            setCities(data.cities);\r\n                          }\r\n                        } catch (error) {\r\n                          console.error('获取城市数据失败:', error);\r\n                        } finally {\r\n                          // 省份变化后立即触发搜索\r\n                          handleSearch({}, searchForm.getFieldsValue());\r\n                        }\r\n                      })();\r\n                    } else {\r\n                      setCities([]);\r\n                      setDistricts([]);\r\n                      // 清空省份后立即触发搜索\r\n                      handleSearch({}, searchForm.getFieldsValue());\r\n                    }\r\n                  }}\r\n                >\r\n                  {provinces.map(province => (\r\n                    <Option key={province} value={province}>{province}</Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n              <Form.Item name=\"city\" label=\"城市\">\r\n                <Select \r\n                  placeholder=\"请选择城市\" \r\n                  style={{ width: 120 }}\r\n                  allowClear\r\n                  disabled={!searchForm.getFieldValue('province')}\r\n                  onChange={(value) => {\r\n                    searchForm.setFieldsValue({ district: undefined });\r\n                    if (value) {\r\n                      // 获取区县数据\r\n                      (async () => {\r\n                        try {\r\n                          const data = await getRegions({ \r\n                            province: searchForm.getFieldValue('province'),\r\n                            city: value \r\n                          });\r\n                          if (data && data.districts) {\r\n                            setDistricts(data.districts);\r\n                          }\r\n                        } catch (error) {\r\n                          console.error('获取区县数据失败:', error);\r\n                        } finally {\r\n                          // 城市变化后立即触发搜索\r\n                          handleSearch({}, searchForm.getFieldsValue());\r\n                        }\r\n                      })();\r\n                    } else {\r\n                      setDistricts([]);\r\n                      // 清空城市后立即触发搜索\r\n                      handleSearch({}, searchForm.getFieldsValue());\r\n                    }\r\n                  }}\r\n                >\r\n                  {cities.map(city => (\r\n                    <Option key={city} value={city}>{city}</Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n              <Form.Item name=\"district\" label=\"区县\">\r\n                <Select \r\n                  placeholder=\"请选择区县\" \r\n                  style={{ width: 120 }}\r\n                  allowClear\r\n                  disabled={!searchForm.getFieldValue('city')}\r\n                  onChange={(value) => {\r\n                    // 区县变化后立即触发搜索\r\n                    handleSearch({}, searchForm.getFieldsValue());\r\n                  }}\r\n                >\r\n                  {districts.map(district => (\r\n                    <Option key={district} value={district}>{district}</Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n              <Form.Item>\r\n                <Button onClick={handleResetSearch} icon={<SearchOutlined />}>\r\n                  重置筛选\r\n                </Button>\r\n              </Form.Item>\r\n            </Form>\r\n            \r\n            <Spin spinning={loading}>\r\n              <Table \r\n                columns={columns} \r\n                dataSource={isSearching ? filteredSchools : schools} \r\n                rowKey={(record) => `school_${record.id}`}\r\n                pagination={{\r\n                  showSizeChanger: true,\r\n                  showQuickJumper: true,\r\n                  pageSizeOptions: ['10', '20', '50', '100'],\r\n                  showTotal: (total) => `共 ${total} 所学校`\r\n                }}\r\n                footer={() => (\r\n                  <div style={{ textAlign: 'right' }}>\r\n                    {isSearching ? (\r\n                      <span>搜索结果: 找到 {filteredSchools.length} 所学校</span>\r\n                    ) : (\r\n                      <span>显示所有学校: 共 {schools.length} 所</span>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              />\r\n            </Spin>\r\n            \r\n            {/* 学校编辑模态框 */}\r\n            <Modal\r\n              title={currentSchool ? \"编辑学校\" : \"添加学校\"}\r\n              open={modalVisible}\r\n              onOk={handleSubmit}\r\n              onCancel={() => setModalVisible(false)}\r\n              confirmLoading={loading}\r\n            >\r\n              <Form\r\n                form={form}\r\n                layout=\"vertical\"\r\n              >\r\n                <Form.Item\r\n                  name=\"name\"\r\n                  label=\"学校名称\"\r\n                  rules={[{ required: true, message: '请输入学校名称' }]}\r\n                >\r\n                  <Input placeholder=\"请输入学校名称\" />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"province\"\r\n                  label=\"省份\"\r\n                >\r\n                  <Select \r\n                    placeholder=\"请选择省份\" \r\n                    onChange={handleProvinceChange}\r\n                    showSearch\r\n                    optionFilterProp=\"children\"\r\n                    filterOption={(input, option) =>\r\n                      option.children && typeof option.children === 'string' ? \r\n                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                    }\r\n                  >\r\n                    {provinces.map(province => (\r\n                      <Option key={province} value={province}>{province}</Option>\r\n                    ))}\r\n                  </Select>\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"city\"\r\n                  label=\"城市\"\r\n                >\r\n                  <Select \r\n                    placeholder=\"请选择城市\" \r\n                    onChange={handleCityChange}\r\n                    disabled={!form.getFieldValue('province')}\r\n                    showSearch\r\n                    optionFilterProp=\"children\"\r\n                    filterOption={(input, option) =>\r\n                      option.children && typeof option.children === 'string' ? \r\n                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                    }\r\n                  >\r\n                    {cities.map(city => (\r\n                      <Option key={city} value={city}>{city}</Option>\r\n                    ))}\r\n                  </Select>\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"district\"\r\n                  label=\"区县\"\r\n                >\r\n                  <Select \r\n                    placeholder=\"请选择区县\"\r\n                    disabled={!form.getFieldValue('city')}\r\n                    showSearch\r\n                    optionFilterProp=\"children\"\r\n                    filterOption={(input, option) =>\r\n                      option.children && typeof option.children === 'string' ? \r\n                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                    }\r\n                  >\r\n                    {districts.map(district => (\r\n                      <Option key={district} value={district}>{district}</Option>\r\n                    ))}\r\n                  </Select>\r\n                </Form.Item>\r\n              </Form>\r\n            </Modal>\r\n          </Card>\r\n        </TabPane>\r\n        \r\n        <TabPane tab={selectedSchool ? `${selectedSchool.name || '学校'}管理` : '学校管理'} key=\"2\">\r\n          {selectedSchool ? (\r\n            <div \r\n              className=\"school-detail-management\" \r\n              data-school-id={selectedSchool.id}\r\n              data-school-name={selectedSchool.name || '未命名学校'}\r\n            >\r\n              <Card title={`${selectedSchool.name || '学校'}详情管理`}>\r\n                <Spin spinning={loading && !classModalVisible && !roleModalVisible}>\r\n                  <Tabs activeKey={detailActiveTab} onChange={handleDetailTabChange}>\r\n                    {/* 学校基本信息 */}\r\n                    <TabPane\r\n                      tab={\r\n                        <span>\r\n                          <SettingOutlined />\r\n                          学校基本信息\r\n                        </span>\r\n                      }\r\n                      key=\"1\"\r\n                    >\r\n                      {schoolDetail ? (\r\n                        <Form\r\n                          form={schoolDetailForm}\r\n                          layout=\"vertical\"\r\n                          initialValues={{\r\n                            name: schoolDetail.name || selectedSchool?.name || '',\r\n                            province: schoolDetail.province || selectedSchool?.province || '',\r\n                            city: schoolDetail.city || selectedSchool?.city || '',\r\n                            district: schoolDetail.district || selectedSchool?.district || '',\r\n                          }}\r\n                          onFinish={handleUpdateSchoolDetail}\r\n                        >\r\n                          <Form.Item\r\n                            name=\"name\"\r\n                            label=\"学校名称\"\r\n                            rules={[{ required: true, message: '请输入学校名称' }]}\r\n                          >\r\n                            <Input />\r\n                          </Form.Item>\r\n                          <Form.Item\r\n                            name=\"province\"\r\n                            label=\"省份\"\r\n                          >\r\n                            <Select \r\n                              placeholder=\"请选择省份\" \r\n                              showSearch\r\n                              optionFilterProp=\"children\"\r\n                              filterOption={(input, option) =>\r\n                                option.children && typeof option.children === 'string' ? \r\n                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                              }\r\n                              onChange={(value) => {\r\n                                // 省份变化时，清空城市和区县\r\n                                const currentForm = form.current;\r\n                                if (currentForm) {\r\n                                  currentForm.setFieldsValue({ city: undefined, district: undefined });\r\n                                }\r\n                                handleProvinceChange(value);\r\n                              }}\r\n                            >\r\n                              {provinces.map(province => (\r\n                                <Option key={province} value={province}>{province}</Option>\r\n                              ))}\r\n                            </Select>\r\n                          </Form.Item>\r\n                          <Form.Item\r\n                            name=\"city\"\r\n                            label=\"城市\"\r\n                          >\r\n                            <Select \r\n                              placeholder=\"请选择城市\" \r\n                              showSearch\r\n                              optionFilterProp=\"children\"\r\n                              filterOption={(input, option) =>\r\n                                option.children && typeof option.children === 'string' ? \r\n                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                              }\r\n                              onChange={(value) => handleCityChange(value)}\r\n                              disabled={!schoolDetail?.province}\r\n                            >\r\n                              {cities.map(city => (\r\n                                <Option key={city} value={city}>{city}</Option>\r\n                              ))}\r\n                            </Select>\r\n                          </Form.Item>\r\n                          <Form.Item\r\n                            name=\"district\"\r\n                            label=\"区县\"\r\n                          >\r\n                            <Select \r\n                              placeholder=\"请选择区县\"\r\n                              showSearch\r\n                              optionFilterProp=\"children\"\r\n                              filterOption={(input, option) =>\r\n                                option.children && typeof option.children === 'string' ? \r\n                                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                              }\r\n                              disabled={!schoolDetail?.city}\r\n                            >\r\n                              {districts.map(district => (\r\n                                <Option key={district} value={district}>{district}</Option>\r\n                              ))}\r\n                            </Select>\r\n                          </Form.Item>\r\n                          <Form.Item>\r\n                            <Button type=\"primary\" htmlType=\"submit\">\r\n                              保存修改\r\n                            </Button>\r\n                          </Form.Item>\r\n                        </Form>\r\n                      ) : (\r\n                        <Spin tip=\"加载学校信息中...\" />\r\n                      )}\r\n                    </TabPane>\r\n                    \r\n                                          {/* 班级管理 */}\r\n                    <TabPane\r\n                      tab={\r\n                        <span>\r\n                          <TeamOutlined />\r\n                          班级管理\r\n                        </span>\r\n                      }\r\n                      key=\"2\"\r\n                    >\r\n                      <div style={{ marginBottom: 16, display: 'flex', gap: '10px' }}>\r\n                        <Button\r\n                          type=\"primary\"\r\n                          icon={<PlusOutlined />}\r\n                          onClick={() => showClassModal()}\r\n                        >\r\n                          添加班级\r\n                        </Button>\r\n                        <Button\r\n                          type=\"primary\" \r\n                          icon={<UserOutlined />}\r\n                          onClick={() => showBatchStudentModal()}\r\n                        >\r\n                          批量添加学生\r\n                        </Button>\r\n                        <Button\r\n                          type=\"primary\" \r\n                          icon={<UploadOutlined />}\r\n                          onClick={() => setImportStudentModalVisible(true)}\r\n                        >\r\n                          导入学生名册\r\n                        </Button>\r\n                        <Button\r\n                          type=\"default\" \r\n                          icon={<DownloadOutlined />}\r\n                          onClick={() => downloadStudentTemplate()}\r\n                        >\r\n                          下载导入模板\r\n                        </Button>\r\n                      </div>\r\n                      <Table\r\n                        columns={[\r\n                          {\r\n                            title: '班级名称',\r\n                            dataIndex: 'name',\r\n                            key: 'name',\r\n                          },\r\n                          {\r\n                            title: '年级',\r\n                            dataIndex: 'grade',\r\n                            key: 'grade',\r\n                          },\r\n                          {\r\n                            title: '学生数量',\r\n                            dataIndex: 'student_count',\r\n                            key: 'student_count',\r\n                            render: (text) => text || 0\r\n                          },\r\n                          {\r\n                            title: '操作',\r\n                            key: 'action',\r\n                            render: (_, record) => (\r\n                              <Space size=\"middle\">\r\n                                <Button \r\n                                  type=\"primary\" \r\n                                  icon={<EditOutlined />} \r\n                                  size=\"small\"\r\n                                  onClick={() => showClassModal(record)}\r\n                                >\r\n                                  编辑\r\n                                </Button>\r\n                                <Button \r\n                                  type=\"default\" \r\n                                  icon={<TeamOutlined />} \r\n                                  size=\"small\"\r\n                                  onClick={() => showClassStudents(record)}\r\n                                >\r\n                                  查看学生\r\n                                </Button>\r\n                                <Button \r\n                                  danger \r\n                                  icon={<DeleteOutlined />} \r\n                                  size=\"small\"\r\n                                  onClick={() => handleDeleteClass(record.id)}\r\n                                >\r\n                                  删除\r\n                                </Button>\r\n                              </Space>\r\n                            ),\r\n                          },\r\n                        ]}\r\n                        dataSource={classes}\r\n                        rowKey={(record) => `class_${record.id}`}\r\n                        loading={loading}\r\n                      />\r\n                      \r\n                      {/* 班级编辑模态框 */}\r\n                      <Modal\r\n                        title={currentClass ? \"编辑班级\" : \"添加班级\"}\r\n                        open={classModalVisible}\r\n                        onOk={handleClassSubmit}\r\n                        onCancel={handleClassCancel}\r\n                        confirmLoading={loading}\r\n                      >\r\n                        <Form\r\n                          form={classForm}\r\n                          layout=\"vertical\"\r\n                        >\r\n                          <Form.Item\r\n                            name=\"name\"\r\n                            label=\"班级名称\"\r\n                            rules={[{ required: true, message: '请输入班级名称' }]}\r\n                          >\r\n                            <Input placeholder=\"例如：七年级1班\" />\r\n                          </Form.Item>\r\n                          <Form.Item\r\n                            name=\"grade\"\r\n                            label=\"年级\"\r\n                            rules={[{ required: true, message: '请输入年级' }]}\r\n                          >\r\n                            <Select placeholder=\"请选择年级\">\r\n                              <Option value=\"一年级\">一年级</Option>\r\n                              <Option value=\"二年级\">二年级</Option>\r\n                              <Option value=\"三年级\">三年级</Option>\r\n                              <Option value=\"四年级\">四年级</Option>\r\n                              <Option value=\"五年级\">五年级</Option>\r\n                              <Option value=\"六年级\">六年级</Option>\r\n                              <Option value=\"七年级\">七年级</Option>\r\n                              <Option value=\"八年级\">八年级</Option>\r\n                              <Option value=\"九年级\">九年级</Option>\r\n                              <Option value=\"高一\">高一</Option>\r\n                              <Option value=\"高二\">高二</Option>\r\n                              <Option value=\"高三\">高三</Option>\r\n                            </Select>\r\n                          </Form.Item>\r\n                        </Form>\r\n                      </Modal>\r\n                      \r\n                      {/* 批量添加学生模态框 */}\r\n                      <Modal\r\n                        title=\"批量添加学生\"\r\n                        open={batchStudentModalVisible}\r\n                        onOk={handleBatchAddStudents}\r\n                        onCancel={() => setBatchStudentModalVisible(false)}\r\n                        confirmLoading={loading}\r\n                        width={700}\r\n                      >\r\n                        <Form\r\n                          form={batchStudentForm}\r\n                          layout=\"vertical\"\r\n                        >\r\n                          <Form.Item\r\n                            name=\"class_id\"\r\n                            label=\"选择班级\"\r\n                            rules={[{ required: true, message: '请选择班级' }]}\r\n                          >\r\n                            <Select \r\n                              placeholder=\"请选择班级\"\r\n                              onChange={(value) => setSelectedClassId(value)}\r\n                            >\r\n                              {classes.map(cls => (\r\n                                <Option key={cls.id} value={cls.id}>\r\n                                  {cls.name} ({cls.grade || '未设置年级'})\r\n                                </Option>\r\n                              ))}\r\n                            </Select>\r\n                          </Form.Item>\r\n                          <Form.Item\r\n                            name=\"students_data\"\r\n                            label=\"学生数据\"\r\n                            rules={[{ required: true, message: '请输入学生数据' }]}\r\n                            extra=\"每行一个学生，格式：姓名,用户名,密码。例如：张三,zhangsan,123456\"\r\n                          >\r\n                            <Input.TextArea \r\n                              placeholder=\"张三,zhangsan,123456&#10;李四,lisi,123456&#10;王五,wangwu,123456\" \r\n                              rows={10}\r\n                              style={{ fontFamily: 'monospace' }}\r\n                            />\r\n                          </Form.Item>\r\n                        </Form>\r\n                      </Modal>\r\n                      \r\n                      {/* 班级学生模态框 */}\r\n                      <Modal\r\n                        title={currentClassInfo ? `${currentClassInfo.name} 学生列表` : '班级学生'}\r\n                        open={classStudentsModalVisible}\r\n                        onCancel={() => setClassStudentsModalVisible(false)}\r\n                        footer={[\r\n                          <Button key=\"close\" onClick={() => setClassStudentsModalVisible(false)}>\r\n                            关闭\r\n                          </Button>\r\n                        ]}\r\n                        width={800}\r\n                      >\r\n                        <Table\r\n                          columns={[\r\n                            {\r\n                              title: '学号',\r\n                              dataIndex: 'id',\r\n                              key: 'id',\r\n                              width: 80,\r\n                            },\r\n                            {\r\n                              title: '姓名',\r\n                              dataIndex: 'full_name',\r\n                              key: 'full_name',\r\n                            },\r\n                            {\r\n                              title: '用户名',\r\n                              dataIndex: 'username',\r\n                              key: 'username',\r\n                            },\r\n                            {\r\n                              title: '操作',\r\n                              key: 'action',\r\n                              render: (_, record) => (\r\n                                <Space size=\"middle\">\r\n                                  <Button \r\n                                    danger \r\n                                    size=\"small\"\r\n                                    onClick={() => {\r\n                                      if (currentClassInfo) {\r\n                                        Modal.confirm({\r\n                                          title: '确认移除',\r\n                                          content: `确定要将学生 ${record.full_name || record.username} 从班级中移除吗？`,\r\n                                          onOk: async () => {\r\n                                            try {\r\n                                              const { removeStudentFromClass } = await import('../utils/api');\r\n                                              await removeStudentFromClass(currentClassInfo.id, record.id);\r\n                                              message.success('学生已从班级移除');\r\n                                              showClassStudents(currentClassInfo);\r\n                                            } catch (error) {\r\n                                              console.error('移除学生失败:', error);\r\n                                              message.error('移除学生失败');\r\n                                            }\r\n                                          }\r\n                                        });\r\n                                      }\r\n                                    }}\r\n                                  >\r\n                                    移出班级\r\n                                  </Button>\r\n                                </Space>\r\n                              ),\r\n                            },\r\n                          ]}\r\n                          dataSource={currentClassStudents}\r\n                          rowKey=\"id\"\r\n                          pagination={false}\r\n                          locale={{ emptyText: '班级暂无学生' }}\r\n                        />\r\n                      </Modal>\r\n\r\n                      {/* 导入学生名册模态框 */}\r\n                      <Modal\r\n                        title=\"导入学生名册\"\r\n                        open={importStudentModalVisible}\r\n                        onOk={handleImportStudents}\r\n                        onCancel={() => {\r\n                          setImportStudentModalVisible(false);\r\n                          setImportFileList([]);\r\n                          importStudentForm.resetFields();\r\n                        }}\r\n                        confirmLoading={loading}\r\n                        width={600}\r\n                      >\r\n                        <Form\r\n                          form={importStudentForm}\r\n                          layout=\"vertical\"\r\n                        >\r\n                          <Form.Item\r\n                            name=\"class_id\"\r\n                            label=\"选择班级\"\r\n                            rules={[{ required: true, message: '请选择班级' }]}\r\n                          >\r\n                            <Select placeholder=\"请选择班级\">\r\n                              {classes.map(cls => (\r\n                                <Option key={cls.id} value={cls.id}>\r\n                                  {cls.name} ({cls.grade || '未设置年级'})\r\n                                </Option>\r\n                              ))}\r\n                            </Select>\r\n                          </Form.Item>\r\n                          <Form.Item\r\n                            name=\"file\"\r\n                            label=\"学生名册文件\"\r\n                            rules={[{ required: true, message: '请上传学生名册文件' }]}\r\n                            extra=\"支持的文件格式：.xlsx, .xls, .csv。文件第一行必须是表头，包含：姓名,用户名,密码,手机号等字段\"\r\n                          >\r\n                            <Upload\r\n                              accept=\".xlsx,.xls,.csv\"\r\n                              fileList={importFileList}\r\n                              beforeUpload={(file) => {\r\n                                setImportFileList([file]);\r\n                                return false;\r\n                              }}\r\n                              onRemove={() => {\r\n                                setImportFileList([]);\r\n                              }}\r\n                              maxCount={1}\r\n                            >\r\n                              <Button icon={<UploadOutlined />}>选择文件</Button>\r\n                            </Upload>\r\n                          </Form.Item>\r\n                        </Form>\r\n                        <div style={{ marginTop: 16 }}>\r\n                          <h4>导入说明：</h4>\r\n                          <ol>\r\n                            <li>请先下载导入模板，按照模板格式填写学生信息</li>\r\n                            <li>姓名、用户名、密码为必填字段，其他字段可选</li>\r\n                            <li>用户名必须唯一，如果存在重复将导致导入失败</li>\r\n                            <li>导入成功后，学生将自动添加到选择的班级中</li>\r\n                          </ol>\r\n                        </div>\r\n                      </Modal>\r\n                    </TabPane>\r\n                    \r\n                    {/* 角色管理 */}\r\n                    <TabPane\r\n                      tab={\r\n                        <span>\r\n                          <UserOutlined />\r\n                          角色管理\r\n                        </span>\r\n                      }\r\n                      key=\"3\"\r\n                    >\r\n                      <div style={{ marginBottom: 16 }}>\r\n                        <Button\r\n                          type=\"primary\"\r\n                          icon={<PlusOutlined />}\r\n                          onClick={showAddUserModal}\r\n                        >\r\n                          添加用户\r\n                        </Button>\r\n                      </div>\r\n                      <Table\r\n                        columns={[\r\n                          {\r\n                            title: '用户名',\r\n                            dataIndex: 'username',\r\n                            key: 'username',\r\n                          },\r\n                          {\r\n                            title: '姓名',\r\n                            dataIndex: 'full_name',\r\n                            key: 'full_name',\r\n                          },\r\n                          {\r\n                            title: '身份',\r\n                            key: 'identity',\r\n                            render: (_, record) => getUserRoleTags(record)\r\n                          },\r\n                          {\r\n                            title: '主要角色',\r\n                            dataIndex: 'primary_role',\r\n                            key: 'primary_role',\r\n                            render: (_, record) => (\r\n                              record.primary_role ? <Tag color=\"blue\">{record.primary_role.name}</Tag> : <Tag color=\"default\">未分配</Tag>\r\n                            )\r\n                          },\r\n                          {\r\n                            title: '所有角色',\r\n                            dataIndex: 'roles',\r\n                            key: 'roles',\r\n                            render: (roles) => (\r\n                              <>\r\n                                {roles && roles.map(role => (\r\n                                  <Tag color=\"green\" key={role.id}>\r\n                                    {role.name}\r\n                                  </Tag>\r\n                                ))}\r\n                              </>\r\n                            )\r\n                          },\r\n                          {\r\n                            title: '操作',\r\n                            key: 'action',\r\n                            render: (_, record) => (\r\n                              <Space size=\"middle\">\r\n                                <Button \r\n                                  type=\"primary\" \r\n                                  icon={<UserOutlined />} \r\n                                  size=\"small\"\r\n                                  onClick={() => showRoleModal(record)}\r\n                                >\r\n                                  分配角色\r\n                                </Button>\r\n                                {record.roles && record.roles.length > 0 && record.roles.map(role => (\r\n                                  <Button \r\n                                    key={`${record.id}_${role.id}`}\r\n                                    danger \r\n                                    size=\"small\"\r\n                                    onClick={() => handleRevokeRole(record.id, role.id)}\r\n                                  >\r\n                                    撤销 {role.name}\r\n                                  </Button>\r\n                                ))}\r\n                              </Space>\r\n                            ),\r\n                          },\r\n                        ]}\r\n                        dataSource={users}\r\n                        rowKey={(record) => `user_${record.id}`}\r\n                        loading={loading}\r\n                      />\r\n                      \r\n                      {/* 角色分配模态框 */}\r\n                      <Modal\r\n                        title=\"分配角色\"\r\n                        open={roleModalVisible}\r\n                        onOk={handleRoleSubmit}\r\n                        onCancel={handleRoleCancel}\r\n                        confirmLoading={loading}\r\n                      >\r\n                        {currentUser && (\r\n                          <Form\r\n                            form={roleForm}\r\n                            layout=\"vertical\"\r\n                          >\r\n                            <p>为用户 <strong>{currentUser.full_name || currentUser.username}</strong> 分配角色：</p>\r\n                            <Form.Item\r\n                              name=\"role_id\"\r\n                              label=\"角色\"\r\n                              rules={[{ required: true, message: '请选择角色' }]}\r\n                            >\r\n                              <Select placeholder=\"请选择角色\">\r\n                                {/* 显示从后端获取的角色 */}\r\n                                {roles.map(role => (\r\n                                  <Option key={role.id} value={role.id}>\r\n                                    {role.name} ({role.description || role.code})\r\n                                  </Option>\r\n                                ))}\r\n                                {/* 如果后端角色为空，使用预定义的角色 */}\r\n                                {(!roles || roles.length === 0) && Object.values(USER_ROLES).map(role => (\r\n                                  <Option key={role.value} value={role.value}>\r\n                                    {role.label}\r\n                                  </Option>\r\n                                ))}\r\n                              </Select>\r\n                            </Form.Item>\r\n                          </Form>\r\n                        )}\r\n                      </Modal>\r\n\r\n                      {/* 添加用户模态框 */}\r\n                      <Modal\r\n                        title=\"添加用户\"\r\n                        open={newUserModalVisible}\r\n                        onOk={handleAddUser}\r\n                        onCancel={() => setNewUserModalVisible(false)}\r\n                        confirmLoading={loading}\r\n                        width={600}\r\n                      >\r\n                        <Form\r\n                          form={userForm}\r\n                          layout=\"vertical\"\r\n                          initialValues={{ activeTab: '1' }}\r\n                        >\r\n                          <Form.Item name=\"activeTab\" hidden>\r\n                            <Input />\r\n                          </Form.Item>\r\n                          <Tabs \r\n                            defaultActiveKey=\"1\" \r\n                            onChange={(key) => userForm.setFieldsValue({ activeTab: key })}\r\n                          >\r\n                            <TabPane tab=\"选择现有用户\" key=\"1\">\r\n                              <Form.Item\r\n                                name=\"user_id\"\r\n                                label=\"选择用户\"\r\n                                rules={[{ required: true, message: '请选择用户' }]}\r\n                              >\r\n                                <Select \r\n                                  placeholder=\"请选择用户\"\r\n                                  showSearch\r\n                                  filterOption={(input, option) =>\r\n                                    option.children && typeof option.children === 'string' ? \r\n                                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                                  }\r\n                                >\r\n                                  {allUsers\r\n                                    .filter(user => !users.some(existingUser => existingUser.id === user.id))\r\n                                    .map(user => (\r\n                                      <Option key={user.id} value={user.id}>\r\n                                        {user.full_name || user.username} ({user.username})\r\n                                      </Option>\r\n                                    ))\r\n                                  }\r\n                                </Select>\r\n                              </Form.Item>\r\n                            </TabPane>\r\n                            <TabPane tab=\"创建新用户\" key=\"2\">\r\n                              <Form.Item\r\n                                name=\"username\"\r\n                                label=\"用户名\"\r\n                                rules={[{ required: true, message: '请输入用户名' }]}\r\n                              >\r\n                                <Input placeholder=\"请输入用户名\" />\r\n                              </Form.Item>\r\n                              <Form.Item\r\n                                name=\"password\"\r\n                                label=\"密码\"\r\n                                rules={[{ required: true, message: '请输入密码' }]}\r\n                              >\r\n                                <Input.Password placeholder=\"请输入密码\" />\r\n                              </Form.Item>\r\n                              <Form.Item\r\n                                name=\"full_name\"\r\n                                label=\"姓名\"\r\n                                rules={[{ required: true, message: '请输入姓名' }]}\r\n                              >\r\n                                <Input placeholder=\"请输入姓名\" />\r\n                              </Form.Item>\r\n                              <Form.Item\r\n                                name=\"email\"\r\n                                label=\"邮箱\"\r\n                              >\r\n                                <Input placeholder=\"请输入邮箱\" />\r\n                              </Form.Item>\r\n                              <Form.Item\r\n                                name=\"phone\"\r\n                                label=\"电话\"\r\n                              >\r\n                                <Input placeholder=\"请输入电话\" />\r\n                              </Form.Item>\r\n                              <Form.Item name=\"is_teacher\" valuePropName=\"checked\">\r\n                                <Checkbox>是教师</Checkbox>\r\n                              </Form.Item>\r\n                              <Form.Item name=\"is_admin\" valuePropName=\"checked\">\r\n                                <Checkbox>是管理员</Checkbox>\r\n                              </Form.Item>\r\n                            </TabPane>\r\n                          </Tabs>\r\n                          <Divider />\r\n                          <Form.Item\r\n                            name=\"role_id\"\r\n                            label=\"分配角色\"\r\n                            rules={[{ required: true, message: '请选择角色' }]}\r\n                          >\r\n                            <Select placeholder=\"请选择角色\">\r\n                              {/* 显示从后端获取的角色 */}\r\n                              {roles.map(role => (\r\n                                <Option key={role.id} value={role.id}>\r\n                                  {role.name} ({role.description || role.code})\r\n                                </Option>\r\n                              ))}\r\n                              {/* 如果后端角色为空，使用预定义的角色 */}\r\n                              {(!roles || roles.length === 0) && Object.values(USER_ROLES).map(role => (\r\n                                <Option key={role.value} value={role.value}>\r\n                                  {role.label}\r\n                                </Option>\r\n                              ))}\r\n                            </Select>\r\n                          </Form.Item>\r\n                        </Form>\r\n                      </Modal>\r\n                    </TabPane>\r\n                  </Tabs>\r\n                </Spin>\r\n              </Card>\r\n            </div>\r\n          ) : (\r\n            <div className=\"no-school-selected\">\r\n              <h2>请先选择一个学校</h2>\r\n              <Button \r\n                type=\"primary\" \r\n                onClick={() => setActiveKey(\"1\")}\r\n              >\r\n                返回学校列表\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </TabPane>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuperSchoolManagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AAC5I,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,mBAAmB;AAC7K,SAASC,UAAU,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,UAAU,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,cAAc;AAC9P,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,eAAe,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAM;EAAEC;AAAQ,CAAC,GAAGvC,IAAI;AACxB,MAAM;EAAEwC;AAAO,CAAC,GAAGzC,MAAM;;AAEzB;AACA,MAAM0C,UAAU,GAAG;EACjB;EACAC,WAAW,EAAE;IAAEC,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAQ,CAAC;EACrDC,YAAY,EAAE;IAAEF,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAQ,CAAC;EACvDE,SAAS,EAAE;IAAEH,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAK,CAAC;EAC9CG,cAAc,EAAE;IAAEJ,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAM,CAAC;EACzDI,iBAAiB,EAAE;IAAEL,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAQ,CAAC;EACjEK,cAAc,EAAE;IAAEN,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC;EAC1DM,cAAc,EAAE;IAAEP,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC;EAC1DO,cAAc,EAAE;IAAER,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAO,CAAC;EAC1DQ,aAAa,EAAE;IAAET,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAM,CAAC;EACvDS,OAAO,EAAE;IAAEV,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAK,CAAC;EAC1CU,OAAO,EAAE;IAAEX,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAK,CAAC;EAC1CW,MAAM,EAAE;IAAEZ,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAK;AACzC,CAAC;AAED,MAAMY,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGzB,OAAO,CAAC,CAAC;EAC1B,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuE,OAAO,EAAEC,UAAU,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC+E,IAAI,CAAC,GAAGxE,IAAI,CAACyE,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmF,MAAM,EAAEC,SAAS,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqF,SAAS,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACuF,UAAU,CAAC,GAAGhF,IAAI,CAACyE,OAAO,CAAC,CAAC;EACnC,MAAM,CAACQ,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0F,WAAW,EAAEC,cAAc,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgG,KAAK,EAAEC,QAAQ,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkG,KAAK,EAAEC,QAAQ,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoG,QAAQ,EAAEC,WAAW,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsG,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAC,GAAG,CAAC;EAC3D,MAAM,CAACwG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7G,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8G,YAAY,EAAEC,eAAe,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgH,WAAW,EAAEC,cAAc,CAAC,GAAGjH,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkH,SAAS,CAAC,GAAG3G,IAAI,CAACyE,OAAO,CAAC,CAAC;EAClC,MAAM,CAACmC,QAAQ,CAAC,GAAG5G,IAAI,CAACyE,OAAO,CAAC,CAAC;EACjC,MAAM,CAACoC,QAAQ,CAAC,GAAG7G,IAAI,CAACyE,OAAO,CAAC,CAAC;EACjC,MAAM,CAACqC,gBAAgB,CAAC,GAAG9G,IAAI,CAACyE,OAAO,CAAC,CAAC;EACzC,MAAM,CAACsC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGvH,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACwH,eAAe,EAAEC,kBAAkB,CAAC,GAAGzH,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0H,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EACjF,MAAM,CAAC4H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7H,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC8H,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgI,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGjI,QAAQ,CAAC,KAAK,CAAC;EACjF,MAAM,CAACkI,iBAAiB,CAAC,GAAG3H,IAAI,CAACyE,OAAO,CAAC,CAAC;EAC1C,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqI,gBAAgB,CAAC,GAAG9H,IAAI,CAACyE,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE3C;EACA/E,SAAS,CAAC,MAAM;IACd,IAAImE,IAAI,IAAIA,IAAI,CAACkE,QAAQ,EAAE;MACzB;MACAC,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACxB;QACAC,YAAY,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMqE,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BjE,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkE,IAAI,GAAG,MAAM9G,UAAU,CAAC,CAAC;MAC/B+G,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,IAAI,CAAC;;MAE9B;MACA,IAAIG,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,GAAGA,IAAI,GAAG,EAAE;;MAEjD;MACA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,IAAIA,IAAI,IAAII,KAAK,CAACC,OAAO,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE;QAC7DH,WAAW,GAAGH,IAAI,CAACM,KAAK;MAC1B;;MAEA;MACA,MAAMC,gBAAgB,GAAGJ,WAAW,CAACK,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;QAC1D;QACA,IAAIC,QAAQ,GAAGF,MAAM,CAACG,EAAE;QACxB,IAAID,QAAQ,KAAKE,SAAS,IAAIF,QAAQ,KAAK,IAAI,EAAE;UAC/CV,OAAO,CAACa,IAAI,CAAC,oBAAoB,EAAEJ,KAAK,GAAG,CAAC,CAAC;UAC7CC,QAAQ,GAAGD,KAAK,GAAG,CAAC;QACtB;;QAEA;QACA,MAAMK,SAAS,GAAGC,QAAQ,CAACL,QAAQ,EAAE,EAAE,CAAC;QACxC,IAAI,CAACM,KAAK,CAACF,SAAS,CAAC,EAAE;UACrBJ,QAAQ,GAAGI,SAAS;QACtB;QAEA,OAAO;UACL,GAAGN,MAAM;UACTG,EAAE,EAAED,QAAQ;UAAE;UACdO,QAAQ,EAAET,MAAM,CAACS,QAAQ,IAAI,EAAE;UAC/BC,IAAI,EAAEV,MAAM,CAACU,IAAI,IAAI,EAAE;UACvBC,QAAQ,EAAEX,MAAM,CAACW,QAAQ,IAAI,EAAE;UAC/BC,WAAW,EAAEZ,MAAM,CAACY,WAAW,IAAI;QACrC,CAAC;MACH,CAAC,CAAC;MAEFzF,UAAU,CAAC2E,gBAAgB,CAAC;MAC5BxD,kBAAkB,CAACwD,gBAAgB,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,MAAMF,IAAI,GAAG,MAAM1G,UAAU,CAAC,CAAC;MAC/B,IAAI0G,IAAI,IAAIA,IAAI,CAACzD,SAAS,EAAE;QAC1B0D,OAAO,CAACC,GAAG,CAAC,QAAQF,IAAI,CAACzD,SAAS,CAACgF,MAAM,QAAQ,CAAC;QAClD/E,YAAY,CAACwD,IAAI,CAACzD,SAAS,CAAC;MAC9B,CAAC,MAAM;QACL0D,OAAO,CAACqB,KAAK,CAAC,qBAAqB,EAAEtB,IAAI,CAAC;QAC1C;QACA,IAAI;UACF;UACA,MAAMG,WAAW,GAAG,MAAMjH,UAAU,CAAC,CAAC;UACtC,IAAIkH,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;YAC9B,MAAMqB,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACtB,WAAW,CAACK,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACS,QAAQ,CAAC,CAACQ,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;YAChG,IAAIH,eAAe,CAACD,MAAM,GAAG,CAAC,EAAE;cAC9BtB,OAAO,CAACC,GAAG,CAAC,aAAasB,eAAe,CAACD,MAAM,MAAM,CAAC;cACtD/E,YAAY,CAACgF,eAAe,CAAC;YAC/B;UACF;QACF,CAAC,CAAC,OAAOI,WAAW,EAAE;UACpB3B,OAAO,CAACqB,KAAK,CAAC,eAAe,EAAEM,WAAW,CAAC;QAC7C;MACF;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMO,oBAAoB,GAAG,MAAOlH,KAAK,IAAK;IAC5C0B,IAAI,CAACyF,cAAc,CAAC;MAAEX,IAAI,EAAEN,SAAS;MAAEO,QAAQ,EAAEP;IAAU,CAAC,CAAC;IAC7DnE,SAAS,CAAC,EAAE,CAAC;IACbE,YAAY,CAAC,EAAE,CAAC;IAEhB,IAAI,CAACjC,KAAK,EAAE;IAEZ,IAAI;MACFsF,OAAO,CAACC,GAAG,CAAC,QAAQvF,KAAK,WAAW,CAAC;MACrC,MAAMqF,IAAI,GAAG,MAAM1G,UAAU,CAAC;QAAE4H,QAAQ,EAAEvG;MAAM,CAAC,CAAC;MAClD,IAAIqF,IAAI,IAAIA,IAAI,CAACvD,MAAM,IAAIuD,IAAI,CAACvD,MAAM,CAAC8E,MAAM,GAAG,CAAC,EAAE;QACjDtB,OAAO,CAACC,GAAG,CAAC,QAAQF,IAAI,CAACvD,MAAM,CAAC8E,MAAM,QAAQ,CAAC;QAC/C7E,SAAS,CAACsD,IAAI,CAACvD,MAAM,CAAC;MACxB,CAAC,MAAM;QACLwD,OAAO,CAACqB,KAAK,CAAC,kBAAkB,EAAEtB,IAAI,CAAC;QACvC;QACA,IAAI;UACF;UACA,MAAMG,WAAW,GAAG,MAAMjH,UAAU,CAAC,CAAC;UACtC,IAAIkH,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;YAC9B,MAAM4B,YAAY,GAAG,CAAC,GAAG,IAAIN,GAAG,CAC9BtB,WAAW,CACRuB,MAAM,CAACjB,MAAM,IAAIA,MAAM,CAACS,QAAQ,KAAKvG,KAAK,CAAC,CAC3C6F,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACU,IAAI,CAAC,CAC1BO,MAAM,CAACC,OAAO,CACnB,CAAC,CAAC;YACF,IAAII,YAAY,CAACR,MAAM,GAAG,CAAC,EAAE;cAC3BtB,OAAO,CAACC,GAAG,CAAC,aAAa6B,YAAY,CAACR,MAAM,MAAM,CAAC;cACnD7E,SAAS,CAACqF,YAAY,CAAC;YACzB;UACF;QACF,CAAC,CAAC,OAAOH,WAAW,EAAE;UACpB3B,OAAO,CAACqB,KAAK,CAAC,eAAe,EAAEM,WAAW,CAAC;QAC7C;MACF;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMU,gBAAgB,GAAG,MAAOrH,KAAK,IAAK;IACxC0B,IAAI,CAACyF,cAAc,CAAC;MAAEV,QAAQ,EAAEP;IAAU,CAAC,CAAC;IAC5CjE,YAAY,CAAC,EAAE,CAAC;IAEhB,IAAI,CAACjC,KAAK,EAAE;IAEZ,MAAMuG,QAAQ,GAAG7E,IAAI,CAAC4F,aAAa,CAAC,UAAU,CAAC;IAC/C,IAAI,CAACf,QAAQ,EAAE;IAEf,IAAI;MACFjB,OAAO,CAACC,GAAG,CAAC,QAAQgB,QAAQ,OAAOvG,KAAK,WAAW,CAAC;MACpD,MAAMqF,IAAI,GAAG,MAAM1G,UAAU,CAAC;QAC5B4H,QAAQ,EAAEA,QAAQ;QAClBC,IAAI,EAAExG;MACR,CAAC,CAAC;MACF,IAAIqF,IAAI,IAAIA,IAAI,CAACrD,SAAS,IAAIqD,IAAI,CAACrD,SAAS,CAAC4E,MAAM,GAAG,CAAC,EAAE;QACvDtB,OAAO,CAACC,GAAG,CAAC,QAAQF,IAAI,CAACrD,SAAS,CAAC4E,MAAM,QAAQ,CAAC;QAClD3E,YAAY,CAACoD,IAAI,CAACrD,SAAS,CAAC;MAC9B,CAAC,MAAM;QACLsD,OAAO,CAACqB,KAAK,CAAC,kBAAkB,EAAEtB,IAAI,CAAC;QACvC;QACA,IAAI;UACF;UACA,MAAMG,WAAW,GAAG,MAAMjH,UAAU,CAAC,CAAC;UACtC,IAAIkH,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;YAC9B,MAAM+B,eAAe,GAAG,CAAC,GAAG,IAAIT,GAAG,CACjCtB,WAAW,CACRuB,MAAM,CAACjB,MAAM,IAAIA,MAAM,CAACS,QAAQ,KAAKA,QAAQ,IAAIT,MAAM,CAACU,IAAI,KAAKxG,KAAK,CAAC,CACvE6F,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACW,QAAQ,CAAC,CAC9BM,MAAM,CAACC,OAAO,CACnB,CAAC,CAAC;YACF,IAAIO,eAAe,CAACX,MAAM,GAAG,CAAC,EAAE;cAC9BtB,OAAO,CAACC,GAAG,CAAC,aAAagC,eAAe,CAACX,MAAM,MAAM,CAAC;cACtD3E,YAAY,CAACsF,eAAe,CAAC;YAC/B;UACF;QACF,CAAC,CAAC,OAAON,WAAW,EAAE;UACpB3B,OAAO,CAACqB,KAAK,CAAC,eAAe,EAAEM,WAAW,CAAC;QAC7C;MACF;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMa,SAAS,GAAGA,CAAC1B,MAAM,GAAG,IAAI,KAAK;IACnCvE,gBAAgB,CAACuE,MAAM,CAAC;IACxBpE,IAAI,CAAC+F,WAAW,CAAC,CAAC;IAClB,IAAI3B,MAAM,EAAE;MACVpE,IAAI,CAACyF,cAAc,CAAC;QAClBO,IAAI,EAAE5B,MAAM,CAAC4B,IAAI;QACjBnB,QAAQ,EAAET,MAAM,CAACS,QAAQ;QACzBC,IAAI,EAAEV,MAAM,CAACU,IAAI;QACjBC,QAAQ,EAAEX,MAAM,CAACW;MACnB,CAAC,CAAC;;MAEF;MACA,IAAIX,MAAM,CAACS,QAAQ,EAAE;QACnBW,oBAAoB,CAACpB,MAAM,CAACS,QAAQ,CAAC;MACvC;;MAEA;MACA,IAAIT,MAAM,CAACS,QAAQ,IAAIT,MAAM,CAACU,IAAI,EAAE;QAClCa,gBAAgB,CAACvB,MAAM,CAACU,IAAI,CAAC;MAC/B;IACF;IACAnF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMlG,IAAI,CAACmG,cAAc,CAAC,CAAC;MAC1C1G,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIG,aAAa,EAAE;QACjB;QACAgE,OAAO,CAACC,GAAG,CAAC,cAAcjE,aAAa,CAAC2E,EAAE,MAAM,EAAE2B,MAAM,CAAC;QAEzD,IAAI;UACF;UACA,MAAM;YAAEE;UAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;UACzD,MAAMA,gBAAgB,CAACxG,aAAa,CAAC2E,EAAE,EAAE2B,MAAM,CAAC;UAChD5K,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;QAC3B,CAAC,CAAC,OAAOC,WAAW,EAAE;UACpB1C,OAAO,CAACqB,KAAK,CAAC,gBAAgB,EAAEqB,WAAW,CAAC;;UAE5C;UACA,MAAMvJ,YAAY,CAAC6C,aAAa,CAAC2E,EAAE,EAAE2B,MAAM,CAAC;UAC5C5K,OAAO,CAAC+K,OAAO,CAAC,gBAAgB,CAAC;QACnC;MACF,CAAC,MAAM;QACL;QACAzC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqC,MAAM,CAAC;QAC/B,MAAMpJ,YAAY,CAACoJ,MAAM,CAAC;QAC1B5K,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA1G,eAAe,CAAC,KAAK,CAAC;MACtB+D,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,aAAaA,KAAK,CAAC3J,OAAO,IAAI,MAAM,EAAE,CAAC;IACvD,CAAC,SAAS;MACRmE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8G,YAAY,GAAIhC,EAAE,IAAK;IAC3BhJ,KAAK,CAACiL,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,oCAAoC;MAC7CC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChBpH,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAMzC,YAAY,CAACuH,EAAE,CAAC;UACtBjJ,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;;UAEzB;UACA,IAAIvG,cAAc,IAAIA,cAAc,CAACyE,EAAE,KAAKA,EAAE,EAAE;YAC9CxE,iBAAiB,CAAC,IAAI,CAAC;UACzB;UAEA2D,YAAY,CAAC,CAAC;QAChB,CAAC,CAAC,OAAOuB,KAAK,EAAE;UACd3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;UACvBrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC,CAAC,SAAS;UACRxF,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqH,iBAAiB,GAAG,MAAOxC,QAAQ,IAAK;IAC5C7E,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFmE,OAAO,CAACC,GAAG,CAAC,cAAcS,QAAQ,EAAE,CAAC;MAErC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAC/BV,OAAO,CAACqB,KAAK,CAAC,qBAAqB,CAAC;QACpC3J,OAAO,CAAC2J,KAAK,CAAC,YAAY,CAAC;QAC3BxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMsH,eAAe,GAAGpC,QAAQ,CAACL,QAAQ,EAAE,EAAE,CAAC;MAC9C,IAAIM,KAAK,CAACmC,eAAe,CAAC,EAAE;QAC1BnD,OAAO,CAACqB,KAAK,CAAC,UAAU,EAAEX,QAAQ,CAAC;QACnChJ,OAAO,CAAC2J,KAAK,CAAC,SAAS,CAAC;QACxBxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMkE,IAAI,GAAG,MAAMzG,eAAe,CAAC6J,eAAe,CAAC;MACnDnD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI,CAAC;MAE7B,IAAI,CAACA,IAAI,EAAE;QACTC,OAAO,CAACqB,KAAK,CAAC,mBAAmB,CAAC;QAClC3J,OAAO,CAAC2J,KAAK,CAAC,mBAAmB,CAAC;QAClCxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAqB,eAAe,CAAC6C,IAAI,CAAC;;MAErB;MACAqD,YAAY,CAACD,eAAe,CAAC;MAC7B,IAAIxF,eAAe,KAAK,GAAG,EAAE;QAC3B0F,UAAU,CAAC,CAAC;QACZC,UAAU,CAACH,eAAe,CAAC;QAC3BI,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,aAAaA,KAAK,CAAC3J,OAAO,IAAI,MAAM,EAAE,CAAC;IACvD,CAAC,SAAS;MACRmE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuH,YAAY,GAAG,MAAO1C,QAAQ,IAAK;IACvC7E,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFmE,OAAO,CAACC,GAAG,CAAC,gBAAgBS,QAAQ,QAAQ,OAAOA,QAAQ,EAAE,CAAC;MAE9D,IAAI,CAACA,QAAQ,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAC/BV,OAAO,CAACqB,KAAK,CAAC,kBAAkB,CAAC;QACjCxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIsH,eAAe;MACnB,IAAI;QACFA,eAAe,GAAGpC,QAAQ,CAACL,QAAQ,EAAE,EAAE,CAAC;QACxC,IAAIM,KAAK,CAACmC,eAAe,CAAC,EAAE;UAC1BnD,OAAO,CAACa,IAAI,CAAC,iBAAiB,EAAEH,QAAQ,CAAC;UACzCyC,eAAe,GAAGzC,QAAQ;QAC5B;MACF,CAAC,CAAC,OAAO8C,CAAC,EAAE;QACVxD,OAAO,CAACa,IAAI,CAAC,iBAAiB,EAAEH,QAAQ,CAAC;QACzCyC,eAAe,GAAGzC,QAAQ;MAC5B;MAEAV,OAAO,CAACC,GAAG,CAAC,qBAAqBkD,eAAe,EAAE,CAAC;MACnD,MAAMpD,IAAI,GAAG,MAAMxG,kBAAkB,CAAC4J,eAAe,CAAC;MACtDnD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI,CAAC;;MAE7B;MACA,IAAI0D,WAAW,GAAG,EAAE;MACpB,IAAItD,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;QACvB0D,WAAW,GAAG1D,IAAI;QAClBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwD,WAAW,CAACnC,MAAM,CAAC;MAClD,CAAC,MAAM,IAAIvB,IAAI,IAAII,KAAK,CAACC,OAAO,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE;QAC5CoD,WAAW,GAAG1D,IAAI,CAACM,KAAK;QACxBL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwD,WAAW,CAACnC,MAAM,CAAC;MACxD,CAAC,MAAM,IAAIvB,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC3C0D,WAAW,GAAG,CAAC1D,IAAI,CAAC,CAAC,CAAC;QACtBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC/B,CAAC,MAAM;QACLD,OAAO,CAACa,IAAI,CAAC,YAAY,EAAEd,IAAI,CAAC;MAClC;;MAEA;MACA0D,WAAW,GAAGA,WAAW,CAAClD,GAAG,CAAC,CAACmD,GAAG,EAAEjD,KAAK,KAAK;QAC5C,IAAI,CAACiD,GAAG,CAAC/C,EAAE,IAAI+C,GAAG,CAAC/C,EAAE,KAAK,CAAC,EAAE;UAC3BX,OAAO,CAACa,IAAI,CAAC,WAAW,EAAE6C,GAAG,CAAC;UAC9BA,GAAG,CAAC/C,EAAE,GAAG,cAAcgD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAInD,KAAK,EAAE;QAC9C;QACA,OAAOiD,GAAG;MACZ,CAAC,CAAC;MAEF1D,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEwD,WAAW,CAAC;MACvCrG,UAAU,CAACqG,WAAW,CAAC;IACzB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;MACzB;MACAjE,UAAU,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BxH,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkE,IAAI,GAAG,MAAMpG,cAAc,CAAC,CAAC;MACnC2D,QAAQ,CAACyC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyH,UAAU,GAAG,MAAO5C,QAAQ,IAAK;IACrC7E,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkE,IAAI,GAAG,MAAMjG,cAAc,CAAC4G,QAAQ,CAAC;MAC3ClD,QAAQ,CAACuC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0H,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMxD,IAAI,GAAG,MAAMhG,QAAQ,CAAC,CAAC;MAC7B,IAAIgG,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAIA,IAAI,EAAE;QACvDrC,WAAW,CAACqC,IAAI,CAACM,KAAK,IAAI,EAAE,CAAC;MAC/B,CAAC,MAAM;QACL3C,WAAW,CAACqC,IAAI,IAAI,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMwC,qBAAqB,GAAIC,GAAG,IAAK;IACrC9D,OAAO,CAACC,GAAG,CAAC,aAAa6D,GAAG,YAAYnG,eAAe,EAAE,CAAC;IAC1DC,kBAAkB,CAACkG,GAAG,CAAC;IAEvB,IAAI5H,cAAc,IAAIA,cAAc,CAACyE,EAAE,EAAE;MACvC;MACA,MAAMD,QAAQ,GAAGxE,cAAc,CAACyE,EAAE;MAClCX,OAAO,CAACC,GAAG,CAAC,aAAa6D,GAAG,UAAUpD,QAAQ,EAAE,CAAC;MAEjD,IAAIoD,GAAG,KAAK,GAAG,EAAE;QACf;QACA9D,OAAO,CAACC,GAAG,CAAC,2BAA2BS,QAAQ,EAAE,CAAC;QAClD0C,YAAY,CAAC1C,QAAQ,CAAC;MACxB,CAAC,MAAM,IAAIoD,GAAG,KAAK,GAAG,EAAE;QACtB;QACAT,UAAU,CAAC,CAAC;QACZC,UAAU,CAAC5C,QAAQ,CAAC;QACpB6C,aAAa,CAAC,CAAC;MACjB;IACF;EACF,CAAC;;EAED;EACA,MAAMQ,wBAAwB,GAAG,MAAOzB,MAAM,IAAK;IACjD,IAAI,CAACrF,YAAY,EAAE;MACjBvF,OAAO,CAAC2J,KAAK,CAAC,cAAc,CAAC;MAC7B;IACF;IAEArB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqC,MAAM,CAAC;;IAEhC;IACA,MAAM5B,QAAQ,GAAGzD,YAAY,CAAC0D,EAAE;IAChC,IAAI,CAACD,QAAQ,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAC/BhJ,OAAO,CAAC2J,KAAK,CAAC,cAAc,CAAC;MAC7B;IACF;IAEAxF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;MACA,MAAM;QAAE2G;MAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;;MAEzD;MACAxC,OAAO,CAACC,GAAG,CAAC,gBAAgBS,QAAQ,MAAM,EAAE4B,MAAM,CAAC;;MAEnD;MACA,MAAME,gBAAgB,CAAC9B,QAAQ,EAAE4B,MAAM,CAAC;MAExC5K,OAAO,CAAC+K,OAAO,CAAC,UAAU,CAAC;;MAE3B;MACAS,iBAAiB,CAACxC,QAAQ,CAAC;;MAE3B;MACAZ,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,aAAaA,KAAK,CAAC3J,OAAO,IAAI,MAAM,EAAE,CAAC;IACvD,CAAC,SAAS;MACRmE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmI,cAAc,GAAGA,CAACC,MAAM,GAAG,IAAI,KAAK;IACxC7F,eAAe,CAAC6F,MAAM,CAAC;IACvB1F,SAAS,CAAC4D,WAAW,CAAC,CAAC;IACvB,IAAI8B,MAAM,EAAE;MACV1F,SAAS,CAACsD,cAAc,CAAC;QACvBO,IAAI,EAAE6B,MAAM,CAAC7B,IAAI;QACjB8B,KAAK,EAAED,MAAM,CAACC;MAChB,CAAC,CAAC;IACJ;IACApG,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrG,oBAAoB,CAAC,KAAK,CAAC;IAC3BM,eAAe,CAAC,IAAI,CAAC;IACrBG,SAAS,CAAC4D,WAAW,CAAC,CAAC;EACzB,CAAC;;EAED;EACA,MAAMiC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAM9B,MAAM,GAAG,MAAM/D,SAAS,CAACgE,cAAc,CAAC,CAAC;MAC/C1G,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAACK,cAAc,IAAI,CAACA,cAAc,CAACyE,EAAE,EAAE;QACzCjJ,OAAO,CAAC2J,KAAK,CAAC,cAAc,CAAC;QAC7BxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAIsC,YAAY,EAAE;QAChB;QACA,MAAM1E,eAAe,CAAC0E,YAAY,CAACwC,EAAE,EAAE;UACrC,GAAG2B,MAAM;UACT+B,SAAS,EAAEnI,cAAc,CAACyE;QAC5B,CAAC,CAAC;QACFjJ,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMjJ,oBAAoB,CAAC;UACzB,GAAG8I,MAAM;UACT+B,SAAS,EAAEnI,cAAc,CAACyE;QAC5B,CAAC,CAAC;QACFjJ,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA3E,oBAAoB,CAAC,KAAK,CAAC;MAC3BsF,YAAY,CAAClH,cAAc,CAACyE,EAAE,CAAC;IACjC,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyI,iBAAiB,GAAG,MAAOC,OAAO,IAAK;IAC3C5M,KAAK,CAACiL,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChBpH,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAMnC,eAAe,CAAC6K,OAAO,CAAC;UAC9B7M,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;UAEzB,IAAIvG,cAAc,IAAIA,cAAc,CAACyE,EAAE,EAAE;YACvCyC,YAAY,CAAClH,cAAc,CAACyE,EAAE,CAAC;UACjC;QACF,CAAC,CAAC,OAAOU,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;QACzB,CAAC,SAAS;UACRxF,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2I,aAAa,GAAIP,MAAM,IAAK;IAChC3F,cAAc,CAAC2F,MAAM,CAAC;IACtBzF,QAAQ,CAAC2D,WAAW,CAAC,CAAC;IACtBnE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhG,QAAQ,CAAC0D,WAAW,CAAC,CAAC;IACtB1D,QAAQ,CAACoD,cAAc,CAAC;MAAE6C,SAAS,EAAE;IAAI,CAAC,CAAC;IAC3CxG,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMyG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3G,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,cAAc,CAAC,IAAI,CAAC;IACpBE,QAAQ,CAAC2D,WAAW,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMtC,MAAM,GAAG,MAAM9D,QAAQ,CAAC+D,cAAc,CAAC,CAAC;MAC9C1G,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAACK,cAAc,IAAI,CAACA,cAAc,CAACyE,EAAE,EAAE;QACzCjJ,OAAO,CAAC2J,KAAK,CAAC,cAAc,CAAC;QAC7BxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAMjC,UAAU,CAAC;QACfiL,OAAO,EAAExG,WAAW,CAACsC,EAAE;QACvBmE,OAAO,EAAExC,MAAM,CAACwC,OAAO;QACvBT,SAAS,EAAEnI,cAAc,CAACyE;MAC5B,CAAC,CAAC;MAEFjJ,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;MACzBzE,mBAAmB,CAAC,KAAK,CAAC;MAC1BsF,UAAU,CAACpH,cAAc,CAACyE,EAAE,CAAC;IAC/B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkJ,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMzC,MAAM,GAAG,MAAM7D,QAAQ,CAAC8D,cAAc,CAAC,CAAC;MAC9C1G,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAI,CAACK,cAAc,IAAI,CAACA,cAAc,CAACyE,EAAE,EAAE;QACzCjJ,OAAO,CAAC2J,KAAK,CAAC,cAAc,CAAC;QAC7BxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM6I,SAAS,GAAGjG,QAAQ,CAACuD,aAAa,CAAC,WAAW,CAAC,IAAI,GAAG;MAC5D,IAAIgD,MAAM,GAAG1C,MAAM,CAACuC,OAAO;MAE3B,IAAIH,SAAS,KAAK,GAAG,IAAI,CAACM,MAAM,EAAE;QAChC;QACA,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,EAAE;UACxCxN,OAAO,CAAC2J,KAAK,CAAC,WAAW,CAAC;UAC1BxF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,IAAI;UACF,MAAM;YAAEsJ;UAAW,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;UACnD,MAAMC,OAAO,GAAG,MAAMD,UAAU,CAAC;YAC/BF,QAAQ,EAAE3C,MAAM,CAAC2C,QAAQ;YACzBC,QAAQ,EAAE5C,MAAM,CAAC4C,QAAQ;YACzBG,SAAS,EAAE/C,MAAM,CAAC+C,SAAS;YAC3BC,KAAK,EAAEhD,MAAM,CAACgD,KAAK;YACnBC,KAAK,EAAEjD,MAAM,CAACiD,KAAK;YACnBC,UAAU,EAAElD,MAAM,CAACkD,UAAU,IAAI,KAAK;YACtC7F,QAAQ,EAAE2C,MAAM,CAAC3C,QAAQ,IAAI,KAAK;YAClC0E,SAAS,EAAEnI,cAAc,CAACyE;UAC5B,CAAC,CAAC;UAEFqE,MAAM,GAAGI,OAAO,CAACzE,EAAE;UACnBjJ,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;QAC3B,CAAC,CAAC,OAAOpB,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B3J,OAAO,CAAC2J,KAAK,CAAC,WAAWA,KAAK,CAAC3J,OAAO,IAAI,MAAM,EAAE,CAAC;UACnDmE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA,MAAMjC,UAAU,CAAC;QACfiL,OAAO,EAAEG,MAAM;QACfF,OAAO,EAAExC,MAAM,CAACwC,OAAO;QACvBT,SAAS,EAAEnI,cAAc,CAACyE;MAC5B,CAAC,CAAC;MAEFjJ,OAAO,CAAC+K,OAAO,CAAC,UAAU,CAAC;MAC3BvE,sBAAsB,CAAC,KAAK,CAAC;MAC7BO,QAAQ,CAAC0D,WAAW,CAAC,CAAC;MACtBmB,UAAU,CAACpH,cAAc,CAACyE,EAAE,CAAC;MAC7B4C,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3J,OAAO,CAAC2J,KAAK,CAAC,WAAWA,KAAK,CAAC3J,OAAO,IAAI,MAAM,EAAE,CAAC;IACrD,CAAC,SAAS;MACRmE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4J,qBAAqB,GAAGA,CAAA,KAAM;IAClC7G,2BAA2B,CAAC,IAAI,CAAC;IACjCF,gBAAgB,CAACyD,WAAW,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAMuD,uBAAuB,GAAGA,CAAA,KAAM;IACpC;IACA,MAAMC,OAAO,GAAG,CAAC,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC;IACvH,MAAMC,WAAW,GAAG,CAClB,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,sBAAsB,EAAE,GAAG,EAAE,IAAI,EAC5E,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,EAAE,EAAE,CACnE;IAED,IAAIC,UAAU,GAAGF,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;IACzCD,UAAU,IAAID,WAAW,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI;;IAE1C;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAA0B,CAAC,CAAC;;IAExE;IACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC;IAC3CN,IAAI,CAACO,KAAK,CAACC,UAAU,GAAG,QAAQ;;IAEhC;IACAP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,IAAI,CAAC;IAC/BA,IAAI,CAACW,KAAK,CAAC,CAAC;IACZV,QAAQ,CAACQ,IAAI,CAACG,WAAW,CAACZ,IAAI,CAAC;IAE/BxO,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMsE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAMzE,MAAM,GAAG,MAAM/C,iBAAiB,CAACgD,cAAc,CAAC,CAAC;MACvD,MAAMgC,OAAO,GAAGjC,MAAM,CAAC0E,QAAQ;MAE/B,IAAIxH,cAAc,CAAC8B,MAAM,KAAK,CAAC,EAAE;QAC/B5J,OAAO,CAAC2J,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF;MAEAxF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMoL,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE3H,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1CyH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE5C,OAAO,CAAC;;MAEpC;MACA,MAAM;QAAE6C,mBAAmB;QAAEC;MAAkB,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;;MAE/E;MACA,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAE/BD,MAAM,CAACE,MAAM,GAAG,MAAOhE,CAAC,IAAK;QAC3B,IAAI;UACF,MAAMV,OAAO,GAAGU,CAAC,CAACiE,MAAM,CAACC,MAAM;UAC/B,IAAIC,QAAQ,GAAG,EAAE;UACjB,IAAIC,YAAY,GAAG,CAAC;UACpB,IAAIC,SAAS,GAAG,CAAC;;UAEjB;UACA,MAAMC,KAAK,GAAGhF,OAAO,CAACiF,KAAK,CAAC,SAAS,CAAC;UACtC,MAAMpC,OAAO,GAAGmC,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;;UAEnC;UACA,MAAMC,aAAa,GAAGrC,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;UACzF,MAAMC,aAAa,GAAGzC,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;UACzF,MAAME,aAAa,GAAG1C,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,CAAC;UAExF,IAAIH,aAAa,KAAK,CAAC,CAAC,IAAII,aAAa,KAAK,CAAC,CAAC,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE;YACxE3Q,OAAO,CAAC2J,KAAK,CAAC,2BAA2B,CAAC;YAC1CxF,UAAU,CAAC,KAAK,CAAC;YACjB;UACF;;UAEA;UACA,KAAK,IAAIyM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,KAAK,CAACxG,MAAM,EAAEgH,CAAC,EAAE,EAAE;YAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;YACrC,IAAI,CAACX,KAAK,CAACQ,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,EAAE;YAEtB,MAAMpG,MAAM,GAAGwF,KAAK,CAACQ,CAAC,CAAC,CAACP,KAAK,CAAC,GAAG,CAAC;YAClC,MAAMY,OAAO,GAAG;cACdtD,SAAS,GAAAkD,qBAAA,GAAEjG,MAAM,CAAC0F,aAAa,CAAC,cAAAO,qBAAA,uBAArBA,qBAAA,CAAuBG,IAAI,CAAC,CAAC;cACxCzD,QAAQ,GAAAuD,qBAAA,GAAElG,MAAM,CAAC8F,aAAa,CAAC,cAAAI,qBAAA,uBAArBA,qBAAA,CAAuBE,IAAI,CAAC,CAAC;cACvCxD,QAAQ,GAAAuD,qBAAA,GAAEnG,MAAM,CAAC+F,aAAa,CAAC,cAAAI,qBAAA,uBAArBA,qBAAA,CAAuBC,IAAI,CAAC,CAAC;cACvCE,UAAU,EAAE;YACd,CAAC;;YAED;YACA,MAAMC,UAAU,GAAGlD,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAClF,IAAIU,UAAU,KAAK,CAAC,CAAC,IAAIvG,MAAM,CAACuG,UAAU,CAAC,EAAE;cAC3CF,OAAO,CAACpD,KAAK,GAAGjD,MAAM,CAACuG,UAAU,CAAC,CAACH,IAAI,CAAC,CAAC;YAC3C;YAEA,MAAMI,UAAU,GAAGnD,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAClF,IAAIW,UAAU,KAAK,CAAC,CAAC,IAAIxG,MAAM,CAACwG,UAAU,CAAC,EAAE;cAC3CH,OAAO,CAACrD,KAAK,GAAGhD,MAAM,CAACwG,UAAU,CAAC,CAACJ,IAAI,CAAC,CAAC;YAC3C;YAEA,MAAMK,WAAW,GAAGpD,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpF,IAAIY,WAAW,KAAK,CAAC,CAAC,IAAIzG,MAAM,CAACyG,WAAW,CAAC,EAAE;cAC7CJ,OAAO,CAACK,MAAM,GAAG1G,MAAM,CAACyG,WAAW,CAAC,CAACL,IAAI,CAAC,CAAC;YAC7C;YAEA,MAAMO,SAAS,GAAGtD,OAAO,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAID,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChF,IAAIc,SAAS,KAAK,CAAC,CAAC,IAAI3G,MAAM,CAAC2G,SAAS,CAAC,EAAE;cACzCN,OAAO,CAACO,IAAI,GAAG5G,MAAM,CAAC2G,SAAS,CAAC,CAACP,IAAI,CAAC,CAAC;YACzC;;YAEA;YACA,IAAI,CAACC,OAAO,CAACtD,SAAS,IAAI,CAACsD,OAAO,CAAC1D,QAAQ,IAAI,CAAC0D,OAAO,CAACzD,QAAQ,EAAE;cAChElF,OAAO,CAACa,IAAI,CAAC,IAAIyH,CAAC,WAAW,EAAEK,OAAO,CAAC;cACvCd,SAAS,EAAE;cACX;YACF;YAEAF,QAAQ,CAACwB,IAAI,CAACR,OAAO,CAAC;UACxB;UAEA,IAAIhB,QAAQ,CAACrG,MAAM,KAAK,CAAC,EAAE;YACzB5J,OAAO,CAAC2J,KAAK,CAAC,WAAW,CAAC;YAC1BxF,UAAU,CAAC,KAAK,CAAC;YACjB;UACF;;UAEA;UACAmE,OAAO,CAACC,GAAG,CAAC,QAAQ0H,QAAQ,CAACrG,MAAM,MAAM,CAAC;;UAE1C;UACA,KAAK,MAAMqH,OAAO,IAAIhB,QAAQ,EAAE;YAC9B,IAAI;cACF,MAAM;gBAAExC;cAAW,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;cACnD,MAAMiE,UAAU,GAAG,MAAMjE,UAAU,CAACwD,OAAO,CAAC;;cAE5C;cACA,MAAMtB,iBAAiB,CAAC9C,OAAO,EAAE6E,UAAU,CAACzI,EAAE,CAAC;cAC/CiH,YAAY,EAAE;YAChB,CAAC,CAAC,OAAOvG,KAAK,EAAE;cACdrB,OAAO,CAACqB,KAAK,CAAC,WAAWsH,OAAO,CAAC1D,QAAQ,EAAE,EAAE5D,KAAK,CAAC;cACnDwG,SAAS,EAAE;YACb;UACF;;UAEA;UACA,IAAID,YAAY,GAAG,CAAC,EAAE;YACpBlQ,OAAO,CAAC+K,OAAO,CAAC,QAAQmF,YAAY,SAAS,CAAC;YAC9CtI,4BAA4B,CAAC,KAAK,CAAC;YACnCG,iBAAiB,CAAC,EAAE,CAAC;YACrBF,iBAAiB,CAAC4C,WAAW,CAAC,CAAC;;YAE/B;YACAiB,YAAY,CAAClH,cAAc,CAACyE,EAAE,CAAC;UACjC,CAAC,MAAM;YACLjJ,OAAO,CAAC2J,KAAK,CAAC,YAAY,CAAC;UAC7B;UAEA,IAAIwG,SAAS,GAAG,CAAC,EAAE;YACjBnQ,OAAO,CAAC2R,OAAO,CAAC,GAAGxB,SAAS,2BAA2B,CAAC;UAC1D;QACF,CAAC,CAAC,OAAOxG,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;QACzB,CAAC,SAAS;UACRxF,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC;MAEDyL,MAAM,CAACgC,OAAO,GAAG,MAAM;QACrB5R,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;QACvBxF,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MAEDyL,MAAM,CAACiC,UAAU,CAAC/J,cAAc,CAAC,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;MACzBxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2N,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC/C5N,UAAU,CAAC,IAAI,CAAC;IAChBuD,mBAAmB,CAACqK,WAAW,CAAC;IAEhC,IAAI;MACF;MACA,MAAM;QAAEC;MAAS,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;;MAEjD;MACA,MAAMC,YAAY,GAAG,MAAMD,QAAQ,CAACD,WAAW,CAAC9I,EAAE,EAAE,IAAI,CAAC;;MAEzD;MACA,IAAIgH,QAAQ,GAAG,EAAE;MACjB,IAAIgC,YAAY,IAAIA,YAAY,CAAChC,QAAQ,EAAE;QACzCA,QAAQ,GAAGxH,KAAK,CAACC,OAAO,CAACuJ,YAAY,CAAChC,QAAQ,CAAC,GAAGgC,YAAY,CAAChC,QAAQ,GAAG,EAAE;MAC9E;MAEAzI,uBAAuB,CAACyI,QAAQ,CAAC;MACjC3I,4BAA4B,CAAC,IAAI,CAAC;IACpC,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+N,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMtH,MAAM,GAAG,MAAM5D,gBAAgB,CAAC6D,cAAc,CAAC,CAAC;MACtD1G,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM0I,OAAO,GAAGjC,MAAM,CAAC0E,QAAQ;MAC/B,MAAM6C,YAAY,GAAGvH,MAAM,CAACwH,aAAa,CAACpB,IAAI,CAAC,CAAC;MAEhD,IAAI,CAACmB,YAAY,EAAE;QACjBnS,OAAO,CAAC2J,KAAK,CAAC,SAAS,CAAC;QACxBxF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMkO,YAAY,GAAGF,YAAY,CAAC9B,KAAK,CAAC,IAAI,CAAC;MAC7C,MAAMJ,QAAQ,GAAG,EAAE;MACnB,MAAMqC,MAAM,GAAG,EAAE;MAEjBD,YAAY,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEzJ,KAAK,KAAK;QACpC,MAAM0J,KAAK,GAAGD,IAAI,CAACxB,IAAI,CAAC,CAAC,CAACX,KAAK,CAAC,GAAG,CAAC;QACpC,IAAIoC,KAAK,CAAC7I,MAAM,GAAG,CAAC,EAAE;UACpB0I,MAAM,CAACb,IAAI,CAAC,IAAI1I,KAAK,GAAG,CAAC,UAAUyJ,IAAI,EAAE,CAAC;UAC1C;QACF;QAEA,MAAM,CAACE,QAAQ,EAAEnF,QAAQ,EAAEC,QAAQ,CAAC,GAAGiF,KAAK;QAC5C,IAAI,CAACC,QAAQ,IAAI,CAACnF,QAAQ,IAAI,CAACC,QAAQ,EAAE;UACvC8E,MAAM,CAACb,IAAI,CAAC,IAAI1I,KAAK,GAAG,CAAC,WAAWyJ,IAAI,EAAE,CAAC;UAC3C;QACF;QAEAvC,QAAQ,CAACwB,IAAI,CAAC;UACZ9D,SAAS,EAAE+E,QAAQ,CAAC1B,IAAI,CAAC,CAAC;UAC1BzD,QAAQ,EAAEA,QAAQ,CAACyD,IAAI,CAAC,CAAC;UACzBxD,QAAQ,EAAEA,QAAQ,CAACwD,IAAI,CAAC,CAAC;UACzBE,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAIoB,MAAM,CAAC1I,MAAM,GAAG,CAAC,EAAE;QACrB5J,OAAO,CAAC2J,KAAK,CAAC,YAAY2I,MAAM,CAAClE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC9CjK,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM;QAAEsJ,UAAU;QAAEkC;MAAkB,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;MACtE,MAAMgD,eAAe,GAAG,EAAE;MAC1B,IAAIzC,YAAY,GAAG,CAAC;MACpB,IAAIC,SAAS,GAAG,CAAC;;MAEjB;MACA,KAAK,MAAMc,OAAO,IAAIhB,QAAQ,EAAE;QAC9B,IAAI;UACF,MAAMyB,UAAU,GAAG,MAAMjE,UAAU,CAACwD,OAAO,CAAC;UAC5C0B,eAAe,CAAClB,IAAI,CAACC,UAAU,CAAC;;UAEhC;UACA,MAAM/B,iBAAiB,CAAC9C,OAAO,EAAE6E,UAAU,CAACzI,EAAE,CAAC;UAC/CiH,YAAY,EAAE;QAChB,CAAC,CAAC,OAAOvG,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,WAAWsH,OAAO,CAAC1D,QAAQ,EAAE,EAAE5D,KAAK,CAAC;UACnDwG,SAAS,EAAE;QACb;MACF;;MAEA;MACA,IAAID,YAAY,GAAG,CAAC,EAAE;QACpBlQ,OAAO,CAAC+K,OAAO,CAAC,QAAQmF,YAAY,SAAS,CAAC;QAC9ChJ,2BAA2B,CAAC,KAAK,CAAC;QAClCF,gBAAgB,CAACyD,WAAW,CAAC,CAAC;;QAE9B;QACAiB,YAAY,CAAClH,cAAc,CAACyE,EAAE,CAAC;MACjC,CAAC,MAAM;QACLjJ,OAAO,CAAC2J,KAAK,CAAC,YAAY,CAAC;MAC7B;MAEA,IAAIwG,SAAS,GAAG,CAAC,EAAE;QACjBnQ,OAAO,CAAC2R,OAAO,CAAC,GAAGxB,SAAS,oBAAoB,CAAC;MACnD;IAEF,CAAC,CAAC,OAAOxG,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRxF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyO,gBAAgB,GAAG,MAAAA,CAAOtF,MAAM,EAAEuF,MAAM,KAAK;IACjD5S,KAAK,CAACiL,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChBpH,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,IAAI,CAACK,cAAc,IAAI,CAACA,cAAc,CAACyE,EAAE,EAAE;YACzCjJ,OAAO,CAAC2J,KAAK,CAAC,cAAc,CAAC;YAC7BxF,UAAU,CAAC,KAAK,CAAC;YACjB;UACF;UAEA,MAAMhC,UAAU,CAAC;YACfgL,OAAO,EAAEG,MAAM;YACfF,OAAO,EAAEyF,MAAM;YACflG,SAAS,EAAEnI,cAAc,CAACyE;UAC5B,CAAC,CAAC;UAEFjJ,OAAO,CAAC+K,OAAO,CAAC,QAAQ,CAAC;UACzBa,UAAU,CAACpH,cAAc,CAACyE,EAAE,CAAC;QAC/B,CAAC,CAAC,OAAOU,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;QACzB,CAAC,SAAS;UACRxF,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2O,kBAAkB,GAAIvG,MAAM,IAAK;IACrCjE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgE,MAAM,CAAC;IACnCjE,OAAO,CAACC,GAAG,CAAC,WAAW,OAAOgE,MAAM,CAACtD,EAAE,QAAQsD,MAAM,CAACtD,EAAE,EAAE,CAAC;;IAE3D;IACA,IAAI8J,UAAU,GAAG;MAAE,GAAGxG;IAAO,CAAC;;IAE9B;IACA,IAAI,CAACwG,UAAU,CAACrI,IAAI,EAAE;MACpBqI,UAAU,CAACrI,IAAI,GAAG,OAAO;IAC3B;;IAEA;IACA,IAAI,OAAOqI,UAAU,CAAC9J,EAAE,KAAK,QAAQ,EAAE;MACrC,MAAMG,SAAS,GAAGC,QAAQ,CAAC0J,UAAU,CAAC9J,EAAE,EAAE,EAAE,CAAC;MAC7C,IAAI,CAACK,KAAK,CAACF,SAAS,CAAC,EAAE;QACrB2J,UAAU,CAAC9J,EAAE,GAAGG,SAAS;MAC3B;IACF;IAEAd,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwK,UAAU,CAAC;;IAEpC;IACAvN,eAAe,CAACuN,UAAU,CAAC;;IAE3B;IACAtO,iBAAiB,CAACsO,UAAU,CAAC;;IAE7B;IACA,IAAIA,UAAU,CAAC9J,EAAE,EAAE;MACjBX,OAAO,CAACC,GAAG,CAAC,kBAAkBwK,UAAU,CAAC9J,EAAE,EAAE,CAAC;MAC9CyC,YAAY,CAACqH,UAAU,CAAC9J,EAAE,CAAC;MAC3B0C,UAAU,CAAC,CAAC;MACZC,UAAU,CAACmH,UAAU,CAAC9J,EAAE,CAAC;MACzB4C,aAAa,CAAC,CAAC;;MAEf;MACA3F,kBAAkB,CAAC,GAAG,CAAC;IACzB;;IAEA;IACAoC,OAAO,CAACC,GAAG,CAAC,eAAewK,UAAU,CAAC9J,EAAE,aAAa,CAAC;;IAEtD;IACA+J,YAAY,CAAC,GAAG,CAAC;;IAEjB;IACAC,UAAU,CAAC,MAAM;MACf3K,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwK,UAAU,CAAC9J,EAAE,CAAC;;MAEtD;MACA,IAAIiK,SAAS,KAAK,GAAG,EAAE;QACrB5K,OAAO,CAACa,IAAI,CAAC,qBAAqB,CAAC;QACnC6J,YAAY,CAAC,GAAG,CAAC;MACnB;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMG,OAAO,GAAG,CACd;IACEhI,KAAK,EAAE,IAAI;IACXiI,SAAS,EAAE,IAAI;IACfhH,GAAG,EAAE,IAAI;IACTiH,KAAK,EAAE;EACT,CAAC,EACD;IACElI,KAAK,EAAE,MAAM;IACbiI,SAAS,EAAE,MAAM;IACjBhH,GAAG,EAAE;EACP,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXiI,SAAS,EAAE,UAAU;IACrBhH,GAAG,EAAE,UAAU;IACfkH,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACEpI,KAAK,EAAE,IAAI;IACXiI,SAAS,EAAE,MAAM;IACjBhH,GAAG,EAAE,MAAM;IACXkH,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACEpI,KAAK,EAAE,IAAI;IACXiI,SAAS,EAAE,UAAU;IACrBhH,GAAG,EAAE,UAAU;IACfkH,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACEpI,KAAK,EAAE,MAAM;IACbiI,SAAS,EAAE,aAAa;IACxBhH,GAAG,EAAE,aAAa;IAClBkH,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACEpI,KAAK,EAAE,IAAI;IACXiB,GAAG,EAAE,QAAQ;IACbkH,MAAM,EAAEA,CAACE,CAAC,EAAEjH,MAAM,kBAChB9J,OAAA,CAAAE,SAAA;MAAA8Q,QAAA,gBACEhR,OAAA,CAAC1C,MAAM;QACLwO,IAAI,EAAC,MAAM;QACXmF,OAAO,EAAEA,CAAA,KAAMZ,kBAAkB,CAACvG,MAAM,CAAE;QAAAkH,QAAA,EAC3C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;QACLwO,IAAI,EAAC,MAAM;QACXwF,IAAI,eAAEtR,OAAA,CAAC1B,YAAY;UAAA4S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBJ,OAAO,EAAEA,CAAA,KAAMlJ,SAAS,CAAC+B,MAAM,CAAE;QAAAkH,QAAA,EAClC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;QACLwO,IAAI,EAAC,MAAM;QACXyF,MAAM;QACND,IAAI,eAAEtR,OAAA,CAACzB,cAAc;UAAA2S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBJ,OAAO,EAAEA,CAAA,KAAMzI,YAAY,CAACsB,MAAM,CAACtD,EAAE,CAAE;QAAAwK,QAAA,EACxC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA,eACT;EAEN,CAAC,CACF;;EAED;EACA,MAAMG,eAAe,GAAI7H,GAAG,IAAK;IAC/B9D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6D,GAAG,CAAC;;IAE3B;IACA4G,YAAY,CAAC5G,GAAG,CAAC;IAEjB,IAAIA,GAAG,KAAK,GAAG,EAAE;MACf;MACA3H,iBAAiB,CAAC,IAAI,CAAC;MACvBe,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,MAAM,IAAI4G,GAAG,KAAK,GAAG,IAAI5H,cAAc,EAAE;MACxC8D,OAAO,CAACC,GAAG,CAAC,oBAAoB/D,cAAc,CAACyE,EAAE,EAAE,CAAC;;MAEpD;MACA,IAAI,CAAC1D,YAAY,EAAE;QACjBC,eAAe,CAAChB,cAAc,CAAC;MACjC;;MAEA;MACA,IAAIA,cAAc,CAACyE,EAAE,EAAE;QACrByC,YAAY,CAAClH,cAAc,CAACyE,EAAE,CAAC;MACjC;IACF,CAAC,MAAM,IAAImD,GAAG,KAAK,GAAG,IAAI,CAAC5H,cAAc,EAAE;MACzC;MACA8D,OAAO,CAACqB,KAAK,CAAC,uBAAuB,CAAC;MACtC3J,OAAO,CAAC2J,KAAK,CAAC,UAAU,CAAC;MACzB;MACAqJ,YAAY,CAAC,GAAG,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM,CAACE,SAAS,EAAEF,YAAY,CAAC,GAAGrT,QAAQ,CAAC,GAAG,CAAC;;EAE/C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI4E,cAAc,EAAE;MAClB8D,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzCyK,YAAY,CAAC,GAAG,CAAC;;MAEjB;MACA,IAAIxO,cAAc,CAACyE,EAAE,EAAE;QACrBX,OAAO,CAACC,GAAG,CAAC,mCAAmC/D,cAAc,CAACyE,EAAE,EAAE,CAAC;QACnEyC,YAAY,CAAClH,cAAc,CAACyE,EAAE,CAAC;MACjC;IACF,CAAC,MAAM;MACL+J,YAAY,CAAC,GAAG,CAAC;IACnB;EACF,CAAC,EAAE,CAACxO,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM0P,YAAY,GAAGA,CAACC,aAAa,EAAEC,SAAS,KAAK;IACjD,MAAM;MAAE1J,IAAI;MAAEnB,QAAQ;MAAEC,IAAI;MAAEC;IAAS,CAAC,GAAG2K,SAAS;;IAEpD;IACA,MAAMC,iBAAiB,GAAG3J,IAAI,IAAInB,QAAQ,IAAIC,IAAI,IAAIC,QAAQ;IAC9DnE,cAAc,CAAC+O,iBAAiB,CAAC;;IAEjC;IACA,MAAMC,QAAQ,GAAGtQ,OAAO,CAAC+F,MAAM,CAACjB,MAAM,IAAI;MACxC;MACA,MAAMyL,SAAS,GAAG,CAAC7J,IAAI,IAAK5B,MAAM,CAAC4B,IAAI,IAAI5B,MAAM,CAAC4B,IAAI,CAAC8J,WAAW,CAAC,CAAC,CAAC/D,QAAQ,CAAC/F,IAAI,CAAC8J,WAAW,CAAC,CAAC,CAAE;;MAElG;MACA,MAAMC,aAAa,GAAG,CAAClL,QAAQ,IAAIT,MAAM,CAACS,QAAQ,KAAKA,QAAQ;;MAE/D;MACA,MAAMmL,SAAS,GAAG,CAAClL,IAAI,IAAIV,MAAM,CAACU,IAAI,KAAKA,IAAI;;MAE/C;MACA,MAAMmL,aAAa,GAAG,CAAClL,QAAQ,IAAIX,MAAM,CAACW,QAAQ,KAAKA,QAAQ;;MAE/D;MACA,OAAO8K,SAAS,IAAIE,aAAa,IAAIC,SAAS,IAAIC,aAAa;IACjE,CAAC,CAAC;IAEFvP,kBAAkB,CAACkP,QAAQ,CAAC;;IAE5B;EACF,CAAC;;EAED;EACA,MAAMM,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1P,UAAU,CAACuF,WAAW,CAAC,CAAC;IACxBrF,kBAAkB,CAACpB,OAAO,CAAC;IAC3BsB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,oBACE7C,OAAA;IAAKoS,SAAS,EAAC,4CAA4C;IAAC,kBAAe,uBAAuB;IAAApB,QAAA,eAChGhR,OAAA,CAACpC,IAAI;MACH6S,SAAS,EAAEA,SAAU;MACrB4B,QAAQ,EAAEb,eAAgB;MAAAR,QAAA,gBAE1BhR,OAAA,CAACG,OAAO;QAACmS,GAAG,EAAC,0BAAM;QAAAtB,QAAA,eACjBhR,OAAA,CAAC5C,IAAI;UACHsL,KAAK,EAAC,sCAAQ;UACd6J,KAAK,eACHvS,OAAA,CAAC1C,MAAM;YACLwO,IAAI,EAAC,SAAS;YACdwF,IAAI,eAAEtR,OAAA,CAAC3B,YAAY;cAAA6S,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBJ,OAAO,EAAEA,CAAA,KAAMlJ,SAAS,CAAC,CAAE;YAAAiJ,QAAA,EAC5B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAL,QAAA,gBAGDhR,OAAA,CAACvC,IAAI;YACHwE,IAAI,EAAEQ,UAAW;YACjB+P,MAAM,EAAC,QAAQ;YACflG,KAAK,EAAE;cAAEmG,YAAY,EAAE;YAAG,CAAE;YAC5BC,cAAc,EAAEjB,YAAa;YAAAT,QAAA,gBAE7BhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;cAAC1K,IAAI,EAAC,MAAM;cAACzH,KAAK,EAAC,0BAAM;cAAAwQ,QAAA,eACjChR,OAAA,CAACtC,KAAK;gBACJkV,WAAW,EAAC,4CAAS;gBACrBC,UAAU;gBACVR,QAAQ,EAAGhJ,CAAC,IAAK;kBACf;kBACA,IAAI,CAACA,CAAC,CAACiE,MAAM,CAAC/M,KAAK,EAAE;oBACnBkR,YAAY,CAAC,CAAC,CAAC,EAAEhP,UAAU,CAACqQ,cAAc,CAAC,CAAC,CAAC;kBAC/C;gBACF;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;cAAC1K,IAAI,EAAC,UAAU;cAACzH,KAAK,EAAC,cAAI;cAAAwQ,QAAA,eACnChR,OAAA,CAACrC,MAAM;gBACLiV,WAAW,EAAC,gCAAO;gBACnBtG,KAAK,EAAE;kBAAEsE,KAAK,EAAE;gBAAI,CAAE;gBACtBiC,UAAU;gBACVE,UAAU;gBACVC,gBAAgB,EAAC,UAAU;gBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;gBACDM,QAAQ,EAAG9R,KAAK,IAAK;kBACnBkC,UAAU,CAACiF,cAAc,CAAC;oBAAEX,IAAI,EAAEN,SAAS;oBAAEO,QAAQ,EAAEP;kBAAU,CAAC,CAAC;kBACnE,IAAIlG,KAAK,EAAE;oBACT;oBACA,CAAC,YAAY;sBACX,IAAI;wBACF,MAAMqF,IAAI,GAAG,MAAM1G,UAAU,CAAC;0BAAE4H,QAAQ,EAAEvG;wBAAM,CAAC,CAAC;wBAClD,IAAIqF,IAAI,IAAIA,IAAI,CAACvD,MAAM,EAAE;0BACvBC,SAAS,CAACsD,IAAI,CAACvD,MAAM,CAAC;wBACxB;sBACF,CAAC,CAAC,OAAO6E,KAAK,EAAE;wBACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;sBACnC,CAAC,SAAS;wBACR;wBACAuK,YAAY,CAAC,CAAC,CAAC,EAAEhP,UAAU,CAACqQ,cAAc,CAAC,CAAC,CAAC;sBAC/C;oBACF,CAAC,EAAE,CAAC;kBACN,CAAC,MAAM;oBACLxQ,SAAS,CAAC,EAAE,CAAC;oBACbE,YAAY,CAAC,EAAE,CAAC;oBAChB;oBACAiP,YAAY,CAAC,CAAC,CAAC,EAAEhP,UAAU,CAACqQ,cAAc,CAAC,CAAC,CAAC;kBAC/C;gBACF,CAAE;gBAAA9B,QAAA,EAED7O,SAAS,CAACiE,GAAG,CAACU,QAAQ,iBACrB9G,OAAA,CAACI,MAAM;kBAAgBG,KAAK,EAAEuG,QAAS;kBAAAkK,QAAA,EAAElK;gBAAQ,GAApCA,QAAQ;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;cAAC1K,IAAI,EAAC,MAAM;cAACzH,KAAK,EAAC,cAAI;cAAAwQ,QAAA,eAC/BhR,OAAA,CAACrC,MAAM;gBACLiV,WAAW,EAAC,gCAAO;gBACnBtG,KAAK,EAAE;kBAAEsE,KAAK,EAAE;gBAAI,CAAE;gBACtBiC,UAAU;gBACVQ,QAAQ,EAAE,CAAC5Q,UAAU,CAACoF,aAAa,CAAC,UAAU,CAAE;gBAChDwK,QAAQ,EAAG9R,KAAK,IAAK;kBACnBkC,UAAU,CAACiF,cAAc,CAAC;oBAAEV,QAAQ,EAAEP;kBAAU,CAAC,CAAC;kBAClD,IAAIlG,KAAK,EAAE;oBACT;oBACA,CAAC,YAAY;sBACX,IAAI;wBACF,MAAMqF,IAAI,GAAG,MAAM1G,UAAU,CAAC;0BAC5B4H,QAAQ,EAAErE,UAAU,CAACoF,aAAa,CAAC,UAAU,CAAC;0BAC9Cd,IAAI,EAAExG;wBACR,CAAC,CAAC;wBACF,IAAIqF,IAAI,IAAIA,IAAI,CAACrD,SAAS,EAAE;0BAC1BC,YAAY,CAACoD,IAAI,CAACrD,SAAS,CAAC;wBAC9B;sBACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;wBACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;sBACnC,CAAC,SAAS;wBACR;wBACAuK,YAAY,CAAC,CAAC,CAAC,EAAEhP,UAAU,CAACqQ,cAAc,CAAC,CAAC,CAAC;sBAC/C;oBACF,CAAC,EAAE,CAAC;kBACN,CAAC,MAAM;oBACLtQ,YAAY,CAAC,EAAE,CAAC;oBAChB;oBACAiP,YAAY,CAAC,CAAC,CAAC,EAAEhP,UAAU,CAACqQ,cAAc,CAAC,CAAC,CAAC;kBAC/C;gBACF,CAAE;gBAAA9B,QAAA,EAED3O,MAAM,CAAC+D,GAAG,CAACW,IAAI,iBACd/G,OAAA,CAACI,MAAM;kBAAYG,KAAK,EAAEwG,IAAK;kBAAAiK,QAAA,EAAEjK;gBAAI,GAAxBA,IAAI;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;cAAC1K,IAAI,EAAC,UAAU;cAACzH,KAAK,EAAC,cAAI;cAAAwQ,QAAA,eACnChR,OAAA,CAACrC,MAAM;gBACLiV,WAAW,EAAC,gCAAO;gBACnBtG,KAAK,EAAE;kBAAEsE,KAAK,EAAE;gBAAI,CAAE;gBACtBiC,UAAU;gBACVQ,QAAQ,EAAE,CAAC5Q,UAAU,CAACoF,aAAa,CAAC,MAAM,CAAE;gBAC5CwK,QAAQ,EAAG9R,KAAK,IAAK;kBACnB;kBACAkR,YAAY,CAAC,CAAC,CAAC,EAAEhP,UAAU,CAACqQ,cAAc,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBAAA9B,QAAA,EAEDzO,SAAS,CAAC6D,GAAG,CAACY,QAAQ,iBACrBhH,OAAA,CAACI,MAAM;kBAAgBG,KAAK,EAAEyG,QAAS;kBAAAgK,QAAA,EAAEhK;gBAAQ,GAApCA,QAAQ;kBAAAkK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CAC3D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;cAAA3B,QAAA,eACRhR,OAAA,CAAC1C,MAAM;gBAAC2T,OAAO,EAAEkB,iBAAkB;gBAACb,IAAI,eAAEtR,OAAA,CAACnB,cAAc;kBAAAqS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAL,QAAA,EAAC;cAE9D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEPrR,OAAA,CAACnC,IAAI;YAACyV,QAAQ,EAAE7R,OAAQ;YAAAuP,QAAA,eACtBhR,OAAA,CAAC3C,KAAK;cACJqT,OAAO,EAAEA,OAAQ;cACjB6C,UAAU,EAAE3Q,WAAW,GAAGF,eAAe,GAAGnB,OAAQ;cACpDiS,MAAM,EAAG1J,MAAM,IAAK,UAAUA,MAAM,CAACtD,EAAE,EAAG;cAC1CiN,UAAU,EAAE;gBACVC,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;gBAC1CC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;cAClC,CAAE;cACFC,MAAM,EAAEA,CAAA,kBACN/T,OAAA;gBAAKsM,KAAK,EAAE;kBAAE0H,SAAS,EAAE;gBAAQ,CAAE;gBAAAhD,QAAA,EAChCpO,WAAW,gBACV5C,OAAA;kBAAAgR,QAAA,GAAM,yCAAS,EAACtO,eAAe,CAACyE,MAAM,EAAC,qBAAI;gBAAA;kBAAA+J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAElDrR,OAAA;kBAAAgR,QAAA,GAAM,+CAAU,EAACzP,OAAO,CAAC4F,MAAM,EAAC,SAAE;gBAAA;kBAAA+J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGPrR,OAAA,CAACxC,KAAK;YACJkL,KAAK,EAAE7G,aAAa,GAAG,MAAM,GAAG,MAAO;YACvCoS,IAAI,EAAEtS,YAAa;YACnBmH,IAAI,EAAEZ,YAAa;YACnBgM,QAAQ,EAAEA,CAAA,KAAMtS,eAAe,CAAC,KAAK,CAAE;YACvCuS,cAAc,EAAE1S,OAAQ;YAAAuP,QAAA,eAExBhR,OAAA,CAACvC,IAAI;cACHwE,IAAI,EAAEA,IAAK;cACXuQ,MAAM,EAAC,UAAU;cAAAxB,QAAA,gBAEjBhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;gBACR1K,IAAI,EAAC,MAAM;gBACXzH,KAAK,EAAC,0BAAM;gBACZ4T,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9W,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAAyT,QAAA,eAEhDhR,OAAA,CAACtC,KAAK;kBAACkV,WAAW,EAAC;gBAAS;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;gBACR1K,IAAI,EAAC,UAAU;gBACfzH,KAAK,EAAC,cAAI;gBAAAwQ,QAAA,eAEVhR,OAAA,CAACrC,MAAM;kBACLiV,WAAW,EAAC,gCAAO;kBACnBP,QAAQ,EAAE5K,oBAAqB;kBAC/BsL,UAAU;kBACVC,gBAAgB,EAAC,UAAU;kBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;kBAAAf,QAAA,EAEA7O,SAAS,CAACiE,GAAG,CAACU,QAAQ,iBACrB9G,OAAA,CAACI,MAAM;oBAAgBG,KAAK,EAAEuG,QAAS;oBAAAkK,QAAA,EAAElK;kBAAQ,GAApCA,QAAQ;oBAAAoK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;gBACR1K,IAAI,EAAC,MAAM;gBACXzH,KAAK,EAAC,cAAI;gBAAAwQ,QAAA,eAEVhR,OAAA,CAACrC,MAAM;kBACLiV,WAAW,EAAC,gCAAO;kBACnBP,QAAQ,EAAEzK,gBAAiB;kBAC3ByL,QAAQ,EAAE,CAACpR,IAAI,CAAC4F,aAAa,CAAC,UAAU,CAAE;kBAC1CkL,UAAU;kBACVC,gBAAgB,EAAC,UAAU;kBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;kBAAAf,QAAA,EAEA3O,MAAM,CAAC+D,GAAG,CAACW,IAAI,iBACd/G,OAAA,CAACI,MAAM;oBAAYG,KAAK,EAAEwG,IAAK;oBAAAiK,QAAA,EAAEjK;kBAAI,GAAxBA,IAAI;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;gBACR1K,IAAI,EAAC,UAAU;gBACfzH,KAAK,EAAC,cAAI;gBAAAwQ,QAAA,eAEVhR,OAAA,CAACrC,MAAM;kBACLiV,WAAW,EAAC,gCAAO;kBACnBS,QAAQ,EAAE,CAACpR,IAAI,CAAC4F,aAAa,CAAC,MAAM,CAAE;kBACtCkL,UAAU;kBACVC,gBAAgB,EAAC,UAAU;kBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;kBAAAf,QAAA,EAEAzO,SAAS,CAAC6D,GAAG,CAACY,QAAQ,iBACrBhH,OAAA,CAACI,MAAM;oBAAgBG,KAAK,EAAEyG,QAAS;oBAAAgK,QAAA,EAAEhK;kBAAQ,GAApCA,QAAQ;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC,GA5Oe,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6OlB,CAAC,eAEVrR,OAAA,CAACG,OAAO;QAACmS,GAAG,EAAEvQ,cAAc,GAAG,GAAGA,cAAc,CAACkG,IAAI,IAAI,IAAI,IAAI,GAAG,MAAO;QAAA+I,QAAA,EACxEjP,cAAc,gBACb/B,OAAA;UACEoS,SAAS,EAAC,0BAA0B;UACpC,kBAAgBrQ,cAAc,CAACyE,EAAG;UAClC,oBAAkBzE,cAAc,CAACkG,IAAI,IAAI,OAAQ;UAAA+I,QAAA,eAEjDhR,OAAA,CAAC5C,IAAI;YAACsL,KAAK,EAAE,GAAG3G,cAAc,CAACkG,IAAI,IAAI,IAAI,MAAO;YAAA+I,QAAA,eAChDhR,OAAA,CAACnC,IAAI;cAACyV,QAAQ,EAAE7R,OAAO,IAAI,CAACiC,iBAAiB,IAAI,CAACE,gBAAiB;cAAAoN,QAAA,eACjEhR,OAAA,CAACpC,IAAI;gBAAC6S,SAAS,EAAEjN,eAAgB;gBAAC6O,QAAQ,EAAE3I,qBAAsB;gBAAAsH,QAAA,gBAEhEhR,OAAA,CAACG,OAAO;kBACNmS,GAAG,eACDtS,OAAA;oBAAAgR,QAAA,gBACEhR,OAAA,CAACtB,eAAe;sBAAAwS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,wCAErB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;kBAAAL,QAAA,EAGAlO,YAAY,gBACX9C,OAAA,CAACvC,IAAI;oBACHwE,IAAI,EAAEsD,gBAAiB;oBACvBiN,MAAM,EAAC,UAAU;oBACjB8B,aAAa,EAAE;sBACbrM,IAAI,EAAEnF,YAAY,CAACmF,IAAI,KAAIlG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEkG,IAAI,KAAI,EAAE;sBACrDnB,QAAQ,EAAEhE,YAAY,CAACgE,QAAQ,KAAI/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE+E,QAAQ,KAAI,EAAE;sBACjEC,IAAI,EAAEjE,YAAY,CAACiE,IAAI,KAAIhF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEgF,IAAI,KAAI,EAAE;sBACrDC,QAAQ,EAAElE,YAAY,CAACkE,QAAQ,KAAIjF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiF,QAAQ,KAAI;oBACjE,CAAE;oBACFuN,QAAQ,EAAE3K,wBAAyB;oBAAAoH,QAAA,gBAEnChR,OAAA,CAACvC,IAAI,CAACkV,IAAI;sBACR1K,IAAI,EAAC,MAAM;sBACXzH,KAAK,EAAC,0BAAM;sBACZ4T,KAAK,EAAE,CAAC;wBAAEC,QAAQ,EAAE,IAAI;wBAAE9W,OAAO,EAAE;sBAAU,CAAC,CAAE;sBAAAyT,QAAA,eAEhDhR,OAAA,CAACtC,KAAK;wBAAAwT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;sBACR1K,IAAI,EAAC,UAAU;sBACfzH,KAAK,EAAC,cAAI;sBAAAwQ,QAAA,eAEVhR,OAAA,CAACrC,MAAM;wBACLiV,WAAW,EAAC,gCAAO;wBACnBG,UAAU;wBACVC,gBAAgB,EAAC,UAAU;wBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;wBACDM,QAAQ,EAAG9R,KAAK,IAAK;0BACnB;0BACA,MAAMiU,WAAW,GAAGvS,IAAI,CAACwS,OAAO;0BAChC,IAAID,WAAW,EAAE;4BACfA,WAAW,CAAC9M,cAAc,CAAC;8BAAEX,IAAI,EAAEN,SAAS;8BAAEO,QAAQ,EAAEP;4BAAU,CAAC,CAAC;0BACtE;0BACAgB,oBAAoB,CAAClH,KAAK,CAAC;wBAC7B,CAAE;wBAAAyQ,QAAA,EAED7O,SAAS,CAACiE,GAAG,CAACU,QAAQ,iBACrB9G,OAAA,CAACI,MAAM;0BAAgBG,KAAK,EAAEuG,QAAS;0BAAAkK,QAAA,EAAElK;wBAAQ,GAApCA,QAAQ;0BAAAoK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAqC,CAC3D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;sBACR1K,IAAI,EAAC,MAAM;sBACXzH,KAAK,EAAC,cAAI;sBAAAwQ,QAAA,eAEVhR,OAAA,CAACrC,MAAM;wBACLiV,WAAW,EAAC,gCAAO;wBACnBG,UAAU;wBACVC,gBAAgB,EAAC,UAAU;wBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;wBACDM,QAAQ,EAAG9R,KAAK,IAAKqH,gBAAgB,CAACrH,KAAK,CAAE;wBAC7C8S,QAAQ,EAAE,EAACvQ,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEgE,QAAQ,CAAC;wBAAAkK,QAAA,EAEjC3O,MAAM,CAAC+D,GAAG,CAACW,IAAI,iBACd/G,OAAA,CAACI,MAAM;0BAAYG,KAAK,EAAEwG,IAAK;0BAAAiK,QAAA,EAAEjK;wBAAI,GAAxBA,IAAI;0BAAAmK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAA6B,CAC/C;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;sBACR1K,IAAI,EAAC,UAAU;sBACfzH,KAAK,EAAC,cAAI;sBAAAwQ,QAAA,eAEVhR,OAAA,CAACrC,MAAM;wBACLiV,WAAW,EAAC,gCAAO;wBACnBG,UAAU;wBACVC,gBAAgB,EAAC,UAAU;wBAC3BC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;wBACDsB,QAAQ,EAAE,EAACvQ,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEiE,IAAI,CAAC;wBAAAiK,QAAA,EAE7BzO,SAAS,CAAC6D,GAAG,CAACY,QAAQ,iBACrBhH,OAAA,CAACI,MAAM;0BAAgBG,KAAK,EAAEyG,QAAS;0BAAAgK,QAAA,EAAEhK;wBAAQ,GAApCA,QAAQ;0BAAAkK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAqC,CAC3D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;sBAAA3B,QAAA,eACRhR,OAAA,CAAC1C,MAAM;wBAACwO,IAAI,EAAC,SAAS;wBAAC4I,QAAQ,EAAC,QAAQ;wBAAA1D,QAAA,EAAC;sBAEzC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,gBAEPrR,OAAA,CAACnC,IAAI;oBAAC8W,GAAG,EAAC;kBAAY;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACzB,GA9FG,GAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA+FA,CAAC,eAGVrR,OAAA,CAACG,OAAO;kBACNmS,GAAG,eACDtS,OAAA;oBAAAgR,QAAA,gBACEhR,OAAA,CAACvB,YAAY;sBAAAyS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,4BAElB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;kBAAAL,QAAA,gBAGDhR,OAAA;oBAAKsM,KAAK,EAAE;sBAAEmG,YAAY,EAAE,EAAE;sBAAEmC,OAAO,EAAE,MAAM;sBAAEC,GAAG,EAAE;oBAAO,CAAE;oBAAA7D,QAAA,gBAC7DhR,OAAA,CAAC1C,MAAM;sBACLwO,IAAI,EAAC,SAAS;sBACdwF,IAAI,eAAEtR,OAAA,CAAC3B,YAAY;wBAAA6S,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACvBJ,OAAO,EAAEA,CAAA,KAAMpH,cAAc,CAAC,CAAE;sBAAAmH,QAAA,EACjC;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;sBACLwO,IAAI,EAAC,SAAS;sBACdwF,IAAI,eAAEtR,OAAA,CAACxB,YAAY;wBAAA0S,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACvBJ,OAAO,EAAEA,CAAA,KAAM3F,qBAAqB,CAAC,CAAE;sBAAA0F,QAAA,EACxC;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;sBACLwO,IAAI,EAAC,SAAS;sBACdwF,IAAI,eAAEtR,OAAA,CAACrB,cAAc;wBAAAuS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACzBJ,OAAO,EAAEA,CAAA,KAAM9L,4BAA4B,CAAC,IAAI,CAAE;sBAAA6L,QAAA,EACnD;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;sBACLwO,IAAI,EAAC,SAAS;sBACdwF,IAAI,eAAEtR,OAAA,CAACpB,gBAAgB;wBAAAsS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAC3BJ,OAAO,EAAEA,CAAA,KAAM1F,uBAAuB,CAAC,CAAE;sBAAAyF,QAAA,EAC1C;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNrR,OAAA,CAAC3C,KAAK;oBACJqT,OAAO,EAAE,CACP;sBACEhI,KAAK,EAAE,MAAM;sBACbiI,SAAS,EAAE,MAAM;sBACjBhH,GAAG,EAAE;oBACP,CAAC,EACD;sBACEjB,KAAK,EAAE,IAAI;sBACXiI,SAAS,EAAE,OAAO;sBAClBhH,GAAG,EAAE;oBACP,CAAC,EACD;sBACEjB,KAAK,EAAE,MAAM;sBACbiI,SAAS,EAAE,eAAe;sBAC1BhH,GAAG,EAAE,eAAe;sBACpBkH,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;oBAC5B,CAAC,EACD;sBACEpI,KAAK,EAAE,IAAI;sBACXiB,GAAG,EAAE,QAAQ;sBACbkH,MAAM,EAAEA,CAACE,CAAC,EAAEjH,MAAM,kBAChB9J,OAAA,CAAClC,KAAK;wBAACgX,IAAI,EAAC,QAAQ;wBAAA9D,QAAA,gBAClBhR,OAAA,CAAC1C,MAAM;0BACLwO,IAAI,EAAC,SAAS;0BACdwF,IAAI,eAAEtR,OAAA,CAAC1B,YAAY;4BAAA4S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAE;0BACvByD,IAAI,EAAC,OAAO;0BACZ7D,OAAO,EAAEA,CAAA,KAAMpH,cAAc,CAACC,MAAM,CAAE;0BAAAkH,QAAA,EACvC;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;0BACLwO,IAAI,EAAC,SAAS;0BACdwF,IAAI,eAAEtR,OAAA,CAACvB,YAAY;4BAAAyS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAE;0BACvByD,IAAI,EAAC,OAAO;0BACZ7D,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAACvF,MAAM,CAAE;0BAAAkH,QAAA,EAC1C;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTrR,OAAA,CAAC1C,MAAM;0BACLiU,MAAM;0BACND,IAAI,eAAEtR,OAAA,CAACzB,cAAc;4BAAA2S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAE;0BACzByD,IAAI,EAAC,OAAO;0BACZ7D,OAAO,EAAEA,CAAA,KAAM9G,iBAAiB,CAACL,MAAM,CAACtD,EAAE,CAAE;0BAAAwK,QAAA,EAC7C;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAEX,CAAC,CACD;oBACFkC,UAAU,EAAEvQ,OAAQ;oBACpBwQ,MAAM,EAAG1J,MAAM,IAAK,SAASA,MAAM,CAACtD,EAAE,EAAG;oBACzC/E,OAAO,EAAEA;kBAAQ;oBAAAyP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eAGFrR,OAAA,CAACxC,KAAK;oBACJkL,KAAK,EAAE1E,YAAY,GAAG,MAAM,GAAG,MAAO;oBACtCiQ,IAAI,EAAEvQ,iBAAkB;oBACxBoF,IAAI,EAAEmB,iBAAkB;oBACxBiK,QAAQ,EAAElK,iBAAkB;oBAC5BmK,cAAc,EAAE1S,OAAQ;oBAAAuP,QAAA,eAExBhR,OAAA,CAACvC,IAAI;sBACHwE,IAAI,EAAEmC,SAAU;sBAChBoO,MAAM,EAAC,UAAU;sBAAAxB,QAAA,gBAEjBhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,MAAM;wBACXzH,KAAK,EAAC,0BAAM;wBACZ4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAU,CAAC,CAAE;wBAAAyT,QAAA,eAEhDhR,OAAA,CAACtC,KAAK;0BAACkV,WAAW,EAAC;wBAAU;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,OAAO;wBACZzH,KAAK,EAAC,cAAI;wBACV4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAQ,CAAC,CAAE;wBAAAyT,QAAA,eAE9ChR,OAAA,CAACrC,MAAM;0BAACiV,WAAW,EAAC,gCAAO;0BAAA5B,QAAA,gBACzBhR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,oBAAK;4BAAAyQ,QAAA,EAAC;0BAAG;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAChCrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,cAAI;4BAAAyQ,QAAA,EAAC;0BAAE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC9BrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,cAAI;4BAAAyQ,QAAA,EAAC;0BAAE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eAC9BrR,OAAA,CAACI,MAAM;4BAACG,KAAK,EAAC,cAAI;4BAAAyQ,QAAA,EAAC;0BAAE;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGRrR,OAAA,CAACxC,KAAK;oBACJkL,KAAK,EAAC,sCAAQ;oBACduL,IAAI,EAAEzP,wBAAyB;oBAC/BsE,IAAI,EAAE2G,sBAAuB;oBAC7ByE,QAAQ,EAAEA,CAAA,KAAMzP,2BAA2B,CAAC,KAAK,CAAE;oBACnD0P,cAAc,EAAE1S,OAAQ;oBACxBmP,KAAK,EAAE,GAAI;oBAAAI,QAAA,eAEXhR,OAAA,CAACvC,IAAI;sBACHwE,IAAI,EAAEsC,gBAAiB;sBACvBiO,MAAM,EAAC,UAAU;sBAAAxB,QAAA,gBAEjBhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,UAAU;wBACfzH,KAAK,EAAC,0BAAM;wBACZ4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAQ,CAAC,CAAE;wBAAAyT,QAAA,eAE9ChR,OAAA,CAACrC,MAAM;0BACLiV,WAAW,EAAC,gCAAO;0BACnBP,QAAQ,EAAG9R,KAAK,IAAKoE,kBAAkB,CAACpE,KAAK,CAAE;0BAAAyQ,QAAA,EAE9ChO,OAAO,CAACoD,GAAG,CAACmD,GAAG,iBACdvJ,OAAA,CAACI,MAAM;4BAAcG,KAAK,EAAEgJ,GAAG,CAAC/C,EAAG;4BAAAwK,QAAA,GAChCzH,GAAG,CAACtB,IAAI,EAAC,IAAE,EAACsB,GAAG,CAACQ,KAAK,IAAI,OAAO,EAAC,GACpC;0BAAA,GAFaR,GAAG,CAAC/C,EAAE;4BAAA0K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEX,CACT;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,eAAe;wBACpBzH,KAAK,EAAC,0BAAM;wBACZ4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAU,CAAC,CAAE;wBAChDgV,KAAK,EAAC,8JAA2C;wBAAAvB,QAAA,eAEjDhR,OAAA,CAACtC,KAAK,CAACqX,QAAQ;0BACbnC,WAAW,EAAC,oFAA4D;0BACxEoC,IAAI,EAAE,EAAG;0BACT1I,KAAK,EAAE;4BAAE2I,UAAU,EAAE;0BAAY;wBAAE;0BAAA/D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGRrR,OAAA,CAACxC,KAAK;oBACJkL,KAAK,EAAE1D,gBAAgB,GAAG,GAAGA,gBAAgB,CAACiD,IAAI,OAAO,GAAG,MAAO;oBACnEgM,IAAI,EAAErP,yBAA0B;oBAChCsP,QAAQ,EAAEA,CAAA,KAAMrP,4BAA4B,CAAC,KAAK,CAAE;oBACpDkP,MAAM,EAAE,cACN/T,OAAA,CAAC1C,MAAM;sBAAa2T,OAAO,EAAEA,CAAA,KAAMpM,4BAA4B,CAAC,KAAK,CAAE;sBAAAmM,QAAA,EAAC;oBAExE,GAFY,OAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEX,CAAC,CACT;oBACFT,KAAK,EAAE,GAAI;oBAAAI,QAAA,eAEXhR,OAAA,CAAC3C,KAAK;sBACJqT,OAAO,EAAE,CACP;wBACEhI,KAAK,EAAE,IAAI;wBACXiI,SAAS,EAAE,IAAI;wBACfhH,GAAG,EAAE,IAAI;wBACTiH,KAAK,EAAE;sBACT,CAAC,EACD;wBACElI,KAAK,EAAE,IAAI;wBACXiI,SAAS,EAAE,WAAW;wBACtBhH,GAAG,EAAE;sBACP,CAAC,EACD;wBACEjB,KAAK,EAAE,KAAK;wBACZiI,SAAS,EAAE,UAAU;wBACrBhH,GAAG,EAAE;sBACP,CAAC,EACD;wBACEjB,KAAK,EAAE,IAAI;wBACXiB,GAAG,EAAE,QAAQ;wBACbkH,MAAM,EAAEA,CAACE,CAAC,EAAEjH,MAAM,kBAChB9J,OAAA,CAAClC,KAAK;0BAACgX,IAAI,EAAC,QAAQ;0BAAA9D,QAAA,eAClBhR,OAAA,CAAC1C,MAAM;4BACLiU,MAAM;4BACNuD,IAAI,EAAC,OAAO;4BACZ7D,OAAO,EAAEA,CAAA,KAAM;8BACb,IAAIjM,gBAAgB,EAAE;gCACpBxH,KAAK,CAACiL,OAAO,CAAC;kCACZC,KAAK,EAAE,MAAM;kCACbC,OAAO,EAAE,UAAUmB,MAAM,CAACoB,SAAS,IAAIpB,MAAM,CAACgB,QAAQ,WAAW;kCACjEhC,IAAI,EAAE,MAAAA,CAAA,KAAY;oCAChB,IAAI;sCACF,MAAM;wCAAEoM;sCAAuB,CAAC,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC;sCAC/D,MAAMA,sBAAsB,CAAClQ,gBAAgB,CAACwB,EAAE,EAAEsD,MAAM,CAACtD,EAAE,CAAC;sCAC5DjJ,OAAO,CAAC+K,OAAO,CAAC,UAAU,CAAC;sCAC3B+G,iBAAiB,CAACrK,gBAAgB,CAAC;oCACrC,CAAC,CAAC,OAAOkC,KAAK,EAAE;sCACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;sCAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;oCACzB;kCACF;gCACF,CAAC,CAAC;8BACJ;4BACF,CAAE;4BAAA8J,QAAA,EACH;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAEX,CAAC,CACD;sBACFkC,UAAU,EAAEzO,oBAAqB;sBACjC0O,MAAM,EAAC,IAAI;sBACXC,UAAU,EAAE,KAAM;sBAClB0B,MAAM,EAAE;wBAAEC,SAAS,EAAE;sBAAS;oBAAE;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eAGRrR,OAAA,CAACxC,KAAK;oBACJkL,KAAK,EAAC,sCAAQ;oBACduL,IAAI,EAAE/O,yBAA0B;oBAChC4D,IAAI,EAAE8D,oBAAqB;oBAC3BsH,QAAQ,EAAEA,CAAA,KAAM;sBACd/O,4BAA4B,CAAC,KAAK,CAAC;sBACnCG,iBAAiB,CAAC,EAAE,CAAC;sBACrBF,iBAAiB,CAAC4C,WAAW,CAAC,CAAC;oBACjC,CAAE;oBACFmM,cAAc,EAAE1S,OAAQ;oBACxBmP,KAAK,EAAE,GAAI;oBAAAI,QAAA,gBAEXhR,OAAA,CAACvC,IAAI;sBACHwE,IAAI,EAAEmD,iBAAkB;sBACxBoN,MAAM,EAAC,UAAU;sBAAAxB,QAAA,gBAEjBhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,UAAU;wBACfzH,KAAK,EAAC,0BAAM;wBACZ4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAQ,CAAC,CAAE;wBAAAyT,QAAA,eAE9ChR,OAAA,CAACrC,MAAM;0BAACiV,WAAW,EAAC,gCAAO;0BAAA5B,QAAA,EACxBhO,OAAO,CAACoD,GAAG,CAACmD,GAAG,iBACdvJ,OAAA,CAACI,MAAM;4BAAcG,KAAK,EAAEgJ,GAAG,CAAC/C,EAAG;4BAAAwK,QAAA,GAChCzH,GAAG,CAACtB,IAAI,EAAC,IAAE,EAACsB,GAAG,CAACQ,KAAK,IAAI,OAAO,EAAC,GACpC;0BAAA,GAFaR,GAAG,CAAC/C,EAAE;4BAAA0K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEX,CACT;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,MAAM;wBACXzH,KAAK,EAAC,sCAAQ;wBACd4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAY,CAAC,CAAE;wBAClDgV,KAAK,EAAC,8OAA0D;wBAAAvB,QAAA,eAEhEhR,OAAA,CAAC/B,MAAM;0BACLoX,MAAM,EAAC,iBAAiB;0BACxBC,QAAQ,EAAEjQ,cAAe;0BACzBkQ,YAAY,EAAGC,IAAI,IAAK;4BACtBlQ,iBAAiB,CAAC,CAACkQ,IAAI,CAAC,CAAC;4BACzB,OAAO,KAAK;0BACd,CAAE;0BACFC,QAAQ,EAAEA,CAAA,KAAM;4BACdnQ,iBAAiB,CAAC,EAAE,CAAC;0BACvB,CAAE;0BACFoQ,QAAQ,EAAE,CAAE;0BAAA1E,QAAA,eAEZhR,OAAA,CAAC1C,MAAM;4BAACgU,IAAI,eAAEtR,OAAA,CAACrB,cAAc;8BAAAuS,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAE;4BAAAL,QAAA,EAAC;0BAAI;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACPrR,OAAA;sBAAKsM,KAAK,EAAE;wBAAEqJ,SAAS,EAAE;sBAAG,CAAE;sBAAA3E,QAAA,gBAC5BhR,OAAA;wBAAAgR,QAAA,EAAI;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACdrR,OAAA;wBAAAgR,QAAA,gBACEhR,OAAA;0BAAAgR,QAAA,EAAI;wBAAqB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9BrR,OAAA;0BAAAgR,QAAA,EAAI;wBAAqB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9BrR,OAAA;0BAAAgR,QAAA,EAAI;wBAAqB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC9BrR,OAAA;0BAAAgR,QAAA,EAAI;wBAAoB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA,GAlTJ,GAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmTA,CAAC,eAGVrR,OAAA,CAACG,OAAO;kBACNmS,GAAG,eACDtS,OAAA;oBAAAgR,QAAA,gBACEhR,OAAA,CAACxB,YAAY;sBAAA0S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,4BAElB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;kBAAAL,QAAA,gBAGDhR,OAAA;oBAAKsM,KAAK,EAAE;sBAAEmG,YAAY,EAAE;oBAAG,CAAE;oBAAAzB,QAAA,eAC/BhR,OAAA,CAAC1C,MAAM;sBACLwO,IAAI,EAAC,SAAS;sBACdwF,IAAI,eAAEtR,OAAA,CAAC3B,YAAY;wBAAA6S,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACvBJ,OAAO,EAAE3G,gBAAiB;sBAAA0G,QAAA,EAC3B;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNrR,OAAA,CAAC3C,KAAK;oBACJqT,OAAO,EAAE,CACP;sBACEhI,KAAK,EAAE,KAAK;sBACZiI,SAAS,EAAE,UAAU;sBACrBhH,GAAG,EAAE;oBACP,CAAC,EACD;sBACEjB,KAAK,EAAE,IAAI;sBACXiI,SAAS,EAAE,WAAW;sBACtBhH,GAAG,EAAE;oBACP,CAAC,EACD;sBACEjB,KAAK,EAAE,IAAI;sBACXiB,GAAG,EAAE,UAAU;sBACfkH,MAAM,EAAEA,CAACE,CAAC,EAAEjH,MAAM,KAAKhK,eAAe,CAACgK,MAAM;oBAC/C,CAAC,EACD;sBACEpB,KAAK,EAAE,MAAM;sBACbiI,SAAS,EAAE,cAAc;sBACzBhH,GAAG,EAAE,cAAc;sBACnBkH,MAAM,EAAEA,CAACE,CAAC,EAAEjH,MAAM,KAChBA,MAAM,CAAC8L,YAAY,gBAAG5V,OAAA,CAACjC,GAAG;wBAAC8X,KAAK,EAAC,MAAM;wBAAA7E,QAAA,EAAElH,MAAM,CAAC8L,YAAY,CAAC3N;sBAAI;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,gBAAGrR,OAAA,CAACjC,GAAG;wBAAC8X,KAAK,EAAC,SAAS;wBAAA7E,QAAA,EAAC;sBAAG;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK;oBAE5G,CAAC,EACD;sBACE3I,KAAK,EAAE,MAAM;sBACbiI,SAAS,EAAE,OAAO;sBAClBhH,GAAG,EAAE,OAAO;sBACZkH,MAAM,EAAG3N,KAAK,iBACZlD,OAAA,CAAAE,SAAA;wBAAA8Q,QAAA,EACG9N,KAAK,IAAIA,KAAK,CAACkD,GAAG,CAAC0P,IAAI,iBACtB9V,OAAA,CAACjC,GAAG;0BAAC8X,KAAK,EAAC,OAAO;0BAAA7E,QAAA,EACf8E,IAAI,CAAC7N;wBAAI,GADY6N,IAAI,CAACtP,EAAE;0BAAA0K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAE1B,CACN;sBAAC,gBACF;oBAEN,CAAC,EACD;sBACE3I,KAAK,EAAE,IAAI;sBACXiB,GAAG,EAAE,QAAQ;sBACbkH,MAAM,EAAEA,CAACE,CAAC,EAAEjH,MAAM,kBAChB9J,OAAA,CAAClC,KAAK;wBAACgX,IAAI,EAAC,QAAQ;wBAAA9D,QAAA,gBAClBhR,OAAA,CAAC1C,MAAM;0BACLwO,IAAI,EAAC,SAAS;0BACdwF,IAAI,eAAEtR,OAAA,CAACxB,YAAY;4BAAA0S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAE;0BACvByD,IAAI,EAAC,OAAO;0BACZ7D,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAACP,MAAM,CAAE;0BAAAkH,QAAA,EACtC;wBAED;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,EACRvH,MAAM,CAAC5G,KAAK,IAAI4G,MAAM,CAAC5G,KAAK,CAACiE,MAAM,GAAG,CAAC,IAAI2C,MAAM,CAAC5G,KAAK,CAACkD,GAAG,CAAC0P,IAAI,iBAC/D9V,OAAA,CAAC1C,MAAM;0BAELiU,MAAM;0BACNuD,IAAI,EAAC,OAAO;0BACZ7D,OAAO,EAAEA,CAAA,KAAMd,gBAAgB,CAACrG,MAAM,CAACtD,EAAE,EAAEsP,IAAI,CAACtP,EAAE,CAAE;0BAAAwK,QAAA,GACrD,eACI,EAAC8E,IAAI,CAAC7N,IAAI;wBAAA,GALR,GAAG6B,MAAM,CAACtD,EAAE,IAAIsP,IAAI,CAACtP,EAAE,EAAE;0BAAA0K,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAMxB,CACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG;oBAEX,CAAC,CACD;oBACFkC,UAAU,EAAEnQ,KAAM;oBAClBoQ,MAAM,EAAG1J,MAAM,IAAK,QAAQA,MAAM,CAACtD,EAAE,EAAG;oBACxC/E,OAAO,EAAEA;kBAAQ;oBAAAyP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eAGFrR,OAAA,CAACxC,KAAK;oBACJkL,KAAK,EAAC,0BAAM;oBACZuL,IAAI,EAAErQ,gBAAiB;oBACvBkF,IAAI,EAAE2B,gBAAiB;oBACvByJ,QAAQ,EAAE1J,gBAAiB;oBAC3B2J,cAAc,EAAE1S,OAAQ;oBAAAuP,QAAA,EAEvB9M,WAAW,iBACVlE,OAAA,CAACvC,IAAI;sBACHwE,IAAI,EAAEoC,QAAS;sBACfmO,MAAM,EAAC,UAAU;sBAAAxB,QAAA,gBAEjBhR,OAAA;wBAAAgR,QAAA,GAAG,qBAAI,eAAAhR,OAAA;0BAAAgR,QAAA,EAAS9M,WAAW,CAACgH,SAAS,IAAIhH,WAAW,CAAC4G;wBAAQ;0BAAAoG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,mCAAM;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACjFrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,SAAS;wBACdzH,KAAK,EAAC,cAAI;wBACV4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAQ,CAAC,CAAE;wBAAAyT,QAAA,eAE9ChR,OAAA,CAACrC,MAAM;0BAACiV,WAAW,EAAC,gCAAO;0BAAA5B,QAAA,GAExB9N,KAAK,CAACkD,GAAG,CAAC0P,IAAI,iBACb9V,OAAA,CAACI,MAAM;4BAAeG,KAAK,EAAEuV,IAAI,CAACtP,EAAG;4BAAAwK,QAAA,GAClC8E,IAAI,CAAC7N,IAAI,EAAC,IAAE,EAAC6N,IAAI,CAACC,WAAW,IAAID,IAAI,CAACE,IAAI,EAAC,GAC9C;0BAAA,GAFaF,IAAI,CAACtP,EAAE;4BAAA0K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEZ,CACT,CAAC,EAED,CAAC,CAACnO,KAAK,IAAIA,KAAK,CAACiE,MAAM,KAAK,CAAC,KAAK8O,MAAM,CAAC9N,MAAM,CAAC9H,UAAU,CAAC,CAAC+F,GAAG,CAAC0P,IAAI,iBACnE9V,OAAA,CAACI,MAAM;4BAAkBG,KAAK,EAAEuV,IAAI,CAACvV,KAAM;4BAAAyQ,QAAA,EACxC8E,IAAI,CAACtV;0BAAK,GADAsV,IAAI,CAACvV,KAAK;4BAAA2Q,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEf,CACT,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBACP;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAGRrR,OAAA,CAACxC,KAAK;oBACJkL,KAAK,EAAC,0BAAM;oBACZuL,IAAI,EAAEnQ,mBAAoB;oBAC1BgF,IAAI,EAAE8B,aAAc;oBACpBsJ,QAAQ,EAAEA,CAAA,KAAMnQ,sBAAsB,CAAC,KAAK,CAAE;oBAC9CoQ,cAAc,EAAE1S,OAAQ;oBACxBmP,KAAK,EAAE,GAAI;oBAAAI,QAAA,eAEXhR,OAAA,CAACvC,IAAI;sBACHwE,IAAI,EAAEqC,QAAS;sBACfkO,MAAM,EAAC,UAAU;sBACjB8B,aAAa,EAAE;wBAAE/J,SAAS,EAAE;sBAAI,CAAE;sBAAAyG,QAAA,gBAElChR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBAAC1K,IAAI,EAAC,WAAW;wBAACiO,MAAM;wBAAAlF,QAAA,eAChChR,OAAA,CAACtC,KAAK;0BAAAwT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACZrR,OAAA,CAACpC,IAAI;wBACHuY,gBAAgB,EAAC,GAAG;wBACpB9D,QAAQ,EAAG1I,GAAG,IAAKrF,QAAQ,CAACoD,cAAc,CAAC;0BAAE6C,SAAS,EAAEZ;wBAAI,CAAC,CAAE;wBAAAqH,QAAA,gBAE/DhR,OAAA,CAACG,OAAO;0BAACmS,GAAG,EAAC,sCAAQ;0BAAAtB,QAAA,eACnBhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BACR1K,IAAI,EAAC,SAAS;4BACdzH,KAAK,EAAC,0BAAM;4BACZ4T,KAAK,EAAE,CAAC;8BAAEC,QAAQ,EAAE,IAAI;8BAAE9W,OAAO,EAAE;4BAAQ,CAAC,CAAE;4BAAAyT,QAAA,eAE9ChR,OAAA,CAACrC,MAAM;8BACLiV,WAAW,EAAC,gCAAO;8BACnBG,UAAU;8BACVE,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAACnC,QAAQ,IAAI,OAAOmC,MAAM,CAACnC,QAAQ,KAAK,QAAQ,GACtDmC,MAAM,CAACnC,QAAQ,CAACe,WAAW,CAAC,CAAC,CAACqB,OAAO,CAACF,KAAK,CAACnB,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;8BAAAf,QAAA,EAEA1N,QAAQ,CACNgE,MAAM,CAAChG,IAAI,IAAI,CAAC8B,KAAK,CAACgT,IAAI,CAACC,YAAY,IAAIA,YAAY,CAAC7P,EAAE,KAAKlF,IAAI,CAACkF,EAAE,CAAC,CAAC,CACxEJ,GAAG,CAAC9E,IAAI,iBACPtB,OAAA,CAACI,MAAM;gCAAeG,KAAK,EAAEe,IAAI,CAACkF,EAAG;gCAAAwK,QAAA,GAClC1P,IAAI,CAAC4J,SAAS,IAAI5J,IAAI,CAACwJ,QAAQ,EAAC,IAAE,EAACxJ,IAAI,CAACwJ,QAAQ,EAAC,GACpD;8BAAA,GAFaxJ,IAAI,CAACkF,EAAE;gCAAA0K,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAEZ,CACT;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAEE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA;wBAAC,GAvBY,GAAG;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAwBpB,CAAC,eACVrR,OAAA,CAACG,OAAO;0BAACmS,GAAG,EAAC,gCAAO;0BAAAtB,QAAA,gBAClBhR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BACR1K,IAAI,EAAC,UAAU;4BACfzH,KAAK,EAAC,oBAAK;4BACX4T,KAAK,EAAE,CAAC;8BAAEC,QAAQ,EAAE,IAAI;8BAAE9W,OAAO,EAAE;4BAAS,CAAC,CAAE;4BAAAyT,QAAA,eAE/ChR,OAAA,CAACtC,KAAK;8BAACkV,WAAW,EAAC;4BAAQ;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BACR1K,IAAI,EAAC,UAAU;4BACfzH,KAAK,EAAC,cAAI;4BACV4T,KAAK,EAAE,CAAC;8BAAEC,QAAQ,EAAE,IAAI;8BAAE9W,OAAO,EAAE;4BAAQ,CAAC,CAAE;4BAAAyT,QAAA,eAE9ChR,OAAA,CAACtC,KAAK,CAAC4Y,QAAQ;8BAAC1D,WAAW,EAAC;4BAAO;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC7B,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BACR1K,IAAI,EAAC,WAAW;4BAChBzH,KAAK,EAAC,cAAI;4BACV4T,KAAK,EAAE,CAAC;8BAAEC,QAAQ,EAAE,IAAI;8BAAE9W,OAAO,EAAE;4BAAQ,CAAC,CAAE;4BAAAyT,QAAA,eAE9ChR,OAAA,CAACtC,KAAK;8BAACkV,WAAW,EAAC;4BAAO;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpB,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BACR1K,IAAI,EAAC,OAAO;4BACZzH,KAAK,EAAC,cAAI;4BAAAwQ,QAAA,eAEVhR,OAAA,CAACtC,KAAK;8BAACkV,WAAW,EAAC;4BAAO;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpB,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BACR1K,IAAI,EAAC,OAAO;4BACZzH,KAAK,EAAC,cAAI;4BAAAwQ,QAAA,eAEVhR,OAAA,CAACtC,KAAK;8BAACkV,WAAW,EAAC;4BAAO;8BAAA1B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpB,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BAAC1K,IAAI,EAAC,YAAY;4BAACsO,aAAa,EAAC,SAAS;4BAAAvF,QAAA,eAClDhR,OAAA,CAAC9B,QAAQ;8BAAA8S,QAAA,EAAC;4BAAG;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf,CAAC,eACZrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;4BAAC1K,IAAI,EAAC,UAAU;4BAACsO,aAAa,EAAC,SAAS;4BAAAvF,QAAA,eAChDhR,OAAA,CAAC9B,QAAQ;8BAAA8S,QAAA,EAAC;4BAAI;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAU;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB,CAAC;wBAAA,GAvCW,GAAG;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAwCnB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACPrR,OAAA,CAAChC,OAAO;wBAAAkT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACXrR,OAAA,CAACvC,IAAI,CAACkV,IAAI;wBACR1K,IAAI,EAAC,SAAS;wBACdzH,KAAK,EAAC,0BAAM;wBACZ4T,KAAK,EAAE,CAAC;0BAAEC,QAAQ,EAAE,IAAI;0BAAE9W,OAAO,EAAE;wBAAQ,CAAC,CAAE;wBAAAyT,QAAA,eAE9ChR,OAAA,CAACrC,MAAM;0BAACiV,WAAW,EAAC,gCAAO;0BAAA5B,QAAA,GAExB9N,KAAK,CAACkD,GAAG,CAAC0P,IAAI,iBACb9V,OAAA,CAACI,MAAM;4BAAeG,KAAK,EAAEuV,IAAI,CAACtP,EAAG;4BAAAwK,QAAA,GAClC8E,IAAI,CAAC7N,IAAI,EAAC,IAAE,EAAC6N,IAAI,CAACC,WAAW,IAAID,IAAI,CAACE,IAAI,EAAC,GAC9C;0BAAA,GAFaF,IAAI,CAACtP,EAAE;4BAAA0K,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEZ,CACT,CAAC,EAED,CAAC,CAACnO,KAAK,IAAIA,KAAK,CAACiE,MAAM,KAAK,CAAC,KAAK8O,MAAM,CAAC9N,MAAM,CAAC9H,UAAU,CAAC,CAAC+F,GAAG,CAAC0P,IAAI,iBACnE9V,OAAA,CAACI,MAAM;4BAAkBG,KAAK,EAAEuV,IAAI,CAACvV,KAAM;4BAAAyQ,QAAA,EACxC8E,IAAI,CAACtV;0BAAK,GADAsV,IAAI,CAACvV,KAAK;4BAAA2Q,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEf,CACT,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA,GAtOJ,GAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuOA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAENrR,OAAA;UAAKoS,SAAS,EAAC,oBAAoB;UAAApB,QAAA,gBACjChR,OAAA;YAAAgR,QAAA,EAAI;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBrR,OAAA,CAAC1C,MAAM;YACLwO,IAAI,EAAC,SAAS;YACdmF,OAAO,EAAEA,CAAA,KAAMV,YAAY,CAAC,GAAG,CAAE;YAAAS,QAAA,EAClC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACN,GA9qB6E,GAAG;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+qB1E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChQ,EAAA,CAvrEID,qBAAqB;EAAA,QACRvB,OAAO,EAMTpC,IAAI,CAACyE,OAAO,EAMNzE,IAAI,CAACyE,OAAO,EAgBbzE,IAAI,CAACyE,OAAO,EACbzE,IAAI,CAACyE,OAAO,EACZzE,IAAI,CAACyE,OAAO,EACJzE,IAAI,CAACyE,OAAO,EAOXzE,IAAI,CAACyE,OAAO,EAEbzE,IAAI,CAACyE,OAAO;AAAA;AAAAsU,EAAA,GAzCnCpV,qBAAqB;AAyrE3B,eAAeA,qBAAqB;AAAC,IAAAoV,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}