{"ast": null, "code": "\"use strict\";\n\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { clamp } from \"./utils/innerSliderUtils\";\nvar getDotCount = function getDotCount(spec) {\n  var dots;\n  if (spec.infinite) {\n    dots = Math.ceil(spec.slideCount / spec.slidesToScroll);\n  } else {\n    dots = Math.ceil((spec.slideCount - spec.slidesToShow) / spec.slidesToScroll) + 1;\n  }\n  return dots;\n};\nexport var Dots = /*#__PURE__*/function (_React$PureComponent) {\n  function Dots() {\n    _classCallCheck(this, Dots);\n    return _callSuper(this, Dots, arguments);\n  }\n  _inherits(Dots, _React$PureComponent);\n  return _createClass(Dots, [{\n    key: \"clickHandler\",\n    value: function clickHandler(options, e) {\n      // In Autoplay the focus stays on clicked button even after transition\n      // to next slide. That only goes away by click somewhere outside\n      e.preventDefault();\n      this.props.clickHandler(options);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        onMouseOver = _this$props.onMouseOver,\n        onMouseLeave = _this$props.onMouseLeave,\n        infinite = _this$props.infinite,\n        slidesToScroll = _this$props.slidesToScroll,\n        slidesToShow = _this$props.slidesToShow,\n        slideCount = _this$props.slideCount,\n        currentSlide = _this$props.currentSlide;\n      var dotCount = getDotCount({\n        slideCount: slideCount,\n        slidesToScroll: slidesToScroll,\n        slidesToShow: slidesToShow,\n        infinite: infinite\n      });\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      var dots = [];\n      for (var i = 0; i < dotCount; i++) {\n        var _rightBound = (i + 1) * slidesToScroll - 1;\n        var rightBound = infinite ? _rightBound : clamp(_rightBound, 0, slideCount - 1);\n        var _leftBound = rightBound - (slidesToScroll - 1);\n        var leftBound = infinite ? _leftBound : clamp(_leftBound, 0, slideCount - 1);\n        var className = classnames({\n          \"slick-active\": infinite ? currentSlide >= leftBound && currentSlide <= rightBound : currentSlide === leftBound\n        });\n        var dotOptions = {\n          message: \"dots\",\n          index: i,\n          slidesToScroll: slidesToScroll,\n          currentSlide: currentSlide\n        };\n        var onClick = this.clickHandler.bind(this, dotOptions);\n        dots = dots.concat(/*#__PURE__*/React.createElement(\"li\", {\n          key: i,\n          className: className\n        }, /*#__PURE__*/React.cloneElement(this.props.customPaging(i), {\n          onClick: onClick\n        })));\n      }\n      return /*#__PURE__*/React.cloneElement(this.props.appendDots(dots), _objectSpread({\n        className: this.props.dotsClass\n      }, mouseEvents));\n    }\n  }]);\n}(React.PureComponent);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}