{"ast": null, "code": "import { QrCode, QrSegment } from \"../libs/qrcodegen\";\nimport { ERROR_LEVEL_MAP, getImageSettings, getMarginSize } from \"../utils\";\nimport { useMemo } from 'react';\nexport function useQRCode(_ref) {\n  var value = _ref.value,\n    level = _ref.level,\n    minVersion = _ref.minVersion,\n    includeMargin = _ref.includeMargin,\n    marginSize = _ref.marginSize,\n    imageSettings = _ref.imageSettings,\n    size = _ref.size;\n  var qrcode = useMemo(function () {\n    var segments = QrSegment.makeSegments(value);\n    return QrCode.encodeSegments(segments, ERROR_LEVEL_MAP[level], minVersion);\n  }, [value, level, minVersion]);\n  var _useMemo = useMemo(function () {\n      var cs = qrcode.getModules();\n      var mg = getMarginSize(includeMargin, marginSize);\n      var ncs = cs.length + mg * 2;\n      var cis = getImageSettings(cs, size, mg, imageSettings);\n      return {\n        cells: cs,\n        margin: mg,\n        numCells: ncs,\n        calculatedImageSettings: cis\n      };\n    }, [qrcode, size, imageSettings, includeMargin, marginSize]),\n    cells = _useMemo.cells,\n    margin = _useMemo.margin,\n    numCells = _useMemo.numCells,\n    calculatedImageSettings = _useMemo.calculatedImageSettings;\n  return {\n    qrcode: qrcode,\n    margin: margin,\n    cells: cells,\n    numCells: numCells,\n    calculatedImageSettings: calculatedImageSettings\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}