{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusStyle, resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBreadcrumbStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    calc\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      color: token.itemColor,\n      fontSize: token.fontSize,\n      [iconCls]: {\n        fontSize: token.iconFontSize\n      },\n      ol: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        margin: 0,\n        padding: 0,\n        listStyle: 'none'\n      },\n      a: Object.assign({\n        color: token.linkColor,\n        transition: `color ${token.motionDurationMid}`,\n        padding: `0 ${unit(token.paddingXXS)}`,\n        borderRadius: token.borderRadiusSM,\n        height: token.fontHeight,\n        display: 'inline-block',\n        marginInline: calc(token.marginXXS).mul(-1).equal(),\n        '&:hover': {\n          color: token.linkHoverColor,\n          backgroundColor: token.colorBgTextHover\n        }\n      }, genFocusStyle(token)),\n      'li:last-child': {\n        color: token.lastItemColor\n      },\n      [`${componentCls}-separator`]: {\n        marginInline: token.separatorMargin,\n        color: token.separatorColor\n      },\n      [`${componentCls}-link`]: {\n        [`\n          > ${iconCls} + span,\n          > ${iconCls} + a\n        `]: {\n          marginInlineStart: token.marginXXS\n        }\n      },\n      [`${componentCls}-overlay-link`]: {\n        borderRadius: token.borderRadiusSM,\n        height: token.fontHeight,\n        display: 'inline-block',\n        padding: `0 ${unit(token.paddingXXS)}`,\n        marginInline: calc(token.marginXXS).mul(-1).equal(),\n        [`> ${iconCls}`]: {\n          marginInlineStart: token.marginXXS,\n          fontSize: token.fontSizeIcon\n        },\n        '&:hover': {\n          color: token.linkHoverColor,\n          backgroundColor: token.colorBgTextHover,\n          a: {\n            color: token.linkHoverColor\n          }\n        },\n        a: {\n          '&:hover': {\n            backgroundColor: 'transparent'\n          }\n        }\n      },\n      // rtl style\n      [`&${token.componentCls}-rtl`]: {\n        direction: 'rtl'\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  itemColor: token.colorTextDescription,\n  lastItemColor: token.colorText,\n  iconFontSize: token.fontSize,\n  linkColor: token.colorTextDescription,\n  linkHoverColor: token.colorText,\n  separatorColor: token.colorTextDescription,\n  separatorMargin: token.marginXS\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Breadcrumb', token => {\n  const breadcrumbToken = mergeToken(token, {});\n  return genBreadcrumbStyle(breadcrumbToken);\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}