{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SmallDashOutlinedSvg from \"@ant-design/icons-svg/es/asn/SmallDashOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SmallDashOutlined = function SmallDashOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SmallDashOutlinedSvg\n  }));\n};\n\n/**![small-dash](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTExMiA0NzZoNzJ2NzJoLTcyem0xODIgMGg3MnY3MmgtNzJ6bTM2NCAwaDcydjcyaC03MnptMTgyIDBoNzJ2NzJoLTcyem0tMzY0IDBoNzJ2NzJoLTcyeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SmallDashOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SmallDashOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}