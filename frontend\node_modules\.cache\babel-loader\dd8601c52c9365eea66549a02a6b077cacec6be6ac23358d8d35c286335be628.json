{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useSize from '../config-provider/hooks/useSize';\nimport useStyle from './style';\nconst sizeClassNameMap = {\n  small: 'sm',\n  middle: 'md'\n};\nconst Divider = props => {\n  const {\n    getPrefixCls,\n    direction,\n    className: dividerClassName,\n    style: dividerStyle\n  } = useComponentConfig('divider');\n  const {\n      prefixCls: customizePrefixCls,\n      type = 'horizontal',\n      orientation = 'center',\n      orientationMargin,\n      className,\n      rootClassName,\n      children,\n      dashed,\n      variant = 'solid',\n      plain,\n      style,\n      size: customSize\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"type\", \"orientation\", \"orientationMargin\", \"className\", \"rootClassName\", \"children\", \"dashed\", \"variant\", \"plain\", \"style\", \"size\"]);\n  const prefixCls = getPrefixCls('divider', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const sizeFullName = useSize(customSize);\n  const sizeCls = sizeClassNameMap[sizeFullName];\n  const hasChildren = !!children;\n  const mergedOrientation = React.useMemo(() => {\n    if (orientation === 'left') {\n      return direction === 'rtl' ? 'end' : 'start';\n    }\n    if (orientation === 'right') {\n      return direction === 'rtl' ? 'start' : 'end';\n    }\n    return orientation;\n  }, [direction, orientation]);\n  const hasMarginStart = mergedOrientation === 'start' && orientationMargin != null;\n  const hasMarginEnd = mergedOrientation === 'end' && orientationMargin != null;\n  const classString = classNames(prefixCls, dividerClassName, hashId, cssVarCls, `${prefixCls}-${type}`, {\n    [`${prefixCls}-with-text`]: hasChildren,\n    [`${prefixCls}-with-text-${mergedOrientation}`]: hasChildren,\n    [`${prefixCls}-dashed`]: !!dashed,\n    [`${prefixCls}-${variant}`]: variant !== 'solid',\n    [`${prefixCls}-plain`]: !!plain,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-no-default-orientation-margin-start`]: hasMarginStart,\n    [`${prefixCls}-no-default-orientation-margin-end`]: hasMarginEnd,\n    [`${prefixCls}-${sizeCls}`]: !!sizeCls\n  }, className, rootClassName);\n  const memoizedOrientationMargin = React.useMemo(() => {\n    if (typeof orientationMargin === 'number') {\n      return orientationMargin;\n    }\n    if (/^\\d+$/.test(orientationMargin)) {\n      return Number(orientationMargin);\n    }\n    return orientationMargin;\n  }, [orientationMargin]);\n  const innerStyle = {\n    marginInlineStart: hasMarginStart ? memoizedOrientationMargin : undefined,\n    marginInlineEnd: hasMarginEnd ? memoizedOrientationMargin : undefined\n  };\n  // Warning children not work in vertical mode\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Divider');\n    process.env.NODE_ENV !== \"production\" ? warning(!children || type !== 'vertical', 'usage', '`children` not working in `vertical` mode.') : void 0;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    className: classString,\n    style: Object.assign(Object.assign({}, dividerStyle), style)\n  }, restProps, {\n    role: \"separator\"\n  }), children && type !== 'vertical' && (/*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-inner-text`,\n    style: innerStyle\n  }, children))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Divider.displayName = 'Divider';\n}\nexport default Divider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}