{"ast": null, "code": "import React from 'react';\nimport useToken from '../../theme/useToken';\nimport { devUseWarning } from '../warning';\nimport zIndexContext from '../zindexContext';\n// Z-Index control range\n// Container: 1000 + offset 100 (max base + 10 * offset = 2000)\n// Popover: offset 50\n// Notification: Container Max zIndex + componentOffset\nconst CONTAINER_OFFSET = 100;\nconst CONTAINER_OFFSET_MAX_COUNT = 10;\nexport const CONTAINER_MAX_OFFSET = CONTAINER_OFFSET * CONTAINER_OFFSET_MAX_COUNT;\n/**\n * Static function will default be the `CONTAINER_MAX_OFFSET`.\n * But it still may have children component like Select, Dropdown.\n * So the warning zIndex should exceed the `CONTAINER_MAX_OFFSET`.\n */\nconst CONTAINER_MAX_OFFSET_WITH_CHILDREN = CONTAINER_MAX_OFFSET + CONTAINER_OFFSET;\nexport const containerBaseZIndexOffset = {\n  Modal: CONTAINER_OFFSET,\n  Drawer: CONTAINER_OFFSET,\n  Popover: CONTAINER_OFFSET,\n  Popconfirm: CONTAINER_OFFSET,\n  Tooltip: CONTAINER_OFFSET,\n  Tour: CONTAINER_OFFSET,\n  FloatButton: CONTAINER_OFFSET\n};\nexport const consumerBaseZIndexOffset = {\n  SelectLike: 50,\n  Dropdown: 50,\n  DatePicker: 50,\n  Menu: 50,\n  ImagePreview: 1\n};\nfunction isContainerType(type) {\n  return type in containerBaseZIndexOffset;\n}\nexport const useZIndex = (componentType, customZIndex) => {\n  const [, token] = useToken();\n  const parentZIndex = React.useContext(zIndexContext);\n  const isContainer = isContainerType(componentType);\n  let result;\n  if (customZIndex !== undefined) {\n    result = [customZIndex, customZIndex];\n  } else {\n    let zIndex = parentZIndex !== null && parentZIndex !== void 0 ? parentZIndex : 0;\n    if (isContainer) {\n      zIndex +=\n      // Use preset token zIndex by default but not stack when has parent container\n      (parentZIndex ? 0 : token.zIndexPopupBase) +\n      // Container offset\n      containerBaseZIndexOffset[componentType];\n    } else {\n      zIndex += consumerBaseZIndexOffset[componentType];\n    }\n    result = [parentZIndex === undefined ? customZIndex : zIndex, zIndex];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning(componentType);\n    const maxZIndex = token.zIndexPopupBase + CONTAINER_MAX_OFFSET_WITH_CHILDREN;\n    const currentZIndex = result[0] || 0;\n    process.env.NODE_ENV !== \"production\" ? warning(customZIndex !== undefined || currentZIndex <= maxZIndex, 'usage', '`zIndex` is over design token `zIndexPopupBase` too much. It may cause unexpected override.') : void 0;\n  }\n  return result;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}