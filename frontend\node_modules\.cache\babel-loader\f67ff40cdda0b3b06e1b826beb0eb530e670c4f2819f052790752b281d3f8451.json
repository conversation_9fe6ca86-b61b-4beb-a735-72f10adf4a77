{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genPlaceholderStyle, initInputToken } from '../../input/style';\nimport { resetComponent, textEllipsis } from '../../style';\nimport { genCompactItemStyle } from '../../style/compact-item';\nimport { initMoveMotion, initSlideMotion, slideDownIn, slideDownOut, slideUpIn, slideUpOut } from '../../style/motion';\nimport { genRoundedArrow } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genPickerMultipleStyle from './multiple';\nimport genPickerPanelStyle, { genPanelStyle } from './panel';\nimport { initPanelComponentToken, initPickerPanelToken, prepareComponentToken } from './token';\nimport genVariantsStyle from './variants';\nexport { initPickerPanelToken, initPanelComponentToken, genPanelStyle };\nconst genPickerPadding = (paddingBlock, paddingInline) => {\n  return {\n    padding: `${unit(paddingBlock)} ${unit(paddingInline)}`\n  };\n};\nconst genPickerStatusStyle = token => {\n  const {\n    componentCls,\n    colorError,\n    colorWarning\n  } = token;\n  return {\n    [`${componentCls}:not(${componentCls}-disabled):not([disabled])`]: {\n      [`&${componentCls}-status-error`]: {\n        [`${componentCls}-active-bar`]: {\n          background: colorError\n        }\n      },\n      [`&${componentCls}-status-warning`]: {\n        [`${componentCls}-active-bar`]: {\n          background: colorWarning\n        }\n      }\n    }\n  };\n};\nconst genPickerStyle = token => {\n  var _a;\n  const {\n    componentCls,\n    antCls,\n    paddingInline,\n    lineWidth,\n    lineType,\n    colorBorder,\n    borderRadius,\n    motionDurationMid,\n    colorTextDisabled,\n    colorTextPlaceholder,\n    fontSizeLG,\n    inputFontSizeLG,\n    fontSizeSM,\n    inputFontSizeSM,\n    controlHeightSM,\n    paddingInlineSM,\n    paddingXS,\n    marginXS,\n    colorIcon,\n    lineWidthBold,\n    colorPrimary,\n    motionDurationSlow,\n    zIndexPopup,\n    paddingXXS,\n    sizePopupArrow,\n    colorBgElevated,\n    borderRadiusLG,\n    boxShadowSecondary,\n    borderRadiusSM,\n    colorSplit,\n    cellHoverBg,\n    presetsWidth,\n    presetsMaxWidth,\n    boxShadowPopoverArrow,\n    fontHeight,\n    lineHeightLG\n  } = token;\n  return [{\n    [componentCls]: Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genPickerPadding(token.paddingBlock, token.paddingInline)), {\n      position: 'relative',\n      display: 'inline-flex',\n      alignItems: 'center',\n      lineHeight: 1,\n      borderRadius,\n      transition: `border ${motionDurationMid}, box-shadow ${motionDurationMid}, background ${motionDurationMid}`,\n      [`${componentCls}-prefix`]: {\n        flex: '0 0 auto',\n        marginInlineEnd: token.inputAffixPadding\n      },\n      // ======================== Input =========================\n      [`${componentCls}-input`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        alignItems: 'center',\n        width: '100%',\n        '> input': Object.assign(Object.assign({\n          position: 'relative',\n          display: 'inline-block',\n          width: '100%',\n          color: 'inherit',\n          fontSize: (_a = token.inputFontSize) !== null && _a !== void 0 ? _a : token.fontSize,\n          lineHeight: token.lineHeight,\n          transition: `all ${motionDurationMid}`\n        }, genPlaceholderStyle(colorTextPlaceholder)), {\n          flex: 'auto',\n          // Fix Firefox flex not correct:\n          // https://github.com/ant-design/ant-design/pull/20023#issuecomment-564389553\n          minWidth: 1,\n          height: 'auto',\n          padding: 0,\n          background: 'transparent',\n          border: 0,\n          fontFamily: 'inherit',\n          '&:focus': {\n            boxShadow: 'none',\n            outline: 0\n          },\n          '&[disabled]': {\n            background: 'transparent',\n            color: colorTextDisabled,\n            cursor: 'not-allowed'\n          }\n        }),\n        '&-placeholder': {\n          '> input': {\n            color: colorTextPlaceholder\n          }\n        }\n      },\n      // Size\n      '&-large': Object.assign(Object.assign({}, genPickerPadding(token.paddingBlockLG, token.paddingInlineLG)), {\n        [`${componentCls}-input > input`]: {\n          fontSize: inputFontSizeLG !== null && inputFontSizeLG !== void 0 ? inputFontSizeLG : fontSizeLG,\n          lineHeight: lineHeightLG\n        }\n      }),\n      '&-small': Object.assign(Object.assign({}, genPickerPadding(token.paddingBlockSM, token.paddingInlineSM)), {\n        [`${componentCls}-input > input`]: {\n          fontSize: inputFontSizeSM !== null && inputFontSizeSM !== void 0 ? inputFontSizeSM : fontSizeSM\n        }\n      }),\n      [`${componentCls}-suffix`]: {\n        display: 'flex',\n        flex: 'none',\n        alignSelf: 'center',\n        marginInlineStart: token.calc(paddingXS).div(2).equal(),\n        color: colorTextDisabled,\n        lineHeight: 1,\n        pointerEvents: 'none',\n        transition: `opacity ${motionDurationMid}, color ${motionDurationMid}`,\n        '> *': {\n          verticalAlign: 'top',\n          '&:not(:last-child)': {\n            marginInlineEnd: marginXS\n          }\n        }\n      },\n      [`${componentCls}-clear`]: {\n        position: 'absolute',\n        top: '50%',\n        insetInlineEnd: 0,\n        color: colorTextDisabled,\n        lineHeight: 1,\n        transform: 'translateY(-50%)',\n        cursor: 'pointer',\n        opacity: 0,\n        transition: `opacity ${motionDurationMid}, color ${motionDurationMid}`,\n        '> *': {\n          verticalAlign: 'top'\n        },\n        '&:hover': {\n          color: colorIcon\n        }\n      },\n      '&:hover': {\n        [`${componentCls}-clear`]: {\n          opacity: 1\n        },\n        // Should use the following selector, but since `:has` has poor compatibility,\n        // we use `:not(:last-child)` instead, which may cause some problems in some cases.\n        // [`${componentCls}-suffix:has(+ ${componentCls}-clear)`]: {\n        [`${componentCls}-suffix:not(:last-child)`]: {\n          opacity: 0\n        }\n      },\n      [`${componentCls}-separator`]: {\n        position: 'relative',\n        display: 'inline-block',\n        width: '1em',\n        height: fontSizeLG,\n        color: colorTextDisabled,\n        fontSize: fontSizeLG,\n        verticalAlign: 'top',\n        cursor: 'default',\n        [`${componentCls}-focused &`]: {\n          color: colorIcon\n        },\n        [`${componentCls}-range-separator &`]: {\n          [`${componentCls}-disabled &`]: {\n            cursor: 'not-allowed'\n          }\n        }\n      },\n      // ======================== Range =========================\n      '&-range': {\n        position: 'relative',\n        display: 'inline-flex',\n        // Active bar\n        [`${componentCls}-active-bar`]: {\n          bottom: token.calc(lineWidth).mul(-1).equal(),\n          height: lineWidthBold,\n          background: colorPrimary,\n          opacity: 0,\n          transition: `all ${motionDurationSlow} ease-out`,\n          pointerEvents: 'none'\n        },\n        [`&${componentCls}-focused`]: {\n          [`${componentCls}-active-bar`]: {\n            opacity: 1\n          }\n        },\n        [`${componentCls}-range-separator`]: {\n          alignItems: 'center',\n          padding: `0 ${unit(paddingXS)}`,\n          lineHeight: 1\n        }\n      },\n      // ======================== Clear =========================\n      '&-range, &-multiple': {\n        // Clear\n        [`${componentCls}-clear`]: {\n          insetInlineEnd: paddingInline\n        },\n        [`&${componentCls}-small`]: {\n          [`${componentCls}-clear`]: {\n            insetInlineEnd: paddingInlineSM\n          }\n        }\n      },\n      // ======================= Dropdown =======================\n      '&-dropdown': Object.assign(Object.assign(Object.assign({}, resetComponent(token)), genPanelStyle(token)), {\n        pointerEvents: 'none',\n        position: 'absolute',\n        // Fix incorrect position of picker popup\n        // https://github.com/ant-design/ant-design/issues/35590\n        top: -9999,\n        left: {\n          _skip_check_: true,\n          value: -9999\n        },\n        zIndex: zIndexPopup,\n        [`&${componentCls}-dropdown-hidden`]: {\n          display: 'none'\n        },\n        '&-rtl': {\n          direction: 'rtl'\n        },\n        [`&${componentCls}-dropdown-placement-bottomLeft,\n            &${componentCls}-dropdown-placement-bottomRight`]: {\n          [`${componentCls}-range-arrow`]: {\n            top: 0,\n            display: 'block',\n            transform: 'translateY(-100%)'\n          }\n        },\n        [`&${componentCls}-dropdown-placement-topLeft,\n            &${componentCls}-dropdown-placement-topRight`]: {\n          [`${componentCls}-range-arrow`]: {\n            bottom: 0,\n            display: 'block',\n            transform: 'translateY(100%) rotate(180deg)'\n          }\n        },\n        [`&${antCls}-slide-up-appear, &${antCls}-slide-up-enter`]: {\n          [`${componentCls}-range-arrow${componentCls}-range-arrow`]: {\n            transition: 'none'\n          }\n        },\n        [`&${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-topLeft,\n          &${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-topRight,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-topLeft,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-topRight`]: {\n          animationName: slideDownIn\n        },\n        [`&${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-bottomLeft,\n          &${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-bottomRight,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-bottomLeft,\n          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-bottomRight`]: {\n          animationName: slideUpIn\n        },\n        // https://github.com/ant-design/ant-design/issues/48727\n        [`&${antCls}-slide-up-leave ${componentCls}-panel-container`]: {\n          pointerEvents: 'none'\n        },\n        [`&${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-topLeft,\n          &${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-topRight`]: {\n          animationName: slideDownOut\n        },\n        [`&${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-bottomLeft,\n          &${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-bottomRight`]: {\n          animationName: slideUpOut\n        },\n        // Time picker with additional style\n        [`${componentCls}-panel > ${componentCls}-time-panel`]: {\n          paddingTop: paddingXXS\n        },\n        // ======================== Ranges ========================\n        [`${componentCls}-range-wrapper`]: {\n          display: 'flex',\n          position: 'relative'\n        },\n        [`${componentCls}-range-arrow`]: Object.assign(Object.assign({\n          position: 'absolute',\n          zIndex: 1,\n          display: 'none',\n          paddingInline: token.calc(paddingInline).mul(1.5).equal(),\n          boxSizing: 'content-box',\n          transition: `all ${motionDurationSlow} ease-out`\n        }, genRoundedArrow(token, colorBgElevated, boxShadowPopoverArrow)), {\n          '&:before': {\n            insetInlineStart: token.calc(paddingInline).mul(1.5).equal()\n          }\n        }),\n        [`${componentCls}-panel-container`]: {\n          overflow: 'hidden',\n          verticalAlign: 'top',\n          background: colorBgElevated,\n          borderRadius: borderRadiusLG,\n          boxShadow: boxShadowSecondary,\n          transition: `margin ${motionDurationSlow}`,\n          display: 'inline-block',\n          pointerEvents: 'auto',\n          // ======================== Layout ========================\n          [`${componentCls}-panel-layout`]: {\n            display: 'flex',\n            flexWrap: 'nowrap',\n            alignItems: 'stretch'\n          },\n          // ======================== Preset ========================\n          [`${componentCls}-presets`]: {\n            display: 'flex',\n            flexDirection: 'column',\n            minWidth: presetsWidth,\n            maxWidth: presetsMaxWidth,\n            ul: {\n              height: 0,\n              flex: 'auto',\n              listStyle: 'none',\n              overflow: 'auto',\n              margin: 0,\n              padding: paddingXS,\n              borderInlineEnd: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n              li: Object.assign(Object.assign({}, textEllipsis), {\n                borderRadius: borderRadiusSM,\n                paddingInline: paddingXS,\n                paddingBlock: token.calc(controlHeightSM).sub(fontHeight).div(2).equal(),\n                cursor: 'pointer',\n                transition: `all ${motionDurationSlow}`,\n                '+ li': {\n                  marginTop: marginXS\n                },\n                '&:hover': {\n                  background: cellHoverBg\n                }\n              })\n            }\n          },\n          // ======================== Panels ========================\n          [`${componentCls}-panels`]: {\n            display: 'inline-flex',\n            flexWrap: 'nowrap',\n            // [`${componentCls}-panel`]: {\n            //   borderWidth: `0 0 ${unit(lineWidth)}`,\n            // },\n            '&:last-child': {\n              [`${componentCls}-panel`]: {\n                borderWidth: 0\n              }\n            }\n          },\n          [`${componentCls}-panel`]: {\n            verticalAlign: 'top',\n            background: 'transparent',\n            borderRadius: 0,\n            borderWidth: 0,\n            [`${componentCls}-content, table`]: {\n              textAlign: 'center'\n            },\n            '&-focused': {\n              borderColor: colorBorder\n            }\n          }\n        }\n      }),\n      '&-dropdown-range': {\n        padding: `${unit(token.calc(sizePopupArrow).mul(2).div(3).equal())} 0`,\n        '&-hidden': {\n          display: 'none'\n        }\n      },\n      '&-rtl': {\n        direction: 'rtl',\n        [`${componentCls}-separator`]: {\n          transform: 'scale(-1, 1)'\n        },\n        [`${componentCls}-footer`]: {\n          '&-extra': {\n            direction: 'rtl'\n          }\n        }\n      }\n    })\n  },\n  // Follow code may reuse in other components\n  initSlideMotion(token, 'slide-up'), initSlideMotion(token, 'slide-down'), initMoveMotion(token, 'move-up'), initMoveMotion(token, 'move-down')];\n};\n// ============================== Export ==============================\nexport default genStyleHooks('DatePicker', token => {\n  const pickerToken = mergeToken(initInputToken(token), initPickerPanelToken(token), {\n    inputPaddingHorizontalBase: token.calc(token.paddingSM).sub(1).equal(),\n    multipleSelectItemHeight: token.multipleItemHeight,\n    selectHeight: token.controlHeight\n  });\n  return [genPickerPanelStyle(pickerToken), genPickerStyle(pickerToken), genVariantsStyle(pickerToken), genPickerStatusStyle(pickerToken), genPickerMultipleStyle(pickerToken),\n  // =====================================================\n  // ==             Space Compact                       ==\n  // =====================================================\n  genCompactItemStyle(token, {\n    focusElCls: `${token.componentCls}-focused`\n  })];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}