{"ast": null, "code": "/**\n * @import {Event, Resolver, TokenizeContext} from 'micromark-util-types'\n */\n\n/**\n * Call all `resolveAll`s.\n *\n * @param {ReadonlyArray<{resolveAll?: Resolver | undefined}>} constructs\n *   List of constructs, optionally with `resolveAll`s.\n * @param {Array<Event>} events\n *   List of events.\n * @param {TokenizeContext} context\n *   Context used by `tokenize`.\n * @returns {Array<Event>}\n *   Changed events.\n */\nexport function resolveAll(constructs, events, context) {\n  /** @type {Array<Resolver>} */\n  const called = [];\n  let index = -1;\n  while (++index < constructs.length) {\n    const resolve = constructs[index].resolveAll;\n    if (resolve && !called.includes(resolve)) {\n      events = resolve(events, context);\n      called.push(resolve);\n    }\n  }\n  return events;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}