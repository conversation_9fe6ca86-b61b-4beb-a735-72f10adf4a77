{"ast": null, "code": "import { generate } from '@ant-design/colors';\nimport defaultAlgorithm from '../default';\nimport { defaultPresetColors } from '../seed';\nimport genColorMapToken from '../shared/genColorMapToken';\nimport { generateColorPalettes, generateNeutralColorPalettes } from './colors';\nconst derivative = (token, mapToken) => {\n  const colorPalettes = Object.keys(defaultPresetColors).map(colorKey => {\n    const colors = generate(token[colorKey], {\n      theme: 'dark'\n    });\n    return Array.from({\n      length: 10\n    }, () => 1).reduce((prev, _, i) => {\n      prev[`${colorKey}-${i + 1}`] = colors[i];\n      prev[`${colorKey}${i + 1}`] = colors[i];\n      return prev;\n    }, {});\n  }).reduce((prev, cur) => {\n    prev = Object.assign(Object.assign({}, prev), cur);\n    return prev;\n  }, {});\n  const mergedMapToken = mapToken !== null && mapToken !== void 0 ? mapToken : defaultAlgorithm(token);\n  const colorMapToken = genColorMapToken(token, {\n    generateColorPalettes,\n    generateNeutralColorPalettes\n  });\n  return Object.assign(Object.assign(Object.assign(Object.assign({}, mergedMapToken), colorPalettes), colorMapToken), {\n    // Customize selected item background color\n    // https://github.com/ant-design/ant-design/issues/30524#issuecomment-871961867\n    colorPrimaryBg: colorMapToken.colorPrimaryBorder,\n    colorPrimaryBgHover: colorMapToken.colorPrimaryBorderHover\n  });\n};\nexport default derivative;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}