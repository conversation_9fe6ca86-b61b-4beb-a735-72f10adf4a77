{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nexport default function Icon(_ref) {\n  var icon = _ref.icon,\n    props = _ref.props,\n    children = _ref.children;\n  var iconNode;\n  if (icon === null || icon === false) {\n    return null;\n  }\n  if (typeof icon === 'function') {\n    iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n  } else if (typeof icon !== \"boolean\") {\n    // Compatible for origin definition\n    iconNode = icon;\n  }\n  return iconNode || children || null;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}