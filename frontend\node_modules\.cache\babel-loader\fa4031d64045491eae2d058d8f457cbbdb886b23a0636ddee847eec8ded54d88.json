{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nvar calcThumbStyle = function calcThumbStyle(targetElement, vertical) {\n  if (!targetElement) return null;\n  var style = {\n    left: targetElement.offsetLeft,\n    right: targetElement.parentElement.clientWidth - targetElement.clientWidth - targetElement.offsetLeft,\n    width: targetElement.clientWidth,\n    top: targetElement.offsetTop,\n    bottom: targetElement.parentElement.clientHeight - targetElement.clientHeight - targetElement.offsetTop,\n    height: targetElement.clientHeight\n  };\n  if (vertical) {\n    // Adjusts positioning and size for vertical layout by setting horizontal properties to 0 and using vertical properties from the style object.\n    return {\n      left: 0,\n      right: 0,\n      width: 0,\n      top: style.top,\n      bottom: style.bottom,\n      height: style.height\n    };\n  }\n  return {\n    left: style.left,\n    right: style.right,\n    width: style.width,\n    top: 0,\n    bottom: 0,\n    height: 0\n  };\n};\nvar toPX = function toPX(value) {\n  return value !== undefined ? \"\".concat(value, \"px\") : undefined;\n};\nexport default function MotionThumb(props) {\n  var prefixCls = props.prefixCls,\n    containerRef = props.containerRef,\n    value = props.value,\n    getValueIndex = props.getValueIndex,\n    motionName = props.motionName,\n    onMotionStart = props.onMotionStart,\n    onMotionEnd = props.onMotionEnd,\n    direction = props.direction,\n    _props$vertical = props.vertical,\n    vertical = _props$vertical === void 0 ? false : _props$vertical;\n  var thumbRef = React.useRef(null);\n  var _React$useState = React.useState(value),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    prevValue = _React$useState2[0],\n    setPrevValue = _React$useState2[1];\n\n  // =========================== Effect ===========================\n  var findValueElement = function findValueElement(val) {\n    var _containerRef$current;\n    var index = getValueIndex(val);\n    var ele = (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.querySelectorAll(\".\".concat(prefixCls, \"-item\"))[index];\n    return (ele === null || ele === void 0 ? void 0 : ele.offsetParent) && ele;\n  };\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    prevStyle = _React$useState4[0],\n    setPrevStyle = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    nextStyle = _React$useState6[0],\n    setNextStyle = _React$useState6[1];\n  useLayoutEffect(function () {\n    if (prevValue !== value) {\n      var prev = findValueElement(prevValue);\n      var next = findValueElement(value);\n      var calcPrevStyle = calcThumbStyle(prev, vertical);\n      var calcNextStyle = calcThumbStyle(next, vertical);\n      setPrevValue(value);\n      setPrevStyle(calcPrevStyle);\n      setNextStyle(calcNextStyle);\n      if (prev && next) {\n        onMotionStart();\n      } else {\n        onMotionEnd();\n      }\n    }\n  }, [value]);\n  var thumbStart = React.useMemo(function () {\n    if (vertical) {\n      var _prevStyle$top;\n      return toPX((_prevStyle$top = prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.top) !== null && _prevStyle$top !== void 0 ? _prevStyle$top : 0);\n    }\n    if (direction === 'rtl') {\n      return toPX(-(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.right));\n    }\n    return toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.left);\n  }, [vertical, direction, prevStyle]);\n  var thumbActive = React.useMemo(function () {\n    if (vertical) {\n      var _nextStyle$top;\n      return toPX((_nextStyle$top = nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.top) !== null && _nextStyle$top !== void 0 ? _nextStyle$top : 0);\n    }\n    if (direction === 'rtl') {\n      return toPX(-(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.right));\n    }\n    return toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.left);\n  }, [vertical, direction, nextStyle]);\n\n  // =========================== Motion ===========================\n  var onAppearStart = function onAppearStart() {\n    if (vertical) {\n      return {\n        transform: 'translateY(var(--thumb-start-top))',\n        height: 'var(--thumb-start-height)'\n      };\n    }\n    return {\n      transform: 'translateX(var(--thumb-start-left))',\n      width: 'var(--thumb-start-width)'\n    };\n  };\n  var onAppearActive = function onAppearActive() {\n    if (vertical) {\n      return {\n        transform: 'translateY(var(--thumb-active-top))',\n        height: 'var(--thumb-active-height)'\n      };\n    }\n    return {\n      transform: 'translateX(var(--thumb-active-left))',\n      width: 'var(--thumb-active-width)'\n    };\n  };\n  var onVisibleChanged = function onVisibleChanged() {\n    setPrevStyle(null);\n    setNextStyle(null);\n    onMotionEnd();\n  };\n\n  // =========================== Render ===========================\n  // No need motion when nothing exist in queue\n  if (!prevStyle || !nextStyle) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionName: motionName,\n    motionAppear: true,\n    onAppearStart: onAppearStart,\n    onAppearActive: onAppearActive,\n    onVisibleChanged: onVisibleChanged\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    var mergedStyle = _objectSpread(_objectSpread({}, motionStyle), {}, {\n      '--thumb-start-left': thumbStart,\n      '--thumb-start-width': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.width),\n      '--thumb-active-left': thumbActive,\n      '--thumb-active-width': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.width),\n      '--thumb-start-top': thumbStart,\n      '--thumb-start-height': toPX(prevStyle === null || prevStyle === void 0 ? void 0 : prevStyle.height),\n      '--thumb-active-top': thumbActive,\n      '--thumb-active-height': toPX(nextStyle === null || nextStyle === void 0 ? void 0 : nextStyle.height)\n    });\n\n    // It's little ugly which should be refactor when @umi/test update to latest jsdom\n    var motionProps = {\n      ref: composeRef(thumbRef, ref),\n      style: mergedStyle,\n      className: classNames(\"\".concat(prefixCls, \"-thumb\"), motionClassName)\n    };\n    if (process.env.NODE_ENV === 'test') {\n      motionProps['data-test-style'] = JSON.stringify(mergedStyle);\n    }\n    return /*#__PURE__*/React.createElement(\"div\", motionProps);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}