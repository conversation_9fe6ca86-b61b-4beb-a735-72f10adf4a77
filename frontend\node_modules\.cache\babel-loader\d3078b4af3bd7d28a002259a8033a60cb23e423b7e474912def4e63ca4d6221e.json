{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Callback} Callback\n * @typedef {import('./lib/index.js').Middleware} Middleware\n * @typedef {import('./lib/index.js').Pipeline} Pipeline\n * @typedef {import('./lib/index.js').Run} Run\n * @typedef {import('./lib/index.js').Use} Use\n */\n\nexport { trough, wrap } from './lib/index.js';", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}