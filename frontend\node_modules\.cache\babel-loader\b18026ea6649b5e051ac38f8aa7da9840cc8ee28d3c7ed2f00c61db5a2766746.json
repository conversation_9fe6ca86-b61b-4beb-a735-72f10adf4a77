{"ast": null, "code": "import axios from 'axios';\nimport moment from 'moment';\n\n// 动态获取API基础URL\nconst getBaseURL = () => {\n  // 检查是否强制使用本地API（开发测试用）\n  const forceLocal = localStorage.getItem('FORCE_LOCAL_API') === 'true';\n  if (forceLocal) {\n    console.log('🔧 强制使用本地API');\n    return 'http://localhost:8083/api';\n  }\n\n  // 临时强制使用本地API（开发调试）\n  if (window.location.hostname === 'localhost') {\n    console.log('🔧 开发环境强制使用本地API');\n    return 'http://localhost:8083/api';\n  }\n\n  // 如果设置了环境变量，使用环境变量\n  if (process.env.REACT_APP_API_URL) {\n    console.log('🔧 使用环境变量API URL:', process.env.REACT_APP_API_URL);\n    return process.env.REACT_APP_API_URL;\n  }\n\n  // 否则根据当前域名动态构建\n  const protocol = window.location.protocol;\n  const hostname = window.location.hostname;\n  const port = window.location.port;\n  let apiUrl;\n\n  // 如果是localhost或127.0.0.1，使用8083端口\n  if (hostname === 'localhost' || hostname === '127.0.0.1') {\n    apiUrl = `${protocol}//${hostname}:8083/api`;\n  } else if (hostname === '17learn.cn') {\n    // 特定外网域名，使用花生壳内网穿透\n    apiUrl = 'http://danphy.xicp.net:23277/api';\n  } else {\n    // 其他外网访问时，使用当前页面的端口（通常是同一个端口）\n    if (port) {\n      apiUrl = `${protocol}//${hostname}:${port}/api`;\n    } else {\n      // 如果没有端口，使用默认端口\n      apiUrl = `${protocol}//${hostname}/api`;\n    }\n  }\n  console.log('🌐 动态构建API URL:', apiUrl);\n  console.log('📍 当前页面信息:', {\n    protocol,\n    hostname,\n    port\n  });\n  return apiUrl;\n};\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: getBaseURL(),\n  headers: {\n    'Content-Type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest' // 帮助某些服务器识别AJAX请求\n  },\n  // 添加更长的超时时间，处理可能的网络延迟\n  timeout: 30000,\n  // 增加到30秒\n  // 跨域请求设置\n  withCredentials: false,\n  // 修改为false，避免CORS问题\n  // 最大重定向次数\n  maxRedirects: 5\n});\nconsole.log('API Base URL:', getBaseURL());\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    var _config$method;\n    config.headers['Authorization'] = `Bearer ${token}`;\n    console.log(`🔐 请求 ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url} 携带token: ${token.substring(0, 15)}...`);\n    console.log(`📍 完整URL: ${config.baseURL}${config.url}`);\n  } else {\n    var _config$method2;\n    console.warn(`⚠️ 请求 ${(_config$method2 = config.method) === null || _config$method2 === void 0 ? void 0 : _config$method2.toUpperCase()} ${config.url} 无token`);\n    console.log(`📍 完整URL: ${config.baseURL}${config.url}`);\n  }\n  return config;\n}, error => {\n  console.error('❌ 请求拦截器错误:', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  var _response$config$meth;\n  console.log(`✅ API响应成功: ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url}`, response.status);\n\n  // 如果是blob响应，返回原始数据\n  if (response.config.responseType === 'blob') {\n    return response.data;\n  }\n  return response.data;\n}, error => {\n  var _error$config, _error$config$method, _error$config2, _error$response, _error$response2, _error$config3;\n  const method = ((_error$config = error.config) === null || _error$config === void 0 ? void 0 : (_error$config$method = _error$config.method) === null || _error$config$method === void 0 ? void 0 : _error$config$method.toUpperCase()) || 'UNKNOWN';\n  const url = ((_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.url) || 'unknown';\n  const status = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) || 'no response';\n  const data = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {};\n  console.error(`❌ API响应错误: ${method} ${url}`, {\n    status,\n    data,\n    message: error.message,\n    baseURL: (_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.baseURL\n  });\n  if (error.response && error.response.status === 401) {\n    console.warn('🔐 认证失败，清除本地数据并重定向到登录页');\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    // 避免在登录页再次跳转到登录页形成循环\n    if (window.location.pathname !== '/login') {\n      console.log('🔄 重定向到登录页');\n      window.location.href = '/login';\n    }\n  }\n  return Promise.reject(error.response ? error.response.data : error);\n});\n\n// 认证相关\nexport const login = (username, password) => {\n  const formData = new URLSearchParams();\n  formData.append('username', username);\n  formData.append('password', password);\n\n  // 使用api实例的baseURL，而不是硬编码URL\n  return api.post('/login', formData, {\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded'\n    }\n  });\n};\nexport const register = async userData => {\n  try {\n    console.log('调用register API, userData:', userData);\n    const response = await api.post('/user', userData);\n    console.log('注册成功:', response);\n    return response;\n  } catch (error) {\n    console.error('注册失败:', error);\n    throw error; // 重新抛出错误，让调用者处理\n  }\n};\nexport const getCurrentUser = () => {\n  return api.get('/me').then(userData => {\n    console.log('获取到用户数据:', userData);\n\n    // 确保本地存储的用户数据是最新的\n    const token = localStorage.getItem('token');\n    if (token && userData) {\n      // 合并token到用户数据\n      userData.access_token = token;\n\n      // 更新localStorage中的用户数据\n      localStorage.setItem('user', JSON.stringify(userData));\n    }\n    return userData;\n  }).catch(error => {\n    console.error('获取用户数据失败:', error);\n    throw error;\n  });\n};\n\n// 班级管理API\nexport const getClasses = async (params = {}) => {\n  try {\n    console.log('调用getClasses API', params ? `参数: ${JSON.stringify(params)}` : '');\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时\n\n    // 构建查询参数\n    const queryParams = new URLSearchParams();\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.grade_id) queryParams.append('grade_id', params.grade_id);\n    if (params.grade) queryParams.append('grade', params.grade);\n\n    // 尝试多个可能的API端点\n    let url = '/classes'; // 首先尝试直接的路径\n    let response = null;\n    try {\n      console.log('尝试请求URL:', url);\n      response = await api.get(url, {\n        signal: controller.signal,\n        params: params\n      });\n    } catch (firstError) {\n      console.log('第一个API端点失败，尝试备用端点:', firstError);\n      try {\n        url = '/admin/classes'; // 尝试admin路径\n        console.log('尝试请求URL:', url);\n        response = await api.get(url, {\n          signal: controller.signal,\n          params: params\n        });\n      } catch (secondError) {\n        console.log('第二个API端点也失败，尝试最终备用端点:', secondError);\n        // 如果前两个都失败，尝试使用getClassesBySchool获取所有班级\n        try {\n          const adminResponse = await api.get('/admin', {\n            signal: controller.signal\n          });\n          if (adminResponse && adminResponse.school_id) {\n            console.log('获取到管理员学校ID:', adminResponse.school_id);\n            url = `/admin/schools/${adminResponse.school_id}/classes`;\n            console.log('尝试请求URL:', url);\n            response = await api.get(url, {\n              signal: controller.signal\n            });\n          } else {\n            console.log('无法获取管理员学校ID，尝试默认学校ID');\n            url = `/admin/schools/1/classes`;\n            console.log('尝试请求URL:', url);\n            response = await api.get(url, {\n              signal: controller.signal\n            });\n          }\n        } catch (finalError) {\n          console.error('所有API端点都失败:', finalError);\n          throw finalError;\n        }\n      }\n    }\n    clearTimeout(timeoutId);\n    console.log('获取班级列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取班级列表失败:', error);\n    // 在此处不抛出错误，而是返回空数组，让调用方决定如何处理\n    return [];\n  }\n};\nexport const getClass = (id, noCache = false) => {\n  console.log('调用getClass API, id:', id, noCache ? '(禁用缓存)' : '(允许使用缓存)');\n\n  // 构建请求参数，添加时间戳以避免缓存\n  const params = noCache ? {\n    _t: new Date().getTime()\n  } : {};\n\n  // 添加超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error(`获取班级 ${id} 详情请求超时`);\n  }, 15000); // 15秒超时\n\n  // 最大重试次数\n  const maxRetries = 2;\n  let retryCount = 0;\n\n  // 使用真实API调用获取班级详情\n  const tryFetchClass = () => {\n    console.log(`尝试获取班级 ${id} 详情 (尝试 ${retryCount + 1}/${maxRetries + 1})`);\n\n    // 添加详细的请求头配置\n    const headers = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest',\n      'Cache-Control': noCache ? 'no-cache, no-store' : 'default'\n    };\n    return api.get(`/admin/classes/${id}`, {\n      params: params,\n      signal: controller.signal,\n      headers: headers\n      // 不再需要单独设置withCredentials，因为已在api实例中设置\n    }).then(response => {\n      clearTimeout(timeoutId);\n      console.log(`获取班级 ${id} 详情成功:`, response);\n      return response;\n    }).catch(error => {\n      var _error$response3;\n      console.error(`获取班级 ${id} 详情失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);\n      console.error(`错误类型: ${error.name}, 状态码: ${(_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status}, 消息: ${error.message}`);\n      if (error.name === 'AbortError') {\n        throw new Error('请求超时，请检查网络连接');\n      }\n\n      // 如果还有重试次数，尝试重试\n      if (retryCount < maxRetries) {\n        retryCount++;\n        console.log(`重试获取班级 ${id} 详情 (${retryCount}/${maxRetries})`);\n        return tryFetchClass();\n      }\n\n      // 如果API调用失败，尝试备用API端点\n      console.log(`尝试备用API端点获取班级 ${id} 详情`);\n      return api.get(`/classes/${id}`, {\n        params: params,\n        headers: headers,\n        signal: controller.signal\n        // 不再需要单独设置withCredentials，因为已在api实例中设置\n      }).then(backupResponse => {\n        console.log(`备用API成功获取班级 ${id} 详情:`, backupResponse);\n        return backupResponse;\n      }).catch(backupError => {\n        var _backupError$response;\n        console.error(`备用API获取班级 ${id} 详情失败:`, backupError);\n        console.error(`备用API错误类型: ${backupError.name}, 状态码: ${(_backupError$response = backupError.response) === null || _backupError$response === void 0 ? void 0 : _backupError$response.status}, 消息: ${backupError.message}`);\n\n        // 尝试第三个API端点\n        console.log(`尝试第三个API端点获取班级 ${id} 详情`);\n        return api.get(`/admin/classes/${id}/students`, {\n          params: params,\n          headers: headers,\n          signal: controller.signal\n          // 不再需要单独设置withCredentials，因为已在api实例中设置\n        }).then(thirdResponse => {\n          console.log(`第三个API端点成功获取班级 ${id} 详情:`, thirdResponse);\n          return thirdResponse;\n        }).catch(thirdError => {\n          console.error(`第三个API端点获取班级 ${id} 详情失败:`, thirdError);\n\n          // 所有API尝试都失败，不使用模拟数据，直接返回空数据\n          console.log(`所有API尝试都失败，返回空数据`);\n\n          // 返回空数据\n          return {\n            id: id,\n            name: `班级 ${id}`,\n            description: '',\n            created_at: new Date().toISOString(),\n            students: [],\n            student_count: 0,\n            grade: '',\n            school_id: 1,\n            error: true // 添加错误标记\n          };\n        });\n      });\n    });\n  };\n  return tryFetchClass();\n};\nexport const createClass = classData => {\n  console.log('调用createClass API, data:', classData);\n  return api.post('/admin/classes', classData);\n};\nexport const updateClass = (id, classData) => {\n  return api.put(`/admin/classes/${id}`, classData);\n};\n\n// 批量更新班级\nexport const batchUpdateClasses = (classIds, classData) => {\n  return api.put(`/admin/classes/batch`, {\n    class_ids: classIds,\n    ...classData\n  });\n};\nexport const deleteClass = id => {\n  return api.delete(`/admin/classes/${id}`);\n};\nexport const addStudentToClass = (classId, studentId) => {\n  console.log(`开始添加学生(ID:${studentId})到班级(ID:${classId})`);\n\n  // 检查studentId是否是临时ID\n  const isTempId = typeof studentId === 'string' && studentId.startsWith('temp_');\n\n  // 如果是临时ID，需要先查找真实ID\n  if (isTempId) {\n    console.log(`检测到临时ID ${studentId}，尝试查找真实ID`);\n\n    // 从临时ID中提取用户名\n    const username = studentId.replace('temp_', '');\n    console.log(`从临时ID提取的用户名: ${username}`);\n\n    // 使用用户名查找学生\n    return api.get('/admin/users', {\n      params: {\n        search: username,\n        role: 'student',\n        limit: 10\n      }\n    }).then(response => {\n      console.log(`查找用户名 ${username} 的结果:`, response);\n\n      // 检查返回格式\n      let userList = [];\n      if (Array.isArray(response)) {\n        userList = response;\n      } else if (response && Array.isArray(response.items)) {\n        userList = response.items;\n      }\n\n      // 查找完全匹配的用户\n      const exactMatch = userList.find(u => u.username === username);\n      let realStudentId = null;\n      if (exactMatch) {\n        console.log(`找到完全匹配的用户: ${username}, ID: ${exactMatch.id}`);\n        realStudentId = exactMatch.id;\n      } else if (userList.length > 0) {\n        // 如果没有完全匹配但有结果，使用第一个\n        console.log(`未找到完全匹配，使用第一个结果: ${userList[0].username}, ID: ${userList[0].id}`);\n        realStudentId = userList[0].id;\n      } else {\n        // 没有找到，尝试直接创建学生\n        console.log(`未找到用户 ${username}，尝试直接创建学生`);\n\n        // 从临时ID中提取更多信息\n        const tempParts = studentId.split('_');\n        const studentData = {\n          username: username,\n          full_name: username,\n          // 使用用户名作为姓名\n          email: `${username}@example.com`,\n          password: '123456'\n        };\n\n        // 创建学生\n        return api.post('/admin/users', studentData).then(newUser => {\n          console.log(`成功创建学生: ${username}, ID: ${newUser.id}`);\n          // 使用新创建的学生ID添加到班级\n          return addStudentToClass(classId, newUser.id);\n        }).catch(createError => {\n          console.error(`创建学生 ${username} 失败:`, createError);\n\n          // 如果创建失败，可能是因为用户已存在，再次尝试查找\n          return api.get('/admin/users', {\n            params: {\n              search: username,\n              role: 'student',\n              limit: 10\n            }\n          }).then(secondResponse => {\n            console.log(`二次查找用户名 ${username} 的结果:`, secondResponse);\n            let secondUserList = [];\n            if (Array.isArray(secondResponse)) {\n              secondUserList = secondResponse;\n            } else if (secondResponse && Array.isArray(secondResponse.items)) {\n              secondUserList = secondResponse.items;\n            }\n            const secondExactMatch = secondUserList.find(u => u.username === username);\n            if (secondExactMatch) {\n              console.log(`二次查找找到完全匹配的用户: ${username}, ID: ${secondExactMatch.id}`);\n              return addStudentToClass(classId, secondExactMatch.id);\n            } else if (secondUserList.length > 0) {\n              console.log(`二次查找未找到完全匹配，使用第一个结果: ${secondUserList[0].username}, ID: ${secondUserList[0].id}`);\n              return addStudentToClass(classId, secondUserList[0].id);\n            } else {\n              console.log(`二次查找仍未找到用户 ${username}，返回部分成功状态`);\n              return {\n                id: studentId,\n                status: 'partial_success',\n                message: '无法找到或创建学生，请手动添加'\n              };\n            }\n          }).catch(secondError => {\n            console.error(`二次查找用户 ${username} 失败:`, secondError);\n            return {\n              id: studentId,\n              status: 'partial_success',\n              message: '查找和创建学生都失败，请手动添加'\n            };\n          });\n        });\n      }\n\n      // 使用找到的真实ID添加学生到班级\n      console.log(`使用真实ID ${realStudentId} 添加学生到班级 ${classId}`);\n      return addStudentToClass(classId, realStudentId);\n    }).catch(error => {\n      console.error(`查找用户 ${username} 失败:`, error);\n      return {\n        id: studentId,\n        status: 'partial_success',\n        message: '查找学生信息失败，请刷新页面后重试'\n      };\n    });\n  }\n\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('添加学生到班级请求超时');\n  }, 10000); // 10秒超时\n\n  return api.post(`/admin/classes/${classId}/students`, {\n    student_id: studentId\n  }, {\n    signal: controller.signal\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log(`成功添加学生(ID:${studentId})到班级(ID:${classId})，响应:`, response);\n    return {\n      ...response,\n      status: 'success',\n      message: '学生添加成功'\n    };\n  }).catch(error => {\n    var _error$response4;\n    clearTimeout(timeoutId);\n    console.error(`添加学生(ID:${studentId})到班级(ID:${classId})失败:`, error);\n    console.error(`错误详情:`, ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || error.message);\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      // 超时错误，返回部分成功状态而不是抛出异常\n      console.log('请求超时，返回部分成功状态');\n      return {\n        id: studentId,\n        status: 'partial_success',\n        message: '请求超时，但操作可能已完成，请刷新页面查看最新结果'\n      };\n    }\n    if (error.response && error.response.status === 400 && error.response.data && error.response.data.detail === \"该学生已在班级中\") {\n      // 如果学生已在班级中，不视为错误，返回成功\n      console.log(`学生(ID:${studentId})已在班级(ID:${classId})中，跳过`);\n      return {\n        id: studentId,\n        status: 'skipped',\n        message: '学生已在班级中'\n      };\n    }\n    if (error.response && error.response.status === 422) {\n      // Unprocessable Entity错误，可能是临时ID或无效ID\n      console.log('ID格式无效(422错误)，返回部分成功状态');\n      return {\n        id: studentId,\n        status: 'partial_success',\n        message: '操作可能已成功，请刷新页面查看最新结果'\n      };\n    }\n\n    // 检查是否为后端已成功处理但返回错误的情况\n    if (error.response && error.response.status === 500) {\n      console.log('服务器返回500错误，但操作可能已成功，返回部分成功状态');\n      return {\n        id: studentId,\n        status: 'partial_success',\n        message: '操作可能已成功，请刷新页面查看'\n      };\n    }\n\n    // 为所有错误返回部分成功状态，因为实际上操作可能已成功\n    console.log('返回部分成功状态，建议用户刷新页面');\n    return {\n      id: studentId,\n      status: 'partial_success',\n      message: '操作可能已成功，请刷新页面查看'\n    };\n  });\n};\nexport const removeStudentFromClass = (classId, studentId) => {\n  return api.delete(`/admin/classes/${classId}/students/${studentId}`);\n};\nexport const updateStudentInClass = (classId, studentId, studentData) => {\n  console.log(`调用updateStudentInClass API, classId: ${classId}, studentId: ${studentId}, data:`, studentData);\n  return api.put(`/admin/classes/${classId}/students/${studentId}`, studentData).then(response => {\n    console.log('学生信息更新成功:', response);\n    return response;\n  }).catch(error => {\n    console.error('学生信息更新失败:', error);\n    throw error;\n  });\n};\n\n// 用户管理API\nexport const getUsersCount = async () => {\n  try {\n    console.log('获取用户总数...');\n\n    // 直接请求所有用户，使用非常大的limit值\n    console.log('直接请求所有用户...');\n    const allUsersResponse = await api.get('/admin/users?limit=10000&skip=0');\n    if (Array.isArray(allUsersResponse)) {\n      console.log(`获取到所有用户，总数: ${allUsersResponse.length}`);\n      return allUsersResponse.length;\n    } else if (allUsersResponse && typeof allUsersResponse === 'object' && 'total' in allUsersResponse) {\n      console.log(`API返回的用户总数: ${allUsersResponse.total}`);\n      return allUsersResponse.total;\n    }\n\n    // 如果上述方法失败，尝试使用多次请求，每次增加skip值，直到获取不到数据为止\n    console.log('尝试使用多次请求获取总用户数...');\n    let totalCount = 0;\n    let batchSize = 100;\n    let currentSkip = 0;\n    let hasMoreData = true;\n    while (hasMoreData) {\n      const batchResponse = await api.get(`/admin/users?limit=${batchSize}&skip=${currentSkip}`);\n      if (Array.isArray(batchResponse) && batchResponse.length > 0) {\n        totalCount += batchResponse.length;\n        currentSkip += batchSize;\n        console.log(`批次获取用户: 当前总数=${totalCount}, skip=${currentSkip}`);\n\n        // 如果返回的数据少于batchSize，说明已经获取完所有数据\n        if (batchResponse.length < batchSize) {\n          hasMoreData = false;\n        }\n      } else {\n        hasMoreData = false;\n      }\n\n      // 安全检查，避免无限循环\n      if (currentSkip > 10000) {\n        console.warn('达到最大请求次数，中止获取');\n        break;\n      }\n    }\n    if (totalCount > 0) {\n      console.log(`通过多次请求获取到的总用户数: ${totalCount}`);\n      return totalCount;\n    }\n\n    // 如果所有方法都失败，返回一个硬编码的值\n    console.warn('无法确定用户总数，返回硬编码值63');\n    return 63; // 硬编码的用户总数\n  } catch (error) {\n    console.error('获取用户总数失败:', error);\n    return 63; // 出错时的硬编码值\n  }\n};\nexport const getUsers = async (params = {}) => {\n  try {\n    console.log('调用getUsers API', params ? `参数: ${JSON.stringify(params)}` : '');\n\n    // 首先获取用户总数\n    let totalUsers = 100; // 默认值\n    try {\n      totalUsers = await getUsersCount();\n      console.log(`获取到的用户总数: ${totalUsers}`);\n    } catch (countError) {\n      console.error('获取用户总数失败，使用默认值:', countError);\n    }\n\n    // 构建查询参数\n    const queryParams = new URLSearchParams();\n    if (params.search) queryParams.append('search', params.search);\n    if (params.role) queryParams.append('role', params.role);\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.is_active !== undefined) queryParams.append('is_active', params.is_active);\n\n    // 确保分页参数正确传递\n    const skip = params.skip !== undefined ? parseInt(params.skip) : 0;\n    const limit = params.limit !== undefined ? parseInt(params.limit) : 10;\n    queryParams.append('skip', skip);\n    queryParams.append('limit', limit);\n\n    // 记录详细的分页信息\n    console.log(`分页请求: skip=${skip}, limit=${limit}, 页码=${skip / limit + 1}`);\n\n    // 构建URL\n    let url = '/admin/users';\n    if (queryParams.toString()) {\n      url += `?${queryParams.toString()}`;\n    }\n    console.log('发送用户API请求:', url);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    // 直接发起请求，确保参数被正确传递\n    const response = await api.get(url, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('获取用户API响应:', response);\n\n    // 处理响应格式\n    if (response && typeof response === 'object' && 'items' in response) {\n      // 新的响应格式\n      console.log('使用新格式响应 - 总数:', response.total, '用户数:', response.items.length);\n\n      // 验证搜索结果\n      if (params.search && response.items.length > 0) {\n        console.log('搜索结果示例:', response.items.map(user => user.username).join(', '));\n      } else if (params.search) {\n        console.log('搜索无匹配结果');\n      }\n\n      // 确保total字段存在且为数值\n      if (response.total === undefined || response.total === null) {\n        console.warn('API响应中缺少total字段，使用预先获取的总数');\n        response.total = totalUsers;\n      }\n      return response;\n    } else if (Array.isArray(response)) {\n      // 兼容旧格式 - 直接返回数组\n      console.log('收到旧格式响应(数组) - 用户数:', response.length);\n\n      // 无论如何，尝试获取所有用户\n      let allUsers = [];\n      try {\n        console.log('尝试获取所有用户...');\n        const allUsersResponse = await api.get('/admin/users?limit=10000&skip=0');\n        if (Array.isArray(allUsersResponse) && allUsersResponse.length > 0) {\n          console.log(`获取到所有用户: ${allUsersResponse.length}条`);\n          allUsers = allUsersResponse;\n        } else {\n          console.log('无法获取所有用户，使用当前响应');\n          allUsers = response;\n        }\n      } catch (error) {\n        console.error('获取所有用户失败，使用当前响应:', error);\n        allUsers = response;\n      }\n      let filteredItems = allUsers;\n\n      // 如果有搜索参数，对数据进行本地过滤\n      if (params.search) {\n        const searchTerm = params.search.toLowerCase();\n        console.log('在前端执行本地搜索:', searchTerm);\n        filteredItems = allUsers.filter(user => {\n          const matchUsername = user.username && user.username.toLowerCase().includes(searchTerm);\n          const matchFullName = user.full_name && user.full_name.toLowerCase().includes(searchTerm);\n          const matchEmail = user.email && user.email.toLowerCase().includes(searchTerm);\n          if (matchUsername || matchFullName || matchEmail) {\n            console.log('匹配到用户:', user.username);\n            return true;\n          }\n          return false;\n        });\n        console.log('本地过滤后的结果数:', filteredItems.length);\n        if (filteredItems.length > 0) {\n          console.log('搜索结果用户名:', filteredItems.map(user => user.username).join(', '));\n        }\n      }\n\n      // 手动应用分页 - 确保有足够的数据\n      console.log(`手动应用分页: skip=${skip}, limit=${limit}, 总数=${filteredItems.length}，实际用户总数=${totalUsers}`);\n\n      // 检查是否有足够的数据进行分页\n      if (skip >= filteredItems.length) {\n        console.warn(`请求的起始位置(${skip})超过了可用数据长度(${filteredItems.length})，返回空数组`);\n        return {\n          total: totalUsers,\n          items: []\n        };\n      }\n\n      // 应用分页\n      const paginatedItems = filteredItems.slice(skip, skip + limit);\n      console.log(`分页后结果数: ${paginatedItems.length}, 范围: ${skip} - ${skip + paginatedItems.length}`);\n\n      // 返回分页后的结果\n      return {\n        total: totalUsers,\n        items: paginatedItems\n      };\n    } else {\n      console.error('无法识别的用户数据格式:', response);\n      return {\n        total: totalUsers,\n        items: []\n      };\n    }\n  } catch (error) {\n    console.error('获取用户列表失败:', error);\n    throw error;\n  }\n};\nexport const createUser = async userData => {\n  try {\n    console.log('创建用户:', userData);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.post('/admin/users', userData, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('创建用户成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建用户失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\nexport const batchCreateStudents = studentsData => {\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('批量创建学生请求超时');\n  }, 15000); // 15秒超时\n\n  // 调试日志\n  console.log('批量创建学生请求数据:', JSON.stringify(studentsData));\n\n  // 确保每个学生对象都有正确的字段格式\n  const validatedData = studentsData.map(student => ({\n    username: String(student.username || ''),\n    // 强制转换为字符串\n    email: student.email || '',\n    full_name: student.full_name || '',\n    phone: student.phone || '',\n    password: student.password || '123456'\n  }));\n  console.log('验证后的批量创建学生数据:', JSON.stringify(validatedData));\n  return api.post('/admin/batch-students', validatedData, {\n    signal: controller.signal\n  }).then(async response => {\n    clearTimeout(timeoutId);\n    console.log('批量创建学生成功，响应:', response);\n\n    // 检查响应是否为空数组\n    if (Array.isArray(response) && response.length === 0) {\n      console.log('后端返回空数组，尝试直接创建学生');\n\n      // 直接创建学生\n      const createdStudents = [];\n      for (const student of validatedData) {\n        try {\n          console.log(`尝试直接创建学生: ${student.username}`);\n          const newUser = await api.post('/admin/users', student);\n          console.log(`成功创建学生: ${student.username}, ID: ${newUser.id}`);\n          createdStudents.push(newUser);\n        } catch (error) {\n          console.error(`直接创建学生 ${student.username} 失败:`, error);\n\n          // 如果创建失败，可能是因为用户已存在，尝试查找\n          try {\n            console.log(`尝试查找用户名为 ${student.username} 的学生`);\n            const users = await api.get('/admin/users', {\n              params: {\n                search: student.username,\n                role: 'student',\n                limit: 10\n              }\n            });\n            console.log(`查找用户名 ${student.username} 的结果:`, users);\n\n            // 检查返回格式\n            let userList = [];\n            if (Array.isArray(users)) {\n              userList = users;\n            } else if (users && Array.isArray(users.items)) {\n              userList = users.items;\n            }\n\n            // 查找完全匹配的用户\n            const exactMatch = userList.find(u => u.username === student.username);\n            if (exactMatch) {\n              console.log(`找到完全匹配的用户: ${student.username}, ID: ${exactMatch.id}`);\n              createdStudents.push(exactMatch);\n            } else if (userList.length > 0) {\n              // 如果没有完全匹配但有结果，使用第一个\n              console.log(`未找到完全匹配，使用第一个结果: ${userList[0].username}, ID: ${userList[0].id}`);\n              createdStudents.push(userList[0]);\n            } else {\n              // 没有找到，创建临时对象\n              console.log(`未找到用户 ${student.username}，创建临时对象`);\n              createdStudents.push({\n                ...student,\n                id: `temp_${student.username}`,\n                temp_data: true\n              });\n            }\n          } catch (searchError) {\n            console.error(`查找用户 ${student.username} 失败:`, searchError);\n            // 创建临时对象\n            createdStudents.push({\n              ...student,\n              id: `temp_${student.username}`,\n              temp_data: true\n            });\n          }\n        }\n      }\n      console.log('直接创建学生结果:', createdStudents);\n      return createdStudents;\n    }\n    return response;\n  }).catch(error => {\n    clearTimeout(timeoutId);\n    console.error('批量创建学生失败:', error);\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    if (!error.response) {\n      // 网络错误，没有收到响应\n      throw new Error('网络错误，请检查您的网络连接');\n    } else if (error.response.status >= 500) {\n      // 服务器错误，返回临时数据\n      console.log('服务器错误，返回临时数据供前端处理');\n      return validatedData.map((student, index) => ({\n        ...student,\n        id: `temp_${student.username}`,\n        temp_data: true\n      }));\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      var _error$response5, _error$response5$data;\n      // 其他错误\n      const errorMessage = ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.detail) || error.message || '未知错误';\n      throw new Error(`批量创建学生失败: ${errorMessage}`);\n    }\n  });\n};\n\n// 作业相关\nexport const getHomeworks = async params => {\n  try {\n    console.log('获取作业列表，参数:', params);\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时\n\n    // 添加禁用缓存的请求头\n    const headers = {};\n    if (params && params.cache === false) {\n      console.log('禁用作业列表缓存');\n      headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';\n      headers['Pragma'] = 'no-cache';\n      headers['Expires'] = '0';\n      // 移除cache参数，避免传递给后端\n      delete params.cache;\n    }\n\n    // 添加时间戳参数，确保不使用缓存\n    const newParams = {\n      ...params\n    };\n    if (!newParams._t) {\n      newParams._t = new Date().getTime();\n    }\n    const response = await api.get('/homework', {\n      params: newParams,\n      signal: controller.signal,\n      headers\n    });\n    clearTimeout(timeoutId);\n    console.log('获取作业列表原始响应:', response);\n\n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 后端直接返回数组格式，这是正常情况\n      console.log('获取作业列表成功 (数组格式):', response.length, '条记录');\n\n      // 将数组格式转换为前端期望的格式\n      return {\n        items: response,\n        total: response.length,\n        page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n        limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n      };\n    } else if (response && typeof response === 'object') {\n      if (Array.isArray(response.items)) {\n        // 已经是分页对象格式\n        console.log('获取作业列表成功 (分页对象格式):', response.items.length, '条记录，共', response.total, '条');\n        return response;\n      } else {\n        console.warn('作业列表响应格式异常 (无items数组):', response);\n        // 尝试将整个响应对象作为单个项目的数组返回\n        if (response.id) {\n          console.log('响应似乎是单个作业对象，转换为数组');\n          const items = [response];\n          return {\n            items: items,\n            total: 1,\n            page: 1,\n            limit: 10\n          };\n        }\n\n        // 返回空数组\n        return {\n          items: [],\n          total: 0,\n          page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n          limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n        };\n      }\n    } else {\n      console.error('作业列表响应格式无效:', response);\n      return {\n        items: [],\n        total: 0,\n        page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n        limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n      };\n    }\n  } catch (error) {\n    console.error('获取作业列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('获取作业列表请求超时');\n      throw new Error('请求超时，请检查网络连接');\n    }\n\n    // 使用模拟数据，避免UI崩溃\n    const mockData = {\n      items: [],\n      total: 0,\n      page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n      limit: (params === null || params === void 0 ? void 0 : params.limit) || 10,\n      error: error.message || '未知错误'\n    };\n    console.log('返回模拟数据:', mockData);\n    return mockData;\n  }\n};\nexport const getHomework = async id => {\n  return await api.get(`/homework/${id}`);\n};\nexport const getHomeworkHistory = async (studentId, assignmentId) => {\n  try {\n    console.log(`获取作业历史版本，学生ID: ${studentId}, 作业任务ID: ${assignmentId}`);\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.get('/homework/history', {\n      params: {\n        student_id: studentId,\n        assignment_id: assignmentId\n      },\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('获取作业历史版本原始响应:', response);\n\n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 后端直接返回数组格式，这是正常情况\n      console.log('获取作业历史版本成功:', response.length, '个版本');\n      return response;\n    } else {\n      console.error('作业历史版本响应格式无效:', response);\n      return [];\n    }\n  } catch (error) {\n    console.error('获取作业历史版本失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('获取作业历史版本请求超时');\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\nexport const getAnnotatedImages = async homeworkId => {\n  return await api.get(`/homework/${homeworkId}/annotated-images`);\n};\nexport const generateAnnotations = async homeworkId => {\n  return await api.post(`/homework/${homeworkId}/generate-annotations`);\n};\nexport const correctHomework = async id => {\n  return await api.post(`/homework/${id}/correct`);\n};\nexport const submitHomework = formData => {\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('提交作业请求超时');\n  }, 10000); // 10秒超时，开发环境足够\n\n  return api.post('/homework', formData, {\n    signal: controller.signal,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log('作业提交成功，响应:', response);\n    return response;\n  }).catch(error => {\n    clearTimeout(timeoutId);\n    console.error('作业提交失败:', error);\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    if (!error.response) {\n      // 网络错误，没有收到响应\n      throw new Error('网络错误，请检查您的网络连接');\n    } else if (error.response.status >= 500) {\n      // 服务器错误\n      throw new Error('服务器错误，请稍后再试');\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      var _error$response6, _error$response6$data;\n      // 其他错误\n      const errorMessage = ((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.detail) || error.message || '未知错误';\n      throw new Error(`作业提交失败: ${errorMessage}`);\n    }\n  });\n};\nexport const batchUploadHomework = formData => {\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('批量上传作业请求超时');\n  }, 15000); // 15秒超时，开发环境批量上传\n\n  return api.post('/homework/batch', formData, {\n    signal: controller.signal,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log('批量上传作业成功，响应:', response);\n    return response;\n  }).catch(error => {\n    clearTimeout(timeoutId);\n    console.error('批量上传作业失败:', error);\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    if (!error.response) {\n      // 网络错误，没有收到响应\n      throw new Error('网络错误，请检查您的网络连接');\n    } else if (error.response.status >= 500) {\n      // 服务器错误\n      throw new Error('服务器错误，请稍后再试');\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      var _error$response7, _error$response7$data;\n      // 其他错误\n      const errorMessage = ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.detail) || error.message || '未知错误';\n      throw new Error(`批量上传作业失败: ${errorMessage}`);\n    }\n  });\n};\nexport const updateHomework = (id, data) => {\n  return api.put(`/homework/${id}`, data);\n};\nexport const deleteHomework = id => {\n  console.log(`开始删除作业，ID: ${id}`);\n\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n  return api.delete(`/homework/${id}`, {\n    signal: controller.signal\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log(`作业删除成功，ID: ${id}`, response);\n    return response;\n  }).catch(error => {\n    var _error$response8, _error$response8$data;\n    clearTimeout(timeoutId);\n    console.error(`作业删除失败，ID: ${id}:`, error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n\n    // 自定义错误信息\n    const errorMessage = ((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.detail) || error.message || '未知错误';\n    throw new Error(`删除作业失败: ${errorMessage}`);\n  });\n};\nexport const createHomeworkAssignment = data => {\n  console.log('创建作业任务，数据:', JSON.stringify(data));\n\n  // 检查网络连接\n  if (!navigator.onLine) {\n    console.error('网络连接已断开');\n    return Promise.reject(new Error('网络连接已断开，请检查您的网络设置'));\n  }\n\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('创建作业任务请求超时');\n  }, 30000); // 30秒超时\n\n  return api.post('/homework-assignment', data, {\n    signal: controller.signal,\n    headers: {\n      'Content-Type': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest'\n    }\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log('作业任务创建成功，响应:', response);\n    return response;\n  }).catch(error => {\n    clearTimeout(timeoutId);\n    console.error('作业任务创建失败:', error);\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    if (!error.response) {\n      // 网络错误，没有收到响应\n      console.error('网络错误详情:', error);\n      if (error.message) {\n        throw new Error(`连接错误: ${error.message}`);\n      } else {\n        throw new Error('网络错误，请检查您的网络连接');\n      }\n    } else if (error.response.status >= 500) {\n      var _error$response9, _error$response0, _error$response0$data;\n      // 服务器错误\n      console.error('服务器500错误详情:', (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data);\n      const errorDetail = ((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.detail) || '未知服务器错误';\n      throw new Error(`服务器错误: ${errorDetail}`);\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      var _error$response1, _error$response1$data;\n      // 其他错误\n      const errorMessage = ((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.detail) || error.message || '未知错误';\n      throw new Error(`作业任务创建失败: ${errorMessage}`);\n    }\n  });\n};\nexport const deleteHomeworkAssignment = id => {\n  return api.delete(`/homework-assignment/${id}`);\n};\nexport const updateHomeworkAssignmentStatus = (id, status) => {\n  console.log(`更新作业任务状态，ID: ${id}, 状态: ${status}`);\n\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n  return api.patch(`/homework-assignment/${id}/status`, {\n    status\n  }, {\n    signal: controller.signal\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log(`作业任务状态更新成功，ID: ${id}`, response);\n    return response;\n  }).catch(error => {\n    var _error$response10, _error$response10$dat;\n    clearTimeout(timeoutId);\n    console.error(`作业任务状态更新失败，ID: ${id}:`, error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n\n    // 自定义错误信息\n    const errorMessage = ((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.detail) || error.message || '未知错误';\n    throw new Error(`更新作业任务状态失败: ${errorMessage}`);\n  });\n};\nexport const getHomeworkAssignment = id => {\n  return api.get(`/homework-assignment/${id}`);\n};\n\n// 获取学生作业任务列表\nexport const getStudentHomeworkAssignments = async () => {\n  try {\n    console.log('获取学生作业任务列表');\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.get('/student/homework-assignments', {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('获取学生作业任务列表成功:', response);\n\n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 时间转换函数，统一转换为北京时间\n      const formatTime = time => {\n        if (!time) return null;\n        // 使用moment处理时间，使用480分钟偏移转换为北京时间，更加准确\n        const beijingTime = moment(time).utcOffset(480);\n        return beijingTime.isValid() ? beijingTime.format('YYYY-MM-DD HH:mm:ss') : null;\n      };\n\n      // 处理每个作业任务的时间字段\n      const processedResponse = response.map(homework => ({\n        ...homework,\n        // 按优先级处理提交时间\n        submit_time: formatTime(homework.submitted_at || homework.submit_time || homework.created_at),\n        // 其他时间字段也转换为北京时间\n        created_at: formatTime(homework.created_at),\n        updated_at: formatTime(homework.updated_at),\n        due_date: formatTime(homework.due_date)\n      }));\n      return processedResponse;\n    } else {\n      console.error('学生作业任务列表响应格式无效:', response);\n      return [];\n    }\n  } catch (error) {\n    var _error$response11, _error$response11$dat;\n    console.error('获取学生作业任务列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('获取学生作业任务列表请求超时');\n      throw new Error('请求超时，请检查网络连接');\n    }\n\n    // 返回空数组而不是抛出错误，避免组件加载失败\n    throw new Error(((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.detail) || error.message || '获取作业任务列表失败');\n  }\n};\nexport const getHomeworkAssignments = async params => {\n  try {\n    console.log('获取作业任务列表，参数:', params);\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    let retries = 0;\n    const maxRetries = 2;\n    let response;\n\n    // 添加 include_teacher_details 参数，获取老师的详细信息，包括学校信息\n    const apiParams = {\n      ...params,\n      include_teacher_details: true\n    };\n    while (retries <= maxRetries) {\n      try {\n        response = await api.get('/homework-assignment', {\n          params: apiParams,\n          signal: controller.signal\n        });\n        break; // 如果成功，跳出重试循环\n      } catch (retryError) {\n        retries++;\n        if (retries > maxRetries) {\n          throw retryError; // 重试次数用完，抛出错误\n        }\n        console.log(`获取作业任务列表失败，正在进行第${retries}次重试...`);\n        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒再重试\n      }\n    }\n    clearTimeout(timeoutId);\n    console.log('获取作业任务列表原始响应:', response);\n\n    // 时间转换函数，统一转换为北京时间\n    const formatTime = time => {\n      if (!time) return null;\n      // 使用moment处理时间，并转换为北京时间\n      const beijingTime = moment(time).utcOffset('+08:00');\n      return beijingTime.isValid() ? beijingTime.format('YYYY/MM/DD HH:mm:ss') : null;\n    };\n\n    // 处理作业任务对象的时间字段\n    const processHomeworkTimes = homework => ({\n      ...homework,\n      // 按优先级处理提交时间\n      submit_time: formatTime(homework.submitted_at || homework.submit_time || homework.created_at),\n      // 其他时间字段也转换为北京时间\n      created_at: formatTime(homework.created_at),\n      updated_at: formatTime(homework.updated_at),\n      due_date: formatTime(homework.due_date)\n    });\n\n    // 检查响应是否为空或undefined\n    if (!response) {\n      console.error('作业任务列表响应为空');\n      return {\n        items: [],\n        total: 0,\n        page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n        limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n      };\n    }\n\n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 后端直接返回数组格式，这是正常情况\n      console.log('获取作业任务列表成功 (数组格式):', response.length, '条记录');\n\n      // 处理每个作业任务的时间，并返回分页格式\n      const processedItems = response.map(processHomeworkTimes);\n      return {\n        items: processedItems,\n        total: processedItems.length,\n        page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n        limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n      };\n    } else if (response && typeof response === 'object') {\n      if (Array.isArray(response.items)) {\n        // 已经是分页对象格式\n        console.log('获取作业任务列表成功 (分页对象格式):', response.items.length, '条记录，共', response.total, '条');\n\n        // 处理分页对象中每个作业任务的时间\n        return {\n          ...response,\n          items: response.items.map(processHomeworkTimes)\n        };\n      } else {\n        console.warn('作业任务列表响应格式异常 (无items数组):', response);\n        // 尝试将整个响应对象作为单个项目的数组返回\n        if (response.id) {\n          console.log('响应似乎是单个作业任务对象，转换为数组');\n          const processedItem = processHomeworkTimes(response);\n          return {\n            items: [processedItem],\n            total: 1,\n            page: 1,\n            limit: 10\n          };\n        }\n\n        // 返回空数组\n        return {\n          items: [],\n          total: 0,\n          page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n          limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n        };\n      }\n    } else {\n      console.error('作业任务列表响应格式无效:', response);\n      return {\n        items: [],\n        total: 0,\n        page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n        limit: (params === null || params === void 0 ? void 0 : params.limit) || 10\n      };\n    }\n  } catch (error) {\n    console.error('获取作业任务列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('获取作业任务列表请求超时');\n      return {\n        items: [],\n        total: 0,\n        page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n        limit: (params === null || params === void 0 ? void 0 : params.limit) || 10,\n        error: '请求超时，请稍后重试'\n      };\n    }\n\n    // 返回空数据而不是抛出错误，避免组件加载失败\n    return {\n      items: [],\n      total: 0,\n      page: (params === null || params === void 0 ? void 0 : params.page) || 1,\n      limit: (params === null || params === void 0 ? void 0 : params.limit) || 10,\n      error: error.detail || error.message || '未知错误'\n    };\n  }\n};\n\n// 错题相关\nexport const getWrongQuestions = async params => {\n  try {\n    console.log('获取错题列表，参数:', params);\n    const response = await api.get('/wrong-questions', {\n      params\n    });\n    console.log('错题列表API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('获取错题列表失败:', error);\n    // 返回空数组而不是抛出错误，避免页面崩溃\n    return [];\n  }\n};\nexport const getWrongQuestion = async id => {\n  try {\n    console.log('获取错题详情，ID:', id);\n    const response = await api.get(`/wrong-questions/${id}`);\n    console.log('错题详情API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('获取错题详情失败:', error);\n    throw error;\n  }\n};\nexport const getReinforcementExercises = async params => {\n  try {\n    console.log('获取强化练习，参数:', params);\n    const response = await api.get('/reinforcement-exercises', {\n      params\n    });\n    console.log('强化练习API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('获取强化练习失败:', error);\n    // 返回空数组而不是抛出错误\n    return [];\n  }\n};\n\n// 管理员专用错题API\nexport const getAdminWrongQuestions = async params => {\n  try {\n    console.log('管理员获取错题列表，参数:', params);\n    const response = await api.get('/homework/admin/wrong-questions', {\n      params\n    });\n    console.log('管理员错题列表API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('管理员获取错题列表失败:', error);\n    return [];\n  }\n};\n\n// 教师专用错题API\nexport const getTeacherWrongQuestions = async params => {\n  try {\n    console.log('教师获取错题列表，参数:', params);\n    const response = await api.get('/homework/teacher/wrong-questions', {\n      params\n    });\n    console.log('教师错题列表API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('教师获取错题列表失败:', error);\n    return [];\n  }\n};\nexport const updateReinforcementExercise = (id, isCompleted) => {\n  return api.put(`/reinforcement-exercises/${id}?is_completed=${isCompleted}`);\n};\n\n// 删除强化训练记录\nexport const deleteReinforcementExercise = exerciseId => {\n  return api.delete(`/reinforcement-exercises/${exerciseId}`);\n};\n\n// 错题强化训练相关\nexport const generateExercises = (wrongQuestionId, count = 1) => {\n  // 添加超时处理，AI生成需要更长时间\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('生成练习请求超时');\n  }, 60000); // 60秒超时，给AI足够时间\n\n  return api.post('/ai/generate-exercises', {\n    wrong_question_id: wrongQuestionId,\n    count\n  }, {\n    signal: controller.signal\n  }).then(response => {\n    clearTimeout(timeoutId);\n    console.log('生成练习成功:', response);\n    return response;\n  }).catch(error => {\n    clearTimeout(timeoutId);\n    console.error('生成练习失败:', error);\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('AI生成超时，请稍后重试');\n    }\n    throw error;\n  });\n};\nexport const evaluateExerciseAnswer = (exerciseId, studentAnswer) => {\n  return api.post('/ai/evaluate-answer', {\n    exercise_id: exerciseId,\n    student_answer: studentAnswer\n  });\n};\n\n// 获取指定练习的答题记录\nexport const getExerciseAnswerRecords = exerciseId => {\n  return api.get(`/ai/exercise-answer-records/${exerciseId}`);\n};\n\n// 获取学生的所有答题记录\nexport const getStudentAnswerRecords = (limit = 50, offset = 0) => {\n  return api.get('/ai/student-answer-records', {\n    params: {\n      limit,\n      offset\n    }\n  });\n};\n\n// 学校数据 - 硬编码数据避免依赖后端API\nconst SCHOOLS_DATA = [{\n  id: 1,\n  school_name: '成都立格实验学校',\n  province: '四川省',\n  city: '成都市',\n  district: '锦江区'\n}, {\n  id: 2,\n  school_name: '成都市石室中学',\n  province: '四川省',\n  city: '成都市',\n  district: '武侯区'\n}, {\n  id: 3,\n  school_name: '成都市第七中学',\n  province: '四川省',\n  city: '成都市',\n  district: '青羊区'\n}, {\n  id: 4,\n  school_name: '成都外国语学校',\n  province: '四川省',\n  city: '成都市',\n  district: '高新区'\n}, {\n  id: 5,\n  school_name: '成都市树德中学',\n  province: '四川省',\n  city: '成都市',\n  district: '锦江区'\n}, {\n  id: 6,\n  school_name: '成都市第九中学',\n  province: '四川省',\n  city: '成都市',\n  district: '成华区'\n}, {\n  id: 7,\n  school_name: '成都市第十二中学',\n  province: '四川省',\n  city: '成都市',\n  district: '金牛区'\n}, {\n  id: 8,\n  school_name: '成都市嘉祥外国语学校',\n  province: '四川省',\n  city: '成都市',\n  district: '锦江区'\n}, {\n  id: 9,\n  school_name: '成都市实验中学',\n  province: '四川省',\n  city: '成都市',\n  district: '青羊区'\n}, {\n  id: 10,\n  school_name: '成都市第三中学',\n  province: '四川省',\n  city: '成都市',\n  district: '武侯区'\n}, {\n  id: 11,\n  school_name: '成都七中实验学校',\n  province: '四川省',\n  city: '成都市',\n  district: '双流区'\n}, {\n  id: 12,\n  school_name: '成都市双流中学',\n  province: '四川省',\n  city: '成都市',\n  district: '双流区'\n}, {\n  id: 13,\n  school_name: '绵阳中学',\n  province: '四川省',\n  city: '绵阳市',\n  district: '涪城区'\n}, {\n  id: 14,\n  school_name: '绵阳南山中学',\n  province: '四川省',\n  city: '绵阳市',\n  district: '涪城区'\n}, {\n  id: 15,\n  school_name: '绵阳东辰国际学校',\n  province: '四川省',\n  city: '绵阳市',\n  district: '涪城区'\n}, {\n  id: 16,\n  school_name: '绵阳富乐中学',\n  province: '四川省',\n  city: '绵阳市',\n  district: '科创区'\n}, {\n  id: 17,\n  school_name: '重庆市第一中学',\n  province: '重庆市',\n  city: '重庆市',\n  district: '渝中区'\n}, {\n  id: 18,\n  school_name: '重庆市第八中学',\n  province: '重庆市',\n  city: '重庆市',\n  district: '沙坪坝区'\n}, {\n  id: 19,\n  school_name: '重庆巴蜀中学',\n  province: '重庆市',\n  city: '重庆市',\n  district: '渝中区'\n}, {\n  id: 20,\n  school_name: '重庆南开中学',\n  province: '重庆市',\n  city: '重庆市',\n  district: '南岸区'\n}, {\n  id: 21,\n  school_name: '北京市第四中学',\n  province: '北京市',\n  city: '北京市',\n  district: '海淀区'\n}, {\n  id: 22,\n  school_name: '北京市第二中学',\n  province: '北京市',\n  city: '北京市',\n  district: '西城区'\n}, {\n  id: 23,\n  school_name: '上海市格致中学',\n  province: '上海市',\n  city: '上海市',\n  district: '黄浦区'\n}, {\n  id: 24,\n  school_name: '上海市第二中学',\n  province: '上海市',\n  city: '上海市',\n  district: '徐汇区'\n}, {\n  id: 25,\n  school_name: '广州市第二中学',\n  province: '广东省',\n  city: '广州市',\n  district: '越秀区'\n}, {\n  id: 26,\n  school_name: '深圳市高级中学',\n  province: '广东省',\n  city: '深圳市',\n  district: '福田区'\n}];\n\n// 学校管理相关\n// 公开API - 不需要登录也能获取学校列表\nexport const getSchools = async (params = {}) => {\n  try {\n    console.log('调用getSchools API, 参数:', params);\n\n    // 如果提供了地区参数，使用公开API\n    if (params.province || params.city || params.district) {\n      try {\n        const queryParams = new URLSearchParams();\n        if (params.province) queryParams.append('province', params.province);\n        if (params.city) queryParams.append('city', params.city);\n        if (params.district) queryParams.append('district', params.district);\n        const response = await api.get(`/schools/public?${queryParams.toString()}`);\n        console.log('获取学校列表成功:', response);\n        return response;\n      } catch (error) {\n        console.error('公开API获取学校列表失败，使用本地数据:', error);\n        // 如果API调用失败，返回硬编码的学校数据\n        return SCHOOLS_DATA.filter(school => {\n          let match = true;\n          if (params.province) match = match && school.province === params.province;\n          if (params.city) match = match && school.city === params.city;\n          if (params.district) match = match && school.district === params.district;\n          return match;\n        });\n      }\n    }\n    // 否则使用需要认证的API\n    else {\n      // 先尝试管理员API\n      try {\n        // 确保超级管理员可以看到所有学校\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        const isAdmin = user && user.is_admin;\n        console.log('当前用户是否为超级管理员:', isAdmin);\n\n        // 确保请求的是学校列表，而不是单个学校\n        const response = await api.get('/admin/schools');\n        console.log('从管理员API获取学校列表成功:', response);\n        return response;\n      } catch (adminError) {\n        console.log('管理员API获取学校列表失败，尝试使用普通API:', adminError);\n        try {\n          // 如果管理员API失败，尝试使用普通学校API\n          const response = await api.get('/schools/');\n          console.log('从普通API获取学校列表成功:', response);\n\n          // 确保返回的是数组数据\n          if (Array.isArray(response)) {\n            // 处理每个学校对象，确保地理位置字段存在\n            return response.map(school => ({\n              ...school,\n              province: school.province || '',\n              city: school.city || '',\n              district: school.district || ''\n            }));\n          } else if (response && Array.isArray(response.items)) {\n            // 如果是分页响应格式\n            return {\n              ...response,\n              items: response.items.map(school => ({\n                ...school,\n                province: school.province || '',\n                city: school.city || '',\n                district: school.district || ''\n              }))\n            };\n          } else {\n            // 其他情况，直接返回\n            return response;\n          }\n        } catch (error) {\n          console.error('所有API获取学校列表失败，返回空数组:', error);\n          return [];\n        }\n      }\n    }\n  } catch (error) {\n    console.error('获取学校列表失败:', error);\n    return [];\n  }\n};\n\n// 获取公开班级列表，用于注册页面\nexport const getPublicClasses = async (params = {}) => {\n  try {\n    console.log('调用getPublicClasses API, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.grade) queryParams.append('grade', params.grade);\n    try {\n      const response = await api.get(`/schools/public/classes?${queryParams.toString()}`);\n      console.log('获取班级列表成功:', response);\n      return response;\n    } catch (apiError) {\n      console.error('获取班级列表API调用失败:', apiError);\n      // 如果API调用失败，返回一个空数组\n      return [];\n    }\n  } catch (error) {\n    console.error('获取班级列表失败:', error);\n    return [];\n  }\n};\n\n// 获取公开科目列表，用于注册页面\nexport const getPublicSubjects = async (schoolId = null) => {\n  try {\n    console.log('调用getPublicSubjects API, schoolId:', schoolId);\n    try {\n      const params = schoolId ? {\n        school_id: schoolId\n      } : {};\n      const response = await api.get('/public/subjects', {\n        params\n      });\n      console.log('获取科目列表成功:', response);\n      return response;\n    } catch (apiError) {\n      console.error('获取科目列表API调用失败:', apiError);\n      // 如果API调用失败，返回默认科目列表\n      return [{\n        id: 1,\n        name: '语文'\n      }, {\n        id: 2,\n        name: '数学'\n      }, {\n        id: 3,\n        name: '英语'\n      }, {\n        id: 4,\n        name: '物理'\n      }, {\n        id: 5,\n        name: '化学'\n      }, {\n        id: 6,\n        name: '生物'\n      }, {\n        id: 7,\n        name: '历史'\n      }, {\n        id: 8,\n        name: '地理'\n      }, {\n        id: 9,\n        name: '政治'\n      }];\n    }\n  } catch (error) {\n    console.error('获取科目列表失败:', error);\n    return [];\n  }\n};\n\n// 区域数据 - 直接使用硬编码数据避免依赖后端API\nconst REGION_DATA = {\n  provinces: ['北京市', '天津市', '河北省', '山西省', '内蒙古自治区', '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '重庆市', '四川省', '贵州省', '云南省', '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区', '新疆维吾尔自治区', '香港特别行政区', '澳门特别行政区', '台湾省'],\n  cities: {\n    '四川省': ['成都市', '绵阳市', '德阳市', '南充市', '宜宾市', '自贡市', '泸州市', '雅安市', '眉山市', '乐山市', '广元市', '遂宁市', '内江市', '资阳市', '达州市', '广安市', '巴中市', '攀枝花市', '甘孜藏族自治州', '阿坝藏族羌族自治州', '凉山彝族自治州'],\n    '重庆市': ['重庆市'],\n    '北京市': ['北京市'],\n    '上海市': ['上海市'],\n    '天津市': ['天津市'],\n    '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],\n    '辽宁省': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],\n    '山西省': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市'],\n    '河北省': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'],\n    '山东省': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],\n    '河南省': ['郑州市', '开封市', '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '濮阳市', '许昌市', '漯河市', '三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市', '济源市'],\n    '江苏省': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],\n    '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'],\n    '安徽省': ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市'],\n    '福建省': ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市'],\n    '江西省': ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市'],\n    '湖北省': ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市', '恩施土家族苗族自治州', '仙桃市', '潜江市', '天门市', '神农架林区'],\n    '湖南省': ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '湘西土家族苗族自治州'],\n    '广西壮族自治区': ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市', '来宾市', '崇左市'],\n    '海南省': ['海口市', '三亚市', '三沙市', '儋州市', '五指山市', '琼海市', '文昌市', '万宁市', '东方市', '定安县', '屯昌县', '澄迈县', '临高县', '白沙黎族自治县', '昌江黎族自治县', '乐东黎族自治县', '陵水黎族自治县', '保亭黎族苗族自治县', '琼中黎族苗族自治县'],\n    '贵州省': ['贵阳市', '六盘水市', '遵义市', '安顺市', '毕节市', '铜仁市', '黔西南布依族苗族自治州', '黔东南苗族侗族自治州', '黔南布依族苗族自治州'],\n    '云南省': ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市', '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州', '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州', '怒江傈僳族自治州', '迪庆藏族自治州'],\n    '西藏自治区': ['拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市', '阿里地区'],\n    '陕西省': ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市'],\n    '甘肃省': ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市', '临夏回族自治州', '甘南藏族自治州'],\n    '青海省': ['西宁市', '海东市', '海北藏族自治州', '黄南藏族自治州', '海南藏族自治州', '果洛藏族自治州', '玉树藏族自治州', '海西蒙古族藏族自治州'],\n    '宁夏回族自治区': ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市'],\n    '新疆维吾尔自治区': ['乌鲁木齐市', '克拉玛依市', '吐鲁番市', '哈密市', '昌吉回族自治州', '博尔塔拉蒙古自治州', '巴音郭楞蒙古自治州', '阿克苏地区', '克孜勒苏柯尔克孜自治州', '喀什地区', '和田地区', '伊犁哈萨克自治州', '塔城地区', '阿勒泰地区', '石河子市', '阿拉尔市', '图木舒克市', '五家渠市', '北屯市', '铁门关市', '双河市', '可克达拉市', '昆玉市', '胡杨河市', '新星市'],\n    '内蒙古自治区': ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市', '兴安盟', '锡林郭勒盟', '阿拉善盟'],\n    '吉林省': ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市', '延边朝鲜族自治州'],\n    '黑龙江省': ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市', '七台河市', '牡丹江市', '黑河市', '绥化市', '大兴安岭地区'],\n    '香港特别行政区': ['香港特别行政区'],\n    '澳门特别行政区': ['澳门特别行政区'],\n    '台湾省': ['台北市', '高雄市', '台中市', '台南市', '新北市', '宜兰县', '桃园市', '新竹县', '苗栗县', '彰化县', '南投县', '云林县', '嘉义县', '屏东县', '台东县', '花莲县', '澎湖县', '基隆市', '新竹市', '嘉义市', '金门县', '连江县']\n  },\n  districts: {\n    // 四川省\n    '成都市': ['武侯区', '青羊区', '锦江区', '高新区', '成华区', '金牛区', '双流区', '温江区', '郫都区', '新都区', '龙泉驿区', '青白江区', '彭州市', '崇州市', '邛崃市', '都江堰市', '简阳市'],\n    '绵阳市': ['涪城区', '科创区', '安州区', '游仙区', '三台县', '盐亭县', '梓潼县', '北川羌族自治县', '平武县', '江油市'],\n    '德阳市': ['旌阳区', '罗江区', '中江县', '广汉市', '什邡市', '绵竹市'],\n    '南充市': ['顺庆区', '高坪区', '嘉陵区', '南部县', '营山县', '蓬安县', '仪陇县', '西充县', '阆中市'],\n    '宜宾市': ['翠屏区', '南溪区', '叙州区', '江安县', '长宁县', '高县', '珙县', '筠连县', '兴文县', '屏山县'],\n    // 直辖市\n    '重庆市': ['渝中区', '沙坪坝区', '南岸区', '九龙坡区', '渝北区', '江北区', '大渡口区', '北碚区', '巴南区', '涪陵区', '万州区', '黔江区', '长寿区', '江津区', '合川区', '永川区', '南川区', '綦江区', '大足区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平区', '武隆区', '城口县', '丰都县', '垫江县', '忠县', '云阳县', '奉节县', '巫山县', '巫溪县', '石柱土家族自治县', '秀山土家族苗族自治县', '酉阳土家族苗族自治县', '彭水苗族土家族自治县'],\n    '北京市': ['海淀区', '西城区', '朝阳区', '东城区', '丰台区', '石景山区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],\n    '上海市': ['黄浦区', '徐汇区', '静安区', '浦东新区', '长宁区', '普陀区', '虹口区', '杨浦区', '宝山区', '闵行区', '嘉定区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],\n    '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],\n    // 广东省\n    '广州市': ['越秀区', '天河区', '海珠区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],\n    '深圳市': ['福田区', '南山区', '罗湖区', '盐田区', '龙岗区', '宝安区', '龙华区', '坪山区', '光明区'],\n    '珠海市': ['香洲区', '斗门区', '金湾区'],\n    '汕头市': ['龙湖区', '金平区', '濠江区', '潮阳区', '潮南区', '澄海区', '南澳县'],\n    '佛山市': ['禅城区', '南海区', '顺德区', '高明区', '三水区'],\n    // 辽宁省\n    '沈阳市': ['和平区', '沈河区', '大东区', '皇姑区', '铁西区', '浑南区', '于洪区', '苏家屯区', '沈北新区', '康平县', '法库县', '辽中区', '新民市'],\n    '大连市': ['中山区', '西岗区', '沙河口区', '甘井子区', '旅顺口区', '金州区', '普兰店区', '瓦房店市', '庄河市', '长海县'],\n    '鞍山市': ['铁东区', '铁西区', '立山区', '千山区', '台安县', '岫岩满族自治县', '海城市'],\n    '抚顺市': ['新抚区', '东洲区', '望花区', '顺城区', '抚顺县', '新宾满族自治县', '清原满族自治县'],\n    // 山西省\n    '太原市': ['小店区', '迎泽区', '杏花岭区', '尖草坪区', '万柏林区', '晋源区', '清徐县', '阳曲县', '娄烦县', '古交市'],\n    '大同市': ['平城区', '云冈区', '新荣区', '阳高县', '天镇县', '广灵县', '灵丘县', '浑源县', '左云县', '云州区'],\n    '阳泉市': ['城区', '矿区', '郊区', '平定县', '盂县'],\n    // 河北省\n    '石家庄市': ['长安区', '桥西区', '新华区', '井陉矿区', '裕华区', '藁城区', '鹿泉区', '栾城区', '井陉县', '正定县', '行唐县', '灵寿县', '高邑县', '深泽县', '赞皇县', '无极县', '平山县', '元氏县', '赵县', '晋州市', '新乐市'],\n    '唐山市': ['路南区', '路北区', '古冶区', '开平区', '丰南区', '丰润区', '曹妃甸区', '滦州市', '滦南县', '乐亭县', '迁西县', '玉田县', '遵化市', '迁安市'],\n    '秦皇岛市': ['海港区', '山海关区', '北戴河区', '抚宁区', '青龙满族自治县', '昌黎县', '卢龙县'],\n    // 山东省\n    '济南市': ['历下区', '市中区', '槐荫区', '天桥区', '历城区', '长清区', '章丘区', '济阳区', '莱芜区', '钢城区', '平阴县', '商河县'],\n    '青岛市': ['市南区', '市北区', '黄岛区', '崂山区', '李沧区', '城阳区', '即墨区', '胶州市', '平度市', '莱西市'],\n    '淄博市': ['淄川区', '张店区', '博山区', '临淄区', '周村区', '桓台县', '高青县', '沂源县'],\n    // 河南省\n    '郑州市': ['中原区', '二七区', '管城回族区', '金水区', '上街区', '惠济区', '中牟县', '巩义市', '荥阳市', '新密市', '新郑市', '登封市'],\n    '开封市': ['鼓楼区', '龙亭区', '顺河回族区', '禹王台区', '祥符区', '杞县', '通许县', '尉氏县', '兰考县'],\n    '洛阳市': ['老城区', '西工区', '瀍河回族区', '涧西区', '偃师区', '孟津区', '洛龙区', '新安县', '栾川县', '嵩县', '汝阳县', '宜阳县', '洛宁县', '伊川县'],\n    // 江苏省\n    '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],\n    '无锡市': ['锡山区', '惠山区', '滨湖区', '梁溪区', '新吴区', '江阴市', '宜兴市'],\n    '徐州市': ['鼓楼区', '云龙区', '贾汪区', '泉山区', '铜山区', '丰县', '沛县', '睢宁县', '新沂市', '邳州市'],\n    // 浙江省\n    '杭州市': ['上城区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '富阳区', '临安区', '桐庐县', '淳安县', '建德市', '临平区', '钱塘区'],\n    '宁波市': ['海曙区', '江北区', '北仑区', '镇海区', '鄞州区', '奉化区', '象山县', '宁海县', '余姚市', '慈溪市'],\n    '温州市': ['鹿城区', '龙湾区', '瓯海区', '洞头区', '永嘉县', '平阳县', '苍南县', '文成县', '泰顺县', '瑞安市', '乐清市', '龙港市'],\n    // 安徽省\n    '合肥市': ['瑶海区', '庐阳区', '蜀山区', '包河区', '长丰县', '肥东县', '肥西县', '庐江县', '巢湖市'],\n    '芜湖市': ['镜湖区', '弋江区', '鸠江区', '湾沚区', '繁昌区', '南陵县', '无为市'],\n    '蚌埠市': ['龙子湖区', '蚌山区', '禹会区', '淮上区', '怀远县', '五河县', '固镇县'],\n    // 福建省\n    '福州市': ['鼓楼区', '台江区', '仓山区', '马尾区', '晋安区', '长乐区', '闽侯县', '连江县', '罗源县', '闽清县', '永泰县', '平潭县', '福清市'],\n    '厦门市': ['思明区', '海沧区', '湖里区', '集美区', '同安区', '翔安区'],\n    '莆田市': ['城厢区', '涵江区', '荔城区', '秀屿区', '仙游县'],\n    // 江西省\n    '南昌市': ['东湖区', '西湖区', '青云谱区', '青山湖区', '新建区', '红谷滩区', '南昌县', '安义县', '进贤县'],\n    '景德镇市': ['昌江区', '珠山区', '浮梁县', '乐平市'],\n    '萍乡市': ['安源区', '湘东区', '莲花县', '上栗县', '芦溪县'],\n    // 湖北省\n    '武汉市': ['江岸区', '江汉区', '硚口区', '汉阳区', '武昌区', '青山区', '洪山区', '东西湖区', '汉南区', '蔡甸区', '江夏区', '黄陂区', '新洲区'],\n    '黄石市': ['黄石港区', '西塞山区', '下陆区', '铁山区', '阳新县', '大冶市'],\n    '十堰市': ['茅箭区', '张湾区', '郧阳区', '郧西县', '竹山县', '竹溪县', '房县', '丹江口市'],\n    // 湖南省\n    '长沙市': ['芙蓉区', '天心区', '岳麓区', '开福区', '雨花区', '望城区', '长沙县', '浏阳市', '宁乡市'],\n    '株洲市': ['荷塘区', '芦淞区', '石峰区', '天元区', '渌口区', '攸县', '茶陵县', '炎陵县', '醴陵市'],\n    '湘潭市': ['雨湖区', '岳塘区', '湘潭县', '湘乡市', '韶山市'],\n    // 广西壮族自治区\n    '南宁市': ['兴宁区', '青秀区', '江南区', '西乡塘区', '良庆区', '邕宁区', '武鸣区', '隆安县', '马山县', '上林县', '宾阳县', '横县'],\n    '柳州市': ['城中区', '鱼峰区', '柳南区', '柳北区', '柳江区', '柳城县', '鹿寨县', '融安县', '融水苗族自治县', '三江侗族自治县'],\n    '桂林市': ['秀峰区', '叠彩区', '象山区', '七星区', '雁山区', '临桂区', '阳朔县', '灵川县', '全州县', '兴安县', '永福县', '灌阳县', '龙胜各族自治县', '资源县', '平乐县', '荔浦市', '恭城瑶族自治县'],\n    // 海南省\n    '海口市': ['秀英区', '龙华区', '琼山区', '美兰区'],\n    '三亚市': ['海棠区', '吉阳区', '天涯区', '崖州区'],\n    // 贵州省\n    '贵阳市': ['南明区', '云岩区', '花溪区', '乌当区', '白云区', '观山湖区', '开阳县', '息烽县', '修文县', '清镇市'],\n    '六盘水市': ['钟山区', '六枝特区', '水城区', '盘州市'],\n    // 云南省\n    '昆明市': ['五华区', '盘龙区', '官渡区', '西山区', '东川区', '呈贡区', '晋宁区', '富民县', '宜良县', '石林彝族自治县', '嵩明县', '禄劝彝族苗族自治县', '寻甸回族彝族自治县', '安宁市'],\n    '曲靖市': ['麒麟区', '沾益区', '马龙区', '陆良县', '师宗县', '罗平县', '富源县', '会泽县', '宣威市'],\n    // 西藏自治区\n    '拉萨市': ['城关区', '堆龙德庆区', '达孜区', '林周县', '当雄县', '尼木县', '曲水县', '墨竹工卡县'],\n    '日喀则市': ['桑珠孜区', '南木林县', '江孜县', '定日县', '萨迦县', '拉孜县', '昂仁县', '谢通门县', '白朗县', '仁布县', '康马县', '定结县', '仲巴县', '亚东县', '吉隆县', '聂拉木县', '萨嘎县', '岗巴县'],\n    // 陕西省\n    '西安市': ['新城区', '碑林区', '莲湖区', '灞桥区', '未央区', '雁塔区', '阎良区', '临潼区', '长安区', '高陵区', '鄠邑区', '蓝田县', '周至县'],\n    '铜川市': ['王益区', '印台区', '耀州区', '宜君县'],\n    // 甘肃省\n    '兰州市': ['城关区', '七里河区', '西固区', '安宁区', '红古区', '永登县', '皋兰县', '榆中县'],\n    '嘉峪关市': ['市辖区'],\n    // 青海省\n    '西宁市': ['城东区', '城中区', '城西区', '城北区', '湟中区', '大通回族土族自治县', '湟源县'],\n    '海东市': ['乐都区', '平安区', '民和回族土族自治县', '互助土族自治县', '化隆回族自治县', '循化撒拉族自治县'],\n    // 宁夏回族自治区\n    '银川市': ['兴庆区', '西夏区', '金凤区', '永宁县', '贺兰县', '灵武市'],\n    '石嘴山市': ['大武口区', '惠农区', '平罗县'],\n    // 新疆维吾尔自治区\n    '乌鲁木齐市': ['天山区', '沙依巴克区', '新市区', '水磨沟区', '头屯河区', '达坂城区', '米东区', '乌鲁木齐县'],\n    '克拉玛依市': ['独山子区', '克拉玛依区', '白碱滩区', '乌尔禾区'],\n    // 内蒙古自治区\n    '呼和浩特市': ['新城区', '回民区', '玉泉区', '赛罕区', '土默特左旗', '托克托县', '和林格尔县', '清水河县', '武川县'],\n    '包头市': ['东河区', '昆都仑区', '青山区', '石拐区', '白云鄂博矿区', '九原区', '土默特右旗', '固阳县', '达尔罕茂明安联合旗'],\n    // 吉林省\n    '长春市': ['南关区', '宽城区', '朝阳区', '二道区', '绿园区', '双阳区', '九台区', '农安县', '榆树市', '德惠市', '公主岭市'],\n    '吉林市': ['昌邑区', '龙潭区', '船营区', '丰满区', '永吉县', '蛟河市', '桦甸市', '舒兰市', '磐石市'],\n    // 黑龙江省\n    '哈尔滨市': ['道里区', '南岗区', '道外区', '平房区', '松北区', '香坊区', '呼兰区', '阿城区', '双城区', '依兰县', '方正县', '宾县', '巴彦县', '木兰县', '通河县', '延寿县', '尚志市', '五常市'],\n    '齐齐哈尔市': ['龙沙区', '建华区', '铁锋区', '昂昂溪区', '富拉尔基区', '碾子山区', '梅里斯达斡尔族区', '龙江县', '依安县', '泰来县', '甘南县', '富裕县', '克山县', '克东县', '拜泉县', '讷河市'],\n    // 香港、澳门、台湾\n    '香港特别行政区': ['中西区', '东区', '南区', '湾仔区', '九龙城区', '观塘区', '深水埗区', '黄大仙区', '油尖旺区', '离岛区', '葵青区', '北区', '西贡区', '沙田区', '大埔区', '荃湾区', '屯门区', '元朗区'],\n    '澳门特别行政区': ['花地玛堂区', '圣安多尼堂区', '大堂区', '望德堂区', '风顺堂区', '嘉模堂区', '圣方济各堂区', '路氹城'],\n    '台北市': ['中正区', '大同区', '中山区', '万华区', '信义区', '松山区', '大安区', '南港区', '北投区', '内湖区', '士林区', '文山区']\n  }\n};\n\n// 获取省份、城市、区县列表\nexport const getRegions = async (params = {}) => {\n  try {\n    // 1. 获取省份\n    if (!params.province) {\n      console.log('调用API获取省份数据');\n      try {\n        const response = await api.get('/regions/public/provinces');\n        return {\n          provinces: response.provinces\n        };\n      } catch (error) {\n        console.log('API调用失败，使用本地省份数据');\n        return {\n          provinces: REGION_DATA.provinces\n        };\n      }\n    }\n\n    // 2. 获取城市\n    if (params.province && !params.city) {\n      console.log(`调用API获取城市数据: ${params.province}`);\n      try {\n        const response = await api.get(`/regions/public/cities?province=${params.province}`);\n        return {\n          cities: response.cities\n        };\n      } catch (error) {\n        console.log(`API调用失败，使用本地城市数据: ${params.province}`);\n        const cities = REGION_DATA.cities[params.province] || [];\n        return {\n          cities\n        };\n      }\n    }\n\n    // 3. 获取区县\n    if (params.province && params.city) {\n      console.log(`调用API获取区县数据: ${params.city}`);\n      try {\n        const response = await api.get(`/regions/public/districts?city=${params.city}`);\n        return {\n          districts: response.districts\n        };\n      } catch (error) {\n        console.log(`API调用失败，使用本地区县数据: ${params.city}`);\n        const districts = REGION_DATA.districts[params.city] || [];\n        return {\n          districts\n        };\n      }\n    }\n\n    // 兜底\n    return {};\n  } catch (error) {\n    console.error('获取地区数据失败:', error);\n    return {};\n  }\n};\nexport const getSchool = async id => {\n  try {\n    const response = await api.get(`/schools/${id}`);\n    return response;\n  } catch (error) {\n    console.error(`获取学校详情失败, ID: ${id}:`, error);\n    throw new Error(`获取学校详情失败: ${error.message || '未知错误'}`);\n  }\n};\nexport const createSchool = async schoolData => {\n  try {\n    const response = await api.post('/schools/', schoolData);\n    return response;\n  } catch (error) {\n    console.error('创建学校失败:', error);\n    throw new Error(`创建学校失败: ${error.message || '未知错误'}`);\n  }\n};\nexport const updateSchool = async (id, schoolData) => {\n  try {\n    const response = await api.put(`/schools/${id}`, schoolData);\n    return response;\n  } catch (error) {\n    console.error(`更新学校失败, ID: ${id}:`, error);\n    throw new Error(`更新学校失败: ${error.message || '未知错误'}`);\n  }\n};\nexport const deleteSchool = async id => {\n  try {\n    console.log(`尝试删除学校，ID: ${id}`);\n    const response = await api.delete(`/admin/schools/${id}`);\n    console.log(`删除学校成功，ID: ${id}`);\n    return response;\n  } catch (error) {\n    console.error(`删除学校失败, ID: ${id}:`, error);\n    throw new Error(`删除学校失败: ${error.message || '未知错误'}`);\n  }\n};\n\n// 作业进度相关\nexport const getHomeworkProgress = async assignmentId => {\n  try {\n    console.log(`获取作业提交情况，作业任务ID: ${assignmentId}`);\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    // 使用现有的API端点，但添加作业任务ID作为过滤条件\n    const response = await api.get('/homework', {\n      params: {\n        assignment_id: assignmentId\n      },\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('获取作业提交情况成功:', response);\n\n    // 处理响应数据\n    if (Array.isArray(response)) {\n      return {\n        items: response,\n        total: response.length\n      };\n    } else if (response && Array.isArray(response.items)) {\n      return response;\n    } else {\n      console.warn('作业提交情况响应格式异常:', response);\n      return {\n        items: [],\n        total: 0\n      };\n    }\n  } catch (error) {\n    var _error$response12, _error$response12$dat;\n    console.error(`获取作业提交情况失败，作业任务ID: ${assignmentId}:`, error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    const errorMessage = ((_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : (_error$response12$dat = _error$response12.data) === null || _error$response12$dat === void 0 ? void 0 : _error$response12$dat.detail) || error.message || '未知错误';\n    throw new Error(`获取作业提交情况失败: ${errorMessage}`);\n  }\n};\n\n// 统计相关\nexport const getTeacherStatistics = async (params = {}) => {\n  try {\n    console.log('获取教师统计数据，参数:', params);\n\n    // 因为后端不直接支持日期筛选和班级筛选，我们需要使用export API\n    let apiEndpoint = '/statistics/teacher';\n\n    // 检查是否有日期参数、班级参数或作业参数，如果有则使用export API，它支持这些参数\n    if (params.start_date && params.end_date || params.class_id || params.assignment_id) {\n      console.log('检测到日期参数、班级参数或作业参数，使用export API获取详细统计数据:', params);\n      apiEndpoint = '/statistics/export';\n    }\n    const response = await api.get(apiEndpoint, {\n      params\n    });\n    console.log('获取教师统计数据成功:', response);\n\n    // 处理export API返回的数据格式\n    if (apiEndpoint === '/statistics/export') {\n      // 从export API返回的数据中构建统计信息\n      console.log('处理export API返回的数据');\n      const exportData = response.data || [];\n\n      // 获取班级名称（如果指定了class_id，就只有一个班级）\n      let className = '';\n      if (exportData.length > 0 && exportData[0].class_name) {\n        className = exportData[0].class_name;\n      }\n\n      // 计算统计数据\n      const totalHomeworks = exportData.length;\n      const gradedHomeworks = exportData.filter(hw => hw.status === 'graded').length;\n\n      // 计算正确题数统计\n      let correctCounts = exportData.filter(hw => hw.status === 'graded' && hw.accuracy != null).map(hw => Math.round(hw.accuracy * 10)); // 假设满分是10题\n\n      const highestCorrectCount = correctCounts.length > 0 ? Math.max(...correctCounts) : 0;\n      const lowestCorrectCount = correctCounts.length > 0 ? Math.min(...correctCounts) : 0;\n      const averageCorrectCount = correctCounts.length > 0 ? correctCounts.reduce((sum, count) => sum + count, 0) / correctCounts.length : 0;\n\n      // 构建处理后的响应\n      response.class_count = 1;\n      response.homework_count = totalHomeworks;\n      response.graded_homework_count = gradedHomeworks;\n      response.highest_correct_count = highestCorrectCount;\n      response.average_correct_count = averageCorrectCount;\n      response.lowest_correct_count = lowestCorrectCount;\n\n      // 如果选中了特定班级，就用这个班级构建class_statistics\n      if (params.class_id) {\n        const classStatObj = {\n          class_id: parseInt(params.class_id),\n          class_name: className || `班级 ${params.class_id}`,\n          homework_count: totalHomeworks,\n          student_count: new Set(exportData.map(hw => hw.student_name)).size,\n          highest_correct_count: highestCorrectCount,\n          average_correct_count: averageCorrectCount,\n          lowest_correct_count: lowestCorrectCount,\n          // 添加作业任务数和平均分字段\n          assignment_count: new Set(exportData.map(hw => hw.assignment_id)).size,\n          average_score: exportData.filter(hw => hw.status === 'graded' && hw.score != null).length > 0 ? exportData.filter(hw => hw.status === 'graded' && hw.score != null).reduce((sum, hw) => sum + hw.score, 0) / exportData.filter(hw => hw.status === 'graded' && hw.score != null).length : 0\n        };\n        response.class_statistics = {\n          [`class_${params.class_id}`]: classStatObj\n        };\n        response.class_statistics_array = [classStatObj];\n        console.log('为选定班级构建统计数据:', classStatObj);\n      }\n    }\n\n    // 格式化班级统计数据，将对象转换为数组\n    let classStatsArray = [];\n    if (response && response.class_statistics && typeof response.class_statistics === 'object') {\n      try {\n        classStatsArray = Object.entries(response.class_statistics).filter(([key, value]) => typeof value === 'object' && value !== null && value.class_id).map(([key, value]) => {\n          // 确保每个班级对象都有assignment_count和average_score字段\n          if (value.assignment_count === undefined) {\n            console.log(`班级 ${value.class_id || key} 缺少assignment_count字段，设置为默认值0`);\n            value.assignment_count = 0;\n          }\n          if (value.average_score === undefined) {\n            console.log(`班级 ${value.class_id || key} 缺少average_score字段，设置为默认值0`);\n            value.average_score = 0;\n          }\n          return value;\n        });\n        console.log('班级统计数据格式化:', classStatsArray.length, '个班级');\n      } catch (err) {\n        console.error('处理班级统计数据时出错:', err);\n      }\n    } else if (response && Array.isArray(response.class_statistics)) {\n      // 如果已经是数组格式，直接使用\n      classStatsArray = response.class_statistics.map(cls => {\n        // 确保每个班级对象都有assignment_count和average_score字段\n        if (cls.assignment_count === undefined) {\n          console.log(`班级 ${cls.class_id} 缺少assignment_count字段，设置为默认值0`);\n          cls.assignment_count = 0;\n        }\n        if (cls.average_score === undefined) {\n          console.log(`班级 ${cls.class_id} 缺少average_score字段，设置为默认值0`);\n          cls.average_score = 0;\n        }\n        return cls;\n      });\n      console.log('班级统计数据已经是数组格式:', classStatsArray.length, '个班级');\n    } else {\n      console.warn('班级统计数据格式异常:', response === null || response === void 0 ? void 0 : response.class_statistics);\n    }\n\n    // 确保我们至少有一个默认班级（只有在真实数据为空时才使用模拟数据）\n    if (classStatsArray.length === 0) {\n      classStatsArray = [{\n        class_id: 1,\n        class_name: \"示例班级\",\n        student_count: 25,\n        homework_count: 5,\n        average_accuracy: 0.8,\n        highest_correct_count: 5,\n        average_correct_count: 3.5,\n        lowest_correct_count: 2,\n        assignment_count: 3,\n        average_score: 80\n      }];\n    }\n\n    // 确保返回的数据包含预期的字段，避免前端读取时出现undefined\n    return {\n      school_count: response.school_count || 0,\n      class_count: response.class_count || 0,\n      student_count: response.student_count || 0,\n      homework_count: response.homework_count || response.total_homework_count || 0,\n      total_homework_count: response.total_homework_count || response.homework_count || 0,\n      corrected_count: response.corrected_count || response.graded_homework_count || 0,\n      graded_homework_count: response.graded_homework_count || response.corrected_count || 0,\n      pending_homework_count: response.pending_homework_count || (response.homework_count || response.total_homework_count || 0) - (response.graded_homework_count || response.corrected_count || 0),\n      average_accuracy: response.average_accuracy || 0,\n      highest_correct_count: response.highest_correct_count || 0,\n      average_correct_count: response.average_correct_count || 0,\n      lowest_correct_count: response.lowest_correct_count || 0,\n      class_statistics: response.class_statistics || {},\n      class_statistics_array: classStatsArray,\n      recent_homeworks: response.recent_homeworks || response.recent_assignments || []\n    };\n  } catch (error) {\n    console.error('获取教师统计数据失败:', error);\n    // 返回模拟数据，避免UI崩溃\n    return {\n      school_count: 1,\n      class_count: 1,\n      student_count: 25,\n      total_homework_count: 5,\n      graded_homework_count: 3,\n      corrected_count: 3,\n      pending_homework_count: 2,\n      average_accuracy: 0.8,\n      highest_correct_count: 5,\n      average_correct_count: 3.5,\n      lowest_correct_count: 2,\n      class_statistics: {\n        \"class_1\": {\n          class_id: 1,\n          class_name: \"示例班级\",\n          student_count: 25,\n          homework_count: 5,\n          average_accuracy: 0.8,\n          highest_correct_count: 5,\n          average_correct_count: 3.5,\n          lowest_correct_count: 2,\n          assignment_count: 3,\n          average_score: 80\n        }\n      },\n      class_statistics_array: [{\n        class_id: 1,\n        class_name: \"示例班级\",\n        student_count: 25,\n        homework_count: 5,\n        average_accuracy: 0.8,\n        highest_correct_count: 5,\n        average_correct_count: 3.5,\n        lowest_correct_count: 2,\n        assignment_count: 3,\n        average_score: 80\n      }],\n      recent_homeworks: [{\n        id: 1,\n        title: \"示例作业1\",\n        correct_count: 5,\n        student_id: 1,\n        student_name: \"示例学生\",\n        created_at: new Date().toISOString()\n      }],\n      error: error.message || '未知错误'\n    };\n  }\n};\nexport const getStudentStatistics = async (params = {}) => {\n  try {\n    console.log('获取学生统计数据，参数:', params);\n    const response = await api.get('/statistics/student', {\n      params\n    });\n    console.log('获取学生统计数据成功:', response);\n\n    // 确保accuracy_trend是数组\n    if (!response.accuracy_trend || !Array.isArray(response.accuracy_trend)) {\n      console.warn('正确率趋势数据格式异常:', response === null || response === void 0 ? void 0 : response.accuracy_trend);\n      response.accuracy_trend = [];\n    }\n    return response;\n  } catch (error) {\n    console.error('获取学生统计数据失败:', error);\n    // 返回模拟数据，避免UI崩溃\n    return {\n      total_homework_count: 0,\n      completed_homework_count: 0,\n      pending_homework_count: 0,\n      average_accuracy: 0,\n      wrong_question_count: 0,\n      reinforcement_exercise_count: 0,\n      completed_exercise_count: 0,\n      highest_correct_count: 0,\n      average_correct_count: 0,\n      lowest_correct_count: 0,\n      accuracy_trend: [],\n      error: error.message || '未知错误'\n    };\n  }\n};\nexport const getWrongQuestionStatistics = () => {\n  return api.get('/statistics/wrong-questions');\n};\nexport const exportStatistics = params => {\n  return api.get('/statistics/export', {\n    params\n  });\n};\n\n// AI助手相关\nexport const chatWithAI = messages => {\n  return api.post('/ai/chat', {\n    messages\n  });\n};\n\n// 错误分析和强化建议\nexport const analyzeError = data => {\n  console.log('调用analyzeError API, data:', data);\n  return api.post('/ai/analyze-error', data);\n};\nexport const generateReinforcement = data => {\n  console.log('调用generateReinforcement API, data:', data);\n  return api.post('/ai/generate-reinforcement', data);\n};\n\n// AI配置管理相关\nexport const getAiConfigs = async () => {\n  try {\n    console.log('获取AI配置列表 - 开始');\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n\n    // 获取当前用户信息来判断权限\n    let isAdmin = false;\n    try {\n      const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n      isAdmin = userInfo.is_admin || false;\n      console.log('用户是否为管理员:', isAdmin);\n    } catch (e) {\n      console.log('无法获取用户信息，使用默认权限');\n    }\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    // 根据用户权限选择接口\n    const endpoint = isAdmin ? '/admin/ai-configs' : '/admin/ai-configs/available';\n    console.log('使用接口:', endpoint);\n    const response = await api.get(endpoint, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('获取AI配置列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取AI配置列表失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\nexport const createAiConfig = async configData => {\n  try {\n    console.log('创建AI配置 - 开始', configData);\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.post('/admin/ai-configs', configData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('创建AI配置成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建AI配置失败:', error);\n    console.error('错误类型:', error.name);\n    console.error('错误消息:', error.message);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    if (error.response) {\n      var _error$response$data;\n      console.error('错误状态码:', error.response.status);\n      console.error('错误响应:', error.response.data);\n      throw new Error(`创建失败: ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message}`);\n    }\n    throw new Error(`操作失败: ${error.message}`);\n  }\n};\nexport const updateAiConfig = async (id, configData) => {\n  try {\n    console.log(`更新AI配置(ID: ${id}) - 开始`, configData);\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.put(`/admin/ai-configs/${id}`, configData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('更新AI配置成功:', response);\n    return response;\n  } catch (error) {\n    console.error(`更新AI配置(ID: ${id})失败:`, error);\n    console.error('错误类型:', error.name);\n    console.error('错误消息:', error.message);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    if (error.response) {\n      var _error$response$data2;\n      console.error('错误状态码:', error.response.status);\n      console.error('错误响应:', error.response.data);\n      throw new Error(`更新失败: ${((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message}`);\n    }\n    throw new Error(`操作失败: ${error.message}`);\n  }\n};\nexport const deleteAiConfig = async id => {\n  try {\n    console.log(`删除AI配置(ID: ${id}) - 开始`);\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.delete(`/admin/ai-configs/${id}`, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('删除AI配置成功:', response);\n    return response;\n  } catch (error) {\n    console.error(`删除AI配置(ID: ${id})失败:`, error);\n    console.error('错误类型:', error.name);\n    console.error('错误消息:', error.message);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    if (error.response) {\n      var _error$response$data3;\n      console.error('错误状态码:', error.response.status);\n      console.error('错误响应:', error.response.data);\n      throw new Error(`删除失败: ${((_error$response$data3 = error.response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.detail) || error.message}`);\n    }\n    throw new Error(`操作失败: ${error.message}`);\n  }\n};\n\n// 作业科目管理API - 已移至文件末尾并更新\n\nexport const createSubject = async subjectData => {\n  try {\n    console.log('调用createSubject API, data:', subjectData);\n    const token = localStorage.getItem('token');\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.post('/admin/subjects', subjectData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('创建作业科目成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建作业科目失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\nexport const updateSubject = async (id, subjectData) => {\n  try {\n    console.log(`调用updateSubject API, id: ${id}, data:`, subjectData);\n    const token = localStorage.getItem('token');\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.put(`/admin/subjects/${id}`, subjectData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('更新作业科目成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新作业科目失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\nexport const deleteSubject = async id => {\n  try {\n    console.log(`调用deleteSubject API, id: ${id}`);\n    const token = localStorage.getItem('token');\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.delete(`/admin/subjects/${id}`, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    clearTimeout(timeoutId);\n    console.log('删除作业科目成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除作业科目失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\n\n// 学校管理API\nexport const getSchoolDetail = async schoolId => {\n  try {\n    console.log(`获取学校信息，ID: ${schoolId}`);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.get(`/admin/schools/${schoolId}`, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('获取学校信息成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取学校信息失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    // 返回默认值避免前端崩溃\n    return {\n      id: schoolId,\n      name: \"加载失败\",\n      province: \"\",\n      city: \"\",\n      district: \"\",\n      address: \"\",\n      contact_info: \"\",\n      is_active: true,\n      class_count: 0,\n      teacher_count: 0,\n      student_count: 0\n    };\n  }\n};\nexport const updateSchoolInfo = async (schoolId, schoolData) => {\n  try {\n    console.log(`更新学校信息，ID: ${schoolId}`, schoolData);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.put(`/admin/schools/${schoolId}`, schoolData, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('更新学校信息成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新学校信息失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\nexport const getClassesBySchool = async schoolId => {\n  try {\n    console.log(`获取学校班级列表，学校ID: ${schoolId}，类型: ${typeof schoolId}`);\n    if (schoolId === null || schoolId === undefined) {\n      console.warn('未提供学校ID，无法获取班级列表');\n      return [];\n    }\n\n    // 尝试将schoolId转换为数字，但如果失败也继续使用原值\n    let numericSchoolId;\n    try {\n      numericSchoolId = parseInt(schoolId, 10);\n      if (isNaN(numericSchoolId)) {\n        console.warn('学校ID不是数字，使用原始值:', schoolId);\n        numericSchoolId = schoolId;\n      }\n    } catch (e) {\n      console.warn('转换学校ID失败，使用原始值:', schoolId);\n      numericSchoolId = schoolId;\n    }\n\n    // 使用处理后的ID\n    schoolId = numericSchoolId;\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    let response;\n    let errorMessages = [];\n    try {\n      // 第一种API路径尝试\n      console.log(`尝试第一种API路径: /admin/schools/${schoolId}/classes`);\n      response = await api.get(`/admin/schools/${schoolId}/classes`, {\n        signal: controller.signal\n      });\n      console.log('第一种API路径成功，响应:', response);\n    } catch (apiError) {\n      errorMessages.push(`第一种API路径错误: ${apiError.message || '未知错误'}`);\n      console.warn(`第一种API路径失败: /admin/schools/${schoolId}/classes`, apiError);\n\n      // 如果第一种API路径失败，尝试第二种路径\n      try {\n        console.log(`尝试第二种API路径: /schools/${schoolId}/classes`);\n        response = await api.get(`/schools/${schoolId}/classes`, {\n          signal: controller.signal\n        });\n        console.log('第二种API路径成功，响应:', response);\n      } catch (secondError) {\n        errorMessages.push(`第二种API路径错误: ${secondError.message || '未知错误'}`);\n        console.warn(`第二种API路径也失败: /schools/${schoolId}/classes`, secondError);\n\n        // 如果两种都失败，尝试查询所有班级然后过滤\n        try {\n          console.log(`尝试第三种API路径: /admin/classes?school_id=${schoolId}`);\n          const allClasses = await api.get(`/admin/classes?school_id=${schoolId}`, {\n            signal: controller.signal\n          });\n          console.log('第三种API路径成功，响应:', allClasses);\n\n          // 过滤属于这个学校的班级\n          response = Array.isArray(allClasses) ? allClasses.filter(c => c.school_id === parseInt(schoolId)) : [];\n          console.log('过滤后的班级列表:', response);\n        } catch (thirdError) {\n          errorMessages.push(`第三种API路径错误: ${thirdError.message || '未知错误'}`);\n          console.error('所有API路径都失败:', errorMessages);\n          response = [];\n        }\n      }\n    }\n    clearTimeout(timeoutId);\n\n    // 确保响应是数组格式\n    if (!Array.isArray(response)) {\n      console.warn('响应不是数组格式，尝试转换:', response);\n      if (response && typeof response === 'object') {\n        if (Array.isArray(response.items)) {\n          response = response.items;\n        } else {\n          response = [response];\n        }\n      } else {\n        response = [];\n      }\n    }\n    console.log('获取学校班级列表成功，最终结果:', response);\n    return response;\n  } catch (error) {\n    console.error('获取学校班级列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空数组避免前端崩溃\n    return [];\n  }\n};\n\n// 获取所有可用年级列表\nexport const getAvailableGrades = async () => {\n  try {\n    console.log('获取可用年级列表');\n    const response = await api.get('/admin/classes/grades');\n    console.log('获取年级列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取年级列表失败:', error);\n    // 返回默认年级列表作为备用\n    return [\"小学一年级\", \"小学二年级\", \"小学三年级\", \"小学四年级\", \"小学五年级\", \"小学六年级\", \"初中一年级\", \"初中二年级\", \"初中三年级\", \"高中一年级\", \"高中二年级\", \"高中三年级\"];\n  }\n};\nexport const createClassForSchool = async classData => {\n  try {\n    console.log('创建班级:', classData);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.post('/admin/classes', classData, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('创建班级成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建班级失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\nexport const updateClassInfo = async (classId, classData) => {\n  try {\n    console.log(`更新班级，ID: ${classId}`, classData);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.put(`/admin/classes/${classId}`, classData, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('更新班级成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新班级失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\nexport const deleteClassById = async classId => {\n  try {\n    console.log(`删除班级，ID: ${classId}`);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.delete(`/admin/classes/${classId}`, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('删除班级成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除班级失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\n\n// 角色管理API\nexport const getSchoolRoles = async () => {\n  try {\n    console.log('获取角色列表');\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    let response;\n    try {\n      // 尝试第一种API路径\n      response = await api.get('/admin/roles', {\n        signal: controller.signal\n      });\n    } catch (apiError) {\n      console.warn('第一种API路径失败: /admin/roles', apiError);\n      // 如果第一种路径失败，尝试第二种\n      try {\n        response = await api.get('/roles', {\n          signal: controller.signal\n        });\n      } catch (secondError) {\n        console.warn('第二种API路径也失败: /roles', secondError);\n        // 返回空数组，让前端组件使用预定义的USER_ROLES\n        response = [];\n      }\n    }\n    clearTimeout(timeoutId);\n    console.log('获取角色列表成功:', response);\n    return Array.isArray(response) ? response : [];\n  } catch (error) {\n    console.error('获取角色列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空数组，让前端组件使用预定义的USER_ROLES\n    return [];\n  }\n};\nexport const getSchoolUsers = async schoolId => {\n  try {\n    console.log(`获取学校用户列表，学校ID: ${schoolId}`);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    let response;\n    try {\n      // 尝试第一种API路径\n      response = await api.get(`/admin/schools/${schoolId}/users`, {\n        signal: controller.signal\n      });\n    } catch (apiError) {\n      console.warn(`第一种API路径失败: /admin/schools/${schoolId}/users`, apiError);\n      // 如果失败，尝试第二种路径\n      try {\n        response = await api.get(`/schools/${schoolId}/users`, {\n          signal: controller.signal\n        });\n      } catch (secondError) {\n        console.warn(`第二种API路径也失败: /schools/${schoolId}/users`, secondError);\n        // 返回空数组避免前端崩溃\n        response = [];\n      }\n    }\n    clearTimeout(timeoutId);\n    console.log('获取学校用户列表成功:', response);\n    return Array.isArray(response) ? response : [];\n  } catch (error) {\n    console.error('获取学校用户列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空数组避免前端崩溃\n    return [];\n  }\n};\nexport const assignRole = async roleData => {\n  try {\n    console.log('分配角色:', roleData);\n    const response = await api.post('/admin/roles/assign', roleData);\n    console.log('分配角色成功:', response);\n    return response;\n  } catch (error) {\n    console.error('分配角色失败:', error);\n    throw error;\n  }\n};\nexport const revokeRole = async roleData => {\n  try {\n    console.log('撤销角色:', roleData);\n    const response = await api.post('/admin/roles/revoke', roleData);\n    console.log('撤销角色成功:', response);\n    return response;\n  } catch (error) {\n    console.error('撤销角色失败:', error);\n    throw error;\n  }\n};\n\n// 获取仪表盘统计数据\nexport const getDashboardStatistics = async (params = {}) => {\n  try {\n    console.log('调用getDashboardStatistics API', params ? `参数: ${JSON.stringify(params)}` : '');\n    const response = await api.get('/statistics/statistics/dashboard', {\n      params\n    });\n    console.log('获取仪表盘统计数据成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取仪表盘统计数据失败:', error);\n    throw error;\n  }\n};\n\n// 获取注册设置\nexport const getRegistrationSettings = async () => {\n  try {\n    console.log('获取注册设置...');\n    console.log('发起请求: GET /api/public/registration-settings');\n    // 注意：这是公开API，不需要token\n    const response = await axios.get('/api/public/registration-settings');\n    console.log('注册设置API响应:', response);\n    console.log('获取注册设置成功:', response.data);\n    return response.data;\n  } catch (error) {\n    console.error('获取注册设置失败:', error);\n    console.error('错误详情:', error.response ? error.response.data : '无响应数据');\n    console.error('错误状态:', error.response ? error.response.status : '未知状态码');\n    // 默认返回都允许注册\n    return {\n      allow_student_registration: true,\n      allow_teacher_registration: true\n    };\n  }\n};\n\n// 获取高级注册设置\nexport const getAdvancedRegistrationSettings = async () => {\n  try {\n    console.log('获取高级注册设置...');\n    console.log('发起请求: GET /api/public/advanced-registration-settings');\n    // 注意：这是公开API，不需要token\n    const response = await axios.get('/api/public/advanced-registration-settings');\n    console.log('高级注册设置API响应:', response);\n    console.log('获取高级注册设置成功:', response.data);\n    return response.data;\n  } catch (error) {\n    console.error('获取高级注册设置失败:', error);\n    console.error('错误详情:', error.response ? error.response.data : '无响应数据');\n    console.error('错误状态:', error.response ? error.response.status : '未知状态码');\n    // 返回null表示获取失败\n    return null;\n  }\n};\n\n// 获取可用角色\nexport const getAvailableRoles = async () => {\n  try {\n    console.log('获取可用角色...');\n    console.log('发起请求: GET /api/public/available-roles');\n    // 注意：这是公开API，不需要token\n    const response = await axios.get('/api/public/available-roles');\n    console.log('可用角色API响应:', response);\n    if (response.data && Array.isArray(response.data.roles)) {\n      console.log('获取到可用角色:', response.data.roles.length, '个');\n      console.log('角色列表:', response.data.roles.map(r => r.name).join(', '));\n\n      // 检查是否有可用角色\n      if (response.data.roles.length === 0) {\n        console.warn('API返回的可用角色列表为空');\n      }\n    } else {\n      console.warn('API返回的数据格式不符合预期:', response.data);\n    }\n    return response.data;\n  } catch (error) {\n    console.error('获取可用角色失败:', error);\n    console.error('错误详情:', error.response ? error.response.data : '无响应数据');\n    console.error('错误状态:', error.response ? error.response.status : '未知状态码');\n\n    // 尝试从错误响应中获取更多信息\n    if (error.response && error.response.data) {\n      console.error('服务器返回的错误信息:', error.response.data);\n    }\n\n    // 返回默认角色\n    console.log('返回默认角色配置');\n    return {\n      roles: [{\n        name: \"student\",\n        requires_approval: false,\n        fields: {\n          school: {\n            required: true\n          },\n          class: {\n            required: true\n          },\n          subject: {\n            required: false,\n            hidden: true\n          }\n        }\n      }, {\n        name: \"teacher\",\n        requires_approval: true,\n        fields: {\n          school: {\n            required: true\n          },\n          class: {\n            required: false\n          },\n          subject: {\n            required: true\n          }\n        }\n      }],\n      allow_school_creation: false\n    };\n  }\n};\n\n// 获取注册审批列表\nexport const getRegistrationApprovals = async (params = {}) => {\n  try {\n    console.log('获取注册审批列表, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.status) queryParams.append('status', params.status);\n    if (params.role_name) queryParams.append('role_name', params.role_name);\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n    const response = await api.get(`/admin/registration-approvals?${queryParams.toString()}`);\n    console.log('获取注册审批列表成功:', response);\n    // 由于axios拦截器已经返回response.data，这里直接返回response即可\n    return response;\n  } catch (error) {\n    console.error('获取注册审批列表失败:', error);\n    throw error;\n  }\n};\n\n// 一级审核注册申请\nexport const firstReviewRegistration = async (approvalId, data) => {\n  try {\n    console.log(`一级审核注册申请 ID:${approvalId}, 数据:`, data);\n    const response = await api.put(`/admin/registration-approvals/${approvalId}/first-review`, data);\n    console.log('一级审核注册申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('一级审核注册申请失败:', error);\n    throw error;\n  }\n};\n\n// 二级审核注册申请\nexport const finalReviewRegistration = async (approvalId, data) => {\n  try {\n    console.log(`二级审核注册申请 ID:${approvalId}, 数据:`, data);\n    const response = await api.put(`/admin/registration-approvals/${approvalId}/final-review`, data);\n    console.log('二级审核注册申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('二级审核注册申请失败:', error);\n    throw error;\n  }\n};\n\n// 高级注册API\nexport const advancedRegister = async userData => {\n  try {\n    console.log('调用高级注册API, 数据:', userData);\n    const response = await axios.post('/api/advanced-register', userData);\n    console.log('高级注册成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('高级注册失败:', error);\n    if (error.response && error.response.data) {\n      throw error.response.data;\n    }\n    throw error;\n  }\n};\n\n// 搜索学生\nexport const searchStudents = async (params = {}) => {\n  try {\n    console.log('搜索学生, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.name) queryParams.append('name', params.name);\n    if (params.class_id) queryParams.append('class_id', params.class_id);\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    const response = await axios.get(`/api/public/student-search?${queryParams.toString()}`);\n    console.log('搜索学生成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('搜索学生失败:', error);\n    throw error;\n  }\n};\n\n// 创建家长-学生绑定\nexport const createParentStudentBinding = async bindingData => {\n  try {\n    console.log('创建家长-学生绑定, 数据:', bindingData);\n    const response = await api.post('/parent-student-binding', bindingData);\n    console.log('创建家长-学生绑定成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建家长-学生绑定失败:', error);\n    throw error;\n  }\n};\n\n// 验证绑定码\nexport const verifyBindingCode = async verificationData => {\n  try {\n    console.log('验证绑定码, 数据:', verificationData);\n    const response = await api.post('/verify-binding-code', verificationData);\n    console.log('验证绑定码成功:', response);\n    return response;\n  } catch (error) {\n    console.error('验证绑定码失败:', error);\n    throw error;\n  }\n};\n\n// 创建临时家长-学生绑定（用于注册流程，不需要认证）\nexport const createTempParentStudentBinding = async bindingData => {\n  try {\n    console.log('创建临时家长-学生绑定, 数据:', bindingData);\n    const response = await axios.post(`${getBaseURL()}/public/temp-parent-student-binding`, bindingData);\n    console.log('创建临时家长-学生绑定成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('创建临时家长-学生绑定失败:', error);\n    throw error;\n  }\n};\n\n// 验证临时绑定码（用于注册流程，不需要认证）\nexport const verifyTempBindingCode = async verificationData => {\n  try {\n    console.log('验证临时绑定码, 数据:', verificationData);\n    const response = await axios.post(`${getBaseURL()}/public/verify-temp-binding-code`, verificationData);\n    console.log('验证临时绑定码成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('验证临时绑定码失败:', error);\n    throw error;\n  }\n};\n\n// 获取学生的待验证绑定请求\nexport const getPendingBindings = async () => {\n  try {\n    console.log('获取待验证绑定请求');\n    const response = await api.get('/students/pending-bindings');\n    console.log('获取待验证绑定请求成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取待验证绑定请求失败:', error);\n    throw error;\n  }\n};\n\n// 拒绝绑定请求\nexport const rejectBinding = async bindingId => {\n  try {\n    console.log('拒绝绑定请求:', bindingId);\n    const response = await api.post(`/students/reject-binding/${bindingId}`);\n    console.log('拒绝绑定请求成功:', response);\n    return response;\n  } catch (error) {\n    console.error('拒绝绑定请求失败:', error);\n    throw error;\n  }\n};\n\n// 创建学校申请\nexport const createSchoolApplication = async applicationData => {\n  try {\n    console.log('创建学校申请, 数据:', applicationData);\n    const response = await api.post('/school-application', applicationData);\n    console.log('创建学校申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建学校申请失败:', error);\n    throw error;\n  }\n};\n\n// 获取用户的学校申请列表\nexport const getUserSchoolApplications = async () => {\n  try {\n    console.log('获取用户的学校申请列表');\n    const response = await api.get('/user/school-applications');\n    console.log('获取用户的学校申请列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取用户的学校申请列表失败:', error);\n    throw error;\n  }\n};\n\n// 管理员获取所有学校申请\nexport const getSchoolApplications = async (params = {}) => {\n  try {\n    console.log('获取所有学校申请, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.status) queryParams.append('status', params.status);\n    const response = await api.get(`/admin/school-applications?${queryParams.toString()}`);\n    console.log('获取所有学校申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取所有学校申请失败:', error);\n    throw error;\n  }\n};\n\n// 审批学校申请\nexport const reviewSchoolApplication = async (applicationId, reviewData) => {\n  try {\n    console.log(`审批学校申请 ID:${applicationId}, 数据:`, reviewData);\n    const response = await api.put(`/admin/school-applications/${applicationId}/review`, reviewData);\n    console.log('审批学校申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('审批学校申请失败:', error);\n    throw error;\n  }\n};\n\n// 获取班级学生列表\nexport const getClassStudents = async (schoolId, classId, params = {}) => {\n  try {\n    console.log(`获取班级学生列表，学校ID: ${schoolId}, 班级ID: ${classId}`);\n    const queryParams = new URLSearchParams();\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n    const response = await api.get(`/schools/${schoolId}/classes/${classId}/students?${queryParams.toString()}`);\n    console.log('获取班级学生列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取班级学生列表失败:', error);\n    throw error;\n  }\n};\nexport default api;\n\n// 获取科目分类列表\nexport const getSubjectCategories = async (params = {}) => {\n  try {\n    console.log('调用getSubjectCategories API');\n    const queryParams = new URLSearchParams();\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n    const response = await api.get(`/admin/subject-categories?${queryParams.toString()}`);\n    console.log('获取科目分类列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取科目分类列表失败:', error);\n    return {\n      total: 0,\n      items: []\n    };\n  }\n};\n\n// 创建科目分类\nexport const createSubjectCategory = async categoryData => {\n  try {\n    console.log('调用createSubjectCategory API');\n    const response = await api.post('/admin/subject-categories', categoryData);\n    console.log('创建科目分类成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建科目分类失败:', error);\n    throw error;\n  }\n};\n\n// 更新科目分类\nexport const updateSubjectCategory = async (categoryId, categoryData) => {\n  try {\n    console.log(`调用updateSubjectCategory API, ID: ${categoryId}`);\n    const response = await api.put(`/admin/subject-categories/${categoryId}`, categoryData);\n    console.log('更新科目分类成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新科目分类失败:', error);\n    throw error;\n  }\n};\n\n// 删除科目分类\nexport const deleteSubjectCategory = async categoryId => {\n  try {\n    console.log(`调用deleteSubjectCategory API, ID: ${categoryId}`);\n    const response = await api.delete(`/admin/subject-categories/${categoryId}`);\n    console.log('删除科目分类成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除科目分类失败:', error);\n    throw error;\n  }\n};\n\n// 获取所有年级\nexport const getAllGrades = async () => {\n  try {\n    console.log('调用getAllGrades API');\n    const response = await api.get('/admin/grades');\n    console.log('获取年级列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取年级列表失败:', error);\n    return [];\n  }\n};\n\n// 获取指定年级的科目\nexport const getSubjectsByGrade = async grade => {\n  try {\n    console.log(`调用getSubjectsByGrade API, 年级: ${grade}`);\n    const response = await api.get(`/admin/subjects/by-grade/${encodeURIComponent(grade)}`);\n    console.log('获取年级科目列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取年级科目列表失败:', error);\n    return [];\n  }\n};\n\n// 修改getSubjects函数，支持分类筛选\nexport const getSubjects = async (params = {}) => {\n  try {\n    console.log('调用getSubjects API');\n    const queryParams = new URLSearchParams();\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n    if (params.category_id) queryParams.append('category_id', params.category_id);\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.get(`/admin/subjects?${queryParams.toString()}`, {\n      signal: controller.signal\n    });\n    clearTimeout(timeoutId);\n    console.log('获取作业科目列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取作业科目列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空对象，避免前端崩溃\n    return {\n      total: 0,\n      items: []\n    };\n  }\n};\n\n// 获取角色列表\nexport const getRoles = async () => {\n  try {\n    console.log('调用getRoles API');\n    const response = await api.get('/admin/roles');\n    console.log('获取角色列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取角色列表失败:', error);\n    return [];\n  }\n};\n\n// 获取角色的科目权限\nexport const getRoleSubjectPermissions = async roleId => {\n  try {\n    console.log(`调用getRoleSubjectPermissions API, 角色ID: ${roleId}`);\n    const response = await api.get(`/admin/roles/${roleId}/subject-permissions`);\n    console.log('获取角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取角色科目权限失败:', error);\n    return [];\n  }\n};\n\n// 创建角色的科目权限\nexport const createRoleSubjectPermission = async (roleId, permissionData) => {\n  try {\n    console.log(`调用createRoleSubjectPermission API, 角色ID: ${roleId}`);\n    const response = await api.post(`/admin/roles/${roleId}/subject-permissions`, permissionData);\n    console.log('创建角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 更新角色的科目权限\nexport const updateRoleSubjectPermission = async (roleId, permissionId, permissionData) => {\n  try {\n    console.log(`调用updateRoleSubjectPermission API, 角色ID: ${roleId}, 权限ID: ${permissionId}`);\n    const response = await api.put(`/admin/roles/${roleId}/subject-permissions/${permissionId}`, permissionData);\n    console.log('更新角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 删除角色的科目权限\nexport const deleteRoleSubjectPermission = async (roleId, permissionId) => {\n  try {\n    console.log(`调用deleteRoleSubjectPermission API, 角色ID: ${roleId}, 权限ID: ${permissionId}`);\n    const response = await api.delete(`/admin/roles/${roleId}/subject-permissions/${permissionId}`);\n    console.log('删除角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 批量设置角色的科目权限\nexport const batchSetRoleSubjectPermissions = async (roleId, permissionsData) => {\n  try {\n    console.log(`调用batchSetRoleSubjectPermissions API, 角色ID: ${roleId}`);\n    const response = await api.post(`/admin/roles/${roleId}/batch-subject-permissions`, {\n      role_id: roleId,\n      permissions: permissionsData\n    });\n    console.log('批量设置角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('批量设置角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 获取当前用户对指定科目的权限\nexport const getUserSubjectPermission = async subjectId => {\n  try {\n    console.log(`调用getUserSubjectPermission API, 科目ID: ${subjectId}`);\n    const response = await api.get(`/public/user/subject-permissions/${subjectId}`);\n    console.log('获取用户科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取用户科目权限失败:', error);\n    return {\n      subject_id: subjectId,\n      can_view: true,\n      can_edit: false,\n      can_delete: false,\n      can_assign: false\n    };\n  }\n};\n\n// 拍照解题\nexport const photoSolve = async formData => {\n  try {\n    console.log('🚀 调用拍照解题API - 设置3分钟超时');\n    const response = await api.post('/students/photo-solve', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      timeout: 180000 // 3分钟超时 - 给AI充足的解题时间\n    });\n    console.log('✅ 拍照解题成功:', response);\n    return response;\n  } catch (error) {\n    console.error('❌ 拍照解题失败:', error);\n\n    // 更详细的错误处理\n    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {\n      throw new Error('AI正在深度分析题目，处理时间较长。建议：\\n• 确保图片清晰完整\\n• 题目文字清楚可读\\n• 稍后重试或联系老师');\n    }\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["axios", "moment", "getBaseURL", "forceLocal", "localStorage", "getItem", "console", "log", "window", "location", "hostname", "process", "env", "REACT_APP_API_URL", "protocol", "port", "apiUrl", "api", "create", "baseURL", "headers", "timeout", "withCredentials", "maxRedirects", "interceptors", "request", "use", "config", "token", "_config$method", "method", "toUpperCase", "url", "substring", "_config$method2", "warn", "error", "Promise", "reject", "response", "_response$config$meth", "status", "responseType", "data", "_error$config", "_error$config$method", "_error$config2", "_error$response", "_error$response2", "_error$config3", "message", "removeItem", "pathname", "href", "login", "username", "password", "formData", "URLSearchParams", "append", "post", "register", "userData", "getCurrentUser", "get", "then", "access_token", "setItem", "JSON", "stringify", "catch", "getClasses", "params", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "queryParams", "school_id", "grade_id", "grade", "signal", "firstError", "secondError", "adminResponse", "finalError", "clearTimeout", "getClass", "id", "noCache", "_t", "Date", "getTime", "maxRetries", "retryCount", "tryFetchClass", "_error$response3", "name", "Error", "backupResponse", "backup<PERSON><PERSON>r", "_backupError$response", "thirdResponse", "thirdError", "description", "created_at", "toISOString", "students", "student_count", "createClass", "classData", "updateClass", "put", "batchUpdateClasses", "classIds", "class_ids", "deleteClass", "delete", "addStudentToClass", "classId", "studentId", "isTempId", "startsWith", "replace", "search", "role", "limit", "userList", "Array", "isArray", "items", "exactMatch", "find", "u", "realStudentId", "length", "tempParts", "split", "studentData", "full_name", "email", "newUser", "createError", "secondResponse", "secondUserList", "secondExactMatch", "student_id", "_error$response4", "code", "detail", "removeStudentFromClass", "updateStudentInClass", "getUsersCount", "allUsersResponse", "total", "totalCount", "batchSize", "currentSkip", "hasMoreData", "batchResponse", "getUsers", "totalUsers", "countError", "is_active", "undefined", "skip", "parseInt", "toString", "map", "user", "join", "allUsers", "filteredItems", "searchTerm", "toLowerCase", "filter", "matchUsername", "includes", "matchFullName", "matchEmail", "paginatedItems", "slice", "createUser", "batchCreateStudents", "studentsData", "validatedData", "student", "String", "phone", "createdStudents", "push", "users", "temp_data", "searchError", "index", "_error$response5", "_error$response5$data", "errorMessage", "getHomeworks", "cache", "newParams", "page", "mockData", "getHomework", "getHomeworkHistory", "assignmentId", "assignment_id", "getAnnotatedImages", "homeworkId", "generateAnnotations", "correctHomework", "submitHomework", "_error$response6", "_error$response6$data", "batchUploadHomework", "_error$response7", "_error$response7$data", "updateHomework", "deleteHomework", "_error$response8", "_error$response8$data", "createHomeworkAssignment", "navigator", "onLine", "_error$response9", "_error$response0", "_error$response0$data", "errorDetail", "_error$response1", "_error$response1$data", "deleteHomeworkAssignment", "updateHomeworkAssignmentStatus", "patch", "_error$response10", "_error$response10$dat", "getHomeworkAssignment", "getStudentHomeworkAssignments", "formatTime", "time", "beijingTime", "utcOffset", "<PERSON><PERSON><PERSON><PERSON>", "format", "processedResponse", "homework", "submit_time", "submitted_at", "updated_at", "due_date", "_error$response11", "_error$response11$dat", "getHomeworkAssignments", "retries", "apiParams", "include_teacher_details", "retryError", "resolve", "processHomeworkTimes", "processedItems", "processedItem", "getWrongQuestions", "getWrongQuestion", "getReinforcementExercises", "getAdminWrongQuestions", "getTeacherWrongQuestions", "updateReinforcementExercise", "isCompleted", "deleteReinforcementExercise", "exerciseId", "generateExercises", "wrongQuestionId", "count", "wrong_question_id", "evaluateExerciseAnswer", "studentAnswer", "exercise_id", "student_answer", "getExerciseAnswerRecords", "getStudentAnswerRecords", "offset", "SCHOOLS_DATA", "school_name", "province", "city", "district", "getSchools", "school", "match", "parse", "isAdmin", "is_admin", "adminError", "getPublicClasses", "apiError", "getPublicSubjects", "schoolId", "REGION_DATA", "provinces", "cities", "districts", "getRegions", "getSchool", "createSchool", "schoolData", "updateSchool", "deleteSchool", "getHomeworkProgress", "_error$response12", "_error$response12$dat", "getTeacherStatistics", "apiEndpoint", "start_date", "end_date", "class_id", "exportData", "className", "class_name", "totalHomeworks", "gradedHomeworks", "hw", "correctCounts", "accuracy", "Math", "round", "highestCorrectCount", "max", "lowestCorrectCount", "min", "averageCorrectCount", "reduce", "sum", "class_count", "homework_count", "graded_homework_count", "highest_correct_count", "average_correct_count", "lowest_correct_count", "classStatObj", "Set", "student_name", "size", "assignment_count", "average_score", "score", "class_statistics", "class_statistics_array", "classStatsArray", "Object", "entries", "key", "value", "err", "cls", "average_accuracy", "school_count", "total_homework_count", "corrected_count", "pending_homework_count", "recent_homeworks", "recent_assignments", "title", "correct_count", "getStudentStatistics", "accuracy_trend", "completed_homework_count", "wrong_question_count", "reinforcement_exercise_count", "completed_exercise_count", "getWrongQuestionStatistics", "exportStatistics", "chatWithAI", "messages", "analyzeError", "generateReinforcement", "getAiConfigs", "userInfo", "e", "endpoint", "createAiConfig", "configData", "_error$response$data", "updateAiConfig", "_error$response$data2", "deleteAiConfig", "_error$response$data3", "createSubject", "subjectData", "updateSubject", "deleteSubject", "getSchoolDetail", "address", "contact_info", "teacher_count", "updateSchoolInfo", "getClassesBySchool", "numericSchoolId", "isNaN", "errorMessages", "allClasses", "c", "getAvailableGrades", "createClassForSchool", "updateClassInfo", "deleteClassById", "getSchoolRoles", "getSchoolUsers", "assignRole", "roleData", "revokeRole", "getDashboardStatistics", "getRegistrationSettings", "allow_student_registration", "allow_teacher_registration", "getAdvancedRegistrationSettings", "getAvailableRoles", "roles", "r", "requires_approval", "fields", "required", "class", "subject", "hidden", "allow_school_creation", "getRegistrationApprovals", "role_name", "firstReviewRegistration", "approvalId", "finalReviewRegistration", "advancedRegister", "searchStudents", "createParentStudentBinding", "bindingData", "verifyBindingCode", "verificationData", "createTempParentStudentBinding", "verifyTempBindingCode", "getPendingBindings", "rejectBinding", "bindingId", "createSchoolApplication", "applicationData", "getUserSchoolApplications", "getSchoolApplications", "reviewSchoolApplication", "applicationId", "reviewData", "getClassStudents", "getSubjectCategories", "createSubjectCategory", "categoryData", "updateSubjectCategory", "categoryId", "deleteSubjectCategory", "getAllGrades", "getSubjectsByGrade", "encodeURIComponent", "getSubjects", "category_id", "getRoles", "getRoleSubjectPermissions", "roleId", "createRoleSubjectPermission", "permissionData", "updateRoleSubjectPermission", "permissionId", "deleteRoleSubjectPermission", "batchSetRoleSubjectPermissions", "permissionsData", "role_id", "permissions", "getUserSubjectPermission", "subjectId", "subject_id", "can_view", "can_edit", "can_delete", "can_assign", "photoSolve"], "sources": ["D:/pythonproject/checkingsys/frontend/src/utils/api.js"], "sourcesContent": ["import axios from 'axios';\nimport moment from 'moment';\n\n// 动态获取API基础URL\nconst getBaseURL = () => {\n  // 检查是否强制使用本地API（开发测试用）\n  const forceLocal = localStorage.getItem('FORCE_LOCAL_API') === 'true';\n  if (forceLocal) {\n    console.log('🔧 强制使用本地API');\n    return 'http://localhost:8083/api';\n  }\n\n  // 临时强制使用本地API（开发调试）\n  if (window.location.hostname === 'localhost') {\n    console.log('🔧 开发环境强制使用本地API');\n    return 'http://localhost:8083/api';\n  }\n\n  // 如果设置了环境变量，使用环境变量\n  if (process.env.REACT_APP_API_URL) {\n    console.log('🔧 使用环境变量API URL:', process.env.REACT_APP_API_URL);\n    return process.env.REACT_APP_API_URL;\n  }\n\n  // 否则根据当前域名动态构建\n  const protocol = window.location.protocol;\n  const hostname = window.location.hostname;\n  const port = window.location.port;\n\n  let apiUrl;\n\n  // 如果是localhost或127.0.0.1，使用8083端口\n  if (hostname === 'localhost' || hostname === '127.0.0.1') {\n    apiUrl = `${protocol}//${hostname}:8083/api`;\n  } else if (hostname === '17learn.cn') {\n    // 特定外网域名，使用花生壳内网穿透\n    apiUrl = 'http://danphy.xicp.net:23277/api';\n  } else {\n    // 其他外网访问时，使用当前页面的端口（通常是同一个端口）\n    if (port) {\n      apiUrl = `${protocol}//${hostname}:${port}/api`;\n    } else {\n      // 如果没有端口，使用默认端口\n      apiUrl = `${protocol}//${hostname}/api`;\n    }\n  }\n\n  console.log('🌐 动态构建API URL:', apiUrl);\n  console.log('📍 当前页面信息:', { protocol, hostname, port });\n\n  return apiUrl;\n};\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: getBaseURL(),\n  headers: {\n    'Content-Type': 'application/json',\n    'X-Requested-With': 'XMLHttpRequest'  // 帮助某些服务器识别AJAX请求\n  },\n  // 添加更长的超时时间，处理可能的网络延迟\n  timeout: 30000,  // 增加到30秒\n  // 跨域请求设置\n  withCredentials: false,  // 修改为false，避免CORS问题\n  // 最大重定向次数\n  maxRedirects: 5\n});\n\nconsole.log('API Base URL:', getBaseURL());\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n      console.log(`🔐 请求 ${config.method?.toUpperCase()} ${config.url} 携带token: ${token.substring(0, 15)}...`);\n      console.log(`📍 完整URL: ${config.baseURL}${config.url}`);\n    } else {\n      console.warn(`⚠️ 请求 ${config.method?.toUpperCase()} ${config.url} 无token`);\n      console.log(`📍 完整URL: ${config.baseURL}${config.url}`);\n    }\n    return config;\n  },\n  (error) => {\n    console.error('❌ 请求拦截器错误:', error);\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    console.log(`✅ API响应成功: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.status);\n\n    // 如果是blob响应，返回原始数据\n    if (response.config.responseType === 'blob') {\n      return response.data;\n    }\n\n    return response.data;\n  },\n  (error) => {\n    const method = error.config?.method?.toUpperCase() || 'UNKNOWN';\n    const url = error.config?.url || 'unknown';\n    const status = error.response?.status || 'no response';\n    const data = error.response?.data || {};\n\n    console.error(`❌ API响应错误: ${method} ${url}`, {\n      status,\n      data,\n      message: error.message,\n      baseURL: error.config?.baseURL\n    });\n\n    if (error.response && error.response.status === 401) {\n      console.warn('🔐 认证失败，清除本地数据并重定向到登录页');\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      // 避免在登录页再次跳转到登录页形成循环\n      if (window.location.pathname !== '/login') {\n        console.log('🔄 重定向到登录页');\n        window.location.href = '/login';\n      }\n    }\n\n    return Promise.reject(error.response ? error.response.data : error);\n  }\n);\n\n// 认证相关\nexport const login = (username, password) => {\n  const formData = new URLSearchParams();\n  formData.append('username', username);\n  formData.append('password', password);\n\n  // 使用api实例的baseURL，而不是硬编码URL\n  return api.post('/login', formData, {\n    headers: {\n      'Content-Type': 'application/x-www-form-urlencoded'\n    }\n  });\n};\n\nexport const register = async (userData) => {\n  try {\n    console.log('调用register API, userData:', userData);\n    const response = await api.post('/user', userData);\n    console.log('注册成功:', response);\n    return response;\n  } catch (error) {\n    console.error('注册失败:', error);\n    throw error; // 重新抛出错误，让调用者处理\n  }\n};\n\nexport const getCurrentUser = () => {\n  return api.get('/me')\n    .then(userData => {\n      console.log('获取到用户数据:', userData);\n      \n      // 确保本地存储的用户数据是最新的\n      const token = localStorage.getItem('token');\n      if (token && userData) {\n        // 合并token到用户数据\n        userData.access_token = token;\n        \n        // 更新localStorage中的用户数据\n        localStorage.setItem('user', JSON.stringify(userData));\n      }\n      \n      return userData;\n    })\n    .catch(error => {\n      console.error('获取用户数据失败:', error);\n      throw error;\n    });\n};\n\n// 班级管理API\nexport const getClasses = async (params = {}) => {\n  try {\n    console.log('调用getClasses API', params ? `参数: ${JSON.stringify(params)}` : '');\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时\n    \n    // 构建查询参数\n    const queryParams = new URLSearchParams();\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.grade_id) queryParams.append('grade_id', params.grade_id);\n    if (params.grade) queryParams.append('grade', params.grade);\n    \n    // 尝试多个可能的API端点\n    let url = '/classes';  // 首先尝试直接的路径\n    let response = null;\n    \n    try {\n      console.log('尝试请求URL:', url);\n      response = await api.get(url, {\n        signal: controller.signal,\n        params: params\n      });\n    } catch (firstError) {\n      console.log('第一个API端点失败，尝试备用端点:', firstError);\n      try {\n        url = '/admin/classes';  // 尝试admin路径\n        console.log('尝试请求URL:', url);\n        response = await api.get(url, {\n          signal: controller.signal,\n          params: params\n        });\n      } catch (secondError) {\n        console.log('第二个API端点也失败，尝试最终备用端点:', secondError);\n        // 如果前两个都失败，尝试使用getClassesBySchool获取所有班级\n        try {\n          const adminResponse = await api.get('/admin', { signal: controller.signal });\n          if (adminResponse && adminResponse.school_id) {\n            console.log('获取到管理员学校ID:', adminResponse.school_id);\n            url = `/admin/schools/${adminResponse.school_id}/classes`;\n            console.log('尝试请求URL:', url);\n            response = await api.get(url, { signal: controller.signal });\n          } else {\n            console.log('无法获取管理员学校ID，尝试默认学校ID');\n            url = `/admin/schools/1/classes`;\n            console.log('尝试请求URL:', url);\n            response = await api.get(url, { signal: controller.signal });\n          }\n        } catch (finalError) {\n          console.error('所有API端点都失败:', finalError);\n          throw finalError;\n        }\n      }\n    }\n    \n    clearTimeout(timeoutId);\n    console.log('获取班级列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取班级列表失败:', error);\n    // 在此处不抛出错误，而是返回空数组，让调用方决定如何处理\n    return [];\n  }\n};\n\nexport const getClass = (id, noCache = false) => {\n  console.log('调用getClass API, id:', id, noCache ? '(禁用缓存)' : '(允许使用缓存)');\n  \n  // 构建请求参数，添加时间戳以避免缓存\n  const params = noCache ? { _t: new Date().getTime() } : {};\n  \n  // 添加超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error(`获取班级 ${id} 详情请求超时`);\n  }, 15000); // 15秒超时\n  \n  // 最大重试次数\n  const maxRetries = 2;\n  let retryCount = 0;\n  \n  // 使用真实API调用获取班级详情\n  const tryFetchClass = () => {\n    console.log(`尝试获取班级 ${id} 详情 (尝试 ${retryCount + 1}/${maxRetries + 1})`);\n    \n    // 添加详细的请求头配置\n    const headers = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest',\n      'Cache-Control': noCache ? 'no-cache, no-store' : 'default'\n    };\n    \n    return api.get(`/admin/classes/${id}`, { \n      params: params,\n      signal: controller.signal,\n      headers: headers\n      // 不再需要单独设置withCredentials，因为已在api实例中设置\n    })\n    .then(response => {\n      clearTimeout(timeoutId);\n      console.log(`获取班级 ${id} 详情成功:`, response);\n      return response;\n    })\n    .catch(error => {\n      console.error(`获取班级 ${id} 详情失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, error);\n      console.error(`错误类型: ${error.name}, 状态码: ${error.response?.status}, 消息: ${error.message}`);\n      \n      if (error.name === 'AbortError') {\n        throw new Error('请求超时，请检查网络连接');\n      }\n      \n      // 如果还有重试次数，尝试重试\n      if (retryCount < maxRetries) {\n        retryCount++;\n        console.log(`重试获取班级 ${id} 详情 (${retryCount}/${maxRetries})`);\n        return tryFetchClass();\n      }\n      \n      // 如果API调用失败，尝试备用API端点\n      console.log(`尝试备用API端点获取班级 ${id} 详情`);\n      return api.get(`/classes/${id}`, { \n        params: params,\n        headers: headers,\n        signal: controller.signal\n        // 不再需要单独设置withCredentials，因为已在api实例中设置\n      })\n        .then(backupResponse => {\n          console.log(`备用API成功获取班级 ${id} 详情:`, backupResponse);\n          return backupResponse;\n        })\n        .catch(backupError => {\n          console.error(`备用API获取班级 ${id} 详情失败:`, backupError);\n          console.error(`备用API错误类型: ${backupError.name}, 状态码: ${backupError.response?.status}, 消息: ${backupError.message}`);\n          \n          // 尝试第三个API端点\n          console.log(`尝试第三个API端点获取班级 ${id} 详情`);\n          return api.get(`/admin/classes/${id}/students`, { \n            params: params,\n            headers: headers,\n            signal: controller.signal\n            // 不再需要单独设置withCredentials，因为已在api实例中设置\n          })\n            .then(thirdResponse => {\n              console.log(`第三个API端点成功获取班级 ${id} 详情:`, thirdResponse);\n              return thirdResponse;\n            })\n            .catch(thirdError => {\n              console.error(`第三个API端点获取班级 ${id} 详情失败:`, thirdError);\n              \n              // 所有API尝试都失败，不使用模拟数据，直接返回空数据\n              console.log(`所有API尝试都失败，返回空数据`);\n              \n              // 返回空数据\n              return {\n                id: id,\n                name: `班级 ${id}`,\n                description: '',\n                created_at: new Date().toISOString(),\n                students: [],\n                student_count: 0,\n                grade: '',\n                school_id: 1,\n                error: true // 添加错误标记\n              };\n            });\n        });\n    });\n  };\n  \n  return tryFetchClass();\n};\n\nexport const createClass = (classData) => {\n  console.log('调用createClass API, data:', classData);\n  return api.post('/admin/classes', classData);\n};\n\nexport const updateClass = (id, classData) => {\n  return api.put(`/admin/classes/${id}`, classData);\n};\n\n// 批量更新班级\nexport const batchUpdateClasses = (classIds, classData) => {\n  return api.put(`/admin/classes/batch`, { class_ids: classIds, ...classData });\n};\n\nexport const deleteClass = (id) => {\n  return api.delete(`/admin/classes/${id}`);\n};\n\nexport const addStudentToClass = (classId, studentId) => {\n  console.log(`开始添加学生(ID:${studentId})到班级(ID:${classId})`);\n  \n  // 检查studentId是否是临时ID\n  const isTempId = typeof studentId === 'string' && studentId.startsWith('temp_');\n  \n  // 如果是临时ID，需要先查找真实ID\n  if (isTempId) {\n    console.log(`检测到临时ID ${studentId}，尝试查找真实ID`);\n    \n    // 从临时ID中提取用户名\n    const username = studentId.replace('temp_', '');\n    console.log(`从临时ID提取的用户名: ${username}`);\n    \n    // 使用用户名查找学生\n    return api.get('/admin/users', { \n      params: { \n        search: username,\n        role: 'student',\n        limit: 10\n      }\n    })\n    .then(response => {\n      console.log(`查找用户名 ${username} 的结果:`, response);\n      \n      // 检查返回格式\n      let userList = [];\n      if (Array.isArray(response)) {\n        userList = response;\n      } else if (response && Array.isArray(response.items)) {\n        userList = response.items;\n      }\n      \n      // 查找完全匹配的用户\n      const exactMatch = userList.find(u => u.username === username);\n      let realStudentId = null;\n      \n      if (exactMatch) {\n        console.log(`找到完全匹配的用户: ${username}, ID: ${exactMatch.id}`);\n        realStudentId = exactMatch.id;\n      } else if (userList.length > 0) {\n        // 如果没有完全匹配但有结果，使用第一个\n        console.log(`未找到完全匹配，使用第一个结果: ${userList[0].username}, ID: ${userList[0].id}`);\n        realStudentId = userList[0].id;\n      } else {\n        // 没有找到，尝试直接创建学生\n        console.log(`未找到用户 ${username}，尝试直接创建学生`);\n        \n        // 从临时ID中提取更多信息\n        const tempParts = studentId.split('_');\n        const studentData = {\n          username: username,\n          full_name: username, // 使用用户名作为姓名\n          email: `${username}@example.com`,\n          password: '123456'\n        };\n        \n        // 创建学生\n        return api.post('/admin/users', studentData)\n          .then(newUser => {\n            console.log(`成功创建学生: ${username}, ID: ${newUser.id}`);\n            // 使用新创建的学生ID添加到班级\n            return addStudentToClass(classId, newUser.id);\n          })\n          .catch(createError => {\n            console.error(`创建学生 ${username} 失败:`, createError);\n            \n            // 如果创建失败，可能是因为用户已存在，再次尝试查找\n            return api.get('/admin/users', { \n              params: { \n                search: username,\n                role: 'student',\n                limit: 10\n              }\n            })\n            .then(secondResponse => {\n              console.log(`二次查找用户名 ${username} 的结果:`, secondResponse);\n              \n              let secondUserList = [];\n              if (Array.isArray(secondResponse)) {\n                secondUserList = secondResponse;\n              } else if (secondResponse && Array.isArray(secondResponse.items)) {\n                secondUserList = secondResponse.items;\n              }\n              \n              const secondExactMatch = secondUserList.find(u => u.username === username);\n              \n              if (secondExactMatch) {\n                console.log(`二次查找找到完全匹配的用户: ${username}, ID: ${secondExactMatch.id}`);\n                return addStudentToClass(classId, secondExactMatch.id);\n              } else if (secondUserList.length > 0) {\n                console.log(`二次查找未找到完全匹配，使用第一个结果: ${secondUserList[0].username}, ID: ${secondUserList[0].id}`);\n                return addStudentToClass(classId, secondUserList[0].id);\n              } else {\n                console.log(`二次查找仍未找到用户 ${username}，返回部分成功状态`);\n                return {\n                  id: studentId,\n                  status: 'partial_success', \n                  message: '无法找到或创建学生，请手动添加'\n                };\n              }\n            })\n            .catch(secondError => {\n              console.error(`二次查找用户 ${username} 失败:`, secondError);\n              return {\n                id: studentId,\n                status: 'partial_success', \n                message: '查找和创建学生都失败，请手动添加'\n              };\n            });\n          });\n      }\n      \n      // 使用找到的真实ID添加学生到班级\n      console.log(`使用真实ID ${realStudentId} 添加学生到班级 ${classId}`);\n      return addStudentToClass(classId, realStudentId);\n    })\n    .catch(error => {\n      console.error(`查找用户 ${username} 失败:`, error);\n      return {\n        id: studentId,\n        status: 'partial_success', \n        message: '查找学生信息失败，请刷新页面后重试'\n      };\n    });\n  }\n  \n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('添加学生到班级请求超时');\n  }, 10000); // 10秒超时\n  \n  return api.post(`/admin/classes/${classId}/students`, { student_id: studentId }, {\n    signal: controller.signal\n  })\n  .then(response => {\n    clearTimeout(timeoutId);\n    console.log(`成功添加学生(ID:${studentId})到班级(ID:${classId})，响应:`, response);\n    return {\n      ...response,\n      status: 'success',\n      message: '学生添加成功'\n    };\n  })\n  .catch(error => {\n    clearTimeout(timeoutId);\n    console.error(`添加学生(ID:${studentId})到班级(ID:${classId})失败:`, error);\n    console.error(`错误详情:`, error.response?.data || error.message);\n    \n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      // 超时错误，返回部分成功状态而不是抛出异常\n      console.log('请求超时，返回部分成功状态');\n      return { \n        id: studentId, \n        status: 'partial_success', \n        message: '请求超时，但操作可能已完成，请刷新页面查看最新结果' \n      };\n    }\n    \n    if (error.response && error.response.status === 400 && \n        error.response.data && error.response.data.detail === \"该学生已在班级中\") {\n      // 如果学生已在班级中，不视为错误，返回成功\n      console.log(`学生(ID:${studentId})已在班级(ID:${classId})中，跳过`);\n      return { id: studentId, status: 'skipped', message: '学生已在班级中' };\n    }\n    \n    if (error.response && error.response.status === 422) {\n      // Unprocessable Entity错误，可能是临时ID或无效ID\n      console.log('ID格式无效(422错误)，返回部分成功状态');\n      return { \n        id: studentId, \n        status: 'partial_success', \n        message: '操作可能已成功，请刷新页面查看最新结果' \n      };\n    }\n    \n    // 检查是否为后端已成功处理但返回错误的情况\n    if (error.response && error.response.status === 500) {\n      console.log('服务器返回500错误，但操作可能已成功，返回部分成功状态');\n      return { id: studentId, status: 'partial_success', message: '操作可能已成功，请刷新页面查看' };\n    }\n    \n    // 为所有错误返回部分成功状态，因为实际上操作可能已成功\n    console.log('返回部分成功状态，建议用户刷新页面');\n    return { id: studentId, status: 'partial_success', message: '操作可能已成功，请刷新页面查看' };\n  });\n};\n\nexport const removeStudentFromClass = (classId, studentId) => {\n  return api.delete(`/admin/classes/${classId}/students/${studentId}`);\n};\n\nexport const updateStudentInClass = (classId, studentId, studentData) => {\n  console.log(`调用updateStudentInClass API, classId: ${classId}, studentId: ${studentId}, data:`, studentData);\n  return api.put(`/admin/classes/${classId}/students/${studentId}`, studentData)\n    .then(response => {\n      console.log('学生信息更新成功:', response);\n      return response;\n    })\n    .catch(error => {\n      console.error('学生信息更新失败:', error);\n      throw error;\n    });\n};\n\n// 用户管理API\nexport const getUsersCount = async () => {\n  try {\n    console.log('获取用户总数...');\n    \n    // 直接请求所有用户，使用非常大的limit值\n    console.log('直接请求所有用户...');\n    const allUsersResponse = await api.get('/admin/users?limit=10000&skip=0');\n    \n    if (Array.isArray(allUsersResponse)) {\n      console.log(`获取到所有用户，总数: ${allUsersResponse.length}`);\n      return allUsersResponse.length;\n    } else if (allUsersResponse && typeof allUsersResponse === 'object' && 'total' in allUsersResponse) {\n      console.log(`API返回的用户总数: ${allUsersResponse.total}`);\n      return allUsersResponse.total;\n    }\n    \n    // 如果上述方法失败，尝试使用多次请求，每次增加skip值，直到获取不到数据为止\n    console.log('尝试使用多次请求获取总用户数...');\n    let totalCount = 0;\n    let batchSize = 100;\n    let currentSkip = 0;\n    let hasMoreData = true;\n    \n    while (hasMoreData) {\n      const batchResponse = await api.get(`/admin/users?limit=${batchSize}&skip=${currentSkip}`);\n      \n      if (Array.isArray(batchResponse) && batchResponse.length > 0) {\n        totalCount += batchResponse.length;\n        currentSkip += batchSize;\n        console.log(`批次获取用户: 当前总数=${totalCount}, skip=${currentSkip}`);\n        \n        // 如果返回的数据少于batchSize，说明已经获取完所有数据\n        if (batchResponse.length < batchSize) {\n          hasMoreData = false;\n        }\n      } else {\n        hasMoreData = false;\n      }\n      \n      // 安全检查，避免无限循环\n      if (currentSkip > 10000) {\n        console.warn('达到最大请求次数，中止获取');\n        break;\n      }\n    }\n    \n    if (totalCount > 0) {\n      console.log(`通过多次请求获取到的总用户数: ${totalCount}`);\n      return totalCount;\n    }\n    \n    // 如果所有方法都失败，返回一个硬编码的值\n    console.warn('无法确定用户总数，返回硬编码值63');\n    return 63; // 硬编码的用户总数\n  } catch (error) {\n    console.error('获取用户总数失败:', error);\n    return 63; // 出错时的硬编码值\n  }\n};\n\nexport const getUsers = async (params = {}) => {\n  try {\n    console.log('调用getUsers API', params ? `参数: ${JSON.stringify(params)}` : '');\n    \n    // 首先获取用户总数\n    let totalUsers = 100; // 默认值\n    try {\n      totalUsers = await getUsersCount();\n      console.log(`获取到的用户总数: ${totalUsers}`);\n    } catch (countError) {\n      console.error('获取用户总数失败，使用默认值:', countError);\n    }\n    \n    // 构建查询参数\n    const queryParams = new URLSearchParams();\n    if (params.search) queryParams.append('search', params.search);\n    if (params.role) queryParams.append('role', params.role);\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.is_active !== undefined) queryParams.append('is_active', params.is_active);\n    \n    // 确保分页参数正确传递\n    const skip = params.skip !== undefined ? parseInt(params.skip) : 0;\n    const limit = params.limit !== undefined ? parseInt(params.limit) : 10;\n    \n    queryParams.append('skip', skip);\n    queryParams.append('limit', limit);\n    \n    // 记录详细的分页信息\n    console.log(`分页请求: skip=${skip}, limit=${limit}, 页码=${skip/limit + 1}`);\n    \n    // 构建URL\n    let url = '/admin/users';\n    if (queryParams.toString()) {\n      url += `?${queryParams.toString()}`;\n    }\n    \n    console.log('发送用户API请求:', url);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    // 直接发起请求，确保参数被正确传递\n    const response = await api.get(url, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('获取用户API响应:', response);\n    \n    // 处理响应格式\n    if (response && typeof response === 'object' && 'items' in response) {\n      // 新的响应格式\n      console.log('使用新格式响应 - 总数:', response.total, '用户数:', response.items.length);\n      \n      // 验证搜索结果\n      if (params.search && response.items.length > 0) {\n        console.log('搜索结果示例:', \n          response.items.map(user => user.username).join(', ')\n        );\n      } else if (params.search) {\n        console.log('搜索无匹配结果');\n      }\n      \n      // 确保total字段存在且为数值\n      if (response.total === undefined || response.total === null) {\n        console.warn('API响应中缺少total字段，使用预先获取的总数');\n        response.total = totalUsers;\n      }\n      \n      return response;\n    } else if (Array.isArray(response)) {\n      // 兼容旧格式 - 直接返回数组\n      console.log('收到旧格式响应(数组) - 用户数:', response.length);\n      \n      // 无论如何，尝试获取所有用户\n      let allUsers = [];\n      try {\n        console.log('尝试获取所有用户...');\n        const allUsersResponse = await api.get('/admin/users?limit=10000&skip=0');\n        if (Array.isArray(allUsersResponse) && allUsersResponse.length > 0) {\n          console.log(`获取到所有用户: ${allUsersResponse.length}条`);\n          allUsers = allUsersResponse;\n        } else {\n          console.log('无法获取所有用户，使用当前响应');\n          allUsers = response;\n        }\n      } catch (error) {\n        console.error('获取所有用户失败，使用当前响应:', error);\n        allUsers = response;\n      }\n      \n      let filteredItems = allUsers;\n      \n      // 如果有搜索参数，对数据进行本地过滤\n      if (params.search) {\n        const searchTerm = params.search.toLowerCase();\n        console.log('在前端执行本地搜索:', searchTerm);\n        \n        filteredItems = allUsers.filter(user => {\n          const matchUsername = user.username && user.username.toLowerCase().includes(searchTerm);\n          const matchFullName = user.full_name && user.full_name.toLowerCase().includes(searchTerm);\n          const matchEmail = user.email && user.email.toLowerCase().includes(searchTerm);\n          \n          if (matchUsername || matchFullName || matchEmail) {\n            console.log('匹配到用户:', user.username);\n            return true;\n          }\n          return false;\n        });\n        \n        console.log('本地过滤后的结果数:', filteredItems.length);\n        if (filteredItems.length > 0) {\n          console.log('搜索结果用户名:', filteredItems.map(user => user.username).join(', '));\n        }\n      }\n      \n      // 手动应用分页 - 确保有足够的数据\n      console.log(`手动应用分页: skip=${skip}, limit=${limit}, 总数=${filteredItems.length}，实际用户总数=${totalUsers}`);\n      \n      // 检查是否有足够的数据进行分页\n      if (skip >= filteredItems.length) {\n        console.warn(`请求的起始位置(${skip})超过了可用数据长度(${filteredItems.length})，返回空数组`);\n        return {\n          total: totalUsers,\n          items: []\n        };\n      }\n      \n      // 应用分页\n      const paginatedItems = filteredItems.slice(skip, skip + limit);\n      console.log(`分页后结果数: ${paginatedItems.length}, 范围: ${skip} - ${skip + paginatedItems.length}`);\n      \n      // 返回分页后的结果\n      return {\n        total: totalUsers,\n        items: paginatedItems\n      };\n    } else {\n      console.error('无法识别的用户数据格式:', response);\n      return { total: totalUsers, items: [] };\n    }\n  } catch (error) {\n    console.error('获取用户列表失败:', error);\n    throw error;\n  }\n};\n\nexport const createUser = async (userData) => {\n  try {\n    console.log('创建用户:', userData);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.post('/admin/users', userData, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('创建用户成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建用户失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\n\nexport const batchCreateStudents = (studentsData) => {\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('批量创建学生请求超时');\n  }, 15000); // 15秒超时\n  \n  // 调试日志\n  console.log('批量创建学生请求数据:', JSON.stringify(studentsData));\n  \n  // 确保每个学生对象都有正确的字段格式\n  const validatedData = studentsData.map(student => ({\n    username: String(student.username || ''), // 强制转换为字符串\n    email: student.email || '',\n    full_name: student.full_name || '',\n    phone: student.phone || '',\n    password: student.password || '123456'\n  }));\n  \n  console.log('验证后的批量创建学生数据:', JSON.stringify(validatedData));\n  \n  return api.post('/admin/batch-students', validatedData, {\n    signal: controller.signal\n  })\n  .then(async response => {\n    clearTimeout(timeoutId);\n    console.log('批量创建学生成功，响应:', response);\n    \n    // 检查响应是否为空数组\n    if (Array.isArray(response) && response.length === 0) {\n      console.log('后端返回空数组，尝试直接创建学生');\n      \n      // 直接创建学生\n      const createdStudents = [];\n      for (const student of validatedData) {\n        try {\n          console.log(`尝试直接创建学生: ${student.username}`);\n          const newUser = await api.post('/admin/users', student);\n          console.log(`成功创建学生: ${student.username}, ID: ${newUser.id}`);\n          createdStudents.push(newUser);\n        } catch (error) {\n          console.error(`直接创建学生 ${student.username} 失败:`, error);\n          \n          // 如果创建失败，可能是因为用户已存在，尝试查找\n          try {\n            console.log(`尝试查找用户名为 ${student.username} 的学生`);\n            const users = await api.get('/admin/users', { \n              params: { \n                search: student.username,\n                role: 'student',\n                limit: 10\n              }\n            });\n            \n            console.log(`查找用户名 ${student.username} 的结果:`, users);\n            \n            // 检查返回格式\n            let userList = [];\n            if (Array.isArray(users)) {\n              userList = users;\n            } else if (users && Array.isArray(users.items)) {\n              userList = users.items;\n            }\n            \n            // 查找完全匹配的用户\n            const exactMatch = userList.find(u => u.username === student.username);\n            if (exactMatch) {\n              console.log(`找到完全匹配的用户: ${student.username}, ID: ${exactMatch.id}`);\n              createdStudents.push(exactMatch);\n            } else if (userList.length > 0) {\n              // 如果没有完全匹配但有结果，使用第一个\n              console.log(`未找到完全匹配，使用第一个结果: ${userList[0].username}, ID: ${userList[0].id}`);\n              createdStudents.push(userList[0]);\n            } else {\n              // 没有找到，创建临时对象\n              console.log(`未找到用户 ${student.username}，创建临时对象`);\n              createdStudents.push({\n                ...student,\n                id: `temp_${student.username}`,\n                temp_data: true\n              });\n            }\n          } catch (searchError) {\n            console.error(`查找用户 ${student.username} 失败:`, searchError);\n            // 创建临时对象\n            createdStudents.push({\n              ...student,\n              id: `temp_${student.username}`,\n              temp_data: true\n            });\n          }\n        }\n      }\n      \n      console.log('直接创建学生结果:', createdStudents);\n      return createdStudents;\n    }\n    \n    return response;\n  })\n  .catch(error => {\n    clearTimeout(timeoutId);\n    console.error('批量创建学生失败:', error);\n    \n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    \n    if (!error.response) {\n      // 网络错误，没有收到响应\n      throw new Error('网络错误，请检查您的网络连接');\n    } else if (error.response.status >= 500) {\n      // 服务器错误，返回临时数据\n      console.log('服务器错误，返回临时数据供前端处理');\n      return validatedData.map((student, index) => ({\n        ...student,\n        id: `temp_${student.username}`,\n        temp_data: true\n      }));\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      // 其他错误\n      const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n      throw new Error(`批量创建学生失败: ${errorMessage}`);\n    }\n  });\n};\n\n// 作业相关\nexport const getHomeworks = async (params) => {\n  try {\n    console.log('获取作业列表，参数:', params);\n    \n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时\n    \n    // 添加禁用缓存的请求头\n    const headers = {};\n    if (params && params.cache === false) {\n      console.log('禁用作业列表缓存');\n      headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';\n      headers['Pragma'] = 'no-cache';\n      headers['Expires'] = '0';\n      // 移除cache参数，避免传递给后端\n      delete params.cache;\n    }\n    \n    // 添加时间戳参数，确保不使用缓存\n    const newParams = { ...params };\n    if (!newParams._t) {\n      newParams._t = new Date().getTime();\n    }\n    \n    const response = await api.get('/homework', { \n      params: newParams,\n      signal: controller.signal,\n      headers\n    });\n    \n    clearTimeout(timeoutId);\n    \n    console.log('获取作业列表原始响应:', response);\n    \n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 后端直接返回数组格式，这是正常情况\n      console.log('获取作业列表成功 (数组格式):', response.length, '条记录');\n      \n      // 将数组格式转换为前端期望的格式\n      return {\n        items: response,\n        total: response.length,\n        page: params?.page || 1,\n        limit: params?.limit || 10\n      };\n    } else if (response && typeof response === 'object') {\n      if (Array.isArray(response.items)) {\n        // 已经是分页对象格式\n        console.log('获取作业列表成功 (分页对象格式):', response.items.length, '条记录，共', response.total, '条');\n        return response;\n      } else {\n        console.warn('作业列表响应格式异常 (无items数组):', response);\n        // 尝试将整个响应对象作为单个项目的数组返回\n        if (response.id) {\n          console.log('响应似乎是单个作业对象，转换为数组');\n          const items = [response];\n          return {\n            items: items,\n            total: 1,\n            page: 1,\n            limit: 10\n          };\n        }\n        \n        // 返回空数组\n        return {\n          items: [],\n          total: 0,\n          page: params?.page || 1,\n          limit: params?.limit || 10\n        };\n      }\n    } else {\n      console.error('作业列表响应格式无效:', response);\n      return {\n        items: [],\n        total: 0,\n        page: params?.page || 1,\n        limit: params?.limit || 10\n      };\n    }\n  } catch (error) {\n    console.error('获取作业列表失败:', error);\n    \n    if (error.name === 'AbortError') {\n      console.error('获取作业列表请求超时');\n      throw new Error('请求超时，请检查网络连接');\n    }\n    \n    // 使用模拟数据，避免UI崩溃\n    const mockData = {\n      items: [],\n      total: 0,\n      page: params?.page || 1,\n      limit: params?.limit || 10,\n      error: error.message || '未知错误'\n    };\n    \n    console.log('返回模拟数据:', mockData);\n    return mockData;\n  }\n};\n\nexport const getHomework = async (id) => {\n  return await api.get(`/homework/${id}`);\n};\n\nexport const getHomeworkHistory = async (studentId, assignmentId) => {\n  try {\n    console.log(`获取作业历史版本，学生ID: ${studentId}, 作业任务ID: ${assignmentId}`);\n    \n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.get('/homework/history', { \n      params: { student_id: studentId, assignment_id: assignmentId },\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    \n    console.log('获取作业历史版本原始响应:', response);\n    \n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 后端直接返回数组格式，这是正常情况\n      console.log('获取作业历史版本成功:', response.length, '个版本');\n      return response;\n    } else {\n      console.error('作业历史版本响应格式无效:', response);\n      return [];\n    }\n  } catch (error) {\n    console.error('获取作业历史版本失败:', error);\n    \n    if (error.name === 'AbortError') {\n      console.error('获取作业历史版本请求超时');\n      throw new Error('请求超时，请检查网络连接');\n    }\n    \n    throw error;\n  }\n};\n\nexport const getAnnotatedImages = async (homeworkId) => {\n  return await api.get(`/homework/${homeworkId}/annotated-images`);\n};\n\nexport const generateAnnotations = async (homeworkId) => {\n  return await api.post(`/homework/${homeworkId}/generate-annotations`);\n};\n\nexport const correctHomework = async (id) => {\n  return await api.post(`/homework/${id}/correct`);\n};\n\nexport const submitHomework = (formData) => {\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('提交作业请求超时');\n  }, 10000); // 10秒超时，开发环境足够\n  \n  return api.post('/homework', formData, {\n    signal: controller.signal,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n  .then(response => {\n    clearTimeout(timeoutId);\n    console.log('作业提交成功，响应:', response);\n    return response;\n  })\n  .catch(error => {\n    clearTimeout(timeoutId);\n    console.error('作业提交失败:', error);\n    \n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    \n    if (!error.response) {\n      // 网络错误，没有收到响应\n      throw new Error('网络错误，请检查您的网络连接');\n    } else if (error.response.status >= 500) {\n      // 服务器错误\n      throw new Error('服务器错误，请稍后再试');\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      // 其他错误\n      const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n      throw new Error(`作业提交失败: ${errorMessage}`);\n    }\n  });\n};\n\nexport const batchUploadHomework = (formData) => {\n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('批量上传作业请求超时');\n  }, 15000); // 15秒超时，开发环境批量上传\n  \n  return api.post('/homework/batch', formData, {\n    signal: controller.signal,\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n  .then(response => {\n    clearTimeout(timeoutId);\n    console.log('批量上传作业成功，响应:', response);\n    return response;\n  })\n  .catch(error => {\n    clearTimeout(timeoutId);\n    console.error('批量上传作业失败:', error);\n    \n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    \n    if (!error.response) {\n      // 网络错误，没有收到响应\n      throw new Error('网络错误，请检查您的网络连接');\n    } else if (error.response.status >= 500) {\n      // 服务器错误\n      throw new Error('服务器错误，请稍后再试');\n    } else if (error.response.status === 401) {\n      // 未授权\n      throw new Error('您的会话已过期，请重新登录');\n    } else if (error.response.status === 403) {\n      // 权限不足\n      throw new Error('您没有权限执行此操作');\n    } else {\n      // 其他错误\n      const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n      throw new Error(`批量上传作业失败: ${errorMessage}`);\n    }\n  });\n};\n\nexport const updateHomework = (id, data) => {\n  return api.put(`/homework/${id}`, data);\n};\n\nexport const deleteHomework = (id) => {\n  console.log(`开始删除作业，ID: ${id}`);\n  \n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n  \n  return api.delete(`/homework/${id}`, {\n    signal: controller.signal\n  })\n    .then(response => {\n      clearTimeout(timeoutId);\n      console.log(`作业删除成功，ID: ${id}`, response);\n      return response;\n    })\n    .catch(error => {\n      clearTimeout(timeoutId);\n      console.error(`作业删除失败，ID: ${id}:`, error);\n      \n      if (error.name === 'AbortError') {\n        throw new Error('请求超时，请检查网络连接');\n      }\n      \n      // 自定义错误信息\n      const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n      throw new Error(`删除作业失败: ${errorMessage}`);\n    });\n};\n\nexport const createHomeworkAssignment = (data) => {\n  console.log('创建作业任务，数据:', JSON.stringify(data));\n  \n  // 检查网络连接\n  if (!navigator.onLine) {\n    console.error('网络连接已断开');\n    return Promise.reject(new Error('网络连接已断开，请检查您的网络设置'));\n  }\n  \n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('创建作业任务请求超时');\n  }, 30000); // 30秒超时\n  \n  return api.post('/homework-assignment', data, {\n    signal: controller.signal,\n    headers: {\n      'Content-Type': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest'\n    }\n  })\n    .then(response => {\n      clearTimeout(timeoutId);\n      console.log('作业任务创建成功，响应:', response);\n      return response;\n    })\n    .catch(error => {\n      clearTimeout(timeoutId);\n      console.error('作业任务创建失败:', error);\n      \n      if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n        throw new Error('请求超时，请检查网络连接');\n      }\n      \n      if (!error.response) {\n        // 网络错误，没有收到响应\n        console.error('网络错误详情:', error);\n        if (error.message) {\n          throw new Error(`连接错误: ${error.message}`);\n        } else {\n          throw new Error('网络错误，请检查您的网络连接');\n        }\n      } else if (error.response.status >= 500) {\n        // 服务器错误\n        console.error('服务器500错误详情:', error.response?.data);\n        const errorDetail = error.response?.data?.detail || '未知服务器错误';\n        throw new Error(`服务器错误: ${errorDetail}`);\n      } else if (error.response.status === 401) {\n        // 未授权\n        throw new Error('您的会话已过期，请重新登录');\n      } else if (error.response.status === 403) {\n        // 权限不足\n        throw new Error('您没有权限执行此操作');\n      } else {\n        // 其他错误\n        const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n        throw new Error(`作业任务创建失败: ${errorMessage}`);\n      }\n    });\n};\n\nexport const deleteHomeworkAssignment = (id) => {\n  return api.delete(`/homework-assignment/${id}`);\n};\n\nexport const updateHomeworkAssignmentStatus = (id, status) => {\n  console.log(`更新作业任务状态，ID: ${id}, 状态: ${status}`);\n  \n  // 添加请求超时处理\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n  \n  return api.patch(`/homework-assignment/${id}/status`, { status }, {\n    signal: controller.signal\n  })\n    .then(response => {\n      clearTimeout(timeoutId);\n      console.log(`作业任务状态更新成功，ID: ${id}`, response);\n      return response;\n    })\n    .catch(error => {\n      clearTimeout(timeoutId);\n      console.error(`作业任务状态更新失败，ID: ${id}:`, error);\n      \n      if (error.name === 'AbortError') {\n        throw new Error('请求超时，请检查网络连接');\n      }\n      \n      // 自定义错误信息\n      const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n      throw new Error(`更新作业任务状态失败: ${errorMessage}`);\n    });\n};\n\nexport const getHomeworkAssignment = (id) => {\n  return api.get(`/homework-assignment/${id}`);\n};\n\n// 获取学生作业任务列表\nexport const getStudentHomeworkAssignments = async () => {\n  try {\n    console.log('获取学生作业任务列表');\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    const response = await api.get('/student/homework-assignments', {\n      signal: controller.signal\n    });\n\n    clearTimeout(timeoutId);\n    console.log('获取学生作业任务列表成功:', response);\n\n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 时间转换函数，统一转换为北京时间\n      const formatTime = (time) => {\n        if (!time) return null;\n        // 使用moment处理时间，使用480分钟偏移转换为北京时间，更加准确\n        const beijingTime = moment(time).utcOffset(480);\n        return beijingTime.isValid() ? beijingTime.format('YYYY-MM-DD HH:mm:ss') : null;\n      };\n\n      // 处理每个作业任务的时间字段\n      const processedResponse = response.map(homework => ({\n        ...homework,\n        // 按优先级处理提交时间\n        submit_time: formatTime(homework.submitted_at || homework.submit_time || homework.created_at),\n        // 其他时间字段也转换为北京时间\n        created_at: formatTime(homework.created_at),\n        updated_at: formatTime(homework.updated_at),\n        due_date: formatTime(homework.due_date)\n      }));\n\n      return processedResponse;\n    } else {\n      console.error('学生作业任务列表响应格式无效:', response);\n      return [];\n    }\n\n  } catch (error) {\n    console.error('获取学生作业任务列表失败:', error);\n\n    if (error.name === 'AbortError') {\n      console.error('获取学生作业任务列表请求超时');\n      throw new Error('请求超时，请检查网络连接');\n    }\n\n    // 返回空数组而不是抛出错误，避免组件加载失败\n    throw new Error(error.response?.data?.detail || error.message || '获取作业任务列表失败');\n  }\n};\n\nexport const getHomeworkAssignments = async (params) => {\n  try {\n    console.log('获取作业任务列表，参数:', params);\n\n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    let retries = 0;\n    const maxRetries = 2;\n    let response;\n\n    // 添加 include_teacher_details 参数，获取老师的详细信息，包括学校信息\n    const apiParams = {\n      ...params,\n      include_teacher_details: true\n    };\n    \n    while (retries <= maxRetries) {\n      try {\n        response = await api.get('/homework-assignment', { \n          params: apiParams,\n          signal: controller.signal\n        });\n        break; // 如果成功，跳出重试循环\n      } catch (retryError) {\n        retries++;\n        if (retries > maxRetries) {\n          throw retryError; // 重试次数用完，抛出错误\n        }\n        console.log(`获取作业任务列表失败，正在进行第${retries}次重试...`);\n        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒再重试\n      }\n    }\n    \n    clearTimeout(timeoutId);\n    \n    console.log('获取作业任务列表原始响应:', response);\n    \n    // 时间转换函数，统一转换为北京时间\n    const formatTime = (time) => {\n      if (!time) return null;\n      // 使用moment处理时间，并转换为北京时间\n      const beijingTime = moment(time).utcOffset('+08:00');\n      return beijingTime.isValid() ? beijingTime.format('YYYY/MM/DD HH:mm:ss') : null;\n    };\n\n    // 处理作业任务对象的时间字段\n    const processHomeworkTimes = (homework) => ({\n      ...homework,\n      // 按优先级处理提交时间\n      submit_time: formatTime(homework.submitted_at || homework.submit_time || homework.created_at),\n      // 其他时间字段也转换为北京时间\n      created_at: formatTime(homework.created_at),\n      updated_at: formatTime(homework.updated_at),\n      due_date: formatTime(homework.due_date)\n    });\n\n    // 检查响应是否为空或undefined\n    if (!response) {\n      console.error('作业任务列表响应为空');\n      return {\n        items: [],\n        total: 0,\n        page: params?.page || 1,\n        limit: params?.limit || 10\n      };\n    }\n    \n    // 检查响应格式\n    if (Array.isArray(response)) {\n      // 后端直接返回数组格式，这是正常情况\n      console.log('获取作业任务列表成功 (数组格式):', response.length, '条记录');\n      \n      // 处理每个作业任务的时间，并返回分页格式\n      const processedItems = response.map(processHomeworkTimes);\n      \n      return {\n        items: processedItems,\n        total: processedItems.length,\n        page: params?.page || 1,\n        limit: params?.limit || 10\n      };\n    } else if (response && typeof response === 'object') {\n      if (Array.isArray(response.items)) {\n        // 已经是分页对象格式\n        console.log('获取作业任务列表成功 (分页对象格式):', response.items.length, '条记录，共', response.total, '条');\n        \n        // 处理分页对象中每个作业任务的时间\n        return {\n          ...response,\n          items: response.items.map(processHomeworkTimes)\n        };\n      } else {\n        console.warn('作业任务列表响应格式异常 (无items数组):', response);\n        // 尝试将整个响应对象作为单个项目的数组返回\n        if (response.id) {\n          console.log('响应似乎是单个作业任务对象，转换为数组');\n          const processedItem = processHomeworkTimes(response);\n          return {\n            items: [processedItem],\n            total: 1,\n            page: 1,\n            limit: 10\n          };\n        }\n        \n        // 返回空数组\n        return {\n          items: [],\n          total: 0,\n          page: params?.page || 1,\n          limit: params?.limit || 10\n        };\n      }\n    } else {\n      console.error('作业任务列表响应格式无效:', response);\n      return {\n        items: [],\n        total: 0,\n        page: params?.page || 1,\n        limit: params?.limit || 10\n      };\n    }\n  } catch (error) {\n    console.error('获取作业任务列表失败:', error);\n    \n    if (error.name === 'AbortError') {\n      console.error('获取作业任务列表请求超时');\n      return {\n        items: [],\n        total: 0,\n        page: params?.page || 1,\n        limit: params?.limit || 10,\n        error: '请求超时，请稍后重试'\n      };\n    }\n    \n    // 返回空数据而不是抛出错误，避免组件加载失败\n    return {\n      items: [],\n      total: 0,\n      page: params?.page || 1,\n      limit: params?.limit || 10,\n      error: error.detail || error.message || '未知错误'\n    };\n  }\n};\n\n// 错题相关\nexport const getWrongQuestions = async (params) => {\n  try {\n    console.log('获取错题列表，参数:', params);\n    const response = await api.get('/wrong-questions', { params });\n    console.log('错题列表API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('获取错题列表失败:', error);\n    // 返回空数组而不是抛出错误，避免页面崩溃\n    return [];\n  }\n};\n\nexport const getWrongQuestion = async (id) => {\n  try {\n    console.log('获取错题详情，ID:', id);\n    const response = await api.get(`/wrong-questions/${id}`);\n    console.log('错题详情API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('获取错题详情失败:', error);\n    throw error;\n  }\n};\n\nexport const getReinforcementExercises = async (params) => {\n  try {\n    console.log('获取强化练习，参数:', params);\n    const response = await api.get('/reinforcement-exercises', { params });\n    console.log('强化练习API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('获取强化练习失败:', error);\n    // 返回空数组而不是抛出错误\n    return [];\n  }\n};\n\n// 管理员专用错题API\nexport const getAdminWrongQuestions = async (params) => {\n  try {\n    console.log('管理员获取错题列表，参数:', params);\n    const response = await api.get('/homework/admin/wrong-questions', { params });\n    console.log('管理员错题列表API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('管理员获取错题列表失败:', error);\n    return [];\n  }\n};\n\n// 教师专用错题API\nexport const getTeacherWrongQuestions = async (params) => {\n  try {\n    console.log('教师获取错题列表，参数:', params);\n    const response = await api.get('/homework/teacher/wrong-questions', { params });\n    console.log('教师错题列表API响应:', response);\n    return response;\n  } catch (error) {\n    console.error('教师获取错题列表失败:', error);\n    return [];\n  }\n};\n\nexport const updateReinforcementExercise = (id, isCompleted) => {\n  return api.put(`/reinforcement-exercises/${id}?is_completed=${isCompleted}`);\n};\n\n// 删除强化训练记录\nexport const deleteReinforcementExercise = (exerciseId) => {\n  return api.delete(`/reinforcement-exercises/${exerciseId}`);\n};\n\n// 错题强化训练相关\nexport const generateExercises = (wrongQuestionId, count = 1) => {\n  // 添加超时处理，AI生成需要更长时间\n  const controller = new AbortController();\n  const timeoutId = setTimeout(() => {\n    controller.abort();\n    console.error('生成练习请求超时');\n  }, 60000); // 60秒超时，给AI足够时间\n\n  return api.post('/ai/generate-exercises', {\n    wrong_question_id: wrongQuestionId,\n    count\n  }, {\n    signal: controller.signal\n  })\n  .then(response => {\n    clearTimeout(timeoutId);\n    console.log('生成练习成功:', response);\n    return response;\n  })\n  .catch(error => {\n    clearTimeout(timeoutId);\n    console.error('生成练习失败:', error);\n\n    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {\n      throw new Error('AI生成超时，请稍后重试');\n    }\n\n    throw error;\n  });\n};\n\nexport const evaluateExerciseAnswer = (exerciseId, studentAnswer) => {\n  return api.post('/ai/evaluate-answer', {\n    exercise_id: exerciseId,\n    student_answer: studentAnswer\n  });\n};\n\n// 获取指定练习的答题记录\nexport const getExerciseAnswerRecords = (exerciseId) => {\n  return api.get(`/ai/exercise-answer-records/${exerciseId}`);\n};\n\n// 获取学生的所有答题记录\nexport const getStudentAnswerRecords = (limit = 50, offset = 0) => {\n  return api.get('/ai/student-answer-records', {\n    params: { limit, offset }\n  });\n};\n\n// 学校数据 - 硬编码数据避免依赖后端API\nconst SCHOOLS_DATA = [\n  { id: 1, school_name: '成都立格实验学校', province: '四川省', city: '成都市', district: '锦江区' },\n  { id: 2, school_name: '成都市石室中学', province: '四川省', city: '成都市', district: '武侯区' },\n  { id: 3, school_name: '成都市第七中学', province: '四川省', city: '成都市', district: '青羊区' },\n  { id: 4, school_name: '成都外国语学校', province: '四川省', city: '成都市', district: '高新区' },\n  { id: 5, school_name: '成都市树德中学', province: '四川省', city: '成都市', district: '锦江区' },\n  { id: 6, school_name: '成都市第九中学', province: '四川省', city: '成都市', district: '成华区' },\n  { id: 7, school_name: '成都市第十二中学', province: '四川省', city: '成都市', district: '金牛区' },\n  { id: 8, school_name: '成都市嘉祥外国语学校', province: '四川省', city: '成都市', district: '锦江区' },\n  { id: 9, school_name: '成都市实验中学', province: '四川省', city: '成都市', district: '青羊区' },\n  { id: 10, school_name: '成都市第三中学', province: '四川省', city: '成都市', district: '武侯区' },\n  { id: 11, school_name: '成都七中实验学校', province: '四川省', city: '成都市', district: '双流区' },\n  { id: 12, school_name: '成都市双流中学', province: '四川省', city: '成都市', district: '双流区' },\n  { id: 13, school_name: '绵阳中学', province: '四川省', city: '绵阳市', district: '涪城区' },\n  { id: 14, school_name: '绵阳南山中学', province: '四川省', city: '绵阳市', district: '涪城区' },\n  { id: 15, school_name: '绵阳东辰国际学校', province: '四川省', city: '绵阳市', district: '涪城区' },\n  { id: 16, school_name: '绵阳富乐中学', province: '四川省', city: '绵阳市', district: '科创区' },\n  { id: 17, school_name: '重庆市第一中学', province: '重庆市', city: '重庆市', district: '渝中区' },\n  { id: 18, school_name: '重庆市第八中学', province: '重庆市', city: '重庆市', district: '沙坪坝区' },\n  { id: 19, school_name: '重庆巴蜀中学', province: '重庆市', city: '重庆市', district: '渝中区' },\n  { id: 20, school_name: '重庆南开中学', province: '重庆市', city: '重庆市', district: '南岸区' },\n  { id: 21, school_name: '北京市第四中学', province: '北京市', city: '北京市', district: '海淀区' },\n  { id: 22, school_name: '北京市第二中学', province: '北京市', city: '北京市', district: '西城区' },\n  { id: 23, school_name: '上海市格致中学', province: '上海市', city: '上海市', district: '黄浦区' },\n  { id: 24, school_name: '上海市第二中学', province: '上海市', city: '上海市', district: '徐汇区' },\n  { id: 25, school_name: '广州市第二中学', province: '广东省', city: '广州市', district: '越秀区' },\n  { id: 26, school_name: '深圳市高级中学', province: '广东省', city: '深圳市', district: '福田区' }\n];\n\n// 学校管理相关\n// 公开API - 不需要登录也能获取学校列表\nexport const getSchools = async (params = {}) => {\n  try {\n    console.log('调用getSchools API, 参数:', params);\n    \n    // 如果提供了地区参数，使用公开API\n    if (params.province || params.city || params.district) {\n      try {\n      const queryParams = new URLSearchParams();\n      if (params.province) queryParams.append('province', params.province);\n      if (params.city) queryParams.append('city', params.city);\n      if (params.district) queryParams.append('district', params.district);\n      \n      const response = await api.get(`/schools/public?${queryParams.toString()}`);\n        console.log('获取学校列表成功:', response);\n        return response;\n      } catch (error) {\n        console.error('公开API获取学校列表失败，使用本地数据:', error);\n        // 如果API调用失败，返回硬编码的学校数据\n        return SCHOOLS_DATA.filter(school => {\n          let match = true;\n          if (params.province) match = match && school.province === params.province;\n          if (params.city) match = match && school.city === params.city;\n          if (params.district) match = match && school.district === params.district;\n          return match;\n        });\n      }\n    } \n    // 否则使用需要认证的API\n    else {\n      // 先尝试管理员API\n      try {\n        // 确保超级管理员可以看到所有学校\n        const user = JSON.parse(localStorage.getItem('user') || '{}');\n        const isAdmin = user && user.is_admin;\n        \n        console.log('当前用户是否为超级管理员:', isAdmin);\n        \n        // 确保请求的是学校列表，而不是单个学校\n        const response = await api.get('/admin/schools');\n        console.log('从管理员API获取学校列表成功:', response);\n        return response;\n      } catch (adminError) {\n        console.log('管理员API获取学校列表失败，尝试使用普通API:', adminError);\n        try {\n        // 如果管理员API失败，尝试使用普通学校API\n        const response = await api.get('/schools/');\n        console.log('从普通API获取学校列表成功:', response);\n        \n        // 确保返回的是数组数据\n        if (Array.isArray(response)) {\n          // 处理每个学校对象，确保地理位置字段存在\n          return response.map(school => ({\n            ...school,\n            province: school.province || '',\n            city: school.city || '',\n            district: school.district || ''\n          }));\n        } else if (response && Array.isArray(response.items)) {\n          // 如果是分页响应格式\n          return {\n            ...response,\n            items: response.items.map(school => ({\n              ...school,\n              province: school.province || '',\n              city: school.city || '',\n              district: school.district || ''\n            }))\n          };\n        } else {\n          // 其他情况，直接返回\n          return response;\n          }\n        } catch (error) {\n          console.error('所有API获取学校列表失败，返回空数组:', error);\n          return [];\n        }\n      }\n    }\n  } catch (error) {\n    console.error('获取学校列表失败:', error);\n    return [];\n  }\n};\n\n// 获取公开班级列表，用于注册页面\nexport const getPublicClasses = async (params = {}) => {\n  try {\n    console.log('调用getPublicClasses API, 参数:', params);\n    \n    const queryParams = new URLSearchParams();\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.grade) queryParams.append('grade', params.grade);\n    \n    try {\n    const response = await api.get(`/schools/public/classes?${queryParams.toString()}`);\n      console.log('获取班级列表成功:', response);\n      return response;\n    } catch (apiError) {\n      console.error('获取班级列表API调用失败:', apiError);\n      // 如果API调用失败，返回一个空数组\n      return [];\n    }\n  } catch (error) {\n    console.error('获取班级列表失败:', error);\n    return [];\n  }\n};\n\n// 获取公开科目列表，用于注册页面\nexport const getPublicSubjects = async (schoolId = null) => {\n  try {\n    console.log('调用getPublicSubjects API, schoolId:', schoolId);\n\n    try {\n      const params = schoolId ? { school_id: schoolId } : {};\n      const response = await api.get('/public/subjects', { params });\n      console.log('获取科目列表成功:', response);\n      return response;\n    } catch (apiError) {\n      console.error('获取科目列表API调用失败:', apiError);\n      // 如果API调用失败，返回默认科目列表\n      return [\n        { id: 1, name: '语文' },\n        { id: 2, name: '数学' },\n        { id: 3, name: '英语' },\n        { id: 4, name: '物理' },\n        { id: 5, name: '化学' },\n        { id: 6, name: '生物' },\n        { id: 7, name: '历史' },\n        { id: 8, name: '地理' },\n        { id: 9, name: '政治' }\n      ];\n    }\n  } catch (error) {\n    console.error('获取科目列表失败:', error);\n    return [];\n  }\n};\n\n// 区域数据 - 直接使用硬编码数据避免依赖后端API\nconst REGION_DATA = {\n  provinces: [\n    '北京市', '天津市', '河北省', '山西省', '内蒙古自治区', \n    '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省', \n    '浙江省', '安徽省', '福建省', '江西省', '山东省', \n    '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区', \n    '海南省', '重庆市', '四川省', '贵州省', '云南省', \n    '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区', \n    '新疆维吾尔自治区', '香港特别行政区', '澳门特别行政区', '台湾省'\n  ],\n  cities: {\n    '四川省': ['成都市', '绵阳市', '德阳市', '南充市', '宜宾市', '自贡市', '泸州市', '雅安市', '眉山市', '乐山市', '广元市', '遂宁市', '内江市', '资阳市', '达州市', '广安市', '巴中市', '攀枝花市', '甘孜藏族自治州', '阿坝藏族羌族自治州', '凉山彝族自治州'],\n    '重庆市': ['重庆市'],\n    '北京市': ['北京市'],\n    '上海市': ['上海市'],\n    '天津市': ['天津市'],\n    '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],\n    '辽宁省': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],\n    '山西省': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市'],\n    '河北省': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'],\n    '山东省': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市'],\n    '河南省': ['郑州市', '开封市', '洛阳市', '平顶山市', '安阳市', '鹤壁市', '新乡市', '焦作市', '濮阳市', '许昌市', '漯河市', '三门峡市', '南阳市', '商丘市', '信阳市', '周口市', '驻马店市', '济源市'],\n    '江苏省': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],\n    '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'],\n    '安徽省': ['合肥市', '芜湖市', '蚌埠市', '淮南市', '马鞍山市', '淮北市', '铜陵市', '安庆市', '黄山市', '滁州市', '阜阳市', '宿州市', '六安市', '亳州市', '池州市', '宣城市'],\n    '福建省': ['福州市', '厦门市', '莆田市', '三明市', '泉州市', '漳州市', '南平市', '龙岩市', '宁德市'],\n    '江西省': ['南昌市', '景德镇市', '萍乡市', '九江市', '新余市', '鹰潭市', '赣州市', '吉安市', '宜春市', '抚州市', '上饶市'],\n    '湖北省': ['武汉市', '黄石市', '十堰市', '宜昌市', '襄阳市', '鄂州市', '荆门市', '孝感市', '荆州市', '黄冈市', '咸宁市', '随州市', '恩施土家族苗族自治州', '仙桃市', '潜江市', '天门市', '神农架林区'],\n    '湖南省': ['长沙市', '株洲市', '湘潭市', '衡阳市', '邵阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市', '怀化市', '娄底市', '湘西土家族苗族自治州'],\n    '广西壮族自治区': ['南宁市', '柳州市', '桂林市', '梧州市', '北海市', '防城港市', '钦州市', '贵港市', '玉林市', '百色市', '贺州市', '河池市', '来宾市', '崇左市'],\n    '海南省': ['海口市', '三亚市', '三沙市', '儋州市', '五指山市', '琼海市', '文昌市', '万宁市', '东方市', '定安县', '屯昌县', '澄迈县', '临高县', '白沙黎族自治县', '昌江黎族自治县', '乐东黎族自治县', '陵水黎族自治县', '保亭黎族苗族自治县', '琼中黎族苗族自治县'],\n    '贵州省': ['贵阳市', '六盘水市', '遵义市', '安顺市', '毕节市', '铜仁市', '黔西南布依族苗族自治州', '黔东南苗族侗族自治州', '黔南布依族苗族自治州'],\n    '云南省': ['昆明市', '曲靖市', '玉溪市', '保山市', '昭通市', '丽江市', '普洱市', '临沧市', '楚雄彝族自治州', '红河哈尼族彝族自治州', '文山壮族苗族自治州', '西双版纳傣族自治州', '大理白族自治州', '德宏傣族景颇族自治州', '怒江傈僳族自治州', '迪庆藏族自治州'],\n    '西藏自治区': ['拉萨市', '日喀则市', '昌都市', '林芝市', '山南市', '那曲市', '阿里地区'],\n    '陕西省': ['西安市', '铜川市', '宝鸡市', '咸阳市', '渭南市', '延安市', '汉中市', '榆林市', '安康市', '商洛市'],\n    '甘肃省': ['兰州市', '嘉峪关市', '金昌市', '白银市', '天水市', '武威市', '张掖市', '平凉市', '酒泉市', '庆阳市', '定西市', '陇南市', '临夏回族自治州', '甘南藏族自治州'],\n    '青海省': ['西宁市', '海东市', '海北藏族自治州', '黄南藏族自治州', '海南藏族自治州', '果洛藏族自治州', '玉树藏族自治州', '海西蒙古族藏族自治州'],\n    '宁夏回族自治区': ['银川市', '石嘴山市', '吴忠市', '固原市', '中卫市'],\n    '新疆维吾尔自治区': ['乌鲁木齐市', '克拉玛依市', '吐鲁番市', '哈密市', '昌吉回族自治州', '博尔塔拉蒙古自治州', '巴音郭楞蒙古自治州', '阿克苏地区', '克孜勒苏柯尔克孜自治州', '喀什地区', '和田地区', '伊犁哈萨克自治州', '塔城地区', '阿勒泰地区', '石河子市', '阿拉尔市', '图木舒克市', '五家渠市', '北屯市', '铁门关市', '双河市', '可克达拉市', '昆玉市', '胡杨河市', '新星市'],\n    '内蒙古自治区': ['呼和浩特市', '包头市', '乌海市', '赤峰市', '通辽市', '鄂尔多斯市', '呼伦贝尔市', '巴彦淖尔市', '乌兰察布市', '兴安盟', '锡林郭勒盟', '阿拉善盟'],\n    '吉林省': ['长春市', '吉林市', '四平市', '辽源市', '通化市', '白山市', '松原市', '白城市', '延边朝鲜族自治州'],\n    '黑龙江省': ['哈尔滨市', '齐齐哈尔市', '鸡西市', '鹤岗市', '双鸭山市', '大庆市', '伊春市', '佳木斯市', '七台河市', '牡丹江市', '黑河市', '绥化市', '大兴安岭地区'],\n    '香港特别行政区': ['香港特别行政区'],\n    '澳门特别行政区': ['澳门特别行政区'],\n    '台湾省': ['台北市', '高雄市', '台中市', '台南市', '新北市', '宜兰县', '桃园市', '新竹县', '苗栗县', '彰化县', '南投县', '云林县', '嘉义县', '屏东县', '台东县', '花莲县', '澎湖县', '基隆市', '新竹市', '嘉义市', '金门县', '连江县']\n  },\n  districts: {\n    // 四川省\n    '成都市': ['武侯区', '青羊区', '锦江区', '高新区', '成华区', '金牛区', '双流区', '温江区', '郫都区', '新都区', '龙泉驿区', '青白江区', '彭州市', '崇州市', '邛崃市', '都江堰市', '简阳市'],\n    '绵阳市': ['涪城区', '科创区', '安州区', '游仙区', '三台县', '盐亭县', '梓潼县', '北川羌族自治县', '平武县', '江油市'],\n    '德阳市': ['旌阳区', '罗江区', '中江县', '广汉市', '什邡市', '绵竹市'],\n    '南充市': ['顺庆区', '高坪区', '嘉陵区', '南部县', '营山县', '蓬安县', '仪陇县', '西充县', '阆中市'],\n    '宜宾市': ['翠屏区', '南溪区', '叙州区', '江安县', '长宁县', '高县', '珙县', '筠连县', '兴文县', '屏山县'],\n    \n    // 直辖市\n    '重庆市': ['渝中区', '沙坪坝区', '南岸区', '九龙坡区', '渝北区', '江北区', '大渡口区', '北碚区', '巴南区', '涪陵区', '万州区', '黔江区', '长寿区', '江津区', '合川区', '永川区', '南川区', '綦江区', '大足区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平区', '武隆区', '城口县', '丰都县', '垫江县', '忠县', '云阳县', '奉节县', '巫山县', '巫溪县', '石柱土家族自治县', '秀山土家族苗族自治县', '酉阳土家族苗族自治县', '彭水苗族土家族自治县'],\n    '北京市': ['海淀区', '西城区', '朝阳区', '东城区', '丰台区', '石景山区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],\n    '上海市': ['黄浦区', '徐汇区', '静安区', '浦东新区', '长宁区', '普陀区', '虹口区', '杨浦区', '宝山区', '闵行区', '嘉定区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],\n    '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],\n    \n    // 广东省\n    '广州市': ['越秀区', '天河区', '海珠区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],\n    '深圳市': ['福田区', '南山区', '罗湖区', '盐田区', '龙岗区', '宝安区', '龙华区', '坪山区', '光明区'],\n    '珠海市': ['香洲区', '斗门区', '金湾区'],\n    '汕头市': ['龙湖区', '金平区', '濠江区', '潮阳区', '潮南区', '澄海区', '南澳县'],\n    '佛山市': ['禅城区', '南海区', '顺德区', '高明区', '三水区'],\n    \n    // 辽宁省\n    '沈阳市': ['和平区', '沈河区', '大东区', '皇姑区', '铁西区', '浑南区', '于洪区', '苏家屯区', '沈北新区', '康平县', '法库县', '辽中区', '新民市'],\n    '大连市': ['中山区', '西岗区', '沙河口区', '甘井子区', '旅顺口区', '金州区', '普兰店区', '瓦房店市', '庄河市', '长海县'],\n    '鞍山市': ['铁东区', '铁西区', '立山区', '千山区', '台安县', '岫岩满族自治县', '海城市'],\n    '抚顺市': ['新抚区', '东洲区', '望花区', '顺城区', '抚顺县', '新宾满族自治县', '清原满族自治县'],\n    \n    // 山西省\n    '太原市': ['小店区', '迎泽区', '杏花岭区', '尖草坪区', '万柏林区', '晋源区', '清徐县', '阳曲县', '娄烦县', '古交市'],\n    '大同市': ['平城区', '云冈区', '新荣区', '阳高县', '天镇县', '广灵县', '灵丘县', '浑源县', '左云县', '云州区'],\n    '阳泉市': ['城区', '矿区', '郊区', '平定县', '盂县'],\n    \n    // 河北省\n    '石家庄市': ['长安区', '桥西区', '新华区', '井陉矿区', '裕华区', '藁城区', '鹿泉区', '栾城区', '井陉县', '正定县', '行唐县', '灵寿县', '高邑县', '深泽县', '赞皇县', '无极县', '平山县', '元氏县', '赵县', '晋州市', '新乐市'],\n    '唐山市': ['路南区', '路北区', '古冶区', '开平区', '丰南区', '丰润区', '曹妃甸区', '滦州市', '滦南县', '乐亭县', '迁西县', '玉田县', '遵化市', '迁安市'],\n    '秦皇岛市': ['海港区', '山海关区', '北戴河区', '抚宁区', '青龙满族自治县', '昌黎县', '卢龙县'],\n    \n    // 山东省\n    '济南市': ['历下区', '市中区', '槐荫区', '天桥区', '历城区', '长清区', '章丘区', '济阳区', '莱芜区', '钢城区', '平阴县', '商河县'],\n    '青岛市': ['市南区', '市北区', '黄岛区', '崂山区', '李沧区', '城阳区', '即墨区', '胶州市', '平度市', '莱西市'],\n    '淄博市': ['淄川区', '张店区', '博山区', '临淄区', '周村区', '桓台县', '高青县', '沂源县'],\n    \n    // 河南省\n    '郑州市': ['中原区', '二七区', '管城回族区', '金水区', '上街区', '惠济区', '中牟县', '巩义市', '荥阳市', '新密市', '新郑市', '登封市'],\n    '开封市': ['鼓楼区', '龙亭区', '顺河回族区', '禹王台区', '祥符区', '杞县', '通许县', '尉氏县', '兰考县'],\n    '洛阳市': ['老城区', '西工区', '瀍河回族区', '涧西区', '偃师区', '孟津区', '洛龙区', '新安县', '栾川县', '嵩县', '汝阳县', '宜阳县', '洛宁县', '伊川县'],\n    \n    // 江苏省\n    '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],\n    '无锡市': ['锡山区', '惠山区', '滨湖区', '梁溪区', '新吴区', '江阴市', '宜兴市'],\n    '徐州市': ['鼓楼区', '云龙区', '贾汪区', '泉山区', '铜山区', '丰县', '沛县', '睢宁县', '新沂市', '邳州市'],\n    \n    // 浙江省\n    '杭州市': ['上城区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '富阳区', '临安区', '桐庐县', '淳安县', '建德市', '临平区', '钱塘区'],\n    '宁波市': ['海曙区', '江北区', '北仑区', '镇海区', '鄞州区', '奉化区', '象山县', '宁海县', '余姚市', '慈溪市'],\n    '温州市': ['鹿城区', '龙湾区', '瓯海区', '洞头区', '永嘉县', '平阳县', '苍南县', '文成县', '泰顺县', '瑞安市', '乐清市', '龙港市'],\n    \n    // 安徽省\n    '合肥市': ['瑶海区', '庐阳区', '蜀山区', '包河区', '长丰县', '肥东县', '肥西县', '庐江县', '巢湖市'],\n    '芜湖市': ['镜湖区', '弋江区', '鸠江区', '湾沚区', '繁昌区', '南陵县', '无为市'],\n    '蚌埠市': ['龙子湖区', '蚌山区', '禹会区', '淮上区', '怀远县', '五河县', '固镇县'],\n    \n    // 福建省\n    '福州市': ['鼓楼区', '台江区', '仓山区', '马尾区', '晋安区', '长乐区', '闽侯县', '连江县', '罗源县', '闽清县', '永泰县', '平潭县', '福清市'],\n    '厦门市': ['思明区', '海沧区', '湖里区', '集美区', '同安区', '翔安区'],\n    '莆田市': ['城厢区', '涵江区', '荔城区', '秀屿区', '仙游县'],\n    \n    // 江西省\n    '南昌市': ['东湖区', '西湖区', '青云谱区', '青山湖区', '新建区', '红谷滩区', '南昌县', '安义县', '进贤县'],\n    '景德镇市': ['昌江区', '珠山区', '浮梁县', '乐平市'],\n    '萍乡市': ['安源区', '湘东区', '莲花县', '上栗县', '芦溪县'],\n    \n    // 湖北省\n    '武汉市': ['江岸区', '江汉区', '硚口区', '汉阳区', '武昌区', '青山区', '洪山区', '东西湖区', '汉南区', '蔡甸区', '江夏区', '黄陂区', '新洲区'],\n    '黄石市': ['黄石港区', '西塞山区', '下陆区', '铁山区', '阳新县', '大冶市'],\n    '十堰市': ['茅箭区', '张湾区', '郧阳区', '郧西县', '竹山县', '竹溪县', '房县', '丹江口市'],\n    \n    // 湖南省\n    '长沙市': ['芙蓉区', '天心区', '岳麓区', '开福区', '雨花区', '望城区', '长沙县', '浏阳市', '宁乡市'],\n    '株洲市': ['荷塘区', '芦淞区', '石峰区', '天元区', '渌口区', '攸县', '茶陵县', '炎陵县', '醴陵市'],\n    '湘潭市': ['雨湖区', '岳塘区', '湘潭县', '湘乡市', '韶山市'],\n    \n    // 广西壮族自治区\n    '南宁市': ['兴宁区', '青秀区', '江南区', '西乡塘区', '良庆区', '邕宁区', '武鸣区', '隆安县', '马山县', '上林县', '宾阳县', '横县'],\n    '柳州市': ['城中区', '鱼峰区', '柳南区', '柳北区', '柳江区', '柳城县', '鹿寨县', '融安县', '融水苗族自治县', '三江侗族自治县'],\n    '桂林市': ['秀峰区', '叠彩区', '象山区', '七星区', '雁山区', '临桂区', '阳朔县', '灵川县', '全州县', '兴安县', '永福县', '灌阳县', '龙胜各族自治县', '资源县', '平乐县', '荔浦市', '恭城瑶族自治县'],\n    \n    // 海南省\n    '海口市': ['秀英区', '龙华区', '琼山区', '美兰区'],\n    '三亚市': ['海棠区', '吉阳区', '天涯区', '崖州区'],\n    \n    // 贵州省\n    '贵阳市': ['南明区', '云岩区', '花溪区', '乌当区', '白云区', '观山湖区', '开阳县', '息烽县', '修文县', '清镇市'],\n    '六盘水市': ['钟山区', '六枝特区', '水城区', '盘州市'],\n    \n    // 云南省\n    '昆明市': ['五华区', '盘龙区', '官渡区', '西山区', '东川区', '呈贡区', '晋宁区', '富民县', '宜良县', '石林彝族自治县', '嵩明县', '禄劝彝族苗族自治县', '寻甸回族彝族自治县', '安宁市'],\n    '曲靖市': ['麒麟区', '沾益区', '马龙区', '陆良县', '师宗县', '罗平县', '富源县', '会泽县', '宣威市'],\n    \n    // 西藏自治区\n    '拉萨市': ['城关区', '堆龙德庆区', '达孜区', '林周县', '当雄县', '尼木县', '曲水县', '墨竹工卡县'],\n    '日喀则市': ['桑珠孜区', '南木林县', '江孜县', '定日县', '萨迦县', '拉孜县', '昂仁县', '谢通门县', '白朗县', '仁布县', '康马县', '定结县', '仲巴县', '亚东县', '吉隆县', '聂拉木县', '萨嘎县', '岗巴县'],\n    \n    // 陕西省\n    '西安市': ['新城区', '碑林区', '莲湖区', '灞桥区', '未央区', '雁塔区', '阎良区', '临潼区', '长安区', '高陵区', '鄠邑区', '蓝田县', '周至县'],\n    '铜川市': ['王益区', '印台区', '耀州区', '宜君县'],\n    \n    // 甘肃省\n    '兰州市': ['城关区', '七里河区', '西固区', '安宁区', '红古区', '永登县', '皋兰县', '榆中县'],\n    '嘉峪关市': ['市辖区'],\n    \n    // 青海省\n    '西宁市': ['城东区', '城中区', '城西区', '城北区', '湟中区', '大通回族土族自治县', '湟源县'],\n    '海东市': ['乐都区', '平安区', '民和回族土族自治县', '互助土族自治县', '化隆回族自治县', '循化撒拉族自治县'],\n    \n    // 宁夏回族自治区\n    '银川市': ['兴庆区', '西夏区', '金凤区', '永宁县', '贺兰县', '灵武市'],\n    '石嘴山市': ['大武口区', '惠农区', '平罗县'],\n    \n    // 新疆维吾尔自治区\n    '乌鲁木齐市': ['天山区', '沙依巴克区', '新市区', '水磨沟区', '头屯河区', '达坂城区', '米东区', '乌鲁木齐县'],\n    '克拉玛依市': ['独山子区', '克拉玛依区', '白碱滩区', '乌尔禾区'],\n    \n    // 内蒙古自治区\n    '呼和浩特市': ['新城区', '回民区', '玉泉区', '赛罕区', '土默特左旗', '托克托县', '和林格尔县', '清水河县', '武川县'],\n    '包头市': ['东河区', '昆都仑区', '青山区', '石拐区', '白云鄂博矿区', '九原区', '土默特右旗', '固阳县', '达尔罕茂明安联合旗'],\n    \n    // 吉林省\n    '长春市': ['南关区', '宽城区', '朝阳区', '二道区', '绿园区', '双阳区', '九台区', '农安县', '榆树市', '德惠市', '公主岭市'],\n    '吉林市': ['昌邑区', '龙潭区', '船营区', '丰满区', '永吉县', '蛟河市', '桦甸市', '舒兰市', '磐石市'],\n    \n    // 黑龙江省\n    '哈尔滨市': ['道里区', '南岗区', '道外区', '平房区', '松北区', '香坊区', '呼兰区', '阿城区', '双城区', '依兰县', '方正县', '宾县', '巴彦县', '木兰县', '通河县', '延寿县', '尚志市', '五常市'],\n    '齐齐哈尔市': ['龙沙区', '建华区', '铁锋区', '昂昂溪区', '富拉尔基区', '碾子山区', '梅里斯达斡尔族区', '龙江县', '依安县', '泰来县', '甘南县', '富裕县', '克山县', '克东县', '拜泉县', '讷河市'],\n    \n    // 香港、澳门、台湾\n    '香港特别行政区': ['中西区', '东区', '南区', '湾仔区', '九龙城区', '观塘区', '深水埗区', '黄大仙区', '油尖旺区', '离岛区', '葵青区', '北区', '西贡区', '沙田区', '大埔区', '荃湾区', '屯门区', '元朗区'],\n    '澳门特别行政区': ['花地玛堂区', '圣安多尼堂区', '大堂区', '望德堂区', '风顺堂区', '嘉模堂区', '圣方济各堂区', '路氹城'],\n    '台北市': ['中正区', '大同区', '中山区', '万华区', '信义区', '松山区', '大安区', '南港区', '北投区', '内湖区', '士林区', '文山区']\n  }\n};\n\n// 获取省份、城市、区县列表\nexport const getRegions = async (params = {}) => {\n  try {\n    // 1. 获取省份\n    if (!params.province) {\n      console.log('调用API获取省份数据');\n      try {\n        const response = await api.get('/regions/public/provinces');\n        return { provinces: response.provinces };\n      } catch (error) {\n        console.log('API调用失败，使用本地省份数据');\n        return { provinces: REGION_DATA.provinces };\n      }\n    }\n\n    // 2. 获取城市\n    if (params.province && !params.city) {\n      console.log(`调用API获取城市数据: ${params.province}`);\n      try {\n        const response = await api.get(`/regions/public/cities?province=${params.province}`);\n        return { cities: response.cities };\n      } catch (error) {\n        console.log(`API调用失败，使用本地城市数据: ${params.province}`);\n        const cities = REGION_DATA.cities[params.province] || [];\n        return { cities };\n      }\n    }\n\n    // 3. 获取区县\n    if (params.province && params.city) {\n      console.log(`调用API获取区县数据: ${params.city}`);\n      try {\n        const response = await api.get(`/regions/public/districts?city=${params.city}`);\n        return { districts: response.districts };\n      } catch (error) {\n        console.log(`API调用失败，使用本地区县数据: ${params.city}`);\n        const districts = REGION_DATA.districts[params.city] || [];\n        return { districts };\n      }\n    }\n\n    // 兜底\n    return {};\n  } catch (error) {\n    console.error('获取地区数据失败:', error);\n    return {};\n  }\n};\n\nexport const getSchool = async (id) => {\n  try {\n    const response = await api.get(`/schools/${id}`);\n    return response;\n  } catch (error) {\n    console.error(`获取学校详情失败, ID: ${id}:`, error);\n    throw new Error(`获取学校详情失败: ${error.message || '未知错误'}`);\n  }\n};\n\nexport const createSchool = async (schoolData) => {\n  try {\n    const response = await api.post('/schools/', schoolData);\n    return response;\n  } catch (error) {\n    console.error('创建学校失败:', error);\n    throw new Error(`创建学校失败: ${error.message || '未知错误'}`);\n  }\n};\n\nexport const updateSchool = async (id, schoolData) => {\n  try {\n    const response = await api.put(`/schools/${id}`, schoolData);\n    return response;\n  } catch (error) {\n    console.error(`更新学校失败, ID: ${id}:`, error);\n    throw new Error(`更新学校失败: ${error.message || '未知错误'}`);\n  }\n};\n\nexport const deleteSchool = async (id) => {\n  try {\n    console.log(`尝试删除学校，ID: ${id}`);\n    const response = await api.delete(`/admin/schools/${id}`);\n    console.log(`删除学校成功，ID: ${id}`);\n    return response;\n  } catch (error) {\n    console.error(`删除学校失败, ID: ${id}:`, error);\n    throw new Error(`删除学校失败: ${error.message || '未知错误'}`);\n  }\n};\n\n// 作业进度相关\nexport const getHomeworkProgress = async (assignmentId) => {\n  try {\n    console.log(`获取作业提交情况，作业任务ID: ${assignmentId}`);\n    \n    // 添加请求超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    // 使用现有的API端点，但添加作业任务ID作为过滤条件\n    const response = await api.get('/homework', {\n      params: { assignment_id: assignmentId },\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('获取作业提交情况成功:', response);\n    \n    // 处理响应数据\n    if (Array.isArray(response)) {\n      return {\n        items: response,\n        total: response.length\n      };\n    } else if (response && Array.isArray(response.items)) {\n      return response;\n    } else {\n      console.warn('作业提交情况响应格式异常:', response);\n      return {\n        items: [],\n        total: 0\n      };\n    }\n  } catch (error) {\n    console.error(`获取作业提交情况失败，作业任务ID: ${assignmentId}:`, error);\n    \n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    \n    const errorMessage = error.response?.data?.detail || error.message || '未知错误';\n    throw new Error(`获取作业提交情况失败: ${errorMessage}`);\n  }\n};\n\n// 统计相关\nexport const getTeacherStatistics = async (params = {}) => {\n  try {\n    console.log('获取教师统计数据，参数:', params);\n    \n    // 因为后端不直接支持日期筛选和班级筛选，我们需要使用export API\n    let apiEndpoint = '/statistics/teacher';\n    \n    // 检查是否有日期参数、班级参数或作业参数，如果有则使用export API，它支持这些参数\n    if ((params.start_date && params.end_date) || params.class_id || params.assignment_id) {\n      console.log('检测到日期参数、班级参数或作业参数，使用export API获取详细统计数据:', params);\n      apiEndpoint = '/statistics/export';\n    }\n    \n    const response = await api.get(apiEndpoint, { params });\n    \n    console.log('获取教师统计数据成功:', response);\n    \n    // 处理export API返回的数据格式\n    if (apiEndpoint === '/statistics/export') {\n      // 从export API返回的数据中构建统计信息\n      console.log('处理export API返回的数据');\n      \n      const exportData = response.data || [];\n      \n      // 获取班级名称（如果指定了class_id，就只有一个班级）\n      let className = '';\n      if (exportData.length > 0 && exportData[0].class_name) {\n        className = exportData[0].class_name;\n      }\n      \n      // 计算统计数据\n      const totalHomeworks = exportData.length;\n      const gradedHomeworks = exportData.filter(hw => hw.status === 'graded').length;\n      \n      // 计算正确题数统计\n      let correctCounts = exportData.filter(hw => hw.status === 'graded' && hw.accuracy != null)\n                                 .map(hw => Math.round(hw.accuracy * 10)); // 假设满分是10题\n      \n      const highestCorrectCount = correctCounts.length > 0 ? Math.max(...correctCounts) : 0;\n      const lowestCorrectCount = correctCounts.length > 0 ? Math.min(...correctCounts) : 0;\n      const averageCorrectCount = correctCounts.length > 0 \n        ? correctCounts.reduce((sum, count) => sum + count, 0) / correctCounts.length \n        : 0;\n      \n      // 构建处理后的响应\n      response.class_count = 1;\n      response.homework_count = totalHomeworks;\n      response.graded_homework_count = gradedHomeworks;\n      response.highest_correct_count = highestCorrectCount;\n      response.average_correct_count = averageCorrectCount;\n      response.lowest_correct_count = lowestCorrectCount;\n      \n      // 如果选中了特定班级，就用这个班级构建class_statistics\n      if (params.class_id) {\n        const classStatObj = {\n          class_id: parseInt(params.class_id),\n          class_name: className || `班级 ${params.class_id}`,\n          homework_count: totalHomeworks,\n          student_count: new Set(exportData.map(hw => hw.student_name)).size,\n          highest_correct_count: highestCorrectCount,\n          average_correct_count: averageCorrectCount,\n          lowest_correct_count: lowestCorrectCount,\n          // 添加作业任务数和平均分字段\n          assignment_count: new Set(exportData.map(hw => hw.assignment_id)).size,\n          average_score: exportData.filter(hw => hw.status === 'graded' && hw.score != null).length > 0\n            ? exportData.filter(hw => hw.status === 'graded' && hw.score != null)\n                .reduce((sum, hw) => sum + hw.score, 0) / \n                exportData.filter(hw => hw.status === 'graded' && hw.score != null).length\n            : 0\n        };\n        \n        response.class_statistics = { [`class_${params.class_id}`]: classStatObj };\n        response.class_statistics_array = [classStatObj];\n        console.log('为选定班级构建统计数据:', classStatObj);\n      }\n    }\n    \n    // 格式化班级统计数据，将对象转换为数组\n    let classStatsArray = [];\n    \n    if (response && response.class_statistics && typeof response.class_statistics === 'object') {\n      try {\n        classStatsArray = Object.entries(response.class_statistics)\n          .filter(([key, value]) => typeof value === 'object' && value !== null && value.class_id)\n          .map(([key, value]) => {\n            // 确保每个班级对象都有assignment_count和average_score字段\n            if (value.assignment_count === undefined) {\n              console.log(`班级 ${value.class_id || key} 缺少assignment_count字段，设置为默认值0`);\n              value.assignment_count = 0;\n            }\n            if (value.average_score === undefined) {\n              console.log(`班级 ${value.class_id || key} 缺少average_score字段，设置为默认值0`);\n              value.average_score = 0;\n            }\n            return value;\n          });\n        \n        console.log('班级统计数据格式化:', classStatsArray.length, '个班级');\n      } catch (err) {\n        console.error('处理班级统计数据时出错:', err);\n      }\n    } else if (response && Array.isArray(response.class_statistics)) {\n      // 如果已经是数组格式，直接使用\n      classStatsArray = response.class_statistics.map(cls => {\n        // 确保每个班级对象都有assignment_count和average_score字段\n        if (cls.assignment_count === undefined) {\n          console.log(`班级 ${cls.class_id} 缺少assignment_count字段，设置为默认值0`);\n          cls.assignment_count = 0;\n        }\n        if (cls.average_score === undefined) {\n          console.log(`班级 ${cls.class_id} 缺少average_score字段，设置为默认值0`);\n          cls.average_score = 0;\n        }\n        return cls;\n      });\n      console.log('班级统计数据已经是数组格式:', classStatsArray.length, '个班级');\n    } else {\n      console.warn('班级统计数据格式异常:', response?.class_statistics);\n    }\n    \n    // 确保我们至少有一个默认班级（只有在真实数据为空时才使用模拟数据）\n    if (classStatsArray.length === 0) {\n      classStatsArray = [\n        {\n          class_id: 1,\n          class_name: \"示例班级\",\n          student_count: 25,\n          homework_count: 5,\n          average_accuracy: 0.8,\n          highest_correct_count: 5,\n          average_correct_count: 3.5,\n          lowest_correct_count: 2,\n          assignment_count: 3,\n          average_score: 80\n        }\n      ];\n    }\n    \n    // 确保返回的数据包含预期的字段，避免前端读取时出现undefined\n    return {\n      school_count: response.school_count || 0,\n      class_count: response.class_count || 0,\n      student_count: response.student_count || 0,\n      homework_count: response.homework_count || response.total_homework_count || 0,\n      total_homework_count: response.total_homework_count || response.homework_count || 0,\n      corrected_count: response.corrected_count || response.graded_homework_count || 0,\n      graded_homework_count: response.graded_homework_count || response.corrected_count || 0,\n      pending_homework_count: response.pending_homework_count || \n                             ((response.homework_count || response.total_homework_count || 0) - \n                              (response.graded_homework_count || response.corrected_count || 0)),\n      average_accuracy: response.average_accuracy || 0,\n      highest_correct_count: response.highest_correct_count || 0,\n      average_correct_count: response.average_correct_count || 0,\n      lowest_correct_count: response.lowest_correct_count || 0,\n      class_statistics: response.class_statistics || {},\n      class_statistics_array: classStatsArray,\n      recent_homeworks: response.recent_homeworks || response.recent_assignments || []\n    };\n  } catch (error) {\n    console.error('获取教师统计数据失败:', error);\n    // 返回模拟数据，避免UI崩溃\n    return {\n      school_count: 1,\n      class_count: 1,\n      student_count: 25,\n      total_homework_count: 5,\n      graded_homework_count: 3,\n      corrected_count: 3,\n      pending_homework_count: 2,\n      average_accuracy: 0.8,\n      highest_correct_count: 5,\n      average_correct_count: 3.5,\n      lowest_correct_count: 2,\n      class_statistics: {\n        \"class_1\": {\n          class_id: 1,\n          class_name: \"示例班级\",\n          student_count: 25,\n          homework_count: 5,\n          average_accuracy: 0.8,\n          highest_correct_count: 5,\n          average_correct_count: 3.5,\n          lowest_correct_count: 2,\n          assignment_count: 3,\n          average_score: 80\n        }\n      },\n      class_statistics_array: [\n        {\n          class_id: 1,\n          class_name: \"示例班级\",\n          student_count: 25,\n          homework_count: 5,\n          average_accuracy: 0.8,\n          highest_correct_count: 5,\n          average_correct_count: 3.5,\n          lowest_correct_count: 2,\n          assignment_count: 3,\n          average_score: 80\n        }\n      ],\n      recent_homeworks: [\n        {\n          id: 1,\n          title: \"示例作业1\",\n          correct_count: 5,\n          student_id: 1,\n          student_name: \"示例学生\",\n          created_at: new Date().toISOString()\n        }\n      ],\n      error: error.message || '未知错误'\n    };\n  }\n};\n\nexport const getStudentStatistics = async (params = {}) => {\n  try {\n    console.log('获取学生统计数据，参数:', params);\n    const response = await api.get('/statistics/student', { params });\n    \n    console.log('获取学生统计数据成功:', response);\n    \n    // 确保accuracy_trend是数组\n    if (!response.accuracy_trend || !Array.isArray(response.accuracy_trend)) {\n      console.warn('正确率趋势数据格式异常:', response?.accuracy_trend);\n      response.accuracy_trend = [];\n    }\n    \n    return response;\n  } catch (error) {\n    console.error('获取学生统计数据失败:', error);\n    // 返回模拟数据，避免UI崩溃\n    return {\n      total_homework_count: 0,\n      completed_homework_count: 0,\n      pending_homework_count: 0,\n      average_accuracy: 0,\n      wrong_question_count: 0,\n      reinforcement_exercise_count: 0,\n      completed_exercise_count: 0,\n      highest_correct_count: 0,\n      average_correct_count: 0,\n      lowest_correct_count: 0,\n      accuracy_trend: [],\n      error: error.message || '未知错误'\n    };\n  }\n};\n\nexport const getWrongQuestionStatistics = () => {\n  return api.get('/statistics/wrong-questions');\n};\n\nexport const exportStatistics = (params) => {\n  return api.get('/statistics/export', { params });\n};\n\n// AI助手相关\nexport const chatWithAI = (messages) => {\n  return api.post('/ai/chat', { messages });\n};\n\n// 错误分析和强化建议\nexport const analyzeError = (data) => {\n  console.log('调用analyzeError API, data:', data);\n  return api.post('/ai/analyze-error', data);\n};\n\nexport const generateReinforcement = (data) => {\n  console.log('调用generateReinforcement API, data:', data);\n  return api.post('/ai/generate-reinforcement', data);\n};\n\n// AI配置管理相关\nexport const getAiConfigs = async () => {\n  try {\n    console.log('获取AI配置列表 - 开始');\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n\n    // 获取当前用户信息来判断权限\n    let isAdmin = false;\n    try {\n      const userInfo = JSON.parse(localStorage.getItem('user') || '{}');\n      isAdmin = userInfo.is_admin || false;\n      console.log('用户是否为管理员:', isAdmin);\n    } catch (e) {\n      console.log('无法获取用户信息，使用默认权限');\n    }\n\n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n\n    // 根据用户权限选择接口\n    const endpoint = isAdmin ? '/admin/ai-configs' : '/admin/ai-configs/available';\n    console.log('使用接口:', endpoint);\n\n    const response = await api.get(endpoint, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n\n    clearTimeout(timeoutId);\n    console.log('获取AI配置列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取AI配置列表失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\n\nexport const createAiConfig = async (configData) => {\n  try {\n    console.log('创建AI配置 - 开始', configData);\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.post('/admin/ai-configs', configData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('创建AI配置成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建AI配置失败:', error);\n    console.error('错误类型:', error.name);\n    console.error('错误消息:', error.message);\n    \n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    \n    if (error.response) {\n      console.error('错误状态码:', error.response.status);\n      console.error('错误响应:', error.response.data);\n      throw new Error(`创建失败: ${error.response.data?.detail || error.message}`);\n    }\n    \n    throw new Error(`操作失败: ${error.message}`);\n  }\n};\n\nexport const updateAiConfig = async (id, configData) => {\n  try {\n    console.log(`更新AI配置(ID: ${id}) - 开始`, configData);\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.put(`/admin/ai-configs/${id}`, configData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('更新AI配置成功:', response);\n    return response;\n  } catch (error) {\n    console.error(`更新AI配置(ID: ${id})失败:`, error);\n    console.error('错误类型:', error.name);\n    console.error('错误消息:', error.message);\n    \n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    \n    if (error.response) {\n      console.error('错误状态码:', error.response.status);\n      console.error('错误响应:', error.response.data);\n      throw new Error(`更新失败: ${error.response.data?.detail || error.message}`);\n    }\n    \n    throw new Error(`操作失败: ${error.message}`);\n  }\n};\n\nexport const deleteAiConfig = async (id) => {\n  try {\n    console.log(`删除AI配置(ID: ${id}) - 开始`);\n    const token = localStorage.getItem('token');\n    console.log('令牌存在:', !!token);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.delete(`/admin/ai-configs/${id}`, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('删除AI配置成功:', response);\n    return response;\n  } catch (error) {\n    console.error(`删除AI配置(ID: ${id})失败:`, error);\n    console.error('错误类型:', error.name);\n    console.error('错误消息:', error.message);\n    \n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    \n    if (error.response) {\n      console.error('错误状态码:', error.response.status);\n      console.error('错误响应:', error.response.data);\n      throw new Error(`删除失败: ${error.response.data?.detail || error.message}`);\n    }\n    \n    throw new Error(`操作失败: ${error.message}`);\n  }\n};\n\n// 作业科目管理API - 已移至文件末尾并更新\n\nexport const createSubject = async (subjectData) => {\n  try {\n    console.log('调用createSubject API, data:', subjectData);\n    const token = localStorage.getItem('token');\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.post('/admin/subjects', subjectData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('创建作业科目成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建作业科目失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\n\nexport const updateSubject = async (id, subjectData) => {\n  try {\n    console.log(`调用updateSubject API, id: ${id}, data:`, subjectData);\n    const token = localStorage.getItem('token');\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.put(`/admin/subjects/${id}`, subjectData, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('更新作业科目成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新作业科目失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\n\nexport const deleteSubject = async (id) => {\n  try {\n    console.log(`调用deleteSubject API, id: ${id}`);\n    const token = localStorage.getItem('token');\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.delete(`/admin/subjects/${id}`, {\n      signal: controller.signal,\n      headers: {\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      }\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('删除作业科目成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除作业科目失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接或服务器状态');\n    }\n    throw error;\n  }\n};\n\n// 学校管理API\nexport const getSchoolDetail = async (schoolId) => {\n  try {\n    console.log(`获取学校信息，ID: ${schoolId}`);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.get(`/admin/schools/${schoolId}`, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('获取学校信息成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取学校信息失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    // 返回默认值避免前端崩溃\n    return {\n      id: schoolId,\n      name: \"加载失败\",\n      province: \"\",\n      city: \"\",\n      district: \"\",\n      address: \"\",\n      contact_info: \"\",\n      is_active: true,\n      class_count: 0,\n      teacher_count: 0,\n      student_count: 0\n    };\n  }\n};\n\nexport const updateSchoolInfo = async (schoolId, schoolData) => {\n  try {\n    console.log(`更新学校信息，ID: ${schoolId}`, schoolData);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.put(`/admin/schools/${schoolId}`, schoolData, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('更新学校信息成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新学校信息失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\n\nexport const getClassesBySchool = async (schoolId) => {\n  try {\n    console.log(`获取学校班级列表，学校ID: ${schoolId}，类型: ${typeof schoolId}`);\n    \n    if (schoolId === null || schoolId === undefined) {\n      console.warn('未提供学校ID，无法获取班级列表');\n      return [];\n    }\n    \n    // 尝试将schoolId转换为数字，但如果失败也继续使用原值\n    let numericSchoolId;\n    try {\n      numericSchoolId = parseInt(schoolId, 10);\n      if (isNaN(numericSchoolId)) {\n        console.warn('学校ID不是数字，使用原始值:', schoolId);\n        numericSchoolId = schoolId;\n      }\n    } catch (e) {\n      console.warn('转换学校ID失败，使用原始值:', schoolId);\n      numericSchoolId = schoolId;\n    }\n    \n    // 使用处理后的ID\n    schoolId = numericSchoolId;\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    let response;\n    let errorMessages = [];\n    \n    try {\n      // 第一种API路径尝试\n      console.log(`尝试第一种API路径: /admin/schools/${schoolId}/classes`);\n      response = await api.get(`/admin/schools/${schoolId}/classes`, {\n        signal: controller.signal\n      });\n      console.log('第一种API路径成功，响应:', response);\n    } catch (apiError) {\n      errorMessages.push(`第一种API路径错误: ${apiError.message || '未知错误'}`);\n      console.warn(`第一种API路径失败: /admin/schools/${schoolId}/classes`, apiError);\n      \n      // 如果第一种API路径失败，尝试第二种路径\n      try {\n        console.log(`尝试第二种API路径: /schools/${schoolId}/classes`);\n        response = await api.get(`/schools/${schoolId}/classes`, {\n          signal: controller.signal\n        });\n        console.log('第二种API路径成功，响应:', response);\n      } catch (secondError) {\n        errorMessages.push(`第二种API路径错误: ${secondError.message || '未知错误'}`);\n        console.warn(`第二种API路径也失败: /schools/${schoolId}/classes`, secondError);\n        \n        // 如果两种都失败，尝试查询所有班级然后过滤\n        try {\n          console.log(`尝试第三种API路径: /admin/classes?school_id=${schoolId}`);\n          const allClasses = await api.get(`/admin/classes?school_id=${schoolId}`, {\n            signal: controller.signal\n          });\n          console.log('第三种API路径成功，响应:', allClasses);\n\n          // 过滤属于这个学校的班级\n          response = Array.isArray(allClasses) ? allClasses.filter(c => c.school_id === parseInt(schoolId)) : [];\n          console.log('过滤后的班级列表:', response);\n        } catch (thirdError) {\n          errorMessages.push(`第三种API路径错误: ${thirdError.message || '未知错误'}`);\n          console.error('所有API路径都失败:', errorMessages);\n          response = [];\n        }\n      }\n    }\n    \n    clearTimeout(timeoutId);\n    \n    // 确保响应是数组格式\n    if (!Array.isArray(response)) {\n      console.warn('响应不是数组格式，尝试转换:', response);\n      if (response && typeof response === 'object') {\n        if (Array.isArray(response.items)) {\n          response = response.items;\n        } else {\n          response = [response];\n        }\n      } else {\n        response = [];\n      }\n    }\n    \n    console.log('获取学校班级列表成功，最终结果:', response);\n    return response;\n  } catch (error) {\n    console.error('获取学校班级列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空数组避免前端崩溃\n    return [];\n  }\n};\n\n// 获取所有可用年级列表\nexport const getAvailableGrades = async () => {\n  try {\n    console.log('获取可用年级列表');\n    const response = await api.get('/admin/classes/grades');\n    console.log('获取年级列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取年级列表失败:', error);\n    // 返回默认年级列表作为备用\n    return [\n      \"小学一年级\", \"小学二年级\", \"小学三年级\", \n      \"小学四年级\", \"小学五年级\", \"小学六年级\",\n      \"初中一年级\", \"初中二年级\", \"初中三年级\",\n      \"高中一年级\", \"高中二年级\", \"高中三年级\"\n    ];\n  }\n};\n\nexport const createClassForSchool = async (classData) => {\n  try {\n    console.log('创建班级:', classData);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.post('/admin/classes', classData, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('创建班级成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建班级失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\n\nexport const updateClassInfo = async (classId, classData) => {\n  try {\n    console.log(`更新班级，ID: ${classId}`, classData);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.put(`/admin/classes/${classId}`, classData, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('更新班级成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新班级失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\n\nexport const deleteClassById = async (classId) => {\n  try {\n    console.log(`删除班级，ID: ${classId}`);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.delete(`/admin/classes/${classId}`, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('删除班级成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除班级失败:', error);\n    if (error.name === 'AbortError') {\n      throw new Error('请求超时，请检查网络连接');\n    }\n    throw error;\n  }\n};\n\n// 角色管理API\nexport const getSchoolRoles = async () => {\n  try {\n    console.log('获取角色列表');\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    let response;\n    try {\n      // 尝试第一种API路径\n      response = await api.get('/admin/roles', {\n        signal: controller.signal\n      });\n    } catch (apiError) {\n      console.warn('第一种API路径失败: /admin/roles', apiError);\n      // 如果第一种路径失败，尝试第二种\n      try {\n        response = await api.get('/roles', {\n          signal: controller.signal\n        });\n      } catch (secondError) {\n        console.warn('第二种API路径也失败: /roles', secondError);\n        // 返回空数组，让前端组件使用预定义的USER_ROLES\n        response = [];\n      }\n    }\n    \n    clearTimeout(timeoutId);\n    console.log('获取角色列表成功:', response);\n    return Array.isArray(response) ? response : [];\n  } catch (error) {\n    console.error('获取角色列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空数组，让前端组件使用预定义的USER_ROLES\n    return [];\n  }\n};\n\nexport const getSchoolUsers = async (schoolId) => {\n  try {\n    console.log(`获取学校用户列表，学校ID: ${schoolId}`);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    let response;\n    try {\n      // 尝试第一种API路径\n      response = await api.get(`/admin/schools/${schoolId}/users`, {\n        signal: controller.signal\n      });\n    } catch (apiError) {\n      console.warn(`第一种API路径失败: /admin/schools/${schoolId}/users`, apiError);\n      // 如果失败，尝试第二种路径\n      try {\n        response = await api.get(`/schools/${schoolId}/users`, {\n          signal: controller.signal\n        });\n      } catch (secondError) {\n        console.warn(`第二种API路径也失败: /schools/${schoolId}/users`, secondError);\n        // 返回空数组避免前端崩溃\n        response = [];\n      }\n    }\n    \n    clearTimeout(timeoutId);\n    console.log('获取学校用户列表成功:', response);\n    return Array.isArray(response) ? response : [];\n  } catch (error) {\n    console.error('获取学校用户列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空数组避免前端崩溃\n    return [];\n  }\n};\n\nexport const assignRole = async (roleData) => {\n  try {\n    console.log('分配角色:', roleData);\n    const response = await api.post('/admin/roles/assign', roleData);\n    console.log('分配角色成功:', response);\n    return response;\n  } catch (error) {\n    console.error('分配角色失败:', error);\n    throw error;\n  }\n};\n\nexport const revokeRole = async (roleData) => {\n  try {\n    console.log('撤销角色:', roleData);\n    const response = await api.post('/admin/roles/revoke', roleData);\n    console.log('撤销角色成功:', response);\n    return response;\n  } catch (error) {\n    console.error('撤销角色失败:', error);\n    throw error;\n  }\n};\n\n// 获取仪表盘统计数据\nexport const getDashboardStatistics = async (params = {}) => {\n  try {\n    console.log('调用getDashboardStatistics API', params ? `参数: ${JSON.stringify(params)}` : '');\n    const response = await api.get('/statistics/statistics/dashboard', { params });\n    console.log('获取仪表盘统计数据成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取仪表盘统计数据失败:', error);\n    throw error;\n  }\n};\n\n// 获取注册设置\nexport const getRegistrationSettings = async () => {\n  try {\n    console.log('获取注册设置...');\n    console.log('发起请求: GET /api/public/registration-settings');\n    // 注意：这是公开API，不需要token\n    const response = await axios.get('/api/public/registration-settings');\n    console.log('注册设置API响应:', response);\n    console.log('获取注册设置成功:', response.data);\n    return response.data;\n  } catch (error) {\n    console.error('获取注册设置失败:', error);\n    console.error('错误详情:', error.response ? error.response.data : '无响应数据');\n    console.error('错误状态:', error.response ? error.response.status : '未知状态码');\n    // 默认返回都允许注册\n    return {\n      allow_student_registration: true,\n      allow_teacher_registration: true\n    };\n  }\n};\n\n// 获取高级注册设置\nexport const getAdvancedRegistrationSettings = async () => {\n  try {\n    console.log('获取高级注册设置...');\n    console.log('发起请求: GET /api/public/advanced-registration-settings');\n    // 注意：这是公开API，不需要token\n    const response = await axios.get('/api/public/advanced-registration-settings');\n    console.log('高级注册设置API响应:', response);\n    console.log('获取高级注册设置成功:', response.data);\n    return response.data;\n  } catch (error) {\n    console.error('获取高级注册设置失败:', error);\n    console.error('错误详情:', error.response ? error.response.data : '无响应数据');\n    console.error('错误状态:', error.response ? error.response.status : '未知状态码');\n    // 返回null表示获取失败\n    return null;\n  }\n};\n\n// 获取可用角色\nexport const getAvailableRoles = async () => {\n  try {\n    console.log('获取可用角色...');\n    console.log('发起请求: GET /api/public/available-roles');\n    // 注意：这是公开API，不需要token\n    const response = await axios.get('/api/public/available-roles');\n    console.log('可用角色API响应:', response);\n    \n    if (response.data && Array.isArray(response.data.roles)) {\n      console.log('获取到可用角色:', response.data.roles.length, '个');\n      console.log('角色列表:', response.data.roles.map(r => r.name).join(', '));\n      \n      // 检查是否有可用角色\n      if (response.data.roles.length === 0) {\n        console.warn('API返回的可用角色列表为空');\n      }\n    } else {\n      console.warn('API返回的数据格式不符合预期:', response.data);\n    }\n    \n    return response.data;\n  } catch (error) {\n    console.error('获取可用角色失败:', error);\n    console.error('错误详情:', error.response ? error.response.data : '无响应数据');\n    console.error('错误状态:', error.response ? error.response.status : '未知状态码');\n    \n    // 尝试从错误响应中获取更多信息\n    if (error.response && error.response.data) {\n      console.error('服务器返回的错误信息:', error.response.data);\n    }\n    \n    // 返回默认角色\n    console.log('返回默认角色配置');\n    return {\n      roles: [\n        { name: \"student\", requires_approval: false, fields: { school: { required: true }, class: { required: true }, subject: { required: false, hidden: true } } },\n        { name: \"teacher\", requires_approval: true, fields: { school: { required: true }, class: { required: false }, subject: { required: true } } }\n      ],\n      allow_school_creation: false\n    };\n  }\n};\n\n// 获取注册审批列表\nexport const getRegistrationApprovals = async (params = {}) => {\n  try {\n    console.log('获取注册审批列表, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.status) queryParams.append('status', params.status);\n    if (params.role_name) queryParams.append('role_name', params.role_name);\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n\n    const response = await api.get(`/admin/registration-approvals?${queryParams.toString()}`);\n    console.log('获取注册审批列表成功:', response);\n    // 由于axios拦截器已经返回response.data，这里直接返回response即可\n    return response;\n  } catch (error) {\n    console.error('获取注册审批列表失败:', error);\n    throw error;\n  }\n};\n\n// 一级审核注册申请\nexport const firstReviewRegistration = async (approvalId, data) => {\n  try {\n    console.log(`一级审核注册申请 ID:${approvalId}, 数据:`, data);\n    const response = await api.put(`/admin/registration-approvals/${approvalId}/first-review`, data);\n    console.log('一级审核注册申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('一级审核注册申请失败:', error);\n    throw error;\n  }\n};\n\n// 二级审核注册申请\nexport const finalReviewRegistration = async (approvalId, data) => {\n  try {\n    console.log(`二级审核注册申请 ID:${approvalId}, 数据:`, data);\n    const response = await api.put(`/admin/registration-approvals/${approvalId}/final-review`, data);\n    console.log('二级审核注册申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('二级审核注册申请失败:', error);\n    throw error;\n  }\n};\n\n// 高级注册API\nexport const advancedRegister = async (userData) => {\n  try {\n    console.log('调用高级注册API, 数据:', userData);\n    const response = await axios.post('/api/advanced-register', userData);\n    console.log('高级注册成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('高级注册失败:', error);\n    if (error.response && error.response.data) {\n      throw error.response.data;\n    }\n    throw error;\n  }\n};\n\n// 搜索学生\nexport const searchStudents = async (params = {}) => {\n  try {\n    console.log('搜索学生, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.name) queryParams.append('name', params.name);\n    if (params.class_id) queryParams.append('class_id', params.class_id);\n    if (params.school_id) queryParams.append('school_id', params.school_id);\n    \n    const response = await axios.get(`/api/public/student-search?${queryParams.toString()}`);\n    console.log('搜索学生成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('搜索学生失败:', error);\n    throw error;\n  }\n};\n\n// 创建家长-学生绑定\nexport const createParentStudentBinding = async (bindingData) => {\n  try {\n    console.log('创建家长-学生绑定, 数据:', bindingData);\n    const response = await api.post('/parent-student-binding', bindingData);\n    console.log('创建家长-学生绑定成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建家长-学生绑定失败:', error);\n    throw error;\n  }\n};\n\n// 验证绑定码\nexport const verifyBindingCode = async (verificationData) => {\n  try {\n    console.log('验证绑定码, 数据:', verificationData);\n    const response = await api.post('/verify-binding-code', verificationData);\n    console.log('验证绑定码成功:', response);\n    return response;\n  } catch (error) {\n    console.error('验证绑定码失败:', error);\n    throw error;\n  }\n};\n\n// 创建临时家长-学生绑定（用于注册流程，不需要认证）\nexport const createTempParentStudentBinding = async (bindingData) => {\n  try {\n    console.log('创建临时家长-学生绑定, 数据:', bindingData);\n    const response = await axios.post(`${getBaseURL()}/public/temp-parent-student-binding`, bindingData);\n    console.log('创建临时家长-学生绑定成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('创建临时家长-学生绑定失败:', error);\n    throw error;\n  }\n};\n\n// 验证临时绑定码（用于注册流程，不需要认证）\nexport const verifyTempBindingCode = async (verificationData) => {\n  try {\n    console.log('验证临时绑定码, 数据:', verificationData);\n    const response = await axios.post(`${getBaseURL()}/public/verify-temp-binding-code`, verificationData);\n    console.log('验证临时绑定码成功:', response);\n    return response.data;\n  } catch (error) {\n    console.error('验证临时绑定码失败:', error);\n    throw error;\n  }\n};\n\n// 获取学生的待验证绑定请求\nexport const getPendingBindings = async () => {\n  try {\n    console.log('获取待验证绑定请求');\n    const response = await api.get('/students/pending-bindings');\n    console.log('获取待验证绑定请求成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取待验证绑定请求失败:', error);\n    throw error;\n  }\n};\n\n// 拒绝绑定请求\nexport const rejectBinding = async (bindingId) => {\n  try {\n    console.log('拒绝绑定请求:', bindingId);\n    const response = await api.post(`/students/reject-binding/${bindingId}`);\n    console.log('拒绝绑定请求成功:', response);\n    return response;\n  } catch (error) {\n    console.error('拒绝绑定请求失败:', error);\n    throw error;\n  }\n};\n\n// 创建学校申请\nexport const createSchoolApplication = async (applicationData) => {\n  try {\n    console.log('创建学校申请, 数据:', applicationData);\n    const response = await api.post('/school-application', applicationData);\n    console.log('创建学校申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建学校申请失败:', error);\n    throw error;\n  }\n};\n\n// 获取用户的学校申请列表\nexport const getUserSchoolApplications = async () => {\n  try {\n    console.log('获取用户的学校申请列表');\n    const response = await api.get('/user/school-applications');\n    console.log('获取用户的学校申请列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取用户的学校申请列表失败:', error);\n    throw error;\n  }\n};\n\n// 管理员获取所有学校申请\nexport const getSchoolApplications = async (params = {}) => {\n  try {\n    console.log('获取所有学校申请, 参数:', params);\n    const queryParams = new URLSearchParams();\n    if (params.status) queryParams.append('status', params.status);\n\n    const response = await api.get(`/admin/school-applications?${queryParams.toString()}`);\n    console.log('获取所有学校申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取所有学校申请失败:', error);\n    throw error;\n  }\n};\n\n// 审批学校申请\nexport const reviewSchoolApplication = async (applicationId, reviewData) => {\n  try {\n    console.log(`审批学校申请 ID:${applicationId}, 数据:`, reviewData);\n    const response = await api.put(`/admin/school-applications/${applicationId}/review`, reviewData);\n    console.log('审批学校申请成功:', response);\n    return response;\n  } catch (error) {\n    console.error('审批学校申请失败:', error);\n    throw error;\n  }\n};\n\n// 获取班级学生列表\nexport const getClassStudents = async (schoolId, classId, params = {}) => {\n  try {\n    console.log(`获取班级学生列表，学校ID: ${schoolId}, 班级ID: ${classId}`);\n\n    const queryParams = new URLSearchParams();\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n\n    const response = await api.get(`/schools/${schoolId}/classes/${classId}/students?${queryParams.toString()}`);\n    console.log('获取班级学生列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取班级学生列表失败:', error);\n    throw error;\n  }\n};\n\nexport default api; \n\n// 获取科目分类列表\nexport const getSubjectCategories = async (params = {}) => {\n  try {\n    console.log('调用getSubjectCategories API');\n    const queryParams = new URLSearchParams();\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n    \n    const response = await api.get(`/admin/subject-categories?${queryParams.toString()}`);\n    console.log('获取科目分类列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取科目分类列表失败:', error);\n    return { total: 0, items: [] };\n  }\n};\n\n// 创建科目分类\nexport const createSubjectCategory = async (categoryData) => {\n  try {\n    console.log('调用createSubjectCategory API');\n    const response = await api.post('/admin/subject-categories', categoryData);\n    console.log('创建科目分类成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建科目分类失败:', error);\n    throw error;\n  }\n};\n\n// 更新科目分类\nexport const updateSubjectCategory = async (categoryId, categoryData) => {\n  try {\n    console.log(`调用updateSubjectCategory API, ID: ${categoryId}`);\n    const response = await api.put(`/admin/subject-categories/${categoryId}`, categoryData);\n    console.log('更新科目分类成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新科目分类失败:', error);\n    throw error;\n  }\n};\n\n// 删除科目分类\nexport const deleteSubjectCategory = async (categoryId) => {\n  try {\n    console.log(`调用deleteSubjectCategory API, ID: ${categoryId}`);\n    const response = await api.delete(`/admin/subject-categories/${categoryId}`);\n    console.log('删除科目分类成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除科目分类失败:', error);\n    throw error;\n  }\n};\n\n// 获取所有年级\nexport const getAllGrades = async () => {\n  try {\n    console.log('调用getAllGrades API');\n    const response = await api.get('/admin/grades');\n    console.log('获取年级列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取年级列表失败:', error);\n    return [];\n  }\n};\n\n// 获取指定年级的科目\nexport const getSubjectsByGrade = async (grade) => {\n  try {\n    console.log(`调用getSubjectsByGrade API, 年级: ${grade}`);\n    const response = await api.get(`/admin/subjects/by-grade/${encodeURIComponent(grade)}`);\n    console.log('获取年级科目列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取年级科目列表失败:', error);\n    return [];\n  }\n};\n\n// 修改getSubjects函数，支持分类筛选\nexport const getSubjects = async (params = {}) => {\n  try {\n    console.log('调用getSubjects API');\n    \n    const queryParams = new URLSearchParams();\n    if (params.skip) queryParams.append('skip', params.skip);\n    if (params.limit) queryParams.append('limit', params.limit);\n    if (params.category_id) queryParams.append('category_id', params.category_id);\n    \n    // 添加超时处理\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时\n    \n    const response = await api.get(`/admin/subjects?${queryParams.toString()}`, {\n      signal: controller.signal\n    });\n    \n    clearTimeout(timeoutId);\n    console.log('获取作业科目列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取作业科目列表失败:', error);\n    if (error.name === 'AbortError') {\n      console.error('请求超时');\n    }\n    // 返回空对象，避免前端崩溃\n    return { total: 0, items: [] };\n  }\n};\n\n// 获取角色列表\nexport const getRoles = async () => {\n  try {\n    console.log('调用getRoles API');\n    const response = await api.get('/admin/roles');\n    console.log('获取角色列表成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取角色列表失败:', error);\n    return [];\n  }\n};\n\n// 获取角色的科目权限\nexport const getRoleSubjectPermissions = async (roleId) => {\n  try {\n    console.log(`调用getRoleSubjectPermissions API, 角色ID: ${roleId}`);\n    const response = await api.get(`/admin/roles/${roleId}/subject-permissions`);\n    console.log('获取角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取角色科目权限失败:', error);\n    return [];\n  }\n};\n\n// 创建角色的科目权限\nexport const createRoleSubjectPermission = async (roleId, permissionData) => {\n  try {\n    console.log(`调用createRoleSubjectPermission API, 角色ID: ${roleId}`);\n    const response = await api.post(`/admin/roles/${roleId}/subject-permissions`, permissionData);\n    console.log('创建角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('创建角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 更新角色的科目权限\nexport const updateRoleSubjectPermission = async (roleId, permissionId, permissionData) => {\n  try {\n    console.log(`调用updateRoleSubjectPermission API, 角色ID: ${roleId}, 权限ID: ${permissionId}`);\n    const response = await api.put(`/admin/roles/${roleId}/subject-permissions/${permissionId}`, permissionData);\n    console.log('更新角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('更新角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 删除角色的科目权限\nexport const deleteRoleSubjectPermission = async (roleId, permissionId) => {\n  try {\n    console.log(`调用deleteRoleSubjectPermission API, 角色ID: ${roleId}, 权限ID: ${permissionId}`);\n    const response = await api.delete(`/admin/roles/${roleId}/subject-permissions/${permissionId}`);\n    console.log('删除角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('删除角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 批量设置角色的科目权限\nexport const batchSetRoleSubjectPermissions = async (roleId, permissionsData) => {\n  try {\n    console.log(`调用batchSetRoleSubjectPermissions API, 角色ID: ${roleId}`);\n    const response = await api.post(`/admin/roles/${roleId}/batch-subject-permissions`, {\n      role_id: roleId,\n      permissions: permissionsData\n    });\n    console.log('批量设置角色科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('批量设置角色科目权限失败:', error);\n    throw error;\n  }\n};\n\n// 获取当前用户对指定科目的权限\nexport const getUserSubjectPermission = async (subjectId) => {\n  try {\n    console.log(`调用getUserSubjectPermission API, 科目ID: ${subjectId}`);\n    const response = await api.get(`/public/user/subject-permissions/${subjectId}`);\n    console.log('获取用户科目权限成功:', response);\n    return response;\n  } catch (error) {\n    console.error('获取用户科目权限失败:', error);\n    return {\n      subject_id: subjectId,\n      can_view: true,\n      can_edit: false,\n      can_delete: false,\n      can_assign: false\n    };\n  }\n};\n\n// 拍照解题\nexport const photoSolve = async (formData) => {\n  try {\n    console.log('🚀 调用拍照解题API - 设置3分钟超时');\n    const response = await api.post('/students/photo-solve', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      timeout: 180000, // 3分钟超时 - 给AI充足的解题时间\n    });\n    console.log('✅ 拍照解题成功:', response);\n    return response;\n  } catch (error) {\n    console.error('❌ 拍照解题失败:', error);\n\n    // 更详细的错误处理\n    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {\n      throw new Error('AI正在深度分析题目，处理时间较长。建议：\\n• 确保图片清晰完整\\n• 题目文字清楚可读\\n• 稍后重试或联系老师');\n    }\n\n    throw error;\n  }\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;;AAE3B;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB;EACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,KAAK,MAAM;EACrE,IAAIF,UAAU,EAAE;IACdG,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,OAAO,2BAA2B;EACpC;;EAEA;EACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,EAAE;IAC5CJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B,OAAO,2BAA2B;EACpC;;EAEA;EACA,IAAII,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;IACjCP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;IAC/D,OAAOF,OAAO,CAACC,GAAG,CAACC,iBAAiB;EACtC;;EAEA;EACA,MAAMC,QAAQ,GAAGN,MAAM,CAACC,QAAQ,CAACK,QAAQ;EACzC,MAAMJ,QAAQ,GAAGF,MAAM,CAACC,QAAQ,CAACC,QAAQ;EACzC,MAAMK,IAAI,GAAGP,MAAM,CAACC,QAAQ,CAACM,IAAI;EAEjC,IAAIC,MAAM;;EAEV;EACA,IAAIN,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,WAAW,EAAE;IACxDM,MAAM,GAAG,GAAGF,QAAQ,KAAKJ,QAAQ,WAAW;EAC9C,CAAC,MAAM,IAAIA,QAAQ,KAAK,YAAY,EAAE;IACpC;IACAM,MAAM,GAAG,kCAAkC;EAC7C,CAAC,MAAM;IACL;IACA,IAAID,IAAI,EAAE;MACRC,MAAM,GAAG,GAAGF,QAAQ,KAAKJ,QAAQ,IAAIK,IAAI,MAAM;IACjD,CAAC,MAAM;MACL;MACAC,MAAM,GAAG,GAAGF,QAAQ,KAAKJ,QAAQ,MAAM;IACzC;EACF;EAEAJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAES,MAAM,CAAC;EACtCV,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;IAAEO,QAAQ;IAAEJ,QAAQ;IAAEK;EAAK,CAAC,CAAC;EAEvD,OAAOC,MAAM;AACf,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGjB,KAAK,CAACkB,MAAM,CAAC;EACvBC,OAAO,EAAEjB,UAAU,CAAC,CAAC;EACrBkB,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,kBAAkB,EAAE,gBAAgB,CAAE;EACxC,CAAC;EACD;EACAC,OAAO,EAAE,KAAK;EAAG;EACjB;EACAC,eAAe,EAAE,KAAK;EAAG;EACzB;EACAC,YAAY,EAAE;AAChB,CAAC,CAAC;AAEFjB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEL,UAAU,CAAC,CAAC,CAAC;;AAE1C;AACAe,GAAG,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIuB,KAAK,EAAE;IAAA,IAAAC,cAAA;IACTF,MAAM,CAACP,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUQ,KAAK,EAAE;IACnDtB,OAAO,CAACC,GAAG,CAAC,UAAAsB,cAAA,GAASF,MAAM,CAACG,MAAM,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,IAAIJ,MAAM,CAACK,GAAG,aAAaJ,KAAK,CAACK,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;IACxG3B,OAAO,CAACC,GAAG,CAAC,aAAaoB,MAAM,CAACR,OAAO,GAAGQ,MAAM,CAACK,GAAG,EAAE,CAAC;EACzD,CAAC,MAAM;IAAA,IAAAE,eAAA;IACL5B,OAAO,CAAC6B,IAAI,CAAC,UAAAD,eAAA,GAASP,MAAM,CAACG,MAAM,cAAAI,eAAA,uBAAbA,eAAA,CAAeH,WAAW,CAAC,CAAC,IAAIJ,MAAM,CAACK,GAAG,SAAS,CAAC;IAC1E1B,OAAO,CAACC,GAAG,CAAC,aAAaoB,MAAM,CAACR,OAAO,GAAGQ,MAAM,CAACK,GAAG,EAAE,CAAC;EACzD;EACA,OAAOL,MAAM;AACf,CAAC,EACAS,KAAK,IAAK;EACT9B,OAAO,CAAC8B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAClC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAnB,GAAG,CAACO,YAAY,CAACe,QAAQ,CAACb,GAAG,CAC1Ba,QAAQ,IAAK;EAAA,IAAAC,qBAAA;EACZlC,OAAO,CAACC,GAAG,CAAC,eAAAiC,qBAAA,GAAcD,QAAQ,CAACZ,MAAM,CAACG,MAAM,cAAAU,qBAAA,uBAAtBA,qBAAA,CAAwBT,WAAW,CAAC,CAAC,IAAIQ,QAAQ,CAACZ,MAAM,CAACK,GAAG,EAAE,EAAEO,QAAQ,CAACE,MAAM,CAAC;;EAE1G;EACA,IAAIF,QAAQ,CAACZ,MAAM,CAACe,YAAY,KAAK,MAAM,EAAE;IAC3C,OAAOH,QAAQ,CAACI,IAAI;EACtB;EAEA,OAAOJ,QAAQ,CAACI,IAAI;AACtB,CAAC,EACAP,KAAK,IAAK;EAAA,IAAAQ,aAAA,EAAAC,oBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,cAAA;EACT,MAAMnB,MAAM,GAAG,EAAAc,aAAA,GAAAR,KAAK,CAACT,MAAM,cAAAiB,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAcd,MAAM,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBd,WAAW,CAAC,CAAC,KAAI,SAAS;EAC/D,MAAMC,GAAG,GAAG,EAAAc,cAAA,GAAAV,KAAK,CAACT,MAAM,cAAAmB,cAAA,uBAAZA,cAAA,CAAcd,GAAG,KAAI,SAAS;EAC1C,MAAMS,MAAM,GAAG,EAAAM,eAAA,GAAAX,KAAK,CAACG,QAAQ,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBN,MAAM,KAAI,aAAa;EACtD,MAAME,IAAI,GAAG,EAAAK,gBAAA,GAAAZ,KAAK,CAACG,QAAQ,cAAAS,gBAAA,uBAAdA,gBAAA,CAAgBL,IAAI,KAAI,CAAC,CAAC;EAEvCrC,OAAO,CAAC8B,KAAK,CAAC,cAAcN,MAAM,IAAIE,GAAG,EAAE,EAAE;IAC3CS,MAAM;IACNE,IAAI;IACJO,OAAO,EAAEd,KAAK,CAACc,OAAO;IACtB/B,OAAO,GAAA8B,cAAA,GAAEb,KAAK,CAACT,MAAM,cAAAsB,cAAA,uBAAZA,cAAA,CAAc9B;EACzB,CAAC,CAAC;EAEF,IAAIiB,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;IACnDnC,OAAO,CAAC6B,IAAI,CAAC,wBAAwB,CAAC;IACtC/B,YAAY,CAAC+C,UAAU,CAAC,OAAO,CAAC;IAChC/C,YAAY,CAAC+C,UAAU,CAAC,MAAM,CAAC;IAC/B;IACA,IAAI3C,MAAM,CAACC,QAAQ,CAAC2C,QAAQ,KAAK,QAAQ,EAAE;MACzC9C,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MACzBC,MAAM,CAACC,QAAQ,CAAC4C,IAAI,GAAG,QAAQ;IACjC;EACF;EAEA,OAAOhB,OAAO,CAACC,MAAM,CAACF,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACI,IAAI,GAAGP,KAAK,CAAC;AACrE,CACF,CAAC;;AAED;AACA,OAAO,MAAMkB,KAAK,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK;EAC3C,MAAMC,QAAQ,GAAG,IAAIC,eAAe,CAAC,CAAC;EACtCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;EACrCE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;;EAErC;EACA,OAAOvC,GAAG,CAAC2C,IAAI,CAAC,QAAQ,EAAEH,QAAQ,EAAE;IAClCrC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMyC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;EAC1C,IAAI;IACFxD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuD,QAAQ,CAAC;IAClD,MAAMvB,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,OAAO,EAAEE,QAAQ,CAAC;IAClDxD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEgC,QAAQ,CAAC;IAC9B,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC7B,MAAMA,KAAK,CAAC,CAAC;EACf;AACF,CAAC;AAED,OAAO,MAAM2B,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAO9C,GAAG,CAAC+C,GAAG,CAAC,KAAK,CAAC,CAClBC,IAAI,CAACH,QAAQ,IAAI;IAChBxD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuD,QAAQ,CAAC;;IAEjC;IACA,MAAMlC,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIuB,KAAK,IAAIkC,QAAQ,EAAE;MACrB;MACAA,QAAQ,CAACI,YAAY,GAAGtC,KAAK;;MAE7B;MACAxB,YAAY,CAAC+D,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAAC,CAAC;IACxD;IAEA,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACDQ,KAAK,CAAClC,KAAK,IAAI;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMmC,UAAU,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiE,MAAM,GAAG,OAAOJ,IAAI,CAACC,SAAS,CAACG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;IAC9E;IACA,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D;IACA,MAAMC,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAACO,SAAS,EAAED,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAACO,SAAS,CAAC;IACvE,IAAIP,MAAM,CAACQ,QAAQ,EAAEF,WAAW,CAACnB,MAAM,CAAC,UAAU,EAAEa,MAAM,CAACQ,QAAQ,CAAC;IACpE,IAAIR,MAAM,CAACS,KAAK,EAAEH,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEa,MAAM,CAACS,KAAK,CAAC;;IAE3D;IACA,IAAIjD,GAAG,GAAG,UAAU,CAAC,CAAE;IACvB,IAAIO,QAAQ,GAAG,IAAI;IAEnB,IAAI;MACFjC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,GAAG,CAAC;MAC5BO,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAChC,GAAG,EAAE;QAC5BkD,MAAM,EAAET,UAAU,CAACS,MAAM;QACzBV,MAAM,EAAEA;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOW,UAAU,EAAE;MACnB7E,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4E,UAAU,CAAC;MAC7C,IAAI;QACFnD,GAAG,GAAG,gBAAgB,CAAC,CAAE;QACzB1B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,GAAG,CAAC;QAC5BO,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAChC,GAAG,EAAE;UAC5BkD,MAAM,EAAET,UAAU,CAACS,MAAM;UACzBV,MAAM,EAAEA;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOY,WAAW,EAAE;QACpB9E,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6E,WAAW,CAAC;QACjD;QACA,IAAI;UACF,MAAMC,aAAa,GAAG,MAAMpE,GAAG,CAAC+C,GAAG,CAAC,QAAQ,EAAE;YAAEkB,MAAM,EAAET,UAAU,CAACS;UAAO,CAAC,CAAC;UAC5E,IAAIG,aAAa,IAAIA,aAAa,CAACN,SAAS,EAAE;YAC5CzE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE8E,aAAa,CAACN,SAAS,CAAC;YACnD/C,GAAG,GAAG,kBAAkBqD,aAAa,CAACN,SAAS,UAAU;YACzDzE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,GAAG,CAAC;YAC5BO,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAChC,GAAG,EAAE;cAAEkD,MAAM,EAAET,UAAU,CAACS;YAAO,CAAC,CAAC;UAC9D,CAAC,MAAM;YACL5E,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;YACnCyB,GAAG,GAAG,0BAA0B;YAChC1B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,GAAG,CAAC;YAC5BO,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAChC,GAAG,EAAE;cAAEkD,MAAM,EAAET,UAAU,CAACS;YAAO,CAAC,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOI,UAAU,EAAE;UACnBhF,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEkD,UAAU,CAAC;UACxC,MAAMA,UAAU;QAClB;MACF;IACF;IAEAC,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC;IACA,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMoD,QAAQ,GAAGA,CAACC,EAAE,EAAEC,OAAO,GAAG,KAAK,KAAK;EAC/CpF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkF,EAAE,EAAEC,OAAO,GAAG,QAAQ,GAAG,UAAU,CAAC;;EAEvE;EACA,MAAMlB,MAAM,GAAGkB,OAAO,GAAG;IAAEC,EAAE,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;EAAE,CAAC,GAAG,CAAC,CAAC;;EAE1D;EACA,MAAMpB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,QAAQqD,EAAE,SAAS,CAAC;EACpC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA,MAAMK,UAAU,GAAG,CAAC;EACpB,IAAIC,UAAU,GAAG,CAAC;;EAElB;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B1F,OAAO,CAACC,GAAG,CAAC,UAAUkF,EAAE,WAAWM,UAAU,GAAG,CAAC,IAAID,UAAU,GAAG,CAAC,GAAG,CAAC;;IAEvE;IACA,MAAM1E,OAAO,GAAG;MACd,cAAc,EAAE,kBAAkB;MAClC,QAAQ,EAAE,kBAAkB;MAC5B,kBAAkB,EAAE,gBAAgB;MACpC,eAAe,EAAEsE,OAAO,GAAG,oBAAoB,GAAG;IACpD,CAAC;IAED,OAAOzE,GAAG,CAAC+C,GAAG,CAAC,kBAAkByB,EAAE,EAAE,EAAE;MACrCjB,MAAM,EAAEA,MAAM;MACdU,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAEA;MACT;IACF,CAAC,CAAC,CACD6C,IAAI,CAAC1B,QAAQ,IAAI;MAChBgD,YAAY,CAACZ,SAAS,CAAC;MACvBrE,OAAO,CAACC,GAAG,CAAC,QAAQkF,EAAE,QAAQ,EAAElD,QAAQ,CAAC;MACzC,OAAOA,QAAQ;IACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;MAAA,IAAA6D,gBAAA;MACd3F,OAAO,CAAC8B,KAAK,CAAC,QAAQqD,EAAE,aAAaM,UAAU,GAAG,CAAC,IAAID,UAAU,GAAG,CAAC,IAAI,EAAE1D,KAAK,CAAC;MACjF9B,OAAO,CAAC8B,KAAK,CAAC,SAASA,KAAK,CAAC8D,IAAI,WAAAD,gBAAA,GAAU7D,KAAK,CAACG,QAAQ,cAAA0D,gBAAA,uBAAdA,gBAAA,CAAgBxD,MAAM,SAASL,KAAK,CAACc,OAAO,EAAE,CAAC;MAE1F,IAAId,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;QAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;MACjC;;MAEA;MACA,IAAIJ,UAAU,GAAGD,UAAU,EAAE;QAC3BC,UAAU,EAAE;QACZzF,OAAO,CAACC,GAAG,CAAC,UAAUkF,EAAE,QAAQM,UAAU,IAAID,UAAU,GAAG,CAAC;QAC5D,OAAOE,aAAa,CAAC,CAAC;MACxB;;MAEA;MACA1F,OAAO,CAACC,GAAG,CAAC,iBAAiBkF,EAAE,KAAK,CAAC;MACrC,OAAOxE,GAAG,CAAC+C,GAAG,CAAC,YAAYyB,EAAE,EAAE,EAAE;QAC/BjB,MAAM,EAAEA,MAAM;QACdpD,OAAO,EAAEA,OAAO;QAChB8D,MAAM,EAAET,UAAU,CAACS;QACnB;MACF,CAAC,CAAC,CACCjB,IAAI,CAACmC,cAAc,IAAI;QACtB9F,OAAO,CAACC,GAAG,CAAC,eAAekF,EAAE,MAAM,EAAEW,cAAc,CAAC;QACpD,OAAOA,cAAc;MACvB,CAAC,CAAC,CACD9B,KAAK,CAAC+B,WAAW,IAAI;QAAA,IAAAC,qBAAA;QACpBhG,OAAO,CAAC8B,KAAK,CAAC,aAAaqD,EAAE,QAAQ,EAAEY,WAAW,CAAC;QACnD/F,OAAO,CAAC8B,KAAK,CAAC,cAAciE,WAAW,CAACH,IAAI,WAAAI,qBAAA,GAAUD,WAAW,CAAC9D,QAAQ,cAAA+D,qBAAA,uBAApBA,qBAAA,CAAsB7D,MAAM,SAAS4D,WAAW,CAACnD,OAAO,EAAE,CAAC;;QAEjH;QACA5C,OAAO,CAACC,GAAG,CAAC,kBAAkBkF,EAAE,KAAK,CAAC;QACtC,OAAOxE,GAAG,CAAC+C,GAAG,CAAC,kBAAkByB,EAAE,WAAW,EAAE;UAC9CjB,MAAM,EAAEA,MAAM;UACdpD,OAAO,EAAEA,OAAO;UAChB8D,MAAM,EAAET,UAAU,CAACS;UACnB;QACF,CAAC,CAAC,CACCjB,IAAI,CAACsC,aAAa,IAAI;UACrBjG,OAAO,CAACC,GAAG,CAAC,kBAAkBkF,EAAE,MAAM,EAAEc,aAAa,CAAC;UACtD,OAAOA,aAAa;QACtB,CAAC,CAAC,CACDjC,KAAK,CAACkC,UAAU,IAAI;UACnBlG,OAAO,CAAC8B,KAAK,CAAC,gBAAgBqD,EAAE,QAAQ,EAAEe,UAAU,CAAC;;UAErD;UACAlG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;UAE/B;UACA,OAAO;YACLkF,EAAE,EAAEA,EAAE;YACNS,IAAI,EAAE,MAAMT,EAAE,EAAE;YAChBgB,WAAW,EAAE,EAAE;YACfC,UAAU,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,WAAW,CAAC,CAAC;YACpCC,QAAQ,EAAE,EAAE;YACZC,aAAa,EAAE,CAAC;YAChB5B,KAAK,EAAE,EAAE;YACTF,SAAS,EAAE,CAAC;YACZ3C,KAAK,EAAE,IAAI,CAAC;UACd,CAAC;QACH,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC;EAED,OAAO4D,aAAa,CAAC,CAAC;AACxB,CAAC;AAED,OAAO,MAAMc,WAAW,GAAIC,SAAS,IAAK;EACxCzG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwG,SAAS,CAAC;EAClD,OAAO9F,GAAG,CAAC2C,IAAI,CAAC,gBAAgB,EAAEmD,SAAS,CAAC;AAC9C,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAACvB,EAAE,EAAEsB,SAAS,KAAK;EAC5C,OAAO9F,GAAG,CAACgG,GAAG,CAAC,kBAAkBxB,EAAE,EAAE,EAAEsB,SAAS,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAMG,kBAAkB,GAAGA,CAACC,QAAQ,EAAEJ,SAAS,KAAK;EACzD,OAAO9F,GAAG,CAACgG,GAAG,CAAC,sBAAsB,EAAE;IAAEG,SAAS,EAAED,QAAQ;IAAE,GAAGJ;EAAU,CAAC,CAAC;AAC/E,CAAC;AAED,OAAO,MAAMM,WAAW,GAAI5B,EAAE,IAAK;EACjC,OAAOxE,GAAG,CAACqG,MAAM,CAAC,kBAAkB7B,EAAE,EAAE,CAAC;AAC3C,CAAC;AAED,OAAO,MAAM8B,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,SAAS,KAAK;EACvDnH,OAAO,CAACC,GAAG,CAAC,aAAakH,SAAS,WAAWD,OAAO,GAAG,CAAC;;EAExD;EACA,MAAME,QAAQ,GAAG,OAAOD,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACE,UAAU,CAAC,OAAO,CAAC;;EAE/E;EACA,IAAID,QAAQ,EAAE;IACZpH,OAAO,CAACC,GAAG,CAAC,WAAWkH,SAAS,WAAW,CAAC;;IAE5C;IACA,MAAMlE,QAAQ,GAAGkE,SAAS,CAACG,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;IAC/CtH,OAAO,CAACC,GAAG,CAAC,gBAAgBgD,QAAQ,EAAE,CAAC;;IAEvC;IACA,OAAOtC,GAAG,CAAC+C,GAAG,CAAC,cAAc,EAAE;MAC7BQ,MAAM,EAAE;QACNqD,MAAM,EAAEtE,QAAQ;QAChBuE,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE;MACT;IACF,CAAC,CAAC,CACD9D,IAAI,CAAC1B,QAAQ,IAAI;MAChBjC,OAAO,CAACC,GAAG,CAAC,SAASgD,QAAQ,OAAO,EAAEhB,QAAQ,CAAC;;MAE/C;MACA,IAAIyF,QAAQ,GAAG,EAAE;MACjB,IAAIC,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;QAC3ByF,QAAQ,GAAGzF,QAAQ;MACrB,CAAC,MAAM,IAAIA,QAAQ,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC,EAAE;QACpDH,QAAQ,GAAGzF,QAAQ,CAAC4F,KAAK;MAC3B;;MAEA;MACA,MAAMC,UAAU,GAAGJ,QAAQ,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/E,QAAQ,KAAKA,QAAQ,CAAC;MAC9D,IAAIgF,aAAa,GAAG,IAAI;MAExB,IAAIH,UAAU,EAAE;QACd9H,OAAO,CAACC,GAAG,CAAC,cAAcgD,QAAQ,SAAS6E,UAAU,CAAC3C,EAAE,EAAE,CAAC;QAC3D8C,aAAa,GAAGH,UAAU,CAAC3C,EAAE;MAC/B,CAAC,MAAM,IAAIuC,QAAQ,CAACQ,MAAM,GAAG,CAAC,EAAE;QAC9B;QACAlI,OAAO,CAACC,GAAG,CAAC,oBAAoByH,QAAQ,CAAC,CAAC,CAAC,CAACzE,QAAQ,SAASyE,QAAQ,CAAC,CAAC,CAAC,CAACvC,EAAE,EAAE,CAAC;QAC9E8C,aAAa,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAACvC,EAAE;MAChC,CAAC,MAAM;QACL;QACAnF,OAAO,CAACC,GAAG,CAAC,SAASgD,QAAQ,WAAW,CAAC;;QAEzC;QACA,MAAMkF,SAAS,GAAGhB,SAAS,CAACiB,KAAK,CAAC,GAAG,CAAC;QACtC,MAAMC,WAAW,GAAG;UAClBpF,QAAQ,EAAEA,QAAQ;UAClBqF,SAAS,EAAErF,QAAQ;UAAE;UACrBsF,KAAK,EAAE,GAAGtF,QAAQ,cAAc;UAChCC,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA,OAAOvC,GAAG,CAAC2C,IAAI,CAAC,cAAc,EAAE+E,WAAW,CAAC,CACzC1E,IAAI,CAAC6E,OAAO,IAAI;UACfxI,OAAO,CAACC,GAAG,CAAC,WAAWgD,QAAQ,SAASuF,OAAO,CAACrD,EAAE,EAAE,CAAC;UACrD;UACA,OAAO8B,iBAAiB,CAACC,OAAO,EAAEsB,OAAO,CAACrD,EAAE,CAAC;QAC/C,CAAC,CAAC,CACDnB,KAAK,CAACyE,WAAW,IAAI;UACpBzI,OAAO,CAAC8B,KAAK,CAAC,QAAQmB,QAAQ,MAAM,EAAEwF,WAAW,CAAC;;UAElD;UACA,OAAO9H,GAAG,CAAC+C,GAAG,CAAC,cAAc,EAAE;YAC7BQ,MAAM,EAAE;cACNqD,MAAM,EAAEtE,QAAQ;cAChBuE,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE;YACT;UACF,CAAC,CAAC,CACD9D,IAAI,CAAC+E,cAAc,IAAI;YACtB1I,OAAO,CAACC,GAAG,CAAC,WAAWgD,QAAQ,OAAO,EAAEyF,cAAc,CAAC;YAEvD,IAAIC,cAAc,GAAG,EAAE;YACvB,IAAIhB,KAAK,CAACC,OAAO,CAACc,cAAc,CAAC,EAAE;cACjCC,cAAc,GAAGD,cAAc;YACjC,CAAC,MAAM,IAAIA,cAAc,IAAIf,KAAK,CAACC,OAAO,CAACc,cAAc,CAACb,KAAK,CAAC,EAAE;cAChEc,cAAc,GAAGD,cAAc,CAACb,KAAK;YACvC;YAEA,MAAMe,gBAAgB,GAAGD,cAAc,CAACZ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/E,QAAQ,KAAKA,QAAQ,CAAC;YAE1E,IAAI2F,gBAAgB,EAAE;cACpB5I,OAAO,CAACC,GAAG,CAAC,kBAAkBgD,QAAQ,SAAS2F,gBAAgB,CAACzD,EAAE,EAAE,CAAC;cACrE,OAAO8B,iBAAiB,CAACC,OAAO,EAAE0B,gBAAgB,CAACzD,EAAE,CAAC;YACxD,CAAC,MAAM,IAAIwD,cAAc,CAACT,MAAM,GAAG,CAAC,EAAE;cACpClI,OAAO,CAACC,GAAG,CAAC,wBAAwB0I,cAAc,CAAC,CAAC,CAAC,CAAC1F,QAAQ,SAAS0F,cAAc,CAAC,CAAC,CAAC,CAACxD,EAAE,EAAE,CAAC;cAC9F,OAAO8B,iBAAiB,CAACC,OAAO,EAAEyB,cAAc,CAAC,CAAC,CAAC,CAACxD,EAAE,CAAC;YACzD,CAAC,MAAM;cACLnF,OAAO,CAACC,GAAG,CAAC,cAAcgD,QAAQ,WAAW,CAAC;cAC9C,OAAO;gBACLkC,EAAE,EAAEgC,SAAS;gBACbhF,MAAM,EAAE,iBAAiB;gBACzBS,OAAO,EAAE;cACX,CAAC;YACH;UACF,CAAC,CAAC,CACDoB,KAAK,CAACc,WAAW,IAAI;YACpB9E,OAAO,CAAC8B,KAAK,CAAC,UAAUmB,QAAQ,MAAM,EAAE6B,WAAW,CAAC;YACpD,OAAO;cACLK,EAAE,EAAEgC,SAAS;cACbhF,MAAM,EAAE,iBAAiB;cACzBS,OAAO,EAAE;YACX,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC;MACN;;MAEA;MACA5C,OAAO,CAACC,GAAG,CAAC,UAAUgI,aAAa,YAAYf,OAAO,EAAE,CAAC;MACzD,OAAOD,iBAAiB,CAACC,OAAO,EAAEe,aAAa,CAAC;IAClD,CAAC,CAAC,CACDjE,KAAK,CAAClC,KAAK,IAAI;MACd9B,OAAO,CAAC8B,KAAK,CAAC,QAAQmB,QAAQ,MAAM,EAAEnB,KAAK,CAAC;MAC5C,OAAO;QACLqD,EAAE,EAAEgC,SAAS;QACbhF,MAAM,EAAE,iBAAiB;QACzBS,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMuB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,aAAa,CAAC;EAC9B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX,OAAOnB,GAAG,CAAC2C,IAAI,CAAC,kBAAkB4D,OAAO,WAAW,EAAE;IAAE2B,UAAU,EAAE1B;EAAU,CAAC,EAAE;IAC/EvC,MAAM,EAAET,UAAU,CAACS;EACrB,CAAC,CAAC,CACDjB,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,aAAakH,SAAS,WAAWD,OAAO,OAAO,EAAEjF,QAAQ,CAAC;IACtE,OAAO;MACL,GAAGA,QAAQ;MACXE,MAAM,EAAE,SAAS;MACjBS,OAAO,EAAE;IACX,CAAC;EACH,CAAC,CAAC,CACDoB,KAAK,CAAClC,KAAK,IAAI;IAAA,IAAAgH,gBAAA;IACd7D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,WAAWqF,SAAS,WAAWD,OAAO,MAAM,EAAEpF,KAAK,CAAC;IAClE9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAE,EAAAgH,gBAAA,GAAAhH,KAAK,CAACG,QAAQ,cAAA6G,gBAAA,uBAAdA,gBAAA,CAAgBzG,IAAI,KAAIP,KAAK,CAACc,OAAO,CAAC;IAE7D,IAAId,KAAK,CAAC8D,IAAI,KAAK,YAAY,IAAI9D,KAAK,CAACiH,IAAI,KAAK,cAAc,EAAE;MAChE;MACA/I,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,OAAO;QACLkF,EAAE,EAAEgC,SAAS;QACbhF,MAAM,EAAE,iBAAiB;QACzBS,OAAO,EAAE;MACX,CAAC;IACH;IAEA,IAAId,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,IAC/CL,KAAK,CAACG,QAAQ,CAACI,IAAI,IAAIP,KAAK,CAACG,QAAQ,CAACI,IAAI,CAAC2G,MAAM,KAAK,UAAU,EAAE;MACpE;MACAhJ,OAAO,CAACC,GAAG,CAAC,SAASkH,SAAS,YAAYD,OAAO,OAAO,CAAC;MACzD,OAAO;QAAE/B,EAAE,EAAEgC,SAAS;QAAEhF,MAAM,EAAE,SAAS;QAAES,OAAO,EAAE;MAAU,CAAC;IACjE;IAEA,IAAId,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACnD;MACAnC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,OAAO;QACLkF,EAAE,EAAEgC,SAAS;QACbhF,MAAM,EAAE,iBAAiB;QACzBS,OAAO,EAAE;MACX,CAAC;IACH;;IAEA;IACA,IAAId,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACnDnC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,OAAO;QAAEkF,EAAE,EAAEgC,SAAS;QAAEhF,MAAM,EAAE,iBAAiB;QAAES,OAAO,EAAE;MAAkB,CAAC;IACjF;;IAEA;IACA5C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,OAAO;MAAEkF,EAAE,EAAEgC,SAAS;MAAEhF,MAAM,EAAE,iBAAiB;MAAES,OAAO,EAAE;IAAkB,CAAC;EACjF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMqG,sBAAsB,GAAGA,CAAC/B,OAAO,EAAEC,SAAS,KAAK;EAC5D,OAAOxG,GAAG,CAACqG,MAAM,CAAC,kBAAkBE,OAAO,aAAaC,SAAS,EAAE,CAAC;AACtE,CAAC;AAED,OAAO,MAAM+B,oBAAoB,GAAGA,CAAChC,OAAO,EAAEC,SAAS,EAAEkB,WAAW,KAAK;EACvErI,OAAO,CAACC,GAAG,CAAC,wCAAwCiH,OAAO,gBAAgBC,SAAS,SAAS,EAAEkB,WAAW,CAAC;EAC3G,OAAO1H,GAAG,CAACgG,GAAG,CAAC,kBAAkBO,OAAO,aAAaC,SAAS,EAAE,EAAEkB,WAAW,CAAC,CAC3E1E,IAAI,CAAC1B,QAAQ,IAAI;IAChBjC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMqH,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,IAAI;IACFnJ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;IAExB;IACAD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,MAAMmJ,gBAAgB,GAAG,MAAMzI,GAAG,CAAC+C,GAAG,CAAC,iCAAiC,CAAC;IAEzE,IAAIiE,KAAK,CAACC,OAAO,CAACwB,gBAAgB,CAAC,EAAE;MACnCpJ,OAAO,CAACC,GAAG,CAAC,eAAemJ,gBAAgB,CAAClB,MAAM,EAAE,CAAC;MACrD,OAAOkB,gBAAgB,CAAClB,MAAM;IAChC,CAAC,MAAM,IAAIkB,gBAAgB,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,IAAI,OAAO,IAAIA,gBAAgB,EAAE;MAClGpJ,OAAO,CAACC,GAAG,CAAC,eAAemJ,gBAAgB,CAACC,KAAK,EAAE,CAAC;MACpD,OAAOD,gBAAgB,CAACC,KAAK;IAC/B;;IAEA;IACArJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAIqJ,UAAU,GAAG,CAAC;IAClB,IAAIC,SAAS,GAAG,GAAG;IACnB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,WAAW,GAAG,IAAI;IAEtB,OAAOA,WAAW,EAAE;MAClB,MAAMC,aAAa,GAAG,MAAM/I,GAAG,CAAC+C,GAAG,CAAC,sBAAsB6F,SAAS,SAASC,WAAW,EAAE,CAAC;MAE1F,IAAI7B,KAAK,CAACC,OAAO,CAAC8B,aAAa,CAAC,IAAIA,aAAa,CAACxB,MAAM,GAAG,CAAC,EAAE;QAC5DoB,UAAU,IAAII,aAAa,CAACxB,MAAM;QAClCsB,WAAW,IAAID,SAAS;QACxBvJ,OAAO,CAACC,GAAG,CAAC,gBAAgBqJ,UAAU,UAAUE,WAAW,EAAE,CAAC;;QAE9D;QACA,IAAIE,aAAa,CAACxB,MAAM,GAAGqB,SAAS,EAAE;UACpCE,WAAW,GAAG,KAAK;QACrB;MACF,CAAC,MAAM;QACLA,WAAW,GAAG,KAAK;MACrB;;MAEA;MACA,IAAID,WAAW,GAAG,KAAK,EAAE;QACvBxJ,OAAO,CAAC6B,IAAI,CAAC,eAAe,CAAC;QAC7B;MACF;IACF;IAEA,IAAIyH,UAAU,GAAG,CAAC,EAAE;MAClBtJ,OAAO,CAACC,GAAG,CAAC,mBAAmBqJ,UAAU,EAAE,CAAC;MAC5C,OAAOA,UAAU;IACnB;;IAEA;IACAtJ,OAAO,CAAC6B,IAAI,CAAC,mBAAmB,CAAC;IACjC,OAAO,EAAE,CAAC,CAAC;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,EAAE,CAAC,CAAC;EACb;AACF,CAAC;AAED,OAAO,MAAM6H,QAAQ,GAAG,MAAAA,CAAOzF,MAAM,GAAG,CAAC,CAAC,KAAK;EAC7C,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiE,MAAM,GAAG,OAAOJ,IAAI,CAACC,SAAS,CAACG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;;IAE5E;IACA,IAAI0F,UAAU,GAAG,GAAG,CAAC,CAAC;IACtB,IAAI;MACFA,UAAU,GAAG,MAAMT,aAAa,CAAC,CAAC;MAClCnJ,OAAO,CAACC,GAAG,CAAC,aAAa2J,UAAU,EAAE,CAAC;IACxC,CAAC,CAAC,OAAOC,UAAU,EAAE;MACnB7J,OAAO,CAAC8B,KAAK,CAAC,iBAAiB,EAAE+H,UAAU,CAAC;IAC9C;;IAEA;IACA,MAAMrF,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAACqD,MAAM,EAAE/C,WAAW,CAACnB,MAAM,CAAC,QAAQ,EAAEa,MAAM,CAACqD,MAAM,CAAC;IAC9D,IAAIrD,MAAM,CAACsD,IAAI,EAAEhD,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAACsD,IAAI,CAAC;IACxD,IAAItD,MAAM,CAACO,SAAS,EAAED,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAACO,SAAS,CAAC;IACvE,IAAIP,MAAM,CAAC4F,SAAS,KAAKC,SAAS,EAAEvF,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAAC4F,SAAS,CAAC;;IAErF;IACA,MAAME,IAAI,GAAG9F,MAAM,CAAC8F,IAAI,KAAKD,SAAS,GAAGE,QAAQ,CAAC/F,MAAM,CAAC8F,IAAI,CAAC,GAAG,CAAC;IAClE,MAAMvC,KAAK,GAAGvD,MAAM,CAACuD,KAAK,KAAKsC,SAAS,GAAGE,QAAQ,CAAC/F,MAAM,CAACuD,KAAK,CAAC,GAAG,EAAE;IAEtEjD,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAE2G,IAAI,CAAC;IAChCxF,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEoE,KAAK,CAAC;;IAElC;IACAzH,OAAO,CAACC,GAAG,CAAC,cAAc+J,IAAI,WAAWvC,KAAK,QAAQuC,IAAI,GAACvC,KAAK,GAAG,CAAC,EAAE,CAAC;;IAEvE;IACA,IAAI/F,GAAG,GAAG,cAAc;IACxB,IAAI8C,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE;MAC1BxI,GAAG,IAAI,IAAI8C,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE;IACrC;IAEAlK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyB,GAAG,CAAC;;IAE9B;IACA,MAAMyC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D;IACA,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAChC,GAAG,EAAE;MAClCkD,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;;IAEnC;IACA,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAIA,QAAQ,EAAE;MACnE;MACAjC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAACoH,KAAK,EAAE,MAAM,EAAEpH,QAAQ,CAAC4F,KAAK,CAACK,MAAM,CAAC;;MAE3E;MACA,IAAIhE,MAAM,CAACqD,MAAM,IAAItF,QAAQ,CAAC4F,KAAK,CAACK,MAAM,GAAG,CAAC,EAAE;QAC9ClI,OAAO,CAACC,GAAG,CAAC,SAAS,EACnBgC,QAAQ,CAAC4F,KAAK,CAACsC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnH,QAAQ,CAAC,CAACoH,IAAI,CAAC,IAAI,CACrD,CAAC;MACH,CAAC,MAAM,IAAInG,MAAM,CAACqD,MAAM,EAAE;QACxBvH,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACxB;;MAEA;MACA,IAAIgC,QAAQ,CAACoH,KAAK,KAAKU,SAAS,IAAI9H,QAAQ,CAACoH,KAAK,KAAK,IAAI,EAAE;QAC3DrJ,OAAO,CAAC6B,IAAI,CAAC,2BAA2B,CAAC;QACzCI,QAAQ,CAACoH,KAAK,GAAGO,UAAU;MAC7B;MAEA,OAAO3H,QAAQ;IACjB,CAAC,MAAM,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAClC;MACAjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgC,QAAQ,CAACiG,MAAM,CAAC;;MAElD;MACA,IAAIoC,QAAQ,GAAG,EAAE;MACjB,IAAI;QACFtK,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B,MAAMmJ,gBAAgB,GAAG,MAAMzI,GAAG,CAAC+C,GAAG,CAAC,iCAAiC,CAAC;QACzE,IAAIiE,KAAK,CAACC,OAAO,CAACwB,gBAAgB,CAAC,IAAIA,gBAAgB,CAAClB,MAAM,GAAG,CAAC,EAAE;UAClElI,OAAO,CAACC,GAAG,CAAC,YAAYmJ,gBAAgB,CAAClB,MAAM,GAAG,CAAC;UACnDoC,QAAQ,GAAGlB,gBAAgB;QAC7B,CAAC,MAAM;UACLpJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BqK,QAAQ,GAAGrI,QAAQ;QACrB;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCwI,QAAQ,GAAGrI,QAAQ;MACrB;MAEA,IAAIsI,aAAa,GAAGD,QAAQ;;MAE5B;MACA,IAAIpG,MAAM,CAACqD,MAAM,EAAE;QACjB,MAAMiD,UAAU,GAAGtG,MAAM,CAACqD,MAAM,CAACkD,WAAW,CAAC,CAAC;QAC9CzK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuK,UAAU,CAAC;QAErCD,aAAa,GAAGD,QAAQ,CAACI,MAAM,CAACN,IAAI,IAAI;UACtC,MAAMO,aAAa,GAAGP,IAAI,CAACnH,QAAQ,IAAImH,IAAI,CAACnH,QAAQ,CAACwH,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,UAAU,CAAC;UACvF,MAAMK,aAAa,GAAGT,IAAI,CAAC9B,SAAS,IAAI8B,IAAI,CAAC9B,SAAS,CAACmC,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,UAAU,CAAC;UACzF,MAAMM,UAAU,GAAGV,IAAI,CAAC7B,KAAK,IAAI6B,IAAI,CAAC7B,KAAK,CAACkC,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,UAAU,CAAC;UAE9E,IAAIG,aAAa,IAAIE,aAAa,IAAIC,UAAU,EAAE;YAChD9K,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEmK,IAAI,CAACnH,QAAQ,CAAC;YACpC,OAAO,IAAI;UACb;UACA,OAAO,KAAK;QACd,CAAC,CAAC;QAEFjD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsK,aAAa,CAACrC,MAAM,CAAC;QAC/C,IAAIqC,aAAa,CAACrC,MAAM,GAAG,CAAC,EAAE;UAC5BlI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsK,aAAa,CAACJ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnH,QAAQ,CAAC,CAACoH,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9E;MACF;;MAEA;MACArK,OAAO,CAACC,GAAG,CAAC,gBAAgB+J,IAAI,WAAWvC,KAAK,QAAQ8C,aAAa,CAACrC,MAAM,WAAW0B,UAAU,EAAE,CAAC;;MAEpG;MACA,IAAII,IAAI,IAAIO,aAAa,CAACrC,MAAM,EAAE;QAChClI,OAAO,CAAC6B,IAAI,CAAC,WAAWmI,IAAI,cAAcO,aAAa,CAACrC,MAAM,SAAS,CAAC;QACxE,OAAO;UACLmB,KAAK,EAAEO,UAAU;UACjB/B,KAAK,EAAE;QACT,CAAC;MACH;;MAEA;MACA,MAAMkD,cAAc,GAAGR,aAAa,CAACS,KAAK,CAAChB,IAAI,EAAEA,IAAI,GAAGvC,KAAK,CAAC;MAC9DzH,OAAO,CAACC,GAAG,CAAC,WAAW8K,cAAc,CAAC7C,MAAM,SAAS8B,IAAI,MAAMA,IAAI,GAAGe,cAAc,CAAC7C,MAAM,EAAE,CAAC;;MAE9F;MACA,OAAO;QACLmB,KAAK,EAAEO,UAAU;QACjB/B,KAAK,EAAEkD;MACT,CAAC;IACH,CAAC,MAAM;MACL/K,OAAO,CAAC8B,KAAK,CAAC,cAAc,EAAEG,QAAQ,CAAC;MACvC,OAAO;QAAEoH,KAAK,EAAEO,UAAU;QAAE/B,KAAK,EAAE;MAAG,CAAC;IACzC;EACF,CAAC,CAAC,OAAO/F,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMmJ,UAAU,GAAG,MAAOzH,QAAQ,IAAK;EAC5C,IAAI;IACFxD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEuD,QAAQ,CAAC;;IAE9B;IACA,MAAMW,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,cAAc,EAAEE,QAAQ,EAAE;MACxDoB,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMoJ,mBAAmB,GAAIC,YAAY,IAAK;EACnD;EACA,MAAMhH,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX;EACA9B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE6D,IAAI,CAACC,SAAS,CAACoH,YAAY,CAAC,CAAC;;EAExD;EACA,MAAMC,aAAa,GAAGD,YAAY,CAAChB,GAAG,CAACkB,OAAO,KAAK;IACjDpI,QAAQ,EAAEqI,MAAM,CAACD,OAAO,CAACpI,QAAQ,IAAI,EAAE,CAAC;IAAE;IAC1CsF,KAAK,EAAE8C,OAAO,CAAC9C,KAAK,IAAI,EAAE;IAC1BD,SAAS,EAAE+C,OAAO,CAAC/C,SAAS,IAAI,EAAE;IAClCiD,KAAK,EAAEF,OAAO,CAACE,KAAK,IAAI,EAAE;IAC1BrI,QAAQ,EAAEmI,OAAO,CAACnI,QAAQ,IAAI;EAChC,CAAC,CAAC,CAAC;EAEHlD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE6D,IAAI,CAACC,SAAS,CAACqH,aAAa,CAAC,CAAC;EAE3D,OAAOzK,GAAG,CAAC2C,IAAI,CAAC,uBAAuB,EAAE8H,aAAa,EAAE;IACtDxG,MAAM,EAAET,UAAU,CAACS;EACrB,CAAC,CAAC,CACDjB,IAAI,CAAC,MAAM1B,QAAQ,IAAI;IACtBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;;IAErC;IACA,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,IAAIA,QAAQ,CAACiG,MAAM,KAAK,CAAC,EAAE;MACpDlI,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;MAE/B;MACA,MAAMuL,eAAe,GAAG,EAAE;MAC1B,KAAK,MAAMH,OAAO,IAAID,aAAa,EAAE;QACnC,IAAI;UACFpL,OAAO,CAACC,GAAG,CAAC,aAAaoL,OAAO,CAACpI,QAAQ,EAAE,CAAC;UAC5C,MAAMuF,OAAO,GAAG,MAAM7H,GAAG,CAAC2C,IAAI,CAAC,cAAc,EAAE+H,OAAO,CAAC;UACvDrL,OAAO,CAACC,GAAG,CAAC,WAAWoL,OAAO,CAACpI,QAAQ,SAASuF,OAAO,CAACrD,EAAE,EAAE,CAAC;UAC7DqG,eAAe,CAACC,IAAI,CAACjD,OAAO,CAAC;QAC/B,CAAC,CAAC,OAAO1G,KAAK,EAAE;UACd9B,OAAO,CAAC8B,KAAK,CAAC,UAAUuJ,OAAO,CAACpI,QAAQ,MAAM,EAAEnB,KAAK,CAAC;;UAEtD;UACA,IAAI;YACF9B,OAAO,CAACC,GAAG,CAAC,YAAYoL,OAAO,CAACpI,QAAQ,MAAM,CAAC;YAC/C,MAAMyI,KAAK,GAAG,MAAM/K,GAAG,CAAC+C,GAAG,CAAC,cAAc,EAAE;cAC1CQ,MAAM,EAAE;gBACNqD,MAAM,EAAE8D,OAAO,CAACpI,QAAQ;gBACxBuE,IAAI,EAAE,SAAS;gBACfC,KAAK,EAAE;cACT;YACF,CAAC,CAAC;YAEFzH,OAAO,CAACC,GAAG,CAAC,SAASoL,OAAO,CAACpI,QAAQ,OAAO,EAAEyI,KAAK,CAAC;;YAEpD;YACA,IAAIhE,QAAQ,GAAG,EAAE;YACjB,IAAIC,KAAK,CAACC,OAAO,CAAC8D,KAAK,CAAC,EAAE;cACxBhE,QAAQ,GAAGgE,KAAK;YAClB,CAAC,MAAM,IAAIA,KAAK,IAAI/D,KAAK,CAACC,OAAO,CAAC8D,KAAK,CAAC7D,KAAK,CAAC,EAAE;cAC9CH,QAAQ,GAAGgE,KAAK,CAAC7D,KAAK;YACxB;;YAEA;YACA,MAAMC,UAAU,GAAGJ,QAAQ,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/E,QAAQ,KAAKoI,OAAO,CAACpI,QAAQ,CAAC;YACtE,IAAI6E,UAAU,EAAE;cACd9H,OAAO,CAACC,GAAG,CAAC,cAAcoL,OAAO,CAACpI,QAAQ,SAAS6E,UAAU,CAAC3C,EAAE,EAAE,CAAC;cACnEqG,eAAe,CAACC,IAAI,CAAC3D,UAAU,CAAC;YAClC,CAAC,MAAM,IAAIJ,QAAQ,CAACQ,MAAM,GAAG,CAAC,EAAE;cAC9B;cACAlI,OAAO,CAACC,GAAG,CAAC,oBAAoByH,QAAQ,CAAC,CAAC,CAAC,CAACzE,QAAQ,SAASyE,QAAQ,CAAC,CAAC,CAAC,CAACvC,EAAE,EAAE,CAAC;cAC9EqG,eAAe,CAACC,IAAI,CAAC/D,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,MAAM;cACL;cACA1H,OAAO,CAACC,GAAG,CAAC,SAASoL,OAAO,CAACpI,QAAQ,SAAS,CAAC;cAC/CuI,eAAe,CAACC,IAAI,CAAC;gBACnB,GAAGJ,OAAO;gBACVlG,EAAE,EAAE,QAAQkG,OAAO,CAACpI,QAAQ,EAAE;gBAC9B0I,SAAS,EAAE;cACb,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOC,WAAW,EAAE;YACpB5L,OAAO,CAAC8B,KAAK,CAAC,QAAQuJ,OAAO,CAACpI,QAAQ,MAAM,EAAE2I,WAAW,CAAC;YAC1D;YACAJ,eAAe,CAACC,IAAI,CAAC;cACnB,GAAGJ,OAAO;cACVlG,EAAE,EAAE,QAAQkG,OAAO,CAACpI,QAAQ,EAAE;cAC9B0I,SAAS,EAAE;YACb,CAAC,CAAC;UACJ;QACF;MACF;MAEA3L,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuL,eAAe,CAAC;MACzC,OAAOA,eAAe;IACxB;IAEA,OAAOvJ,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IACdmD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAEjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,IAAI9D,KAAK,CAACiH,IAAI,KAAK,cAAc,EAAE;MAChE,MAAM,IAAIlD,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,IAAI,CAAC/D,KAAK,CAACG,QAAQ,EAAE;MACnB;MACA,MAAM,IAAI4D,KAAK,CAAC,gBAAgB,CAAC;IACnC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,IAAI,GAAG,EAAE;MACvC;MACAnC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,OAAOmL,aAAa,CAACjB,GAAG,CAAC,CAACkB,OAAO,EAAEQ,KAAK,MAAM;QAC5C,GAAGR,OAAO;QACVlG,EAAE,EAAE,QAAQkG,OAAO,CAACpI,QAAQ,EAAE;QAC9B0I,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;IACL,CAAC,MAAM,IAAI7J,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,eAAe,CAAC;IAClC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,YAAY,CAAC;IAC/B,CAAC,MAAM;MAAA,IAAAiG,gBAAA,EAAAC,qBAAA;MACL;MACA,MAAMC,YAAY,GAAG,EAAAF,gBAAA,GAAAhK,KAAK,CAACG,QAAQ,cAAA6J,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzJ,IAAI,cAAA0J,qBAAA,uBAApBA,qBAAA,CAAsB/C,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;MAC5E,MAAM,IAAIiD,KAAK,CAAC,aAAamG,YAAY,EAAE,CAAC;IAC9C;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG,MAAO/H,MAAM,IAAK;EAC5C,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiE,MAAM,CAAC;;IAEjC;IACA,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D;IACA,MAAMzD,OAAO,GAAG,CAAC,CAAC;IAClB,IAAIoD,MAAM,IAAIA,MAAM,CAACgI,KAAK,KAAK,KAAK,EAAE;MACpClM,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvBa,OAAO,CAAC,eAAe,CAAC,GAAG,qCAAqC;MAChEA,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU;MAC9BA,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG;MACxB;MACA,OAAOoD,MAAM,CAACgI,KAAK;IACrB;;IAEA;IACA,MAAMC,SAAS,GAAG;MAAE,GAAGjI;IAAO,CAAC;IAC/B,IAAI,CAACiI,SAAS,CAAC9G,EAAE,EAAE;MACjB8G,SAAS,CAAC9G,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACrC;IAEA,MAAMtD,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,WAAW,EAAE;MAC1CQ,MAAM,EAAEiI,SAAS;MACjBvH,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D;IACF,CAAC,CAAC;IAEFmE,YAAY,CAACZ,SAAS,CAAC;IAEvBrE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;;IAEpC;IACA,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAC3B;MACAjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgC,QAAQ,CAACiG,MAAM,EAAE,KAAK,CAAC;;MAEvD;MACA,OAAO;QACLL,KAAK,EAAE5F,QAAQ;QACfoH,KAAK,EAAEpH,QAAQ,CAACiG,MAAM;QACtBkE,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;QACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;MAC1B,CAAC;IACH,CAAC,MAAM,IAAIxF,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MACnD,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC,EAAE;QACjC;QACA7H,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgC,QAAQ,CAAC4F,KAAK,CAACK,MAAM,EAAE,OAAO,EAAEjG,QAAQ,CAACoH,KAAK,EAAE,GAAG,CAAC;QACtF,OAAOpH,QAAQ;MACjB,CAAC,MAAM;QACLjC,OAAO,CAAC6B,IAAI,CAAC,wBAAwB,EAAEI,QAAQ,CAAC;QAChD;QACA,IAAIA,QAAQ,CAACkD,EAAE,EAAE;UACfnF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;UAChC,MAAM4H,KAAK,GAAG,CAAC5F,QAAQ,CAAC;UACxB,OAAO;YACL4F,KAAK,EAAEA,KAAK;YACZwB,KAAK,EAAE,CAAC;YACR+C,IAAI,EAAE,CAAC;YACP3E,KAAK,EAAE;UACT,CAAC;QACH;;QAEA;QACA,OAAO;UACLI,KAAK,EAAE,EAAE;UACTwB,KAAK,EAAE,CAAC;UACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;UACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;QAC1B,CAAC;MACH;IACF,CAAC,MAAM;MACLzH,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEG,QAAQ,CAAC;MACtC,OAAO;QACL4F,KAAK,EAAE,EAAE;QACTwB,KAAK,EAAE,CAAC;QACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;QACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;MAC1B,CAAC;IACH;EACF,CAAC,CAAC,OAAO3F,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAEjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,YAAY,CAAC;MAC3B,MAAM,IAAI+D,KAAK,CAAC,cAAc,CAAC;IACjC;;IAEA;IACA,MAAMwG,QAAQ,GAAG;MACfxE,KAAK,EAAE,EAAE;MACTwB,KAAK,EAAE,CAAC;MACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;MACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI,EAAE;MAC1B3F,KAAK,EAAEA,KAAK,CAACc,OAAO,IAAI;IAC1B,CAAC;IAED5C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoM,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB;AACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAOnH,EAAE,IAAK;EACvC,OAAO,MAAMxE,GAAG,CAAC+C,GAAG,CAAC,aAAayB,EAAE,EAAE,CAAC;AACzC,CAAC;AAED,OAAO,MAAMoH,kBAAkB,GAAG,MAAAA,CAAOpF,SAAS,EAAEqF,YAAY,KAAK;EACnE,IAAI;IACFxM,OAAO,CAACC,GAAG,CAAC,kBAAkBkH,SAAS,aAAaqF,YAAY,EAAE,CAAC;;IAEnE;IACA,MAAMrI,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,mBAAmB,EAAE;MAClDQ,MAAM,EAAE;QAAE2E,UAAU,EAAE1B,SAAS;QAAEsF,aAAa,EAAED;MAAa,CAAC;MAC9D5H,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IAEvBrE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAAC;;IAEtC;IACA,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAC3B;MACAjC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAACiG,MAAM,EAAE,KAAK,CAAC;MAClD,OAAOjG,QAAQ;IACjB,CAAC,MAAM;MACLjC,OAAO,CAAC8B,KAAK,CAAC,eAAe,EAAEG,QAAQ,CAAC;MACxC,OAAO,EAAE;IACX;EACF,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAEnC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,cAAc,CAAC;MAC7B,MAAM,IAAI+D,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM4K,kBAAkB,GAAG,MAAOC,UAAU,IAAK;EACtD,OAAO,MAAMhM,GAAG,CAAC+C,GAAG,CAAC,aAAaiJ,UAAU,mBAAmB,CAAC;AAClE,CAAC;AAED,OAAO,MAAMC,mBAAmB,GAAG,MAAOD,UAAU,IAAK;EACvD,OAAO,MAAMhM,GAAG,CAAC2C,IAAI,CAAC,aAAaqJ,UAAU,uBAAuB,CAAC;AACvE,CAAC;AAED,OAAO,MAAME,eAAe,GAAG,MAAO1H,EAAE,IAAK;EAC3C,OAAO,MAAMxE,GAAG,CAAC2C,IAAI,CAAC,aAAa6B,EAAE,UAAU,CAAC;AAClD,CAAC;AAED,OAAO,MAAM2H,cAAc,GAAI3J,QAAQ,IAAK;EAC1C;EACA,MAAMgB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,UAAU,CAAC;EAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX,OAAOnB,GAAG,CAAC2C,IAAI,CAAC,WAAW,EAAEH,QAAQ,EAAE;IACrCyB,MAAM,EAAET,UAAU,CAACS,MAAM;IACzB9D,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC,CACD6C,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IACnC,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IACdmD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAE/B,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,IAAI9D,KAAK,CAACiH,IAAI,KAAK,cAAc,EAAE;MAChE,MAAM,IAAIlD,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,IAAI,CAAC/D,KAAK,CAACG,QAAQ,EAAE;MACnB;MACA,MAAM,IAAI4D,KAAK,CAAC,gBAAgB,CAAC;IACnC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,IAAI,GAAG,EAAE;MACvC;MACA,MAAM,IAAI0D,KAAK,CAAC,aAAa,CAAC;IAChC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,eAAe,CAAC;IAClC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,YAAY,CAAC;IAC/B,CAAC,MAAM;MAAA,IAAAkH,gBAAA,EAAAC,qBAAA;MACL;MACA,MAAMhB,YAAY,GAAG,EAAAe,gBAAA,GAAAjL,KAAK,CAACG,QAAQ,cAAA8K,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1K,IAAI,cAAA2K,qBAAA,uBAApBA,qBAAA,CAAsBhE,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;MAC5E,MAAM,IAAIiD,KAAK,CAAC,WAAWmG,YAAY,EAAE,CAAC;IAC5C;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMiB,mBAAmB,GAAI9J,QAAQ,IAAK;EAC/C;EACA,MAAMgB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX,OAAOnB,GAAG,CAAC2C,IAAI,CAAC,iBAAiB,EAAEH,QAAQ,EAAE;IAC3CyB,MAAM,EAAET,UAAU,CAACS,MAAM;IACzB9D,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC,CACD6C,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrC,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IACdmD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAEjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,IAAI9D,KAAK,CAACiH,IAAI,KAAK,cAAc,EAAE;MAChE,MAAM,IAAIlD,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,IAAI,CAAC/D,KAAK,CAACG,QAAQ,EAAE;MACnB;MACA,MAAM,IAAI4D,KAAK,CAAC,gBAAgB,CAAC;IACnC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,IAAI,GAAG,EAAE;MACvC;MACA,MAAM,IAAI0D,KAAK,CAAC,aAAa,CAAC;IAChC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,eAAe,CAAC;IAClC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,YAAY,CAAC;IAC/B,CAAC,MAAM;MAAA,IAAAqH,gBAAA,EAAAC,qBAAA;MACL;MACA,MAAMnB,YAAY,GAAG,EAAAkB,gBAAA,GAAApL,KAAK,CAACG,QAAQ,cAAAiL,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7K,IAAI,cAAA8K,qBAAA,uBAApBA,qBAAA,CAAsBnE,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;MAC5E,MAAM,IAAIiD,KAAK,CAAC,aAAamG,YAAY,EAAE,CAAC;IAC9C;EACF,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMoB,cAAc,GAAGA,CAACjI,EAAE,EAAE9C,IAAI,KAAK;EAC1C,OAAO1B,GAAG,CAACgG,GAAG,CAAC,aAAaxB,EAAE,EAAE,EAAE9C,IAAI,CAAC;AACzC,CAAC;AAED,OAAO,MAAMgL,cAAc,GAAIlI,EAAE,IAAK;EACpCnF,OAAO,CAACC,GAAG,CAAC,cAAckF,EAAE,EAAE,CAAC;;EAE/B;EACA,MAAMhB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAE/D,OAAO5D,GAAG,CAACqG,MAAM,CAAC,aAAa7B,EAAE,EAAE,EAAE;IACnCP,MAAM,EAAET,UAAU,CAACS;EACrB,CAAC,CAAC,CACCjB,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,cAAckF,EAAE,EAAE,EAAElD,QAAQ,CAAC;IACzC,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IAAA,IAAAwL,gBAAA,EAAAC,qBAAA;IACdtI,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,cAAcqD,EAAE,GAAG,EAAErD,KAAK,CAAC;IAEzC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;;IAEA;IACA,MAAMmG,YAAY,GAAG,EAAAsB,gBAAA,GAAAxL,KAAK,CAACG,QAAQ,cAAAqL,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjL,IAAI,cAAAkL,qBAAA,uBAApBA,qBAAA,CAAsBvE,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;IAC5E,MAAM,IAAIiD,KAAK,CAAC,WAAWmG,YAAY,EAAE,CAAC;EAC5C,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMwB,wBAAwB,GAAInL,IAAI,IAAK;EAChDrC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6D,IAAI,CAACC,SAAS,CAAC1B,IAAI,CAAC,CAAC;;EAE/C;EACA,IAAI,CAACoL,SAAS,CAACC,MAAM,EAAE;IACrB1N,OAAO,CAAC8B,KAAK,CAAC,SAAS,CAAC;IACxB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAI6D,KAAK,CAAC,mBAAmB,CAAC,CAAC;EACvD;;EAEA;EACA,MAAM1B,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX,OAAOnB,GAAG,CAAC2C,IAAI,CAAC,sBAAsB,EAAEjB,IAAI,EAAE;IAC5CuC,MAAM,EAAET,UAAU,CAACS,MAAM;IACzB9D,OAAO,EAAE;MACP,cAAc,EAAE,kBAAkB;MAClC,kBAAkB,EAAE;IACtB;EACF,CAAC,CAAC,CACC6C,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrC,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IACdmD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IAEjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,IAAI9D,KAAK,CAACiH,IAAI,KAAK,cAAc,EAAE;MAChE,MAAM,IAAIlD,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,IAAI,CAAC/D,KAAK,CAACG,QAAQ,EAAE;MACnB;MACAjC,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAACc,OAAO,EAAE;QACjB,MAAM,IAAIiD,KAAK,CAAC,SAAS/D,KAAK,CAACc,OAAO,EAAE,CAAC;MAC3C,CAAC,MAAM;QACL,MAAM,IAAIiD,KAAK,CAAC,gBAAgB,CAAC;MACnC;IACF,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,IAAI,GAAG,EAAE;MAAA,IAAAwL,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACvC;MACA7N,OAAO,CAAC8B,KAAK,CAAC,aAAa,GAAA6L,gBAAA,GAAE7L,KAAK,CAACG,QAAQ,cAAA0L,gBAAA,uBAAdA,gBAAA,CAAgBtL,IAAI,CAAC;MAClD,MAAMyL,WAAW,GAAG,EAAAF,gBAAA,GAAA9L,KAAK,CAACG,QAAQ,cAAA2L,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvL,IAAI,cAAAwL,qBAAA,uBAApBA,qBAAA,CAAsB7E,MAAM,KAAI,SAAS;MAC7D,MAAM,IAAInD,KAAK,CAAC,UAAUiI,WAAW,EAAE,CAAC;IAC1C,CAAC,MAAM,IAAIhM,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,eAAe,CAAC;IAClC,CAAC,MAAM,IAAI/D,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACxC;MACA,MAAM,IAAI0D,KAAK,CAAC,YAAY,CAAC;IAC/B,CAAC,MAAM;MAAA,IAAAkI,gBAAA,EAAAC,qBAAA;MACL;MACA,MAAMhC,YAAY,GAAG,EAAA+B,gBAAA,GAAAjM,KAAK,CAACG,QAAQ,cAAA8L,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1L,IAAI,cAAA2L,qBAAA,uBAApBA,qBAAA,CAAsBhF,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;MAC5E,MAAM,IAAIiD,KAAK,CAAC,aAAamG,YAAY,EAAE,CAAC;IAC9C;EACF,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMiC,wBAAwB,GAAI9I,EAAE,IAAK;EAC9C,OAAOxE,GAAG,CAACqG,MAAM,CAAC,wBAAwB7B,EAAE,EAAE,CAAC;AACjD,CAAC;AAED,OAAO,MAAM+I,8BAA8B,GAAGA,CAAC/I,EAAE,EAAEhD,MAAM,KAAK;EAC5DnC,OAAO,CAACC,GAAG,CAAC,gBAAgBkF,EAAE,SAAShD,MAAM,EAAE,CAAC;;EAEhD;EACA,MAAMgC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAE/D,OAAO5D,GAAG,CAACwN,KAAK,CAAC,wBAAwBhJ,EAAE,SAAS,EAAE;IAAEhD;EAAO,CAAC,EAAE;IAChEyC,MAAM,EAAET,UAAU,CAACS;EACrB,CAAC,CAAC,CACCjB,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,kBAAkBkF,EAAE,EAAE,EAAElD,QAAQ,CAAC;IAC7C,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IAAA,IAAAsM,iBAAA,EAAAC,qBAAA;IACdpJ,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,kBAAkBqD,EAAE,GAAG,EAAErD,KAAK,CAAC;IAE7C,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;;IAEA;IACA,MAAMmG,YAAY,GAAG,EAAAoC,iBAAA,GAAAtM,KAAK,CAACG,QAAQ,cAAAmM,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB/L,IAAI,cAAAgM,qBAAA,uBAApBA,qBAAA,CAAsBrF,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;IAC5E,MAAM,IAAIiD,KAAK,CAAC,eAAemG,YAAY,EAAE,CAAC;EAChD,CAAC,CAAC;AACN,CAAC;AAED,OAAO,MAAMsC,qBAAqB,GAAInJ,EAAE,IAAK;EAC3C,OAAOxE,GAAG,CAAC+C,GAAG,CAAC,wBAAwByB,EAAE,EAAE,CAAC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMoJ,6BAA6B,GAAG,MAAAA,CAAA,KAAY;EACvD,IAAI;IACFvO,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;IAEzB;IACA,MAAMkE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,+BAA+B,EAAE;MAC9DkB,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAAC;;IAEtC;IACA,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAC3B;MACA,MAAMuM,UAAU,GAAIC,IAAI,IAAK;QAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;QACtB;QACA,MAAMC,WAAW,GAAG/O,MAAM,CAAC8O,IAAI,CAAC,CAACE,SAAS,CAAC,GAAG,CAAC;QAC/C,OAAOD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAGF,WAAW,CAACG,MAAM,CAAC,qBAAqB,CAAC,GAAG,IAAI;MACjF,CAAC;;MAED;MACA,MAAMC,iBAAiB,GAAG7M,QAAQ,CAACkI,GAAG,CAAC4E,QAAQ,KAAK;QAClD,GAAGA,QAAQ;QACX;QACAC,WAAW,EAAER,UAAU,CAACO,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACC,WAAW,IAAID,QAAQ,CAAC3I,UAAU,CAAC;QAC7F;QACAA,UAAU,EAAEoI,UAAU,CAACO,QAAQ,CAAC3I,UAAU,CAAC;QAC3C8I,UAAU,EAAEV,UAAU,CAACO,QAAQ,CAACG,UAAU,CAAC;QAC3CC,QAAQ,EAAEX,UAAU,CAACO,QAAQ,CAACI,QAAQ;MACxC,CAAC,CAAC,CAAC;MAEH,OAAOL,iBAAiB;IAC1B,CAAC,MAAM;MACL9O,OAAO,CAAC8B,KAAK,CAAC,iBAAiB,EAAEG,QAAQ,CAAC;MAC1C,OAAO,EAAE;IACX;EAEF,CAAC,CAAC,OAAOH,KAAK,EAAE;IAAA,IAAAsN,iBAAA,EAAAC,qBAAA;IACdrP,OAAO,CAAC8B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IAErC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,gBAAgB,CAAC;MAC/B,MAAM,IAAI+D,KAAK,CAAC,cAAc,CAAC;IACjC;;IAEA;IACA,MAAM,IAAIA,KAAK,CAAC,EAAAuJ,iBAAA,GAAAtN,KAAK,CAACG,QAAQ,cAAAmN,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB/M,IAAI,cAAAgN,qBAAA,uBAApBA,qBAAA,CAAsBrG,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,YAAY,CAAC;EAChF;AACF,CAAC;AAED,OAAO,MAAM0M,sBAAsB,GAAG,MAAOpL,MAAM,IAAK;EACtD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiE,MAAM,CAAC;;IAEnC;IACA,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,IAAIgL,OAAO,GAAG,CAAC;IACf,MAAM/J,UAAU,GAAG,CAAC;IACpB,IAAIvD,QAAQ;;IAEZ;IACA,MAAMuN,SAAS,GAAG;MAChB,GAAGtL,MAAM;MACTuL,uBAAuB,EAAE;IAC3B,CAAC;IAED,OAAOF,OAAO,IAAI/J,UAAU,EAAE;MAC5B,IAAI;QACFvD,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,sBAAsB,EAAE;UAC/CQ,MAAM,EAAEsL,SAAS;UACjB5K,MAAM,EAAET,UAAU,CAACS;QACrB,CAAC,CAAC;QACF,MAAM,CAAC;MACT,CAAC,CAAC,OAAO8K,UAAU,EAAE;QACnBH,OAAO,EAAE;QACT,IAAIA,OAAO,GAAG/J,UAAU,EAAE;UACxB,MAAMkK,UAAU,CAAC,CAAC;QACpB;QACA1P,OAAO,CAACC,GAAG,CAAC,mBAAmBsP,OAAO,QAAQ,CAAC;QAC/C,MAAM,IAAIxN,OAAO,CAAC4N,OAAO,IAAIrL,UAAU,CAACqL,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;MAC3D;IACF;IAEA1K,YAAY,CAACZ,SAAS,CAAC;IAEvBrE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAAC;;IAEtC;IACA,MAAMuM,UAAU,GAAIC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;MACtB;MACA,MAAMC,WAAW,GAAG/O,MAAM,CAAC8O,IAAI,CAAC,CAACE,SAAS,CAAC,QAAQ,CAAC;MACpD,OAAOD,WAAW,CAACE,OAAO,CAAC,CAAC,GAAGF,WAAW,CAACG,MAAM,CAAC,qBAAqB,CAAC,GAAG,IAAI;IACjF,CAAC;;IAED;IACA,MAAMe,oBAAoB,GAAIb,QAAQ,KAAM;MAC1C,GAAGA,QAAQ;MACX;MACAC,WAAW,EAAER,UAAU,CAACO,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACC,WAAW,IAAID,QAAQ,CAAC3I,UAAU,CAAC;MAC7F;MACAA,UAAU,EAAEoI,UAAU,CAACO,QAAQ,CAAC3I,UAAU,CAAC;MAC3C8I,UAAU,EAAEV,UAAU,CAACO,QAAQ,CAACG,UAAU,CAAC;MAC3CC,QAAQ,EAAEX,UAAU,CAACO,QAAQ,CAACI,QAAQ;IACxC,CAAC,CAAC;;IAEF;IACA,IAAI,CAAClN,QAAQ,EAAE;MACbjC,OAAO,CAAC8B,KAAK,CAAC,YAAY,CAAC;MAC3B,OAAO;QACL+F,KAAK,EAAE,EAAE;QACTwB,KAAK,EAAE,CAAC;QACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;QACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;MAC1B,CAAC;IACH;;IAEA;IACA,IAAIE,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAC3B;MACAjC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgC,QAAQ,CAACiG,MAAM,EAAE,KAAK,CAAC;;MAEzD;MACA,MAAM2H,cAAc,GAAG5N,QAAQ,CAACkI,GAAG,CAACyF,oBAAoB,CAAC;MAEzD,OAAO;QACL/H,KAAK,EAAEgI,cAAc;QACrBxG,KAAK,EAAEwG,cAAc,CAAC3H,MAAM;QAC5BkE,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;QACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;MAC1B,CAAC;IACH,CAAC,MAAM,IAAIxF,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MACnD,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC,EAAE;QACjC;QACA7H,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgC,QAAQ,CAAC4F,KAAK,CAACK,MAAM,EAAE,OAAO,EAAEjG,QAAQ,CAACoH,KAAK,EAAE,GAAG,CAAC;;QAExF;QACA,OAAO;UACL,GAAGpH,QAAQ;UACX4F,KAAK,EAAE5F,QAAQ,CAAC4F,KAAK,CAACsC,GAAG,CAACyF,oBAAoB;QAChD,CAAC;MACH,CAAC,MAAM;QACL5P,OAAO,CAAC6B,IAAI,CAAC,0BAA0B,EAAEI,QAAQ,CAAC;QAClD;QACA,IAAIA,QAAQ,CAACkD,EAAE,EAAE;UACfnF,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAClC,MAAM6P,aAAa,GAAGF,oBAAoB,CAAC3N,QAAQ,CAAC;UACpD,OAAO;YACL4F,KAAK,EAAE,CAACiI,aAAa,CAAC;YACtBzG,KAAK,EAAE,CAAC;YACR+C,IAAI,EAAE,CAAC;YACP3E,KAAK,EAAE;UACT,CAAC;QACH;;QAEA;QACA,OAAO;UACLI,KAAK,EAAE,EAAE;UACTwB,KAAK,EAAE,CAAC;UACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;UACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;QAC1B,CAAC;MACH;IACF,CAAC,MAAM;MACLzH,OAAO,CAAC8B,KAAK,CAAC,eAAe,EAAEG,QAAQ,CAAC;MACxC,OAAO;QACL4F,KAAK,EAAE,EAAE;QACTwB,KAAK,EAAE,CAAC;QACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;QACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI;MAC1B,CAAC;IACH;EACF,CAAC,CAAC,OAAO3F,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IAEnC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,cAAc,CAAC;MAC7B,OAAO;QACL+F,KAAK,EAAE,EAAE;QACTwB,KAAK,EAAE,CAAC;QACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;QACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI,EAAE;QAC1B3F,KAAK,EAAE;MACT,CAAC;IACH;;IAEA;IACA,OAAO;MACL+F,KAAK,EAAE,EAAE;MACTwB,KAAK,EAAE,CAAC;MACR+C,IAAI,EAAE,CAAAlI,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEkI,IAAI,KAAI,CAAC;MACvB3E,KAAK,EAAE,CAAAvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuD,KAAK,KAAI,EAAE;MAC1B3F,KAAK,EAAEA,KAAK,CAACkH,MAAM,IAAIlH,KAAK,CAACc,OAAO,IAAI;IAC1C,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMmN,iBAAiB,GAAG,MAAO7L,MAAM,IAAK;EACjD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiE,MAAM,CAAC;IACjC,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kBAAkB,EAAE;MAAEQ;IAAO,CAAC,CAAC;IAC9DlE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IACnC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC;IACA,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMkO,gBAAgB,GAAG,MAAO7K,EAAE,IAAK;EAC5C,IAAI;IACFnF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkF,EAAE,CAAC;IAC7B,MAAMlD,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,oBAAoByB,EAAE,EAAE,CAAC;IACxDnF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IACnC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMmO,yBAAyB,GAAG,MAAO/L,MAAM,IAAK;EACzD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiE,MAAM,CAAC;IACjC,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,0BAA0B,EAAE;MAAEQ;IAAO,CAAC,CAAC;IACtElE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IACnC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC;IACA,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMoO,sBAAsB,GAAG,MAAOhM,MAAM,IAAK;EACtD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiE,MAAM,CAAC;IACpC,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,iCAAiC,EAAE;MAAEQ;IAAO,CAAC,CAAC;IAC7ElE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMqO,wBAAwB,GAAG,MAAOjM,MAAM,IAAK;EACxD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiE,MAAM,CAAC;IACnC,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,mCAAmC,EAAE;MAAEQ;IAAO,CAAC,CAAC;IAC/ElE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMsO,2BAA2B,GAAGA,CAACjL,EAAE,EAAEkL,WAAW,KAAK;EAC9D,OAAO1P,GAAG,CAACgG,GAAG,CAAC,4BAA4BxB,EAAE,iBAAiBkL,WAAW,EAAE,CAAC;AAC9E,CAAC;;AAED;AACA,OAAO,MAAMC,2BAA2B,GAAIC,UAAU,IAAK;EACzD,OAAO5P,GAAG,CAACqG,MAAM,CAAC,4BAA4BuJ,UAAU,EAAE,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAACC,eAAe,EAAEC,KAAK,GAAG,CAAC,KAAK;EAC/D;EACA,MAAMvM,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IACjCH,UAAU,CAACI,KAAK,CAAC,CAAC;IAClBvE,OAAO,CAAC8B,KAAK,CAAC,UAAU,CAAC;EAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;EAEX,OAAOnB,GAAG,CAAC2C,IAAI,CAAC,wBAAwB,EAAE;IACxCqN,iBAAiB,EAAEF,eAAe;IAClCC;EACF,CAAC,EAAE;IACD9L,MAAM,EAAET,UAAU,CAACS;EACrB,CAAC,CAAC,CACDjB,IAAI,CAAC1B,QAAQ,IAAI;IAChBgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACD+B,KAAK,CAAClC,KAAK,IAAI;IACdmD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAE/B,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,IAAI9D,KAAK,CAACiH,IAAI,KAAK,cAAc,EAAE;MAChE,MAAM,IAAIlD,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,MAAM/D,KAAK;EACb,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAM8O,sBAAsB,GAAGA,CAACL,UAAU,EAAEM,aAAa,KAAK;EACnE,OAAOlQ,GAAG,CAAC2C,IAAI,CAAC,qBAAqB,EAAE;IACrCwN,WAAW,EAAEP,UAAU;IACvBQ,cAAc,EAAEF;EAClB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMG,wBAAwB,GAAIT,UAAU,IAAK;EACtD,OAAO5P,GAAG,CAAC+C,GAAG,CAAC,+BAA+B6M,UAAU,EAAE,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMU,uBAAuB,GAAGA,CAACxJ,KAAK,GAAG,EAAE,EAAEyJ,MAAM,GAAG,CAAC,KAAK;EACjE,OAAOvQ,GAAG,CAAC+C,GAAG,CAAC,4BAA4B,EAAE;IAC3CQ,MAAM,EAAE;MAAEuD,KAAK;MAAEyJ;IAAO;EAC1B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG,CACnB;EAAEhM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,UAAU;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,UAAU;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,YAAY;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACnF;EAAEpM,EAAE,EAAE,CAAC;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,UAAU;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAClF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,MAAM;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC9E;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,UAAU;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAClF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAO,CAAC,EAClF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAChF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACjF;EAAEpM,EAAE,EAAE,EAAE;EAAEiM,WAAW,EAAE,SAAS;EAAEC,QAAQ,EAAE,KAAK;EAAEC,IAAI,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,CAClF;;AAED;AACA;AACA,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAOtN,MAAM,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiE,MAAM,CAAC;;IAE5C;IACA,IAAIA,MAAM,CAACmN,QAAQ,IAAInN,MAAM,CAACoN,IAAI,IAAIpN,MAAM,CAACqN,QAAQ,EAAE;MACrD,IAAI;QACJ,MAAM/M,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;QACzC,IAAIc,MAAM,CAACmN,QAAQ,EAAE7M,WAAW,CAACnB,MAAM,CAAC,UAAU,EAAEa,MAAM,CAACmN,QAAQ,CAAC;QACpE,IAAInN,MAAM,CAACoN,IAAI,EAAE9M,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAACoN,IAAI,CAAC;QACxD,IAAIpN,MAAM,CAACqN,QAAQ,EAAE/M,WAAW,CAACnB,MAAM,CAAC,UAAU,EAAEa,MAAM,CAACqN,QAAQ,CAAC;QAEpE,MAAMtP,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,mBAAmBc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;QACzElK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;QAClC,OAAOA,QAAQ;MACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C;QACA,OAAOqP,YAAY,CAACzG,MAAM,CAAC+G,MAAM,IAAI;UACnC,IAAIC,KAAK,GAAG,IAAI;UAChB,IAAIxN,MAAM,CAACmN,QAAQ,EAAEK,KAAK,GAAGA,KAAK,IAAID,MAAM,CAACJ,QAAQ,KAAKnN,MAAM,CAACmN,QAAQ;UACzE,IAAInN,MAAM,CAACoN,IAAI,EAAEI,KAAK,GAAGA,KAAK,IAAID,MAAM,CAACH,IAAI,KAAKpN,MAAM,CAACoN,IAAI;UAC7D,IAAIpN,MAAM,CAACqN,QAAQ,EAAEG,KAAK,GAAGA,KAAK,IAAID,MAAM,CAACF,QAAQ,KAAKrN,MAAM,CAACqN,QAAQ;UACzE,OAAOG,KAAK;QACd,CAAC,CAAC;MACJ;IACF;IACA;IAAA,KACK;MACH;MACA,IAAI;QACF;QACA,MAAMtH,IAAI,GAAGtG,IAAI,CAAC6N,KAAK,CAAC7R,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;QAC7D,MAAM6R,OAAO,GAAGxH,IAAI,IAAIA,IAAI,CAACyH,QAAQ;QAErC7R,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE2R,OAAO,CAAC;;QAErC;QACA,MAAM3P,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,gBAAgB,CAAC;QAChD1D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgC,QAAQ,CAAC;QACzC,OAAOA,QAAQ;MACjB,CAAC,CAAC,OAAO6P,UAAU,EAAE;QACnB9R,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6R,UAAU,CAAC;QACpD,IAAI;UACJ;UACA,MAAM7P,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,WAAW,CAAC;UAC3C1D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgC,QAAQ,CAAC;;UAExC;UACA,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;YAC3B;YACA,OAAOA,QAAQ,CAACkI,GAAG,CAACsH,MAAM,KAAK;cAC7B,GAAGA,MAAM;cACTJ,QAAQ,EAAEI,MAAM,CAACJ,QAAQ,IAAI,EAAE;cAC/BC,IAAI,EAAEG,MAAM,CAACH,IAAI,IAAI,EAAE;cACvBC,QAAQ,EAAEE,MAAM,CAACF,QAAQ,IAAI;YAC/B,CAAC,CAAC,CAAC;UACL,CAAC,MAAM,IAAItP,QAAQ,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC,EAAE;YACpD;YACA,OAAO;cACL,GAAG5F,QAAQ;cACX4F,KAAK,EAAE5F,QAAQ,CAAC4F,KAAK,CAACsC,GAAG,CAACsH,MAAM,KAAK;gBACnC,GAAGA,MAAM;gBACTJ,QAAQ,EAAEI,MAAM,CAACJ,QAAQ,IAAI,EAAE;gBAC/BC,IAAI,EAAEG,MAAM,CAACH,IAAI,IAAI,EAAE;gBACvBC,QAAQ,EAAEE,MAAM,CAACF,QAAQ,IAAI;cAC/B,CAAC,CAAC;YACJ,CAAC;UACH,CAAC,MAAM;YACL;YACA,OAAOtP,QAAQ;UACf;QACF,CAAC,CAAC,OAAOH,KAAK,EAAE;UACd9B,OAAO,CAAC8B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,OAAO,EAAE;QACX;MACF;IACF;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMiQ,gBAAgB,GAAG,MAAAA,CAAO7N,MAAM,GAAG,CAAC,CAAC,KAAK;EACrD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEiE,MAAM,CAAC;IAElD,MAAMM,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAACO,SAAS,EAAED,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAACO,SAAS,CAAC;IACvE,IAAIP,MAAM,CAACS,KAAK,EAAEH,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEa,MAAM,CAACS,KAAK,CAAC;IAE3D,IAAI;MACJ,MAAM1C,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,2BAA2Bc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;MACjFlK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;MAClC,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAO+P,QAAQ,EAAE;MACjBhS,OAAO,CAAC8B,KAAK,CAAC,gBAAgB,EAAEkQ,QAAQ,CAAC;MACzC;MACA,OAAO,EAAE;IACX;EACF,CAAC,CAAC,OAAOlQ,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMmQ,iBAAiB,GAAG,MAAAA,CAAOC,QAAQ,GAAG,IAAI,KAAK;EAC1D,IAAI;IACFlS,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiS,QAAQ,CAAC;IAE3D,IAAI;MACF,MAAMhO,MAAM,GAAGgO,QAAQ,GAAG;QAAEzN,SAAS,EAAEyN;MAAS,CAAC,GAAG,CAAC,CAAC;MACtD,MAAMjQ,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kBAAkB,EAAE;QAAEQ;MAAO,CAAC,CAAC;MAC9DlE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;MAClC,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAO+P,QAAQ,EAAE;MACjBhS,OAAO,CAAC8B,KAAK,CAAC,gBAAgB,EAAEkQ,QAAQ,CAAC;MACzC;MACA,OAAO,CACL;QAAE7M,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,EACrB;QAAET,EAAE,EAAE,CAAC;QAAES,IAAI,EAAE;MAAK,CAAC,CACtB;IACH;EACF,CAAC,CAAC,OAAO9D,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,MAAMqQ,WAAW,GAAG;EAClBC,SAAS,EAAE,CACT,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EACpC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAClC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACjC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACrC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EACvC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CACxC;EACDC,MAAM,EAAE;IACN,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;IACzK,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,CAAC;IACd,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1J,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAC1G,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpF,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACvF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACvH,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IACxI,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnG,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxH,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACrF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;IACvI,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC;IACjH,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC9G,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC;IACzK,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,CAAC;IAC7F,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,CAAC;IAClK,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAC5D,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC7E,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC;IAClH,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC;IAC1F,SAAS,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC/C,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IAC7O,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;IAC3G,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC;IAC3E,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;IAC7G,SAAS,EAAE,CAAC,SAAS,CAAC;IACtB,SAAS,EAAE,CAAC,SAAS,CAAC;IACtB,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EAClK,CAAC;EACDC,SAAS,EAAE;IACT;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IACjI,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;IACjF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACjD,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE3E;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;IAC7S,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACzH,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxH,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAExH;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC7E,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5B,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxD,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE1C;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpG,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IAClF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC;IAC5D,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC;IAEhE;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAChF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC7E,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;IAEtC;IACA,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAC3J,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1G,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC;IAE/D;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC3F,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC7E,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE/D;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC7F,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE1G;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACrF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxD,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE3E;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClG,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC7E,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE3F;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxD,KAAK,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAEzD;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClG,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACjD,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE1C;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACzE,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE1C;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnG,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnD,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC;IAE/D;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACtE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACrE,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAE1C;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;IAC3F,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC;IACrF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC;IAEtI;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACnC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAEnC;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC9E,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IAErC;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,CAAC;IACzH,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAEtE;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;IACnE,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IAE1I;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClG,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAEnC;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE,MAAM,EAAE,CAAC,KAAK,CAAC;IAEf;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC;IAC9D,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;IAEpE;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACjD,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IAE9B;IACA,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;IACxE,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IAE1C;IACA,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC;IAC9E,KAAK,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC;IAElF;IACA,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IACrF,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAEtE;IACA,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACrI,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAElI;IACA,SAAS,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1I,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;IAC9E,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EAC5F;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAOrO,MAAM,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IACF;IACA,IAAI,CAACA,MAAM,CAACmN,QAAQ,EAAE;MACpBrR,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B,IAAI;QACF,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,2BAA2B,CAAC;QAC3D,OAAO;UAAE0O,SAAS,EAAEnQ,QAAQ,CAACmQ;QAAU,CAAC;MAC1C,CAAC,CAAC,OAAOtQ,KAAK,EAAE;QACd9B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/B,OAAO;UAAEmS,SAAS,EAAED,WAAW,CAACC;QAAU,CAAC;MAC7C;IACF;;IAEA;IACA,IAAIlO,MAAM,CAACmN,QAAQ,IAAI,CAACnN,MAAM,CAACoN,IAAI,EAAE;MACnCtR,OAAO,CAACC,GAAG,CAAC,gBAAgBiE,MAAM,CAACmN,QAAQ,EAAE,CAAC;MAC9C,IAAI;QACF,MAAMpP,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,mCAAmCQ,MAAM,CAACmN,QAAQ,EAAE,CAAC;QACpF,OAAO;UAAEgB,MAAM,EAAEpQ,QAAQ,CAACoQ;QAAO,CAAC;MACpC,CAAC,CAAC,OAAOvQ,KAAK,EAAE;QACd9B,OAAO,CAACC,GAAG,CAAC,qBAAqBiE,MAAM,CAACmN,QAAQ,EAAE,CAAC;QACnD,MAAMgB,MAAM,GAAGF,WAAW,CAACE,MAAM,CAACnO,MAAM,CAACmN,QAAQ,CAAC,IAAI,EAAE;QACxD,OAAO;UAAEgB;QAAO,CAAC;MACnB;IACF;;IAEA;IACA,IAAInO,MAAM,CAACmN,QAAQ,IAAInN,MAAM,CAACoN,IAAI,EAAE;MAClCtR,OAAO,CAACC,GAAG,CAAC,gBAAgBiE,MAAM,CAACoN,IAAI,EAAE,CAAC;MAC1C,IAAI;QACF,MAAMrP,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kCAAkCQ,MAAM,CAACoN,IAAI,EAAE,CAAC;QAC/E,OAAO;UAAEgB,SAAS,EAAErQ,QAAQ,CAACqQ;QAAU,CAAC;MAC1C,CAAC,CAAC,OAAOxQ,KAAK,EAAE;QACd9B,OAAO,CAACC,GAAG,CAAC,qBAAqBiE,MAAM,CAACoN,IAAI,EAAE,CAAC;QAC/C,MAAMgB,SAAS,GAAGH,WAAW,CAACG,SAAS,CAACpO,MAAM,CAACoN,IAAI,CAAC,IAAI,EAAE;QAC1D,OAAO;UAAEgB;QAAU,CAAC;MACtB;IACF;;IAEA;IACA,OAAO,CAAC,CAAC;EACX,CAAC,CAAC,OAAOxQ,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,CAAC,CAAC;EACX;AACF,CAAC;AAED,OAAO,MAAM0Q,SAAS,GAAG,MAAOrN,EAAE,IAAK;EACrC,IAAI;IACF,MAAMlD,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,YAAYyB,EAAE,EAAE,CAAC;IAChD,OAAOlD,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,iBAAiBqD,EAAE,GAAG,EAAErD,KAAK,CAAC;IAC5C,MAAM,IAAI+D,KAAK,CAAC,aAAa/D,KAAK,CAACc,OAAO,IAAI,MAAM,EAAE,CAAC;EACzD;AACF,CAAC;AAED,OAAO,MAAM6P,YAAY,GAAG,MAAOC,UAAU,IAAK;EAChD,IAAI;IACF,MAAMzQ,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,WAAW,EAAEoP,UAAU,CAAC;IACxD,OAAOzQ,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,MAAM,IAAI+D,KAAK,CAAC,WAAW/D,KAAK,CAACc,OAAO,IAAI,MAAM,EAAE,CAAC;EACvD;AACF,CAAC;AAED,OAAO,MAAM+P,YAAY,GAAG,MAAAA,CAAOxN,EAAE,EAAEuN,UAAU,KAAK;EACpD,IAAI;IACF,MAAMzQ,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,YAAYxB,EAAE,EAAE,EAAEuN,UAAU,CAAC;IAC5D,OAAOzQ,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,eAAeqD,EAAE,GAAG,EAAErD,KAAK,CAAC;IAC1C,MAAM,IAAI+D,KAAK,CAAC,WAAW/D,KAAK,CAACc,OAAO,IAAI,MAAM,EAAE,CAAC;EACvD;AACF,CAAC;AAED,OAAO,MAAMgQ,YAAY,GAAG,MAAOzN,EAAE,IAAK;EACxC,IAAI;IACFnF,OAAO,CAACC,GAAG,CAAC,cAAckF,EAAE,EAAE,CAAC;IAC/B,MAAMlD,QAAQ,GAAG,MAAMtB,GAAG,CAACqG,MAAM,CAAC,kBAAkB7B,EAAE,EAAE,CAAC;IACzDnF,OAAO,CAACC,GAAG,CAAC,cAAckF,EAAE,EAAE,CAAC;IAC/B,OAAOlD,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,eAAeqD,EAAE,GAAG,EAAErD,KAAK,CAAC;IAC1C,MAAM,IAAI+D,KAAK,CAAC,WAAW/D,KAAK,CAACc,OAAO,IAAI,MAAM,EAAE,CAAC;EACvD;AACF,CAAC;;AAED;AACA,OAAO,MAAMiQ,mBAAmB,GAAG,MAAOrG,YAAY,IAAK;EACzD,IAAI;IACFxM,OAAO,CAACC,GAAG,CAAC,oBAAoBuM,YAAY,EAAE,CAAC;;IAE/C;IACA,MAAMrI,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D;IACA,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,WAAW,EAAE;MAC1CQ,MAAM,EAAE;QAAEuI,aAAa,EAAED;MAAa,CAAC;MACvC5H,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;;IAEpC;IACA,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAC3B,OAAO;QACL4F,KAAK,EAAE5F,QAAQ;QACfoH,KAAK,EAAEpH,QAAQ,CAACiG;MAClB,CAAC;IACH,CAAC,MAAM,IAAIjG,QAAQ,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC,EAAE;MACpD,OAAO5F,QAAQ;IACjB,CAAC,MAAM;MACLjC,OAAO,CAAC6B,IAAI,CAAC,eAAe,EAAEI,QAAQ,CAAC;MACvC,OAAO;QACL4F,KAAK,EAAE,EAAE;QACTwB,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC,CAAC,OAAOvH,KAAK,EAAE;IAAA,IAAAgR,iBAAA,EAAAC,qBAAA;IACd/S,OAAO,CAAC8B,KAAK,CAAC,sBAAsB0K,YAAY,GAAG,EAAE1K,KAAK,CAAC;IAE3D,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,MAAMmG,YAAY,GAAG,EAAA8G,iBAAA,GAAAhR,KAAK,CAACG,QAAQ,cAAA6Q,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzQ,IAAI,cAAA0Q,qBAAA,uBAApBA,qBAAA,CAAsB/J,MAAM,KAAIlH,KAAK,CAACc,OAAO,IAAI,MAAM;IAC5E,MAAM,IAAIiD,KAAK,CAAC,eAAemG,YAAY,EAAE,CAAC;EAChD;AACF,CAAC;;AAED;AACA,OAAO,MAAMgH,oBAAoB,GAAG,MAAAA,CAAO9O,MAAM,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiE,MAAM,CAAC;;IAEnC;IACA,IAAI+O,WAAW,GAAG,qBAAqB;;IAEvC;IACA,IAAK/O,MAAM,CAACgP,UAAU,IAAIhP,MAAM,CAACiP,QAAQ,IAAKjP,MAAM,CAACkP,QAAQ,IAAIlP,MAAM,CAACuI,aAAa,EAAE;MACrFzM,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEiE,MAAM,CAAC;MAC9D+O,WAAW,GAAG,oBAAoB;IACpC;IAEA,MAAMhR,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAACuP,WAAW,EAAE;MAAE/O;IAAO,CAAC,CAAC;IAEvDlE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;;IAEpC;IACA,IAAIgR,WAAW,KAAK,oBAAoB,EAAE;MACxC;MACAjT,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAEhC,MAAMoT,UAAU,GAAGpR,QAAQ,CAACI,IAAI,IAAI,EAAE;;MAEtC;MACA,IAAIiR,SAAS,GAAG,EAAE;MAClB,IAAID,UAAU,CAACnL,MAAM,GAAG,CAAC,IAAImL,UAAU,CAAC,CAAC,CAAC,CAACE,UAAU,EAAE;QACrDD,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC,CAACE,UAAU;MACtC;;MAEA;MACA,MAAMC,cAAc,GAAGH,UAAU,CAACnL,MAAM;MACxC,MAAMuL,eAAe,GAAGJ,UAAU,CAAC3I,MAAM,CAACgJ,EAAE,IAAIA,EAAE,CAACvR,MAAM,KAAK,QAAQ,CAAC,CAAC+F,MAAM;;MAE9E;MACA,IAAIyL,aAAa,GAAGN,UAAU,CAAC3I,MAAM,CAACgJ,EAAE,IAAIA,EAAE,CAACvR,MAAM,KAAK,QAAQ,IAAIuR,EAAE,CAACE,QAAQ,IAAI,IAAI,CAAC,CAC9DzJ,GAAG,CAACuJ,EAAE,IAAIG,IAAI,CAACC,KAAK,CAACJ,EAAE,CAACE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;MAErE,MAAMG,mBAAmB,GAAGJ,aAAa,CAACzL,MAAM,GAAG,CAAC,GAAG2L,IAAI,CAACG,GAAG,CAAC,GAAGL,aAAa,CAAC,GAAG,CAAC;MACrF,MAAMM,kBAAkB,GAAGN,aAAa,CAACzL,MAAM,GAAG,CAAC,GAAG2L,IAAI,CAACK,GAAG,CAAC,GAAGP,aAAa,CAAC,GAAG,CAAC;MACpF,MAAMQ,mBAAmB,GAAGR,aAAa,CAACzL,MAAM,GAAG,CAAC,GAChDyL,aAAa,CAACS,MAAM,CAAC,CAACC,GAAG,EAAE3D,KAAK,KAAK2D,GAAG,GAAG3D,KAAK,EAAE,CAAC,CAAC,GAAGiD,aAAa,CAACzL,MAAM,GAC3E,CAAC;;MAEL;MACAjG,QAAQ,CAACqS,WAAW,GAAG,CAAC;MACxBrS,QAAQ,CAACsS,cAAc,GAAGf,cAAc;MACxCvR,QAAQ,CAACuS,qBAAqB,GAAGf,eAAe;MAChDxR,QAAQ,CAACwS,qBAAqB,GAAGV,mBAAmB;MACpD9R,QAAQ,CAACyS,qBAAqB,GAAGP,mBAAmB;MACpDlS,QAAQ,CAAC0S,oBAAoB,GAAGV,kBAAkB;;MAElD;MACA,IAAI/P,MAAM,CAACkP,QAAQ,EAAE;QACnB,MAAMwB,YAAY,GAAG;UACnBxB,QAAQ,EAAEnJ,QAAQ,CAAC/F,MAAM,CAACkP,QAAQ,CAAC;UACnCG,UAAU,EAAED,SAAS,IAAI,MAAMpP,MAAM,CAACkP,QAAQ,EAAE;UAChDmB,cAAc,EAAEf,cAAc;UAC9BjN,aAAa,EAAE,IAAIsO,GAAG,CAACxB,UAAU,CAAClJ,GAAG,CAACuJ,EAAE,IAAIA,EAAE,CAACoB,YAAY,CAAC,CAAC,CAACC,IAAI;UAClEN,qBAAqB,EAAEV,mBAAmB;UAC1CW,qBAAqB,EAAEP,mBAAmB;UAC1CQ,oBAAoB,EAAEV,kBAAkB;UACxC;UACAe,gBAAgB,EAAE,IAAIH,GAAG,CAACxB,UAAU,CAAClJ,GAAG,CAACuJ,EAAE,IAAIA,EAAE,CAACjH,aAAa,CAAC,CAAC,CAACsI,IAAI;UACtEE,aAAa,EAAE5B,UAAU,CAAC3I,MAAM,CAACgJ,EAAE,IAAIA,EAAE,CAACvR,MAAM,KAAK,QAAQ,IAAIuR,EAAE,CAACwB,KAAK,IAAI,IAAI,CAAC,CAAChN,MAAM,GAAG,CAAC,GACzFmL,UAAU,CAAC3I,MAAM,CAACgJ,EAAE,IAAIA,EAAE,CAACvR,MAAM,KAAK,QAAQ,IAAIuR,EAAE,CAACwB,KAAK,IAAI,IAAI,CAAC,CAChEd,MAAM,CAAC,CAACC,GAAG,EAAEX,EAAE,KAAKW,GAAG,GAAGX,EAAE,CAACwB,KAAK,EAAE,CAAC,CAAC,GACvC7B,UAAU,CAAC3I,MAAM,CAACgJ,EAAE,IAAIA,EAAE,CAACvR,MAAM,KAAK,QAAQ,IAAIuR,EAAE,CAACwB,KAAK,IAAI,IAAI,CAAC,CAAChN,MAAM,GAC5E;QACN,CAAC;QAEDjG,QAAQ,CAACkT,gBAAgB,GAAG;UAAE,CAAC,SAASjR,MAAM,CAACkP,QAAQ,EAAE,GAAGwB;QAAa,CAAC;QAC1E3S,QAAQ,CAACmT,sBAAsB,GAAG,CAACR,YAAY,CAAC;QAChD5U,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE2U,YAAY,CAAC;MAC3C;IACF;;IAEA;IACA,IAAIS,eAAe,GAAG,EAAE;IAExB,IAAIpT,QAAQ,IAAIA,QAAQ,CAACkT,gBAAgB,IAAI,OAAOlT,QAAQ,CAACkT,gBAAgB,KAAK,QAAQ,EAAE;MAC1F,IAAI;QACFE,eAAe,GAAGC,MAAM,CAACC,OAAO,CAACtT,QAAQ,CAACkT,gBAAgB,CAAC,CACxDzK,MAAM,CAAC,CAAC,CAAC8K,GAAG,EAAEC,KAAK,CAAC,KAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACrC,QAAQ,CAAC,CACvFjJ,GAAG,CAAC,CAAC,CAACqL,GAAG,EAAEC,KAAK,CAAC,KAAK;UACrB;UACA,IAAIA,KAAK,CAACT,gBAAgB,KAAKjL,SAAS,EAAE;YACxC/J,OAAO,CAACC,GAAG,CAAC,MAAMwV,KAAK,CAACrC,QAAQ,IAAIoC,GAAG,+BAA+B,CAAC;YACvEC,KAAK,CAACT,gBAAgB,GAAG,CAAC;UAC5B;UACA,IAAIS,KAAK,CAACR,aAAa,KAAKlL,SAAS,EAAE;YACrC/J,OAAO,CAACC,GAAG,CAAC,MAAMwV,KAAK,CAACrC,QAAQ,IAAIoC,GAAG,4BAA4B,CAAC;YACpEC,KAAK,CAACR,aAAa,GAAG,CAAC;UACzB;UACA,OAAOQ,KAAK;QACd,CAAC,CAAC;QAEJzV,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEoV,eAAe,CAACnN,MAAM,EAAE,KAAK,CAAC;MAC1D,CAAC,CAAC,OAAOwN,GAAG,EAAE;QACZ1V,OAAO,CAAC8B,KAAK,CAAC,cAAc,EAAE4T,GAAG,CAAC;MACpC;IACF,CAAC,MAAM,IAAIzT,QAAQ,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAACkT,gBAAgB,CAAC,EAAE;MAC/D;MACAE,eAAe,GAAGpT,QAAQ,CAACkT,gBAAgB,CAAChL,GAAG,CAACwL,GAAG,IAAI;QACrD;QACA,IAAIA,GAAG,CAACX,gBAAgB,KAAKjL,SAAS,EAAE;UACtC/J,OAAO,CAACC,GAAG,CAAC,MAAM0V,GAAG,CAACvC,QAAQ,+BAA+B,CAAC;UAC9DuC,GAAG,CAACX,gBAAgB,GAAG,CAAC;QAC1B;QACA,IAAIW,GAAG,CAACV,aAAa,KAAKlL,SAAS,EAAE;UACnC/J,OAAO,CAACC,GAAG,CAAC,MAAM0V,GAAG,CAACvC,QAAQ,4BAA4B,CAAC;UAC3DuC,GAAG,CAACV,aAAa,GAAG,CAAC;QACvB;QACA,OAAOU,GAAG;MACZ,CAAC,CAAC;MACF3V,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEoV,eAAe,CAACnN,MAAM,EAAE,KAAK,CAAC;IAC9D,CAAC,MAAM;MACLlI,OAAO,CAAC6B,IAAI,CAAC,aAAa,EAAEI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkT,gBAAgB,CAAC;IACzD;;IAEA;IACA,IAAIE,eAAe,CAACnN,MAAM,KAAK,CAAC,EAAE;MAChCmN,eAAe,GAAG,CAChB;QACEjC,QAAQ,EAAE,CAAC;QACXG,UAAU,EAAE,MAAM;QAClBhN,aAAa,EAAE,EAAE;QACjBgO,cAAc,EAAE,CAAC;QACjBqB,gBAAgB,EAAE,GAAG;QACrBnB,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE,GAAG;QAC1BC,oBAAoB,EAAE,CAAC;QACvBK,gBAAgB,EAAE,CAAC;QACnBC,aAAa,EAAE;MACjB,CAAC,CACF;IACH;;IAEA;IACA,OAAO;MACLY,YAAY,EAAE5T,QAAQ,CAAC4T,YAAY,IAAI,CAAC;MACxCvB,WAAW,EAAErS,QAAQ,CAACqS,WAAW,IAAI,CAAC;MACtC/N,aAAa,EAAEtE,QAAQ,CAACsE,aAAa,IAAI,CAAC;MAC1CgO,cAAc,EAAEtS,QAAQ,CAACsS,cAAc,IAAItS,QAAQ,CAAC6T,oBAAoB,IAAI,CAAC;MAC7EA,oBAAoB,EAAE7T,QAAQ,CAAC6T,oBAAoB,IAAI7T,QAAQ,CAACsS,cAAc,IAAI,CAAC;MACnFwB,eAAe,EAAE9T,QAAQ,CAAC8T,eAAe,IAAI9T,QAAQ,CAACuS,qBAAqB,IAAI,CAAC;MAChFA,qBAAqB,EAAEvS,QAAQ,CAACuS,qBAAqB,IAAIvS,QAAQ,CAAC8T,eAAe,IAAI,CAAC;MACtFC,sBAAsB,EAAE/T,QAAQ,CAAC+T,sBAAsB,IAC/B,CAAC/T,QAAQ,CAACsS,cAAc,IAAItS,QAAQ,CAAC6T,oBAAoB,IAAI,CAAC,KAC7D7T,QAAQ,CAACuS,qBAAqB,IAAIvS,QAAQ,CAAC8T,eAAe,IAAI,CAAC,CAAE;MAC1FH,gBAAgB,EAAE3T,QAAQ,CAAC2T,gBAAgB,IAAI,CAAC;MAChDnB,qBAAqB,EAAExS,QAAQ,CAACwS,qBAAqB,IAAI,CAAC;MAC1DC,qBAAqB,EAAEzS,QAAQ,CAACyS,qBAAqB,IAAI,CAAC;MAC1DC,oBAAoB,EAAE1S,QAAQ,CAAC0S,oBAAoB,IAAI,CAAC;MACxDQ,gBAAgB,EAAElT,QAAQ,CAACkT,gBAAgB,IAAI,CAAC,CAAC;MACjDC,sBAAsB,EAAEC,eAAe;MACvCY,gBAAgB,EAAEhU,QAAQ,CAACgU,gBAAgB,IAAIhU,QAAQ,CAACiU,kBAAkB,IAAI;IAChF,CAAC;EACH,CAAC,CAAC,OAAOpU,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC;IACA,OAAO;MACL+T,YAAY,EAAE,CAAC;MACfvB,WAAW,EAAE,CAAC;MACd/N,aAAa,EAAE,EAAE;MACjBuP,oBAAoB,EAAE,CAAC;MACvBtB,qBAAqB,EAAE,CAAC;MACxBuB,eAAe,EAAE,CAAC;MAClBC,sBAAsB,EAAE,CAAC;MACzBJ,gBAAgB,EAAE,GAAG;MACrBnB,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE,GAAG;MAC1BC,oBAAoB,EAAE,CAAC;MACvBQ,gBAAgB,EAAE;QAChB,SAAS,EAAE;UACT/B,QAAQ,EAAE,CAAC;UACXG,UAAU,EAAE,MAAM;UAClBhN,aAAa,EAAE,EAAE;UACjBgO,cAAc,EAAE,CAAC;UACjBqB,gBAAgB,EAAE,GAAG;UACrBnB,qBAAqB,EAAE,CAAC;UACxBC,qBAAqB,EAAE,GAAG;UAC1BC,oBAAoB,EAAE,CAAC;UACvBK,gBAAgB,EAAE,CAAC;UACnBC,aAAa,EAAE;QACjB;MACF,CAAC;MACDG,sBAAsB,EAAE,CACtB;QACEhC,QAAQ,EAAE,CAAC;QACXG,UAAU,EAAE,MAAM;QAClBhN,aAAa,EAAE,EAAE;QACjBgO,cAAc,EAAE,CAAC;QACjBqB,gBAAgB,EAAE,GAAG;QACrBnB,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE,GAAG;QAC1BC,oBAAoB,EAAE,CAAC;QACvBK,gBAAgB,EAAE,CAAC;QACnBC,aAAa,EAAE;MACjB,CAAC,CACF;MACDgB,gBAAgB,EAAE,CAChB;QACE9Q,EAAE,EAAE,CAAC;QACLgR,KAAK,EAAE,OAAO;QACdC,aAAa,EAAE,CAAC;QAChBvN,UAAU,EAAE,CAAC;QACbiM,YAAY,EAAE,MAAM;QACpB1O,UAAU,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,WAAW,CAAC;MACrC,CAAC,CACF;MACDvE,KAAK,EAAEA,KAAK,CAACc,OAAO,IAAI;IAC1B,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAMyT,oBAAoB,GAAG,MAAAA,CAAOnS,MAAM,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiE,MAAM,CAAC;IACnC,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,qBAAqB,EAAE;MAAEQ;IAAO,CAAC,CAAC;IAEjElE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;;IAEpC;IACA,IAAI,CAACA,QAAQ,CAACqU,cAAc,IAAI,CAAC3O,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAACqU,cAAc,CAAC,EAAE;MACvEtW,OAAO,CAAC6B,IAAI,CAAC,cAAc,EAAEI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqU,cAAc,CAAC;MACtDrU,QAAQ,CAACqU,cAAc,GAAG,EAAE;IAC9B;IAEA,OAAOrU,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC;IACA,OAAO;MACLgU,oBAAoB,EAAE,CAAC;MACvBS,wBAAwB,EAAE,CAAC;MAC3BP,sBAAsB,EAAE,CAAC;MACzBJ,gBAAgB,EAAE,CAAC;MACnBY,oBAAoB,EAAE,CAAC;MACvBC,4BAA4B,EAAE,CAAC;MAC/BC,wBAAwB,EAAE,CAAC;MAC3BjC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE,CAAC;MACxBC,oBAAoB,EAAE,CAAC;MACvB2B,cAAc,EAAE,EAAE;MAClBxU,KAAK,EAAEA,KAAK,CAACc,OAAO,IAAI;IAC1B,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAM+T,0BAA0B,GAAGA,CAAA,KAAM;EAC9C,OAAOhW,GAAG,CAAC+C,GAAG,CAAC,6BAA6B,CAAC;AAC/C,CAAC;AAED,OAAO,MAAMkT,gBAAgB,GAAI1S,MAAM,IAAK;EAC1C,OAAOvD,GAAG,CAAC+C,GAAG,CAAC,oBAAoB,EAAE;IAAEQ;EAAO,CAAC,CAAC;AAClD,CAAC;;AAED;AACA,OAAO,MAAM2S,UAAU,GAAIC,QAAQ,IAAK;EACtC,OAAOnW,GAAG,CAAC2C,IAAI,CAAC,UAAU,EAAE;IAAEwT;EAAS,CAAC,CAAC;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAI1U,IAAI,IAAK;EACpCrC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEoC,IAAI,CAAC;EAC9C,OAAO1B,GAAG,CAAC2C,IAAI,CAAC,mBAAmB,EAAEjB,IAAI,CAAC;AAC5C,CAAC;AAED,OAAO,MAAM2U,qBAAqB,GAAI3U,IAAI,IAAK;EAC7CrC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoC,IAAI,CAAC;EACvD,OAAO1B,GAAG,CAAC2C,IAAI,CAAC,4BAA4B,EAAEjB,IAAI,CAAC;AACrD,CAAC;;AAED;AACA,OAAO,MAAM4U,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACFjX,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B,MAAMqB,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,CAAC,CAACqB,KAAK,CAAC;;IAE7B;IACA,IAAIsQ,OAAO,GAAG,KAAK;IACnB,IAAI;MACF,MAAMsF,QAAQ,GAAGpT,IAAI,CAAC6N,KAAK,CAAC7R,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACjE6R,OAAO,GAAGsF,QAAQ,CAACrF,QAAQ,IAAI,KAAK;MACpC7R,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2R,OAAO,CAAC;IACnC,CAAC,CAAC,OAAOuF,CAAC,EAAE;MACVnX,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC;;IAEA;IACA,MAAMkE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D;IACA,MAAM6S,QAAQ,GAAGxF,OAAO,GAAG,mBAAmB,GAAG,6BAA6B;IAC9E5R,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmX,QAAQ,CAAC;IAE9B,MAAMnV,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC0T,QAAQ,EAAE;MACvCxS,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMuV,cAAc,GAAG,MAAOC,UAAU,IAAK;EAClD,IAAI;IACFtX,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqX,UAAU,CAAC;IACtC,MAAMhW,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,CAAC,CAACqB,KAAK,CAAC;;IAE7B;IACA,MAAM6C,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,mBAAmB,EAAEgU,UAAU,EAAE;MAC/D1S,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC8D,IAAI,CAAC;IAClC5F,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACc,OAAO,CAAC;IAErC,IAAId,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IAEA,IAAI/D,KAAK,CAACG,QAAQ,EAAE;MAAA,IAAAsV,oBAAA;MAClBvX,OAAO,CAAC8B,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACG,QAAQ,CAACE,MAAM,CAAC;MAC9CnC,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,CAACI,IAAI,CAAC;MAC3C,MAAM,IAAIwD,KAAK,CAAC,SAAS,EAAA0R,oBAAA,GAAAzV,KAAK,CAACG,QAAQ,CAACI,IAAI,cAAAkV,oBAAA,uBAAnBA,oBAAA,CAAqBvO,MAAM,KAAIlH,KAAK,CAACc,OAAO,EAAE,CAAC;IAC1E;IAEA,MAAM,IAAIiD,KAAK,CAAC,SAAS/D,KAAK,CAACc,OAAO,EAAE,CAAC;EAC3C;AACF,CAAC;AAED,OAAO,MAAM4U,cAAc,GAAG,MAAAA,CAAOrS,EAAE,EAAEmS,UAAU,KAAK;EACtD,IAAI;IACFtX,OAAO,CAACC,GAAG,CAAC,cAAckF,EAAE,QAAQ,EAAEmS,UAAU,CAAC;IACjD,MAAMhW,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,CAAC,CAACqB,KAAK,CAAC;;IAE7B;IACA,MAAM6C,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,qBAAqBxB,EAAE,EAAE,EAAEmS,UAAU,EAAE;MACpE1S,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,cAAcqD,EAAE,MAAM,EAAErD,KAAK,CAAC;IAC5C9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC8D,IAAI,CAAC;IAClC5F,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACc,OAAO,CAAC;IAErC,IAAId,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IAEA,IAAI/D,KAAK,CAACG,QAAQ,EAAE;MAAA,IAAAwV,qBAAA;MAClBzX,OAAO,CAAC8B,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACG,QAAQ,CAACE,MAAM,CAAC;MAC9CnC,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,CAACI,IAAI,CAAC;MAC3C,MAAM,IAAIwD,KAAK,CAAC,SAAS,EAAA4R,qBAAA,GAAA3V,KAAK,CAACG,QAAQ,CAACI,IAAI,cAAAoV,qBAAA,uBAAnBA,qBAAA,CAAqBzO,MAAM,KAAIlH,KAAK,CAACc,OAAO,EAAE,CAAC;IAC1E;IAEA,MAAM,IAAIiD,KAAK,CAAC,SAAS/D,KAAK,CAACc,OAAO,EAAE,CAAC;EAC3C;AACF,CAAC;AAED,OAAO,MAAM8U,cAAc,GAAG,MAAOvS,EAAE,IAAK;EAC1C,IAAI;IACFnF,OAAO,CAACC,GAAG,CAAC,cAAckF,EAAE,QAAQ,CAAC;IACrC,MAAM7D,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,CAAC,CAACqB,KAAK,CAAC;;IAE7B;IACA,MAAM6C,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACqG,MAAM,CAAC,qBAAqB7B,EAAE,EAAE,EAAE;MAC3DP,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,cAAcqD,EAAE,MAAM,EAAErD,KAAK,CAAC;IAC5C9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC8D,IAAI,CAAC;IAClC5F,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACc,OAAO,CAAC;IAErC,IAAId,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IAEA,IAAI/D,KAAK,CAACG,QAAQ,EAAE;MAAA,IAAA0V,qBAAA;MAClB3X,OAAO,CAAC8B,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACG,QAAQ,CAACE,MAAM,CAAC;MAC9CnC,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,CAACI,IAAI,CAAC;MAC3C,MAAM,IAAIwD,KAAK,CAAC,SAAS,EAAA8R,qBAAA,GAAA7V,KAAK,CAACG,QAAQ,CAACI,IAAI,cAAAsV,qBAAA,uBAAnBA,qBAAA,CAAqB3O,MAAM,KAAIlH,KAAK,CAACc,OAAO,EAAE,CAAC;IAC1E;IAEA,MAAM,IAAIiD,KAAK,CAAC,SAAS/D,KAAK,CAACc,OAAO,EAAE,CAAC;EAC3C;AACF,CAAC;;AAED;;AAEA,OAAO,MAAMgV,aAAa,GAAG,MAAOC,WAAW,IAAK;EAClD,IAAI;IACF7X,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4X,WAAW,CAAC;IACtD,MAAMvW,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;IAE3C;IACA,MAAMoE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,iBAAiB,EAAEuU,WAAW,EAAE;MAC9DjT,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMgW,aAAa,GAAG,MAAAA,CAAO3S,EAAE,EAAE0S,WAAW,KAAK;EACtD,IAAI;IACF7X,OAAO,CAACC,GAAG,CAAC,4BAA4BkF,EAAE,SAAS,EAAE0S,WAAW,CAAC;IACjE,MAAMvW,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;IAE3C;IACA,MAAMoE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,mBAAmBxB,EAAE,EAAE,EAAE0S,WAAW,EAAE;MACnEjT,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMiW,aAAa,GAAG,MAAO5S,EAAE,IAAK;EACzC,IAAI;IACFnF,OAAO,CAACC,GAAG,CAAC,4BAA4BkF,EAAE,EAAE,CAAC;IAC7C,MAAM7D,KAAK,GAAGxB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;IAE3C;IACA,MAAMoE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACqG,MAAM,CAAC,mBAAmB7B,EAAE,EAAE,EAAE;MACzDP,MAAM,EAAET,UAAU,CAACS,MAAM;MACzB9D,OAAO,EAAE;QACP,eAAe,EAAE,UAAUQ,KAAK,EAAE;QAClC,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IAEF2D,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;IACvC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMkW,eAAe,GAAG,MAAO9F,QAAQ,IAAK;EACjD,IAAI;IACFlS,OAAO,CAACC,GAAG,CAAC,cAAciS,QAAQ,EAAE,CAAC;;IAErC;IACA,MAAM/N,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kBAAkBwO,QAAQ,EAAE,EAAE;MAC3DtN,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IACA;IACA,OAAO;MACLV,EAAE,EAAE+M,QAAQ;MACZtM,IAAI,EAAE,MAAM;MACZyL,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZ0G,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBpO,SAAS,EAAE,IAAI;MACfwK,WAAW,EAAE,CAAC;MACd6D,aAAa,EAAE,CAAC;MAChB5R,aAAa,EAAE;IACjB,CAAC;EACH;AACF,CAAC;AAED,OAAO,MAAM6R,gBAAgB,GAAG,MAAAA,CAAOlG,QAAQ,EAAEQ,UAAU,KAAK;EAC9D,IAAI;IACF1S,OAAO,CAACC,GAAG,CAAC,cAAciS,QAAQ,EAAE,EAAEQ,UAAU,CAAC;;IAEjD;IACA,MAAMvO,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,kBAAkBuL,QAAQ,EAAE,EAAEQ,UAAU,EAAE;MACvE9N,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMuW,kBAAkB,GAAG,MAAOnG,QAAQ,IAAK;EACpD,IAAI;IACFlS,OAAO,CAACC,GAAG,CAAC,kBAAkBiS,QAAQ,QAAQ,OAAOA,QAAQ,EAAE,CAAC;IAEhE,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKnI,SAAS,EAAE;MAC/C/J,OAAO,CAAC6B,IAAI,CAAC,kBAAkB,CAAC;MAChC,OAAO,EAAE;IACX;;IAEA;IACA,IAAIyW,eAAe;IACnB,IAAI;MACFA,eAAe,GAAGrO,QAAQ,CAACiI,QAAQ,EAAE,EAAE,CAAC;MACxC,IAAIqG,KAAK,CAACD,eAAe,CAAC,EAAE;QAC1BtY,OAAO,CAAC6B,IAAI,CAAC,iBAAiB,EAAEqQ,QAAQ,CAAC;QACzCoG,eAAe,GAAGpG,QAAQ;MAC5B;IACF,CAAC,CAAC,OAAOiF,CAAC,EAAE;MACVnX,OAAO,CAAC6B,IAAI,CAAC,iBAAiB,EAAEqQ,QAAQ,CAAC;MACzCoG,eAAe,GAAGpG,QAAQ;IAC5B;;IAEA;IACAA,QAAQ,GAAGoG,eAAe;;IAE1B;IACA,MAAMnU,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,IAAItC,QAAQ;IACZ,IAAIuW,aAAa,GAAG,EAAE;IAEtB,IAAI;MACF;MACAxY,OAAO,CAACC,GAAG,CAAC,8BAA8BiS,QAAQ,UAAU,CAAC;MAC7DjQ,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kBAAkBwO,QAAQ,UAAU,EAAE;QAC7DtN,MAAM,EAAET,UAAU,CAACS;MACrB,CAAC,CAAC;MACF5E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgC,QAAQ,CAAC;IACzC,CAAC,CAAC,OAAO+P,QAAQ,EAAE;MACjBwG,aAAa,CAAC/M,IAAI,CAAC,eAAeuG,QAAQ,CAACpP,OAAO,IAAI,MAAM,EAAE,CAAC;MAC/D5C,OAAO,CAAC6B,IAAI,CAAC,8BAA8BqQ,QAAQ,UAAU,EAAEF,QAAQ,CAAC;;MAExE;MACA,IAAI;QACFhS,OAAO,CAACC,GAAG,CAAC,wBAAwBiS,QAAQ,UAAU,CAAC;QACvDjQ,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,YAAYwO,QAAQ,UAAU,EAAE;UACvDtN,MAAM,EAAET,UAAU,CAACS;QACrB,CAAC,CAAC;QACF5E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgC,QAAQ,CAAC;MACzC,CAAC,CAAC,OAAO6C,WAAW,EAAE;QACpB0T,aAAa,CAAC/M,IAAI,CAAC,eAAe3G,WAAW,CAAClC,OAAO,IAAI,MAAM,EAAE,CAAC;QAClE5C,OAAO,CAAC6B,IAAI,CAAC,yBAAyBqQ,QAAQ,UAAU,EAAEpN,WAAW,CAAC;;QAEtE;QACA,IAAI;UACF9E,OAAO,CAACC,GAAG,CAAC,wCAAwCiS,QAAQ,EAAE,CAAC;UAC/D,MAAMuG,UAAU,GAAG,MAAM9X,GAAG,CAAC+C,GAAG,CAAC,4BAA4BwO,QAAQ,EAAE,EAAE;YACvEtN,MAAM,EAAET,UAAU,CAACS;UACrB,CAAC,CAAC;UACF5E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwY,UAAU,CAAC;;UAEzC;UACAxW,QAAQ,GAAG0F,KAAK,CAACC,OAAO,CAAC6Q,UAAU,CAAC,GAAGA,UAAU,CAAC/N,MAAM,CAACgO,CAAC,IAAIA,CAAC,CAACjU,SAAS,KAAKwF,QAAQ,CAACiI,QAAQ,CAAC,CAAC,GAAG,EAAE;UACtGlS,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;QACpC,CAAC,CAAC,OAAOiE,UAAU,EAAE;UACnBsS,aAAa,CAAC/M,IAAI,CAAC,eAAevF,UAAU,CAACtD,OAAO,IAAI,MAAM,EAAE,CAAC;UACjE5C,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAE0W,aAAa,CAAC;UAC3CvW,QAAQ,GAAG,EAAE;QACf;MACF;IACF;IAEAgD,YAAY,CAACZ,SAAS,CAAC;;IAEvB;IACA,IAAI,CAACsD,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,EAAE;MAC5BjC,OAAO,CAAC6B,IAAI,CAAC,gBAAgB,EAAEI,QAAQ,CAAC;MACxC,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAC5C,IAAI0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC4F,KAAK,CAAC,EAAE;UACjC5F,QAAQ,GAAGA,QAAQ,CAAC4F,KAAK;QAC3B,CAAC,MAAM;UACL5F,QAAQ,GAAG,CAACA,QAAQ,CAAC;QACvB;MACF,CAAC,MAAM;QACLA,QAAQ,GAAG,EAAE;MACf;IACF;IAEAjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgC,QAAQ,CAAC;IACzC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,MAAM,CAAC;IACvB;IACA;IACA,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAM6W,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACF3Y,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACvB,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,uBAAuB,CAAC;IACvD1D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC;IACA,OAAO,CACL,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,OAAO,EAAE,OAAO,CAC1B;EACH;AACF,CAAC;AAED,OAAO,MAAM8W,oBAAoB,GAAG,MAAOnS,SAAS,IAAK;EACvD,IAAI;IACFzG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEwG,SAAS,CAAC;;IAE/B;IACA,MAAMtC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,gBAAgB,EAAEmD,SAAS,EAAE;MAC3D7B,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM+W,eAAe,GAAG,MAAAA,CAAO3R,OAAO,EAAET,SAAS,KAAK;EAC3D,IAAI;IACFzG,OAAO,CAACC,GAAG,CAAC,YAAYiH,OAAO,EAAE,EAAET,SAAS,CAAC;;IAE7C;IACA,MAAMtC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,kBAAkBO,OAAO,EAAE,EAAET,SAAS,EAAE;MACrE7B,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMgX,eAAe,GAAG,MAAO5R,OAAO,IAAK;EAChD,IAAI;IACFlH,OAAO,CAACC,GAAG,CAAC,YAAYiH,OAAO,EAAE,CAAC;;IAElC;IACA,MAAM/C,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAACqG,MAAM,CAAC,kBAAkBE,OAAO,EAAE,EAAE;MAC7DtC,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IACA,MAAM/D,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMiX,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF/Y,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;;IAErB;IACA,MAAMkE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,IAAItC,QAAQ;IACZ,IAAI;MACF;MACAA,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,cAAc,EAAE;QACvCkB,MAAM,EAAET,UAAU,CAACS;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOoN,QAAQ,EAAE;MACjBhS,OAAO,CAAC6B,IAAI,CAAC,0BAA0B,EAAEmQ,QAAQ,CAAC;MAClD;MACA,IAAI;QACF/P,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,QAAQ,EAAE;UACjCkB,MAAM,EAAET,UAAU,CAACS;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOE,WAAW,EAAE;QACpB9E,OAAO,CAAC6B,IAAI,CAAC,qBAAqB,EAAEiD,WAAW,CAAC;QAChD;QACA7C,QAAQ,GAAG,EAAE;MACf;IACF;IAEAgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAO0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE;EAChD,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,MAAM,CAAC;IACvB;IACA;IACA,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMkX,cAAc,GAAG,MAAO9G,QAAQ,IAAK;EAChD,IAAI;IACFlS,OAAO,CAACC,GAAG,CAAC,kBAAkBiS,QAAQ,EAAE,CAAC;;IAEzC;IACA,MAAM/N,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,IAAItC,QAAQ;IACZ,IAAI;MACF;MACAA,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kBAAkBwO,QAAQ,QAAQ,EAAE;QAC3DtN,MAAM,EAAET,UAAU,CAACS;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOoN,QAAQ,EAAE;MACjBhS,OAAO,CAAC6B,IAAI,CAAC,8BAA8BqQ,QAAQ,QAAQ,EAAEF,QAAQ,CAAC;MACtE;MACA,IAAI;QACF/P,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,YAAYwO,QAAQ,QAAQ,EAAE;UACrDtN,MAAM,EAAET,UAAU,CAACS;QACrB,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOE,WAAW,EAAE;QACpB9E,OAAO,CAAC6B,IAAI,CAAC,yBAAyBqQ,QAAQ,QAAQ,EAAEpN,WAAW,CAAC;QACpE;QACA7C,QAAQ,GAAG,EAAE;MACf;IACF;IAEAgD,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAO0F,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE;EAChD,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,MAAM,CAAC;IACvB;IACA;IACA,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMmX,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC5C,IAAI;IACFlZ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEiZ,QAAQ,CAAC;IAC9B,MAAMjX,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,qBAAqB,EAAE4V,QAAQ,CAAC;IAChElZ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMqX,UAAU,GAAG,MAAOD,QAAQ,IAAK;EAC5C,IAAI;IACFlZ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEiZ,QAAQ,CAAC;IAC9B,MAAMjX,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,qBAAqB,EAAE4V,QAAQ,CAAC;IAChElZ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMsX,sBAAsB,GAAG,MAAAA,CAAOlV,MAAM,GAAG,CAAC,CAAC,KAAK;EAC3D,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiE,MAAM,GAAG,OAAOJ,IAAI,CAACC,SAAS,CAACG,MAAM,CAAC,EAAE,GAAG,EAAE,CAAC;IAC1F,MAAMjC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,kCAAkC,EAAE;MAAEQ;IAAO,CAAC,CAAC;IAC9ElE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMuX,uBAAuB,GAAG,MAAAA,CAAA,KAAY;EACjD,IAAI;IACFrZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxBD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D;IACA,MAAMgC,QAAQ,GAAG,MAAMvC,KAAK,CAACgE,GAAG,CAAC,mCAAmC,CAAC;IACrE1D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IACnCjC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAACI,IAAI,CAAC;IACvC,OAAOJ,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACI,IAAI,GAAG,OAAO,CAAC;IACtErC,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACE,MAAM,GAAG,OAAO,CAAC;IACxE;IACA,OAAO;MACLmX,0BAA0B,EAAE,IAAI;MAChCC,0BAA0B,EAAE;IAC9B,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,+BAA+B,GAAG,MAAAA,CAAA,KAAY;EACzD,IAAI;IACFxZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BD,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnE;IACA,MAAMgC,QAAQ,GAAG,MAAMvC,KAAK,CAACgE,GAAG,CAAC,4CAA4C,CAAC;IAC9E1D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrCjC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAACI,IAAI,CAAC;IACzC,OAAOJ,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACI,IAAI,GAAG,OAAO,CAAC;IACtErC,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACE,MAAM,GAAG,OAAO,CAAC;IACxE;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMsX,iBAAiB,GAAG,MAAAA,CAAA,KAAY;EAC3C,IAAI;IACFzZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxBD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACpD;IACA,MAAMgC,QAAQ,GAAG,MAAMvC,KAAK,CAACgE,GAAG,CAAC,6BAA6B,CAAC;IAC/D1D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IAEnC,IAAIA,QAAQ,CAACI,IAAI,IAAIsF,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAACI,IAAI,CAACqX,KAAK,CAAC,EAAE;MACvD1Z,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgC,QAAQ,CAACI,IAAI,CAACqX,KAAK,CAACxR,MAAM,EAAE,GAAG,CAAC;MACxDlI,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEgC,QAAQ,CAACI,IAAI,CAACqX,KAAK,CAACvP,GAAG,CAACwP,CAAC,IAAIA,CAAC,CAAC/T,IAAI,CAAC,CAACyE,IAAI,CAAC,IAAI,CAAC,CAAC;;MAErE;MACA,IAAIpI,QAAQ,CAACI,IAAI,CAACqX,KAAK,CAACxR,MAAM,KAAK,CAAC,EAAE;QACpClI,OAAO,CAAC6B,IAAI,CAAC,gBAAgB,CAAC;MAChC;IACF,CAAC,MAAM;MACL7B,OAAO,CAAC6B,IAAI,CAAC,kBAAkB,EAAEI,QAAQ,CAACI,IAAI,CAAC;IACjD;IAEA,OAAOJ,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC9B,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACI,IAAI,GAAG,OAAO,CAAC;IACtErC,OAAO,CAAC8B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACE,MAAM,GAAG,OAAO,CAAC;;IAExE;IACA,IAAIL,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACI,IAAI,EAAE;MACzCrC,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACG,QAAQ,CAACI,IAAI,CAAC;IACnD;;IAEA;IACArC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;IACvB,OAAO;MACLyZ,KAAK,EAAE,CACL;QAAE9T,IAAI,EAAE,SAAS;QAAEgU,iBAAiB,EAAE,KAAK;QAAEC,MAAM,EAAE;UAAEpI,MAAM,EAAE;YAAEqI,QAAQ,EAAE;UAAK,CAAC;UAAEC,KAAK,EAAE;YAAED,QAAQ,EAAE;UAAK,CAAC;UAAEE,OAAO,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEG,MAAM,EAAE;UAAK;QAAE;MAAE,CAAC,EAC5J;QAAErU,IAAI,EAAE,SAAS;QAAEgU,iBAAiB,EAAE,IAAI;QAAEC,MAAM,EAAE;UAAEpI,MAAM,EAAE;YAAEqI,QAAQ,EAAE;UAAK,CAAC;UAAEC,KAAK,EAAE;YAAED,QAAQ,EAAE;UAAM,CAAC;UAAEE,OAAO,EAAE;YAAEF,QAAQ,EAAE;UAAK;QAAE;MAAE,CAAC,CAC9I;MACDI,qBAAqB,EAAE;IACzB,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,wBAAwB,GAAG,MAAAA,CAAOjW,MAAM,GAAG,CAAC,CAAC,KAAK;EAC7D,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiE,MAAM,CAAC;IACpC,MAAMM,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAAC/B,MAAM,EAAEqC,WAAW,CAACnB,MAAM,CAAC,QAAQ,EAAEa,MAAM,CAAC/B,MAAM,CAAC;IAC9D,IAAI+B,MAAM,CAACkW,SAAS,EAAE5V,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAACkW,SAAS,CAAC;IACvE,IAAIlW,MAAM,CAACO,SAAS,EAAED,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAACO,SAAS,CAAC;IACvE,IAAIP,MAAM,CAAC8F,IAAI,EAAExF,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAAC8F,IAAI,CAAC;IACxD,IAAI9F,MAAM,CAACuD,KAAK,EAAEjD,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEa,MAAM,CAACuD,KAAK,CAAC;IAE3D,MAAMxF,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,iCAAiCc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;IACzFlK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC;IACA,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMuY,uBAAuB,GAAG,MAAAA,CAAOC,UAAU,EAAEjY,IAAI,KAAK;EACjE,IAAI;IACFrC,OAAO,CAACC,GAAG,CAAC,eAAeqa,UAAU,OAAO,EAAEjY,IAAI,CAAC;IACnD,MAAMJ,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,iCAAiC2T,UAAU,eAAe,EAAEjY,IAAI,CAAC;IAChGrC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMyY,uBAAuB,GAAG,MAAAA,CAAOD,UAAU,EAAEjY,IAAI,KAAK;EACjE,IAAI;IACFrC,OAAO,CAACC,GAAG,CAAC,eAAeqa,UAAU,OAAO,EAAEjY,IAAI,CAAC;IACnD,MAAMJ,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,iCAAiC2T,UAAU,eAAe,EAAEjY,IAAI,CAAC;IAChGrC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM0Y,gBAAgB,GAAG,MAAOhX,QAAQ,IAAK;EAClD,IAAI;IACFxD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuD,QAAQ,CAAC;IACvC,MAAMvB,QAAQ,GAAG,MAAMvC,KAAK,CAAC4D,IAAI,CAAC,wBAAwB,EAAEE,QAAQ,CAAC;IACrExD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACI,IAAI,EAAE;MACzC,MAAMP,KAAK,CAACG,QAAQ,CAACI,IAAI;IAC3B;IACA,MAAMP,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM2Y,cAAc,GAAG,MAAAA,CAAOvW,MAAM,GAAG,CAAC,CAAC,KAAK;EACnD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiE,MAAM,CAAC;IAChC,MAAMM,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAAC0B,IAAI,EAAEpB,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAAC0B,IAAI,CAAC;IACxD,IAAI1B,MAAM,CAACkP,QAAQ,EAAE5O,WAAW,CAACnB,MAAM,CAAC,UAAU,EAAEa,MAAM,CAACkP,QAAQ,CAAC;IACpE,IAAIlP,MAAM,CAACO,SAAS,EAAED,WAAW,CAACnB,MAAM,CAAC,WAAW,EAAEa,MAAM,CAACO,SAAS,CAAC;IAEvE,MAAMxC,QAAQ,GAAG,MAAMvC,KAAK,CAACgE,GAAG,CAAC,8BAA8Bc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;IACxFlK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgC,QAAQ,CAAC;IAChC,OAAOA,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM4Y,0BAA0B,GAAG,MAAOC,WAAW,IAAK;EAC/D,IAAI;IACF3a,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0a,WAAW,CAAC;IAC1C,MAAM1Y,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,yBAAyB,EAAEqX,WAAW,CAAC;IACvE3a,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM8Y,iBAAiB,GAAG,MAAOC,gBAAgB,IAAK;EAC3D,IAAI;IACF7a,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE4a,gBAAgB,CAAC;IAC3C,MAAM5Y,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,sBAAsB,EAAEuX,gBAAgB,CAAC;IACzE7a,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgC,QAAQ,CAAC;IACjC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAChC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMgZ,8BAA8B,GAAG,MAAOH,WAAW,IAAK;EACnE,IAAI;IACF3a,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0a,WAAW,CAAC;IAC5C,MAAM1Y,QAAQ,GAAG,MAAMvC,KAAK,CAAC4D,IAAI,CAAC,GAAG1D,UAAU,CAAC,CAAC,qCAAqC,EAAE+a,WAAW,CAAC;IACpG3a,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgC,QAAQ,CAAC;IACvC,OAAOA,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACtC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMiZ,qBAAqB,GAAG,MAAOF,gBAAgB,IAAK;EAC/D,IAAI;IACF7a,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE4a,gBAAgB,CAAC;IAC7C,MAAM5Y,QAAQ,GAAG,MAAMvC,KAAK,CAAC4D,IAAI,CAAC,GAAG1D,UAAU,CAAC,CAAC,kCAAkC,EAAEib,gBAAgB,CAAC;IACtG7a,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgC,QAAQ,CAAC;IACnC,OAAOA,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMkZ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,IAAI;IACFhb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,4BAA4B,CAAC;IAC5D1D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;IACrC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMmZ,aAAa,GAAG,MAAOC,SAAS,IAAK;EAChD,IAAI;IACFlb,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEib,SAAS,CAAC;IACjC,MAAMjZ,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,4BAA4B4X,SAAS,EAAE,CAAC;IACxElb,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMqZ,uBAAuB,GAAG,MAAOC,eAAe,IAAK;EAChE,IAAI;IACFpb,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEmb,eAAe,CAAC;IAC3C,MAAMnZ,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,qBAAqB,EAAE8X,eAAe,CAAC;IACvEpb,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMuZ,yBAAyB,GAAG,MAAAA,CAAA,KAAY;EACnD,IAAI;IACFrb,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1B,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,2BAA2B,CAAC;IAC3D1D,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgC,QAAQ,CAAC;IACvC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACtC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMwZ,qBAAqB,GAAG,MAAAA,CAAOpX,MAAM,GAAG,CAAC,CAAC,KAAK;EAC1D,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiE,MAAM,CAAC;IACpC,MAAMM,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAAC/B,MAAM,EAAEqC,WAAW,CAACnB,MAAM,CAAC,QAAQ,EAAEa,MAAM,CAAC/B,MAAM,CAAC;IAE9D,MAAMF,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,8BAA8Bc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;IACtFlK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMyZ,uBAAuB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,UAAU,KAAK;EAC1E,IAAI;IACFzb,OAAO,CAACC,GAAG,CAAC,aAAaub,aAAa,OAAO,EAAEC,UAAU,CAAC;IAC1D,MAAMxZ,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,8BAA8B6U,aAAa,SAAS,EAAEC,UAAU,CAAC;IAChGzb,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM4Z,gBAAgB,GAAG,MAAAA,CAAOxJ,QAAQ,EAAEhL,OAAO,EAAEhD,MAAM,GAAG,CAAC,CAAC,KAAK;EACxE,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,kBAAkBiS,QAAQ,WAAWhL,OAAO,EAAE,CAAC;IAE3D,MAAM1C,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAAC8F,IAAI,EAAExF,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAAC8F,IAAI,CAAC;IACxD,IAAI9F,MAAM,CAACuD,KAAK,EAAEjD,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEa,MAAM,CAACuD,KAAK,CAAC;IAE3D,MAAMxF,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,YAAYwO,QAAQ,YAAYhL,OAAO,aAAa1C,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;IAC5GlK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;AAED,eAAenB,GAAG;;AAElB;AACA,OAAO,MAAMgb,oBAAoB,GAAG,MAAAA,CAAOzX,MAAM,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,MAAMuE,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAAC8F,IAAI,EAAExF,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAAC8F,IAAI,CAAC;IACxD,IAAI9F,MAAM,CAACuD,KAAK,EAAEjD,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEa,MAAM,CAACuD,KAAK,CAAC;IAE3D,MAAMxF,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,6BAA6Bc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,CAAC;IACrFlK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,OAAO;MAAEuH,KAAK,EAAE,CAAC;MAAExB,KAAK,EAAE;IAAG,CAAC;EAChC;AACF,CAAC;;AAED;AACA,OAAO,MAAM+T,qBAAqB,GAAG,MAAOC,YAAY,IAAK;EAC3D,IAAI;IACF7b,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,2BAA2B,EAAEuY,YAAY,CAAC;IAC1E7b,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMga,qBAAqB,GAAG,MAAAA,CAAOC,UAAU,EAAEF,YAAY,KAAK;EACvE,IAAI;IACF7b,OAAO,CAACC,GAAG,CAAC,oCAAoC8b,UAAU,EAAE,CAAC;IAC7D,MAAM9Z,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,6BAA6BoV,UAAU,EAAE,EAAEF,YAAY,CAAC;IACvF7b,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMka,qBAAqB,GAAG,MAAOD,UAAU,IAAK;EACzD,IAAI;IACF/b,OAAO,CAACC,GAAG,CAAC,oCAAoC8b,UAAU,EAAE,CAAC;IAC7D,MAAM9Z,QAAQ,GAAG,MAAMtB,GAAG,CAACqG,MAAM,CAAC,6BAA6B+U,UAAU,EAAE,CAAC;IAC5E/b,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMma,YAAY,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACFjc,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,eAAe,CAAC;IAC/C1D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMoa,kBAAkB,GAAG,MAAOvX,KAAK,IAAK;EACjD,IAAI;IACF3E,OAAO,CAACC,GAAG,CAAC,iCAAiC0E,KAAK,EAAE,CAAC;IACrD,MAAM1C,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,4BAA4ByY,kBAAkB,CAACxX,KAAK,CAAC,EAAE,CAAC;IACvF3E,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMsa,WAAW,GAAG,MAAAA,CAAOlY,MAAM,GAAG,CAAC,CAAC,KAAK;EAChD,IAAI;IACFlE,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAEhC,MAAMuE,WAAW,GAAG,IAAIpB,eAAe,CAAC,CAAC;IACzC,IAAIc,MAAM,CAAC8F,IAAI,EAAExF,WAAW,CAACnB,MAAM,CAAC,MAAM,EAAEa,MAAM,CAAC8F,IAAI,CAAC;IACxD,IAAI9F,MAAM,CAACuD,KAAK,EAAEjD,WAAW,CAACnB,MAAM,CAAC,OAAO,EAAEa,MAAM,CAACuD,KAAK,CAAC;IAC3D,IAAIvD,MAAM,CAACmY,WAAW,EAAE7X,WAAW,CAACnB,MAAM,CAAC,aAAa,EAAEa,MAAM,CAACmY,WAAW,CAAC;;IAE7E;IACA,MAAMlY,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAE/D,MAAMtC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,mBAAmBc,WAAW,CAAC0F,QAAQ,CAAC,CAAC,EAAE,EAAE;MAC1EtF,MAAM,EAAET,UAAU,CAACS;IACrB,CAAC,CAAC;IAEFK,YAAY,CAACZ,SAAS,CAAC;IACvBrE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,IAAIA,KAAK,CAAC8D,IAAI,KAAK,YAAY,EAAE;MAC/B5F,OAAO,CAAC8B,KAAK,CAAC,MAAM,CAAC;IACvB;IACA;IACA,OAAO;MAAEuH,KAAK,EAAE,CAAC;MAAExB,KAAK,EAAE;IAAG,CAAC;EAChC;AACF,CAAC;;AAED;AACA,OAAO,MAAMyU,QAAQ,GAAG,MAAAA,CAAA,KAAY;EAClC,IAAI;IACFtc,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,cAAc,CAAC;IAC9C1D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMya,yBAAyB,GAAG,MAAOC,MAAM,IAAK;EACzD,IAAI;IACFxc,OAAO,CAACC,GAAG,CAAC,0CAA0Cuc,MAAM,EAAE,CAAC;IAC/D,MAAMva,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,gBAAgB8Y,MAAM,sBAAsB,CAAC;IAC5Exc,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAM2a,2BAA2B,GAAG,MAAAA,CAAOD,MAAM,EAAEE,cAAc,KAAK;EAC3E,IAAI;IACF1c,OAAO,CAACC,GAAG,CAAC,4CAA4Cuc,MAAM,EAAE,CAAC;IACjE,MAAMva,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,gBAAgBkZ,MAAM,sBAAsB,EAAEE,cAAc,CAAC;IAC7F1c,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM6a,2BAA2B,GAAG,MAAAA,CAAOH,MAAM,EAAEI,YAAY,EAAEF,cAAc,KAAK;EACzF,IAAI;IACF1c,OAAO,CAACC,GAAG,CAAC,4CAA4Cuc,MAAM,WAAWI,YAAY,EAAE,CAAC;IACxF,MAAM3a,QAAQ,GAAG,MAAMtB,GAAG,CAACgG,GAAG,CAAC,gBAAgB6V,MAAM,wBAAwBI,YAAY,EAAE,EAAEF,cAAc,CAAC;IAC5G1c,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM+a,2BAA2B,GAAG,MAAAA,CAAOL,MAAM,EAAEI,YAAY,KAAK;EACzE,IAAI;IACF5c,OAAO,CAACC,GAAG,CAAC,4CAA4Cuc,MAAM,WAAWI,YAAY,EAAE,CAAC;IACxF,MAAM3a,QAAQ,GAAG,MAAMtB,GAAG,CAACqG,MAAM,CAAC,gBAAgBwV,MAAM,wBAAwBI,YAAY,EAAE,CAAC;IAC/F5c,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMgb,8BAA8B,GAAG,MAAAA,CAAON,MAAM,EAAEO,eAAe,KAAK;EAC/E,IAAI;IACF/c,OAAO,CAACC,GAAG,CAAC,+CAA+Cuc,MAAM,EAAE,CAAC;IACpE,MAAMva,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,gBAAgBkZ,MAAM,4BAA4B,EAAE;MAClFQ,OAAO,EAAER,MAAM;MACfS,WAAW,EAAEF;IACf,CAAC,CAAC;IACF/c,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgC,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACrC,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMob,wBAAwB,GAAG,MAAOC,SAAS,IAAK;EAC3D,IAAI;IACFnd,OAAO,CAACC,GAAG,CAAC,yCAAyCkd,SAAS,EAAE,CAAC;IACjE,MAAMlb,QAAQ,GAAG,MAAMtB,GAAG,CAAC+C,GAAG,CAAC,oCAAoCyZ,SAAS,EAAE,CAAC;IAC/End,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,QAAQ,CAAC;IACpC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC,OAAO;MACLsb,UAAU,EAAED,SAAS;MACrBE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE;IACd,CAAC;EACH;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG,MAAOta,QAAQ,IAAK;EAC5C,IAAI;IACFnD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,MAAMgC,QAAQ,GAAG,MAAMtB,GAAG,CAAC2C,IAAI,CAAC,uBAAuB,EAAEH,QAAQ,EAAE;MACjErC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,OAAO,EAAE,MAAM,CAAE;IACnB,CAAC,CAAC;IACFf,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOH,KAAK,EAAE;IACd9B,OAAO,CAAC8B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;;IAEjC;IACA,IAAIA,KAAK,CAACiH,IAAI,KAAK,cAAc,IAAIjH,KAAK,CAACc,OAAO,CAACgI,QAAQ,CAAC,SAAS,CAAC,EAAE;MACtE,MAAM,IAAI/E,KAAK,CAAC,4DAA4D,CAAC;IAC/E;IAEA,MAAM/D,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}