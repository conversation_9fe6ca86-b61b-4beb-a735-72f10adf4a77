{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Effects,\n *   State,\n *   TokenType\n * } from 'micromark-util-types'\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/**\n * Parse titles.\n *\n * ###### Examples\n *\n * ```markdown\n * \"a\"\n * 'b'\n * (c)\n * \"a\n * b\"\n * 'a\n *     b'\n * (a\\)b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole title (`\"a\"`, `'b'`, `(c)`).\n * @param {TokenType} markerType\n *   Type for the markers (`\"`, `'`, `(`, and `)`).\n * @param {TokenType} stringType\n *   Type for the value (`a`).\n * @returns {State}\n *   Start state.\n */\nexport function factoryTitle(effects, ok, nok, type, markerType, stringType) {\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of title.\n   *\n   * ```markdown\n   * > | \"a\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.quotationMark || code === codes.apostrophe || code === codes.leftParenthesis) {\n      effects.enter(type);\n      effects.enter(markerType);\n      effects.consume(code);\n      effects.exit(markerType);\n      marker = code === codes.leftParenthesis ? codes.rightParenthesis : code;\n      return begin;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After opening marker.\n   *\n   * This is also used at the closing marker.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function begin(code) {\n    if (code === marker) {\n      effects.enter(markerType);\n      effects.consume(code);\n      effects.exit(markerType);\n      effects.exit(type);\n      return ok;\n    }\n    effects.enter(stringType);\n    return atBreak(code);\n  }\n\n  /**\n   * At something, before something else.\n   *\n   * ```markdown\n   * > | \"a\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.exit(stringType);\n      return begin(marker);\n    }\n    if (code === codes.eof) {\n      return nok(code);\n    }\n\n    // Note: blank lines can’t exist in content.\n    if (markdownLineEnding(code)) {\n      // To do: use `space_or_tab_eol_with_options`, connect.\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return factorySpace(effects, atBreak, types.linePrefix);\n    }\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return inside(code);\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker || code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      return atBreak(code);\n    }\n    effects.consume(code);\n    return code === codes.backslash ? escape : inside;\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | \"a\\*b\"\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function escape(code) {\n    if (code === marker || code === codes.backslash) {\n      effects.consume(code);\n      return inside;\n    }\n    return inside(code);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}