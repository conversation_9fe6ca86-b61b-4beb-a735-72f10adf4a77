{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { Form, Input, Button, Card, Typography, message, Spin, Alert, Select, Cascader, Tabs } from 'antd';\nimport { UserOutlined, LockOutlined, BankOutlined, SearchOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';\nimport { login as apiLogin, getRegions } from '../utils/api';\nimport { useAuth } from '../utils/auth';\nimport { getSystemInfo, logSystemInfo } from '../utils/detector';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [form] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [emailForm] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login,\n    user\n  } = useAuth();\n\n  // 学校选择相关状态\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [schools, setSchools] = useState([]);\n  const [searchSchools, setSearchSchools] = useState([]);\n  const [loadingProvinces, setLoadingProvinces] = useState(false);\n  const [loadingCities, setLoadingCities] = useState(false);\n  const [loadingDistricts, setLoadingDistricts] = useState(false);\n  const [loadingSchools, setLoadingSchools] = useState(false);\n  const [searchLoading, setSearchLoading] = useState(false);\n  const [selectedProvince, setSelectedProvince] = useState(null);\n  const [selectedCity, setSelectedCity] = useState(null);\n  const [selectedDistrict, setSelectedDistrict] = useState(null);\n  const [useSearch, setUseSearch] = useState(false);\n\n  // 登录方式状态\n  const [loginMethod, setLoginMethod] = useState('school'); // 'school', 'phone', 'email'\n\n  // 系统检测状态\n  const [systemInfo, setSystemInfo] = useState(null);\n\n  // Get the redirect path from location state or default to home\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n\n  // API调用函数\n  const fetchProvinces = async () => {\n    try {\n      setLoadingProvinces(true);\n      console.log('📍 获取省份列表...');\n\n      // 根据环境检测结果选择数据源\n      const currentSystemInfo = systemInfo || getSystemInfo();\n      if (currentSystemInfo.isLocal) {\n        console.log('🏠 本地环境：使用本地数据源');\n        // 使用本地数据\n        const regionsData = await getRegions();\n        console.log('✅ 获取到本地省份列表:', regionsData);\n        if (regionsData.provinces && regionsData.provinces.length > 0) {\n          const provinceOptions = regionsData.provinces.map(province => ({\n            value: province,\n            label: province\n          }));\n          setProvinces(provinceOptions);\n          console.log('✅ 设置省份选项:', provinceOptions.length, '个省份');\n        } else {\n          console.warn('⚠️ 未获取到本地省份数据');\n          message.warning('未获取到省份数据，请刷新页面重试');\n        }\n      } else {\n        console.log('🌐 远程环境：尝试使用远程API');\n        // 尝试使用远程API\n        try {\n          const response = await fetch('/api/public/provinces');\n          if (response.ok) {\n            const data = await response.json();\n            setProvinces(data);\n            console.log('✅ 获取到远程省份列表:', data.length, '个省份');\n          } else {\n            throw new Error('远程API响应失败');\n          }\n        } catch (apiError) {\n          console.warn('⚠️ 远程API失败，回退到本地数据:', apiError.message);\n          // 回退到本地数据\n          const regionsData = await getRegions();\n          if (regionsData.provinces && regionsData.provinces.length > 0) {\n            const provinceOptions = regionsData.provinces.map(province => ({\n              value: province,\n              label: province\n            }));\n            setProvinces(provinceOptions);\n            console.log('✅ 使用本地数据作为回退:', provinceOptions.length, '个省份');\n          }\n        }\n      }\n    } catch (error) {\n      console.error('❌ 获取省份失败:', error);\n      message.error('获取省份列表失败，请检查网络连接');\n    } finally {\n      setLoadingProvinces(false);\n    }\n  };\n  const fetchCities = async province => {\n    try {\n      setLoadingCities(true);\n      const response = await fetch(`/api/public/cities?province=${encodeURIComponent(province)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setCities(data);\n      } else {\n        message.error('获取城市列表失败');\n      }\n    } catch (error) {\n      console.error('获取城市失败:', error);\n      message.error('获取城市列表失败');\n    } finally {\n      setLoadingCities(false);\n    }\n  };\n  const fetchDistricts = async (province, city) => {\n    try {\n      setLoadingDistricts(true);\n      const response = await fetch(`/api/public/districts?province=${encodeURIComponent(province)}&city=${encodeURIComponent(city)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setDistricts(data);\n      } else {\n        message.error('获取区县列表失败');\n      }\n    } catch (error) {\n      console.error('获取区县失败:', error);\n      message.error('获取区县列表失败');\n    } finally {\n      setLoadingDistricts(false);\n    }\n  };\n  const fetchSchools = async (province, city, district = null) => {\n    try {\n      setLoadingSchools(true);\n      let url = `/api/public/schools?province=${encodeURIComponent(province)}&city=${encodeURIComponent(city)}`;\n      if (district) {\n        url += `&district=${encodeURIComponent(district)}`;\n      }\n      const response = await fetch(url);\n      if (response.ok) {\n        const data = await response.json();\n        setSchools(data);\n      } else {\n        message.error('获取学校列表失败');\n      }\n    } catch (error) {\n      console.error('获取学校失败:', error);\n      message.error('获取学校列表失败');\n    } finally {\n      setLoadingSchools(false);\n    }\n  };\n  const searchSchoolsByName = async searchText => {\n    if (!searchText || searchText.length < 2) {\n      setSearchSchools([]);\n      return;\n    }\n    try {\n      setSearchLoading(true);\n      const response = await fetch(`/api/public/schools/search?q=${encodeURIComponent(searchText)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setSearchSchools(data);\n      } else {\n        message.error('搜索学校失败');\n      }\n    } catch (error) {\n      console.error('搜索学校失败:', error);\n      message.error('搜索学校失败');\n    } finally {\n      setSearchLoading(false);\n    }\n  };\n\n  // 事件处理函数\n  const handleProvinceChange = value => {\n    setSelectedProvince(value);\n    setSelectedCity(null);\n    setSelectedDistrict(null);\n    setCities([]);\n    setDistricts([]);\n    setSchools([]);\n    form.setFieldsValue({\n      city: undefined,\n      district: undefined,\n      school_id: undefined\n    });\n    if (value) {\n      fetchCities(value);\n    }\n  };\n  const handleCityChange = value => {\n    setSelectedCity(value);\n    setSelectedDistrict(null);\n    setDistricts([]);\n    setSchools([]);\n    form.setFieldsValue({\n      district: undefined,\n      school_id: undefined\n    });\n    if (value && selectedProvince) {\n      fetchDistricts(selectedProvince, value);\n    }\n  };\n  const handleDistrictChange = value => {\n    setSelectedDistrict(value);\n    setSchools([]);\n    form.setFieldsValue({\n      school_id: undefined\n    });\n    if (selectedProvince && selectedCity) {\n      fetchSchools(selectedProvince, selectedCity, value);\n    }\n  };\n  const handleSearchModeToggle = () => {\n    setUseSearch(!useSearch);\n    form.setFieldsValue({\n      school_id: undefined\n    });\n    setSearchSchools([]);\n  };\n  const handleLoginMethodChange = method => {\n    setLoginMethod(method);\n    setError(null);\n\n    // 清除所有表单的错误状态\n    form.resetFields();\n    phoneForm.resetFields();\n    emailForm.resetFields();\n  };\n\n  // 系统自动检测\n  useEffect(() => {\n    console.log('🚀 登录页面加载，开始系统检测...');\n\n    // 获取系统信息\n    const info = logSystemInfo();\n    setSystemInfo(info);\n\n    // 根据检测结果调整页面行为\n    if (info.isLocal) {\n      console.log('✅ 检测到本地环境，使用本地数据源');\n    } else {\n      console.log('🌐 检测到远程环境，使用远程API');\n    }\n    if (info.isMobile) {\n      console.log('📱 检测到移动端设备，启用移动端优化');\n    } else {\n      console.log('💻 检测到桌面端设备，使用标准布局');\n    }\n\n    // 获取省份数据\n    fetchProvinces();\n  }, []);\n  React.useEffect(() => {\n    console.log('Login component mounted');\n    // 检查是否是从注册页面跳转过来的\n    const fromRegister = sessionStorage.getItem('from_register');\n\n    // 如果是从注册页面来的，不自动重定向，让用户手动登录\n    if (fromRegister) {\n      console.log('User coming from registration page, not auto-redirecting');\n      // 清除标记，以便下次正常工作\n      sessionStorage.removeItem('from_register');\n      return;\n    }\n\n    // 否则，如果已登录，则自动重定向\n    if (user) {\n      console.log('User already logged in, redirecting to:', from);\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [user, navigate, from]);\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      let values;\n      let currentForm;\n\n      // 根据登录方式选择对应的表单\n      if (loginMethod === 'school') {\n        currentForm = form;\n      } else if (loginMethod === 'phone') {\n        currentForm = phoneForm;\n      } else if (loginMethod === 'email') {\n        currentForm = emailForm;\n      }\n      values = await currentForm.validateFields();\n      console.log('Login: Submitting login request:', values, 'Method:', loginMethod);\n      console.log('Login: Current form:', currentForm === form ? 'school' : currentForm === phoneForm ? 'phone' : 'email');\n      let response;\n      if (loginMethod === 'school') {\n        // 学校+用户名登录\n        const formData = new FormData();\n        formData.append('school_id', values.school_id);\n        formData.append('username', values.username);\n        formData.append('password', values.password);\n        response = await fetch('/api/login-with-school', {\n          method: 'POST',\n          body: formData\n        });\n      } else if (loginMethod === 'phone') {\n        // 手机号登录\n        const formData = new FormData();\n        formData.append('username', values.phone);\n        formData.append('password', values.password);\n        response = await fetch('/api/login', {\n          method: 'POST',\n          body: formData\n        });\n      } else if (loginMethod === 'email') {\n        // 邮箱登录\n        const formData = new FormData();\n        formData.append('username', values.email);\n        formData.append('password', values.password);\n        response = await fetch('/api/login', {\n          method: 'POST',\n          body: formData\n        });\n      }\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '登录失败');\n      }\n      const responseData = await response.json();\n      console.log('Login: Response received:', responseData);\n      if (responseData && responseData.access_token) {\n        console.log('Login: Got access token:', responseData.access_token.substring(0, 20) + '...');\n\n        // 构建用户数据对象\n        const userData = {\n          id: responseData.user_id,\n          username: responseData.username,\n          is_teacher: responseData.is_teacher,\n          is_admin: responseData.is_admin,\n          role: responseData.role,\n          school_id: responseData.school_id,\n          school_name: responseData.school_name,\n          email: responseData.email,\n          full_name: responseData.full_name,\n          phone: responseData.phone,\n          subject: responseData.subject\n        };\n        console.log('Login: Built user data:', userData);\n\n        // 使用 AuthContext 的 login 方法\n        login(userData, responseData.access_token);\n        message.success('登录成功');\n\n        // 导航到来源页面或首页\n        console.log('Login: Redirecting to:', from);\n        navigate(from, {\n          replace: true\n        });\n      } else {\n        console.error('Login: Missing token in response:', responseData);\n        setError('登录失败: 服务器返回数据无效');\n        message.error('登录失败: 服务器返回数据无效');\n      }\n    } catch (error) {\n      console.error('Login: Error occurred:', error);\n      let errorMessage = '登录失败: 未知错误';\n      if (error.response) {\n        console.error('Login: Error response:', error.response.status, error.response.data);\n        errorMessage = `登录失败: ${error.response.status} - ${error.response.data.detail || '服务器错误'}`;\n      } else if (error.request) {\n        console.error('Login: No response error:', error.request);\n        errorMessage = '登录失败: 服务器没有响应，请确认后端服务是否运行';\n      } else {\n        console.error('Login: Request setup error:', error.message);\n        errorMessage = `登录失败: ${error.message}`;\n      }\n      setError(errorMessage);\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      background: 'linear-gradient(to right, #1890ff, #52c41a)'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        width: systemInfo !== null && systemInfo !== void 0 && systemInfo.isMobile ? '100%' : systemInfo !== null && systemInfo !== void 0 && systemInfo.isTablet ? 500 : 600,\n        maxWidth: '90vw',\n        padding: '20px'\n      },\n      bordered: false,\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 30\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            color: '#1890ff'\n          },\n          children: \"\\u51EF\\u6B4C\\u4F5C\\u4E1A\\u667A\\u6279\\u6539\\u7CFB\\u7EDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u8BF7\\u767B\\u5F55\\u60A8\\u7684\\u8D26\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading,\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u767B\\u5F55\\u9519\\u8BEF\",\n          description: error,\n          type: \"error\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: loginMethod,\n          onChange: handleLoginMethodChange,\n          centered: true,\n          style: {\n            marginBottom: 20\n          },\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 26\n              }, this), \"\\u5B66\\u6821\\u767B\\u5F55\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 20\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              name: \"school_login_form\",\n              initialValues: {\n                remember: true\n              },\n              onFinish: handleSubmit,\n              size: \"large\",\n              className: \"login-form\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 16\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  onClick: handleSearchModeToggle,\n                  style: {\n                    padding: 0,\n                    marginBottom: 8\n                  },\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this),\n                  children: useSearch ? '使用地区选择' : '直接搜索学校'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 13\n              }, this), !useSearch ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"province\",\n                  rules: [{\n                    required: true,\n                    message: '请选择省份!'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u7701\\u4EFD\",\n                    loading: loadingProvinces,\n                    onChange: handleProvinceChange,\n                    prefix: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 29\n                    }, this),\n                    children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n                      value: province.value,\n                      children: province.label\n                    }, province.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"city\",\n                  rules: [{\n                    required: true,\n                    message: '请选择城市!'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u57CE\\u5E02\",\n                    loading: loadingCities,\n                    onChange: handleCityChange,\n                    disabled: !selectedProvince,\n                    children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n                      value: city.value,\n                      children: city.label\n                    }, city.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"district\",\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u53BF\\uFF08\\u53EF\\u9009\\uFF09\",\n                    loading: loadingDistricts,\n                    onChange: handleDistrictChange,\n                    disabled: !selectedCity,\n                    allowClear: true,\n                    children: districts.map(district => /*#__PURE__*/_jsxDEV(Option, {\n                      value: district.value,\n                      children: district.label\n                    }, district.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"school_id\",\n                  rules: [{\n                    required: true,\n                    message: '请选择学校!'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u5B66\\u6821\",\n                    loading: loadingSchools,\n                    disabled: !selectedCity,\n                    children: schools.map(school => /*#__PURE__*/_jsxDEV(Option, {\n                      value: school.value,\n                      children: [school.label, school.address && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '12px',\n                          color: '#999'\n                        },\n                        children: school.address\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 542,\n                        columnNumber: 27\n                      }, this)]\n                    }, school.value, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"school_id\",\n                  rules: [{\n                    required: true,\n                    message: '请选择学校!'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    showSearch: true,\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B66\\u6821\\u540D\\u79F0\\u641C\\u7D22\",\n                    loading: searchLoading,\n                    onSearch: searchSchoolsByName,\n                    filterOption: false,\n                    notFoundContent: searchLoading ? /*#__PURE__*/_jsxDEV(Spin, {\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 564,\n                      columnNumber: 54\n                    }, this) : '未找到学校',\n                    children: searchSchools.map(school => /*#__PURE__*/_jsxDEV(Option, {\n                      value: school.value,\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: school.label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 569,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: '12px',\n                            color: '#999'\n                          },\n                          children: [school.province, \" \", school.city, \" \", school.district]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 570,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 568,\n                        columnNumber: 25\n                      }, this)\n                    }, school.value, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"username\",\n                rules: [{\n                  required: true,\n                  message: '请输入用户名!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 25\n                  }, this),\n                  placeholder: \"\\u7528\\u6237\\u540D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"password\",\n                rules: [{\n                  required: true,\n                  message: '请输入密码!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 25\n                  }, this),\n                  type: \"password\",\n                  placeholder: \"\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  className: \"login-form-button\",\n                  loading: loading,\n                  style: {\n                    width: '100%'\n                  },\n                  children: \"\\u767B\\u5F55\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)\n          }, \"school\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(PhoneOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 22\n              }, this), \"\\u624B\\u673A\\u767B\\u5F55\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 16\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              form: phoneForm,\n              name: \"phone_login_form\",\n              initialValues: {\n                remember: true\n              },\n              onFinish: handleSubmit,\n              size: \"large\",\n              className: \"login-form\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"phone\",\n                rules: [{\n                  required: true,\n                  message: '请输入手机号!'\n                }, {\n                  pattern: /^1[3-9]\\d{9}$/,\n                  message: '请输入正确的手机号!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(PhoneOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 25\n                  }, this),\n                  placeholder: \"\\u624B\\u673A\\u53F7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"password\",\n                rules: [{\n                  required: true,\n                  message: '请输入密码!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 25\n                  }, this),\n                  type: \"password\",\n                  placeholder: \"\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  className: \"login-form-button\",\n                  loading: loading,\n                  style: {\n                    width: '100%'\n                  },\n                  children: \"\\u767B\\u5F55\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 11\n            }, this)\n          }, \"phone\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 22\n              }, this), \"\\u90AE\\u7BB1\\u767B\\u5F55\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 16\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Form, {\n              form: emailForm,\n              name: \"email_login_form\",\n              initialValues: {\n                remember: true\n              },\n              onFinish: handleSubmit,\n              size: \"large\",\n              className: \"login-form\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"email\",\n                rules: [{\n                  required: true,\n                  message: '请输入邮箱!'\n                }, {\n                  type: 'email',\n                  message: '请输入正确的邮箱格式!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 25\n                  }, this),\n                  placeholder: \"\\u90AE\\u7BB1\\u5730\\u5740\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"password\",\n                rules: [{\n                  required: true,\n                  message: '请输入密码!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 25\n                  }, this),\n                  type: \"password\",\n                  placeholder: \"\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  className: \"login-form-button\",\n                  loading: loading,\n                  style: {\n                    width: '100%'\n                  },\n                  children: \"\\u767B\\u5F55\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 11\n            }, this)\n          }, \"email\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"register-link\",\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            children: \"\\u8FD8\\u6CA1\\u6709\\u8D26\\u53F7\\uFF1F\\u7ACB\\u5373\\u6CE8\\u518C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 30,\n          textAlign: 'center',\n          color: '#888'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u672C\\u5E94\\u7528\\u7531\\u51EF\\u6B4C\\u4E92\\u8054\\u5F00\\u53D1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 410,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"VnqtEtookAdYF061wZ3TILQlDZY=\", false, function () {\n  return [Form.useForm, Form.useForm, Form.useForm, useNavigate, useLocation, useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useLocation", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "message", "Spin", "<PERSON><PERSON>", "Select", "<PERSON>r", "Tabs", "UserOutlined", "LockOutlined", "BankOutlined", "SearchOutlined", "PhoneOutlined", "MailOutlined", "login", "api<PERSON><PERSON><PERSON>", "getRegions", "useAuth", "getSystemInfo", "logSystemInfo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Option", "TabPane", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "loading", "setLoading", "error", "setError", "form", "useForm", "phoneForm", "emailForm", "navigate", "location", "user", "provinces", "setProvinces", "cities", "setCities", "districts", "setDistricts", "schools", "setSchools", "searchSchools", "setSearchSchools", "loadingProvinces", "setLoadingProvinces", "loadingCities", "setLoadingCities", "loadingDistricts", "setLoadingDistricts", "loadingSchools", "setLoadingSchools", "searchLoading", "setSearchLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedProvince", "selectedCity", "setSelectedCity", "selectedDistrict", "setSelectedDistrict", "useSearch", "setUseSearch", "loginMethod", "setLoginMethod", "systemInfo", "setSystemInfo", "from", "state", "pathname", "fetchProvinces", "console", "log", "currentSystemInfo", "isLocal", "regionsData", "length", "provinceOptions", "map", "province", "value", "label", "warn", "warning", "response", "fetch", "ok", "data", "json", "Error", "apiError", "fetchCities", "encodeURIComponent", "fetchDistricts", "city", "fetchSchools", "district", "url", "searchSchoolsByName", "searchText", "handleProvinceChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "school_id", "handleCityChange", "handleDistrictChange", "handleSearchModeToggle", "handleLoginMethodChange", "method", "resetFields", "info", "isMobile", "fromRegister", "sessionStorage", "getItem", "removeItem", "replace", "handleSubmit", "values", "currentForm", "validateFields", "formData", "FormData", "append", "username", "password", "body", "phone", "email", "errorData", "detail", "responseData", "access_token", "substring", "userData", "id", "user_id", "is_teacher", "is_admin", "role", "school_name", "full_name", "subject", "success", "errorMessage", "status", "request", "style", "height", "display", "justifyContent", "alignItems", "background", "children", "width", "isTablet", "max<PERSON><PERSON><PERSON>", "padding", "bordered", "className", "textAlign", "marginBottom", "level", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "spinning", "description", "type", "showIcon", "active<PERSON><PERSON>", "onChange", "centered", "tab", "name", "initialValues", "remember", "onFinish", "size", "onClick", "icon", "<PERSON><PERSON>", "rules", "required", "placeholder", "prefix", "disabled", "allowClear", "school", "address", "fontSize", "showSearch", "onSearch", "filterOption", "notFoundContent", "htmlType", "pattern", "to", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { Form, Input, Button, Card, Typography, message, Spin, Alert, Select, Cascader, Tabs } from 'antd';\nimport { UserOutlined, LockOutlined, BankOutlined, SearchOutlined, PhoneOutlined, MailOutlined } from '@ant-design/icons';\nimport { login as apiLogin, getRegions } from '../utils/api';\nimport { useAuth } from '../utils/auth';\nimport { getSystemInfo, logSystemInfo } from '../utils/detector';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\nconst Login = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [form] = Form.useForm();\n  const [phoneForm] = Form.useForm();\n  const [emailForm] = Form.useForm();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login, user } = useAuth();\n\n  // 学校选择相关状态\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [schools, setSchools] = useState([]);\n  const [searchSchools, setSearchSchools] = useState([]);\n  const [loadingProvinces, setLoadingProvinces] = useState(false);\n  const [loadingCities, setLoadingCities] = useState(false);\n  const [loadingDistricts, setLoadingDistricts] = useState(false);\n  const [loadingSchools, setLoadingSchools] = useState(false);\n  const [searchLoading, setSearchLoading] = useState(false);\n  const [selectedProvince, setSelectedProvince] = useState(null);\n  const [selectedCity, setSelectedCity] = useState(null);\n  const [selectedDistrict, setSelectedDistrict] = useState(null);\n  const [useSearch, setUseSearch] = useState(false);\n\n  // 登录方式状态\n  const [loginMethod, setLoginMethod] = useState('school'); // 'school', 'phone', 'email'\n\n  // 系统检测状态\n  const [systemInfo, setSystemInfo] = useState(null);\n\n  // Get the redirect path from location state or default to home\n  const from = location.state?.from?.pathname || '/';\n\n  // API调用函数\n  const fetchProvinces = async () => {\n    try {\n      setLoadingProvinces(true);\n      console.log('📍 获取省份列表...');\n\n      // 根据环境检测结果选择数据源\n      const currentSystemInfo = systemInfo || getSystemInfo();\n\n      if (currentSystemInfo.isLocal) {\n        console.log('🏠 本地环境：使用本地数据源');\n        // 使用本地数据\n        const regionsData = await getRegions();\n        console.log('✅ 获取到本地省份列表:', regionsData);\n\n        if (regionsData.provinces && regionsData.provinces.length > 0) {\n          const provinceOptions = regionsData.provinces.map(province => ({\n            value: province,\n            label: province\n          }));\n          setProvinces(provinceOptions);\n          console.log('✅ 设置省份选项:', provinceOptions.length, '个省份');\n        } else {\n          console.warn('⚠️ 未获取到本地省份数据');\n          message.warning('未获取到省份数据，请刷新页面重试');\n        }\n      } else {\n        console.log('🌐 远程环境：尝试使用远程API');\n        // 尝试使用远程API\n        try {\n          const response = await fetch('/api/public/provinces');\n          if (response.ok) {\n            const data = await response.json();\n            setProvinces(data);\n            console.log('✅ 获取到远程省份列表:', data.length, '个省份');\n          } else {\n            throw new Error('远程API响应失败');\n          }\n        } catch (apiError) {\n          console.warn('⚠️ 远程API失败，回退到本地数据:', apiError.message);\n          // 回退到本地数据\n          const regionsData = await getRegions();\n          if (regionsData.provinces && regionsData.provinces.length > 0) {\n            const provinceOptions = regionsData.provinces.map(province => ({\n              value: province,\n              label: province\n            }));\n            setProvinces(provinceOptions);\n            console.log('✅ 使用本地数据作为回退:', provinceOptions.length, '个省份');\n          }\n        }\n      }\n    } catch (error) {\n      console.error('❌ 获取省份失败:', error);\n      message.error('获取省份列表失败，请检查网络连接');\n    } finally {\n      setLoadingProvinces(false);\n    }\n  };\n\n  const fetchCities = async (province) => {\n    try {\n      setLoadingCities(true);\n      const response = await fetch(`/api/public/cities?province=${encodeURIComponent(province)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setCities(data);\n      } else {\n        message.error('获取城市列表失败');\n      }\n    } catch (error) {\n      console.error('获取城市失败:', error);\n      message.error('获取城市列表失败');\n    } finally {\n      setLoadingCities(false);\n    }\n  };\n\n  const fetchDistricts = async (province, city) => {\n    try {\n      setLoadingDistricts(true);\n      const response = await fetch(`/api/public/districts?province=${encodeURIComponent(province)}&city=${encodeURIComponent(city)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setDistricts(data);\n      } else {\n        message.error('获取区县列表失败');\n      }\n    } catch (error) {\n      console.error('获取区县失败:', error);\n      message.error('获取区县列表失败');\n    } finally {\n      setLoadingDistricts(false);\n    }\n  };\n\n  const fetchSchools = async (province, city, district = null) => {\n    try {\n      setLoadingSchools(true);\n      let url = `/api/public/schools?province=${encodeURIComponent(province)}&city=${encodeURIComponent(city)}`;\n      if (district) {\n        url += `&district=${encodeURIComponent(district)}`;\n      }\n      const response = await fetch(url);\n      if (response.ok) {\n        const data = await response.json();\n        setSchools(data);\n      } else {\n        message.error('获取学校列表失败');\n      }\n    } catch (error) {\n      console.error('获取学校失败:', error);\n      message.error('获取学校列表失败');\n    } finally {\n      setLoadingSchools(false);\n    }\n  };\n\n  const searchSchoolsByName = async (searchText) => {\n    if (!searchText || searchText.length < 2) {\n      setSearchSchools([]);\n      return;\n    }\n\n    try {\n      setSearchLoading(true);\n      const response = await fetch(`/api/public/schools/search?q=${encodeURIComponent(searchText)}`);\n      if (response.ok) {\n        const data = await response.json();\n        setSearchSchools(data);\n      } else {\n        message.error('搜索学校失败');\n      }\n    } catch (error) {\n      console.error('搜索学校失败:', error);\n      message.error('搜索学校失败');\n    } finally {\n      setSearchLoading(false);\n    }\n  };\n\n  // 事件处理函数\n  const handleProvinceChange = (value) => {\n    setSelectedProvince(value);\n    setSelectedCity(null);\n    setSelectedDistrict(null);\n    setCities([]);\n    setDistricts([]);\n    setSchools([]);\n    form.setFieldsValue({ city: undefined, district: undefined, school_id: undefined });\n\n    if (value) {\n      fetchCities(value);\n    }\n  };\n\n  const handleCityChange = (value) => {\n    setSelectedCity(value);\n    setSelectedDistrict(null);\n    setDistricts([]);\n    setSchools([]);\n    form.setFieldsValue({ district: undefined, school_id: undefined });\n\n    if (value && selectedProvince) {\n      fetchDistricts(selectedProvince, value);\n    }\n  };\n\n  const handleDistrictChange = (value) => {\n    setSelectedDistrict(value);\n    setSchools([]);\n    form.setFieldsValue({ school_id: undefined });\n\n    if (selectedProvince && selectedCity) {\n      fetchSchools(selectedProvince, selectedCity, value);\n    }\n  };\n\n  const handleSearchModeToggle = () => {\n    setUseSearch(!useSearch);\n    form.setFieldsValue({ school_id: undefined });\n    setSearchSchools([]);\n  };\n\n  const handleLoginMethodChange = (method) => {\n    setLoginMethod(method);\n    setError(null);\n\n    // 清除所有表单的错误状态\n    form.resetFields();\n    phoneForm.resetFields();\n    emailForm.resetFields();\n  };\n\n  // 系统自动检测\n  useEffect(() => {\n    console.log('🚀 登录页面加载，开始系统检测...');\n\n    // 获取系统信息\n    const info = logSystemInfo();\n    setSystemInfo(info);\n\n    // 根据检测结果调整页面行为\n    if (info.isLocal) {\n      console.log('✅ 检测到本地环境，使用本地数据源');\n    } else {\n      console.log('🌐 检测到远程环境，使用远程API');\n    }\n\n    if (info.isMobile) {\n      console.log('📱 检测到移动端设备，启用移动端优化');\n    } else {\n      console.log('💻 检测到桌面端设备，使用标准布局');\n    }\n\n    // 获取省份数据\n    fetchProvinces();\n  }, []);\n\n  React.useEffect(() => {\n    console.log('Login component mounted');\n    // 检查是否是从注册页面跳转过来的\n    const fromRegister = sessionStorage.getItem('from_register');\n    \n    // 如果是从注册页面来的，不自动重定向，让用户手动登录\n    if (fromRegister) {\n      console.log('User coming from registration page, not auto-redirecting');\n      // 清除标记，以便下次正常工作\n      sessionStorage.removeItem('from_register');\n      return;\n    }\n    \n    // 否则，如果已登录，则自动重定向\n    if (user) {\n      console.log('User already logged in, redirecting to:', from);\n      navigate(from, { replace: true });\n    }\n  }, [user, navigate, from]);\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let values;\n      let currentForm;\n\n      // 根据登录方式选择对应的表单\n      if (loginMethod === 'school') {\n        currentForm = form;\n      } else if (loginMethod === 'phone') {\n        currentForm = phoneForm;\n      } else if (loginMethod === 'email') {\n        currentForm = emailForm;\n      }\n\n      values = await currentForm.validateFields();\n\n      console.log('Login: Submitting login request:', values, 'Method:', loginMethod);\n      console.log('Login: Current form:', currentForm === form ? 'school' : currentForm === phoneForm ? 'phone' : 'email');\n\n      let response;\n\n      if (loginMethod === 'school') {\n        // 学校+用户名登录\n        const formData = new FormData();\n        formData.append('school_id', values.school_id);\n        formData.append('username', values.username);\n        formData.append('password', values.password);\n\n        response = await fetch('/api/login-with-school', {\n          method: 'POST',\n          body: formData\n        });\n      } else if (loginMethod === 'phone') {\n        // 手机号登录\n        const formData = new FormData();\n        formData.append('username', values.phone);\n        formData.append('password', values.password);\n\n        response = await fetch('/api/login', {\n          method: 'POST',\n          body: formData\n        });\n      } else if (loginMethod === 'email') {\n        // 邮箱登录\n        const formData = new FormData();\n        formData.append('username', values.email);\n        formData.append('password', values.password);\n\n        response = await fetch('/api/login', {\n          method: 'POST',\n          body: formData\n        });\n      }\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '登录失败');\n      }\n\n      const responseData = await response.json();\n\n      console.log('Login: Response received:', responseData);\n\n      if (responseData && responseData.access_token) {\n        console.log('Login: Got access token:', responseData.access_token.substring(0, 20) + '...');\n\n        // 构建用户数据对象\n        const userData = {\n          id: responseData.user_id,\n          username: responseData.username,\n          is_teacher: responseData.is_teacher,\n          is_admin: responseData.is_admin,\n          role: responseData.role,\n          school_id: responseData.school_id,\n          school_name: responseData.school_name,\n          email: responseData.email,\n          full_name: responseData.full_name,\n          phone: responseData.phone,\n          subject: responseData.subject\n        };\n\n        console.log('Login: Built user data:', userData);\n\n        // 使用 AuthContext 的 login 方法\n        login(userData, responseData.access_token);\n        \n        message.success('登录成功');\n        \n        // 导航到来源页面或首页\n        console.log('Login: Redirecting to:', from);\n        navigate(from, { replace: true });\n      } else {\n        console.error('Login: Missing token in response:', responseData);\n        setError('登录失败: 服务器返回数据无效');\n        message.error('登录失败: 服务器返回数据无效');\n      }\n    } catch (error) {\n      console.error('Login: Error occurred:', error);\n      \n      let errorMessage = '登录失败: 未知错误';\n      \n      if (error.response) {\n        console.error('Login: Error response:', error.response.status, error.response.data);\n        errorMessage = `登录失败: ${error.response.status} - ${error.response.data.detail || '服务器错误'}`;\n      } else if (error.request) {\n        console.error('Login: No response error:', error.request);\n        errorMessage = '登录失败: 服务器没有响应，请确认后端服务是否运行';\n      } else {\n        console.error('Login: Request setup error:', error.message);\n        errorMessage = `登录失败: ${error.message}`;\n      }\n      \n      setError(errorMessage);\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      height: '100vh', \n      display: 'flex', \n      justifyContent: 'center', \n      alignItems: 'center',\n      background: 'linear-gradient(to right, #1890ff, #52c41a)'\n    }}>\n      <Card\n        style={{\n          width: systemInfo?.isMobile ? '100%' : systemInfo?.isTablet ? 500 : 600,\n          maxWidth: '90vw',\n          padding: '20px'\n        }}\n        bordered={false}\n        className=\"login-card\"\n      >\n        <div style={{ textAlign: 'center', marginBottom: 30 }}>\n          <Title level={2} style={{ color: '#1890ff' }}>凯歌作业智批改系统</Title>\n          <Title level={4}>请登录您的账户</Title>\n\n\n        </div>\n        \n        <Spin spinning={loading}>\n          {error && (\n            <Alert\n              message=\"登录错误\"\n              description={error}\n              type=\"error\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n          \n          <Tabs\n            activeKey={loginMethod}\n            onChange={handleLoginMethodChange}\n            centered\n            style={{ marginBottom: 20 }}\n          >\n            <TabPane\n              tab={<span><BankOutlined />学校登录</span>}\n              key=\"school\"\n            >\n              <Form\n                form={form}\n                name=\"school_login_form\"\n                initialValues={{ remember: true }}\n                onFinish={handleSubmit}\n                size=\"large\"\n                className=\"login-form\"\n              >\n                {/* 学校选择部分 */}\n            <div style={{ marginBottom: 16 }}>\n              <Button\n                type=\"link\"\n                onClick={handleSearchModeToggle}\n                style={{ padding: 0, marginBottom: 8 }}\n                icon={<SearchOutlined />}\n              >\n                {useSearch ? '使用地区选择' : '直接搜索学校'}\n              </Button>\n            </div>\n\n            {!useSearch ? (\n              <>\n                {/* 分级选择模式 */}\n                <Form.Item\n                  name=\"province\"\n                  rules={[{ required: true, message: '请选择省份!' }]}\n                >\n                  <Select\n                    placeholder=\"请选择省份\"\n                    loading={loadingProvinces}\n                    onChange={handleProvinceChange}\n                    prefix={<BankOutlined />}\n                  >\n                    {provinces.map(province => (\n                      <Option key={province.value} value={province.value}>\n                        {province.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n\n                <Form.Item\n                  name=\"city\"\n                  rules={[{ required: true, message: '请选择城市!' }]}\n                >\n                  <Select\n                    placeholder=\"请选择城市\"\n                    loading={loadingCities}\n                    onChange={handleCityChange}\n                    disabled={!selectedProvince}\n                  >\n                    {cities.map(city => (\n                      <Option key={city.value} value={city.value}>\n                        {city.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n\n                <Form.Item name=\"district\">\n                  <Select\n                    placeholder=\"请选择区县（可选）\"\n                    loading={loadingDistricts}\n                    onChange={handleDistrictChange}\n                    disabled={!selectedCity}\n                    allowClear\n                  >\n                    {districts.map(district => (\n                      <Option key={district.value} value={district.value}>\n                        {district.label}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n\n                <Form.Item\n                  name=\"school_id\"\n                  rules={[{ required: true, message: '请选择学校!' }]}\n                >\n                  <Select\n                    placeholder=\"请选择学校\"\n                    loading={loadingSchools}\n                    disabled={!selectedCity}\n                  >\n                    {schools.map(school => (\n                      <Option key={school.value} value={school.value}>\n                        {school.label}\n                        {school.address && (\n                          <div style={{ fontSize: '12px', color: '#999' }}>\n                            {school.address}\n                          </div>\n                        )}\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </>\n            ) : (\n              <>\n                {/* 搜索模式 */}\n                <Form.Item\n                  name=\"school_id\"\n                  rules={[{ required: true, message: '请选择学校!' }]}\n                >\n                  <Select\n                    showSearch\n                    placeholder=\"请输入学校名称搜索\"\n                    loading={searchLoading}\n                    onSearch={searchSchoolsByName}\n                    filterOption={false}\n                    notFoundContent={searchLoading ? <Spin size=\"small\" /> : '未找到学校'}\n                  >\n                    {searchSchools.map(school => (\n                      <Option key={school.value} value={school.value}>\n                        <div>\n                          <div>{school.label}</div>\n                          <div style={{ fontSize: '12px', color: '#999' }}>\n                            {school.province} {school.city} {school.district}\n                          </div>\n                        </div>\n                      </Option>\n                    ))}\n                  </Select>\n                </Form.Item>\n              </>\n            )}\n\n            <Form.Item\n              name=\"username\"\n              rules={[{ required: true, message: '请输入用户名!' }]}\n            >\n              <Input \n                prefix={<UserOutlined className=\"site-form-item-icon\" />} \n                placeholder=\"用户名\" \n              />\n            </Form.Item>\n            \n            <Form.Item\n              name=\"password\"\n              rules={[{ required: true, message: '请输入密码!' }]}\n            >\n              <Input\n                prefix={<LockOutlined className=\"site-form-item-icon\" />}\n                type=\"password\"\n                placeholder=\"密码\"\n              />\n            </Form.Item>\n            \n            <Form.Item>\n              <Button \n                type=\"primary\" \n                htmlType=\"submit\" \n                className=\"login-form-button\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                登录\n              </Button>\n            </Form.Item>\n          </Form>\n        </TabPane>\n\n        <TabPane\n          tab={<span><PhoneOutlined />手机登录</span>}\n          key=\"phone\"\n        >\n          <Form\n            form={phoneForm}\n            name=\"phone_login_form\"\n            initialValues={{ remember: true }}\n            onFinish={handleSubmit}\n            size=\"large\"\n            className=\"login-form\"\n          >\n            <Form.Item\n              name=\"phone\"\n              rules={[\n                { required: true, message: '请输入手机号!' },\n                { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号!' }\n              ]}\n            >\n              <Input\n                prefix={<PhoneOutlined className=\"site-form-item-icon\" />}\n                placeholder=\"手机号\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[{ required: true, message: '请输入密码!' }]}\n            >\n              <Input\n                prefix={<LockOutlined className=\"site-form-item-icon\" />}\n                type=\"password\"\n                placeholder=\"密码\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                className=\"login-form-button\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                登录\n              </Button>\n            </Form.Item>\n          </Form>\n        </TabPane>\n\n        <TabPane\n          tab={<span><MailOutlined />邮箱登录</span>}\n          key=\"email\"\n        >\n          <Form\n            form={emailForm}\n            name=\"email_login_form\"\n            initialValues={{ remember: true }}\n            onFinish={handleSubmit}\n            size=\"large\"\n            className=\"login-form\"\n          >\n            <Form.Item\n              name=\"email\"\n              rules={[\n                { required: true, message: '请输入邮箱!' },\n                { type: 'email', message: '请输入正确的邮箱格式!' }\n              ]}\n            >\n              <Input\n                prefix={<MailOutlined className=\"site-form-item-icon\" />}\n                placeholder=\"邮箱地址\"\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[{ required: true, message: '请输入密码!' }]}\n            >\n              <Input\n                prefix={<LockOutlined className=\"site-form-item-icon\" />}\n                type=\"password\"\n                placeholder=\"密码\"\n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                className=\"login-form-button\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                登录\n              </Button>\n            </Form.Item>\n          </Form>\n        </TabPane>\n      </Tabs>\n\n          <div className=\"register-link\" style={{ textAlign: 'center' }}>\n            <Link to=\"/register\">还没有账号？立即注册</Link>\n          </div>\n        </Spin>\n        \n        <div style={{ marginTop: 30, textAlign: 'center', color: '#888' }}>\n          <p>本应用由凯歌互联开发</p>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default Login; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,MAAM;AAC1G,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,QAAQ,mBAAmB;AACzH,SAASC,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAC5D,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,aAAa,EAAEC,aAAa,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjE,MAAM;EAAEC;AAAM,CAAC,GAAGvB,UAAU;AAC5B,MAAM;EAAEwB;AAAO,CAAC,GAAGpB,MAAM;AACzB,MAAM;EAAEqB;AAAQ,CAAC,GAAGnB,IAAI;AAExB,MAAMoB,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2C,IAAI,CAAC,GAAGtC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,SAAS,CAAC,GAAGxC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAClC,MAAM,CAACE,SAAS,CAAC,GAAGzC,IAAI,CAACuC,OAAO,CAAC,CAAC;EAClC,MAAMG,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,KAAK;IAAE2B;EAAK,CAAC,GAAGxB,OAAO,CAAC,CAAC;;EAEjC;EACA,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAMkF,IAAI,GAAG,EAAA7C,eAAA,GAAAW,QAAQ,CAACmC,KAAK,cAAA9C,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB6C,IAAI,cAAA5C,oBAAA,uBAApBA,oBAAA,CAAsB8C,QAAQ,KAAI,GAAG;;EAElD;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFxB,mBAAmB,CAAC,IAAI,CAAC;MACzByB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;;MAE3B;MACA,MAAMC,iBAAiB,GAAGR,UAAU,IAAItD,aAAa,CAAC,CAAC;MAEvD,IAAI8D,iBAAiB,CAACC,OAAO,EAAE;QAC7BH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAC9B;QACA,MAAMG,WAAW,GAAG,MAAMlE,UAAU,CAAC,CAAC;QACtC8D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,WAAW,CAAC;QAExC,IAAIA,WAAW,CAACxC,SAAS,IAAIwC,WAAW,CAACxC,SAAS,CAACyC,MAAM,GAAG,CAAC,EAAE;UAC7D,MAAMC,eAAe,GAAGF,WAAW,CAACxC,SAAS,CAAC2C,GAAG,CAACC,QAAQ,KAAK;YAC7DC,KAAK,EAAED,QAAQ;YACfE,KAAK,EAAEF;UACT,CAAC,CAAC,CAAC;UACH3C,YAAY,CAACyC,eAAe,CAAC;UAC7BN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,eAAe,CAACD,MAAM,EAAE,KAAK,CAAC;QACzD,CAAC,MAAM;UACLL,OAAO,CAACW,IAAI,CAAC,eAAe,CAAC;UAC7BvF,OAAO,CAACwF,OAAO,CAAC,kBAAkB,CAAC;QACrC;MACF,CAAC,MAAM;QACLZ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC;QACA,IAAI;UACF,MAAMY,QAAQ,GAAG,MAAMC,KAAK,CAAC,uBAAuB,CAAC;UACrD,IAAID,QAAQ,CAACE,EAAE,EAAE;YACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;YAClCpD,YAAY,CAACmD,IAAI,CAAC;YAClBhB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEe,IAAI,CAACX,MAAM,EAAE,KAAK,CAAC;UACjD,CAAC,MAAM;YACL,MAAM,IAAIa,KAAK,CAAC,WAAW,CAAC;UAC9B;QACF,CAAC,CAAC,OAAOC,QAAQ,EAAE;UACjBnB,OAAO,CAACW,IAAI,CAAC,qBAAqB,EAAEQ,QAAQ,CAAC/F,OAAO,CAAC;UACrD;UACA,MAAMgF,WAAW,GAAG,MAAMlE,UAAU,CAAC,CAAC;UACtC,IAAIkE,WAAW,CAACxC,SAAS,IAAIwC,WAAW,CAACxC,SAAS,CAACyC,MAAM,GAAG,CAAC,EAAE;YAC7D,MAAMC,eAAe,GAAGF,WAAW,CAACxC,SAAS,CAAC2C,GAAG,CAACC,QAAQ,KAAK;cAC7DC,KAAK,EAAED,QAAQ;cACfE,KAAK,EAAEF;YACT,CAAC,CAAC,CAAC;YACH3C,YAAY,CAACyC,eAAe,CAAC;YAC7BN,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEK,eAAe,CAACD,MAAM,EAAE,KAAK,CAAC;UAC7D;QACF;MACF;IACF,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC/B,OAAO,CAAC+B,KAAK,CAAC,kBAAkB,CAAC;IACnC,CAAC,SAAS;MACRoB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAM6C,WAAW,GAAG,MAAOZ,QAAQ,IAAK;IACtC,IAAI;MACF/B,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMoC,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+BO,kBAAkB,CAACb,QAAQ,CAAC,EAAE,CAAC;MAC3F,IAAIK,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClClD,SAAS,CAACiD,IAAI,CAAC;MACjB,CAAC,MAAM;QACL5F,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRsB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM6C,cAAc,GAAG,MAAAA,CAAOd,QAAQ,EAAEe,IAAI,KAAK;IAC/C,IAAI;MACF5C,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMkC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkCO,kBAAkB,CAACb,QAAQ,CAAC,SAASa,kBAAkB,CAACE,IAAI,CAAC,EAAE,CAAC;MAC/H,IAAIV,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClChD,YAAY,CAAC+C,IAAI,CAAC;MACpB,CAAC,MAAM;QACL5F,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRwB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAM6C,YAAY,GAAG,MAAAA,CAAOhB,QAAQ,EAAEe,IAAI,EAAEE,QAAQ,GAAG,IAAI,KAAK;IAC9D,IAAI;MACF5C,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAI6C,GAAG,GAAG,gCAAgCL,kBAAkB,CAACb,QAAQ,CAAC,SAASa,kBAAkB,CAACE,IAAI,CAAC,EAAE;MACzG,IAAIE,QAAQ,EAAE;QACZC,GAAG,IAAI,aAAaL,kBAAkB,CAACI,QAAQ,CAAC,EAAE;MACpD;MACA,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAACY,GAAG,CAAC;MACjC,IAAIb,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC9C,UAAU,CAAC6C,IAAI,CAAC;MAClB,CAAC,MAAM;QACL5F,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR0B,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM8C,mBAAmB,GAAG,MAAOC,UAAU,IAAK;IAChD,IAAI,CAACA,UAAU,IAAIA,UAAU,CAACvB,MAAM,GAAG,CAAC,EAAE;MACxChC,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEA,IAAI;MACFU,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAM8B,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgCO,kBAAkB,CAACO,UAAU,CAAC,EAAE,CAAC;MAC9F,IAAIf,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC5C,gBAAgB,CAAC2C,IAAI,CAAC;MACxB,CAAC,MAAM;QACL5F,OAAO,CAAC+B,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/B,OAAO,CAAC+B,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACR4B,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8C,oBAAoB,GAAIpB,KAAK,IAAK;IACtCxB,mBAAmB,CAACwB,KAAK,CAAC;IAC1BtB,eAAe,CAAC,IAAI,CAAC;IACrBE,mBAAmB,CAAC,IAAI,CAAC;IACzBtB,SAAS,CAAC,EAAE,CAAC;IACbE,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdd,IAAI,CAACyE,cAAc,CAAC;MAAEP,IAAI,EAAEQ,SAAS;MAAEN,QAAQ,EAAEM,SAAS;MAAEC,SAAS,EAAED;IAAU,CAAC,CAAC;IAEnF,IAAItB,KAAK,EAAE;MACTW,WAAW,CAACX,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMwB,gBAAgB,GAAIxB,KAAK,IAAK;IAClCtB,eAAe,CAACsB,KAAK,CAAC;IACtBpB,mBAAmB,CAAC,IAAI,CAAC;IACzBpB,YAAY,CAAC,EAAE,CAAC;IAChBE,UAAU,CAAC,EAAE,CAAC;IACdd,IAAI,CAACyE,cAAc,CAAC;MAAEL,QAAQ,EAAEM,SAAS;MAAEC,SAAS,EAAED;IAAU,CAAC,CAAC;IAElE,IAAItB,KAAK,IAAIzB,gBAAgB,EAAE;MAC7BsC,cAAc,CAACtC,gBAAgB,EAAEyB,KAAK,CAAC;IACzC;EACF,CAAC;EAED,MAAMyB,oBAAoB,GAAIzB,KAAK,IAAK;IACtCpB,mBAAmB,CAACoB,KAAK,CAAC;IAC1BtC,UAAU,CAAC,EAAE,CAAC;IACdd,IAAI,CAACyE,cAAc,CAAC;MAAEE,SAAS,EAAED;IAAU,CAAC,CAAC;IAE7C,IAAI/C,gBAAgB,IAAIE,YAAY,EAAE;MACpCsC,YAAY,CAACxC,gBAAgB,EAAEE,YAAY,EAAEuB,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAM0B,sBAAsB,GAAGA,CAAA,KAAM;IACnC5C,YAAY,CAAC,CAACD,SAAS,CAAC;IACxBjC,IAAI,CAACyE,cAAc,CAAC;MAAEE,SAAS,EAAED;IAAU,CAAC,CAAC;IAC7C1D,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAM+D,uBAAuB,GAAIC,MAAM,IAAK;IAC1C5C,cAAc,CAAC4C,MAAM,CAAC;IACtBjF,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACAC,IAAI,CAACiF,WAAW,CAAC,CAAC;IAClB/E,SAAS,CAAC+E,WAAW,CAAC,CAAC;IACvB9E,SAAS,CAAC8E,WAAW,CAAC,CAAC;EACzB,CAAC;;EAED;EACA3H,SAAS,CAAC,MAAM;IACdqF,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;;IAElC;IACA,MAAMsC,IAAI,GAAGlG,aAAa,CAAC,CAAC;IAC5BsD,aAAa,CAAC4C,IAAI,CAAC;;IAEnB;IACA,IAAIA,IAAI,CAACpC,OAAO,EAAE;MAChBH,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;IAEA,IAAIsC,IAAI,CAACC,QAAQ,EAAE;MACjBxC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC,MAAM;MACLD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;;IAEA;IACAF,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENtF,KAAK,CAACE,SAAS,CAAC,MAAM;IACpBqF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtC;IACA,MAAMwC,YAAY,GAAGC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC;;IAE5D;IACA,IAAIF,YAAY,EAAE;MAChBzC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;MACAyC,cAAc,CAACE,UAAU,CAAC,eAAe,CAAC;MAC1C;IACF;;IAEA;IACA,IAAIjF,IAAI,EAAE;MACRqC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEL,IAAI,CAAC;MAC5DnC,QAAQ,CAACmC,IAAI,EAAE;QAAEiD,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAAClF,IAAI,EAAEF,QAAQ,EAAEmC,IAAI,CAAC,CAAC;EAE1B,MAAMkD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF5F,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI2F,MAAM;MACV,IAAIC,WAAW;;MAEf;MACA,IAAIxD,WAAW,KAAK,QAAQ,EAAE;QAC5BwD,WAAW,GAAG3F,IAAI;MACpB,CAAC,MAAM,IAAImC,WAAW,KAAK,OAAO,EAAE;QAClCwD,WAAW,GAAGzF,SAAS;MACzB,CAAC,MAAM,IAAIiC,WAAW,KAAK,OAAO,EAAE;QAClCwD,WAAW,GAAGxF,SAAS;MACzB;MAEAuF,MAAM,GAAG,MAAMC,WAAW,CAACC,cAAc,CAAC,CAAC;MAE3CjD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE8C,MAAM,EAAE,SAAS,EAAEvD,WAAW,CAAC;MAC/EQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+C,WAAW,KAAK3F,IAAI,GAAG,QAAQ,GAAG2F,WAAW,KAAKzF,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;MAEpH,IAAIsD,QAAQ;MAEZ,IAAIrB,WAAW,KAAK,QAAQ,EAAE;QAC5B;QACA,MAAM0D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEL,MAAM,CAACf,SAAS,CAAC;QAC9CkB,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEL,MAAM,CAACM,QAAQ,CAAC;QAC5CH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEL,MAAM,CAACO,QAAQ,CAAC;QAE5CzC,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,EAAE;UAC/CuB,MAAM,EAAE,MAAM;UACdkB,IAAI,EAAEL;QACR,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI1D,WAAW,KAAK,OAAO,EAAE;QAClC;QACA,MAAM0D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEL,MAAM,CAACS,KAAK,CAAC;QACzCN,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEL,MAAM,CAACO,QAAQ,CAAC;QAE5CzC,QAAQ,GAAG,MAAMC,KAAK,CAAC,YAAY,EAAE;UACnCuB,MAAM,EAAE,MAAM;UACdkB,IAAI,EAAEL;QACR,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI1D,WAAW,KAAK,OAAO,EAAE;QAClC;QACA,MAAM0D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEL,MAAM,CAACU,KAAK,CAAC;QACzCP,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEL,MAAM,CAACO,QAAQ,CAAC;QAE5CzC,QAAQ,GAAG,MAAMC,KAAK,CAAC,YAAY,EAAE;UACnCuB,MAAM,EAAE,MAAM;UACdkB,IAAI,EAAEL;QACR,CAAC,CAAC;MACJ;MAEA,IAAI,CAACrC,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM2C,SAAS,GAAG,MAAM7C,QAAQ,CAACI,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIC,KAAK,CAACwC,SAAS,CAACC,MAAM,IAAI,MAAM,CAAC;MAC7C;MAEA,MAAMC,YAAY,GAAG,MAAM/C,QAAQ,CAACI,IAAI,CAAC,CAAC;MAE1CjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE2D,YAAY,CAAC;MAEtD,IAAIA,YAAY,IAAIA,YAAY,CAACC,YAAY,EAAE;QAC7C7D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE2D,YAAY,CAACC,YAAY,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;;QAE3F;QACA,MAAMC,QAAQ,GAAG;UACfC,EAAE,EAAEJ,YAAY,CAACK,OAAO;UACxBZ,QAAQ,EAAEO,YAAY,CAACP,QAAQ;UAC/Ba,UAAU,EAAEN,YAAY,CAACM,UAAU;UACnCC,QAAQ,EAAEP,YAAY,CAACO,QAAQ;UAC/BC,IAAI,EAAER,YAAY,CAACQ,IAAI;UACvBpC,SAAS,EAAE4B,YAAY,CAAC5B,SAAS;UACjCqC,WAAW,EAAET,YAAY,CAACS,WAAW;UACrCZ,KAAK,EAAEG,YAAY,CAACH,KAAK;UACzBa,SAAS,EAAEV,YAAY,CAACU,SAAS;UACjCd,KAAK,EAAEI,YAAY,CAACJ,KAAK;UACzBe,OAAO,EAAEX,YAAY,CAACW;QACxB,CAAC;QAEDvE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE8D,QAAQ,CAAC;;QAEhD;QACA/H,KAAK,CAAC+H,QAAQ,EAAEH,YAAY,CAACC,YAAY,CAAC;QAE1CzI,OAAO,CAACoJ,OAAO,CAAC,MAAM,CAAC;;QAEvB;QACAxE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEL,IAAI,CAAC;QAC3CnC,QAAQ,CAACmC,IAAI,EAAE;UAAEiD,OAAO,EAAE;QAAK,CAAC,CAAC;MACnC,CAAC,MAAM;QACL7C,OAAO,CAAC7C,KAAK,CAAC,mCAAmC,EAAEyG,YAAY,CAAC;QAChExG,QAAQ,CAAC,iBAAiB,CAAC;QAC3BhC,OAAO,CAAC+B,KAAK,CAAC,iBAAiB,CAAC;MAClC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,IAAIsH,YAAY,GAAG,YAAY;MAE/B,IAAItH,KAAK,CAAC0D,QAAQ,EAAE;QAClBb,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC0D,QAAQ,CAAC6D,MAAM,EAAEvH,KAAK,CAAC0D,QAAQ,CAACG,IAAI,CAAC;QACnFyD,YAAY,GAAG,SAAStH,KAAK,CAAC0D,QAAQ,CAAC6D,MAAM,MAAMvH,KAAK,CAAC0D,QAAQ,CAACG,IAAI,CAAC2C,MAAM,IAAI,OAAO,EAAE;MAC5F,CAAC,MAAM,IAAIxG,KAAK,CAACwH,OAAO,EAAE;QACxB3E,OAAO,CAAC7C,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACwH,OAAO,CAAC;QACzDF,YAAY,GAAG,2BAA2B;MAC5C,CAAC,MAAM;QACLzE,OAAO,CAAC7C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC/B,OAAO,CAAC;QAC3DqJ,YAAY,GAAG,SAAStH,KAAK,CAAC/B,OAAO,EAAE;MACzC;MAEAgC,QAAQ,CAACqH,YAAY,CAAC;MACtBrJ,OAAO,CAAC+B,KAAK,CAACsH,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRvH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEX,OAAA;IAAKqI,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,eACA3I,OAAA,CAACrB,IAAI;MACH0J,KAAK,EAAE;QACLO,KAAK,EAAEzF,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE8C,QAAQ,GAAG,MAAM,GAAG9C,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE0F,QAAQ,GAAG,GAAG,GAAG,GAAG;QACvEC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE;MACX,CAAE;MACFC,QAAQ,EAAE,KAAM;MAChBC,SAAS,EAAC,YAAY;MAAAN,QAAA,gBAEtB3I,OAAA;QAAKqI,KAAK,EAAE;UAAEa,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAR,QAAA,gBACpD3I,OAAA,CAACG,KAAK;UAACiJ,KAAK,EAAE,CAAE;UAACf,KAAK,EAAE;YAAEgB,KAAK,EAAE;UAAU,CAAE;UAAAV,QAAA,EAAC;QAAS;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/DzJ,OAAA,CAACG,KAAK;UAACiJ,KAAK,EAAE,CAAE;UAAAT,QAAA,EAAC;QAAO;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAG7B,CAAC,eAENzJ,OAAA,CAAClB,IAAI;QAAC4K,QAAQ,EAAEhJ,OAAQ;QAAAiI,QAAA,GACrB/H,KAAK,iBACJZ,OAAA,CAACjB,KAAK;UACJF,OAAO,EAAC,0BAAM;UACd8K,WAAW,EAAE/I,KAAM;UACnBgJ,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRxB,KAAK,EAAE;YAAEc,YAAY,EAAE;UAAG;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,eAEDzJ,OAAA,CAACd,IAAI;UACH4K,SAAS,EAAE7G,WAAY;UACvB8G,QAAQ,EAAElE,uBAAwB;UAClCmE,QAAQ;UACR3B,KAAK,EAAE;YAAEc,YAAY,EAAE;UAAG,CAAE;UAAAR,QAAA,gBAE5B3I,OAAA,CAACK,OAAO;YACN4J,GAAG,eAAEjK,OAAA;cAAA2I,QAAA,gBAAM3I,OAAA,CAACX,YAAY;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAd,QAAA,eAGvC3I,OAAA,CAACxB,IAAI;cACHsC,IAAI,EAAEA,IAAK;cACXoJ,IAAI,EAAC,mBAAmB;cACxBC,aAAa,EAAE;gBAAEC,QAAQ,EAAE;cAAK,CAAE;cAClCC,QAAQ,EAAE9D,YAAa;cACvB+D,IAAI,EAAC,OAAO;cACZrB,SAAS,EAAC,YAAY;cAAAN,QAAA,gBAG1B3I,OAAA;gBAAKqI,KAAK,EAAE;kBAAEc,YAAY,EAAE;gBAAG,CAAE;gBAAAR,QAAA,eAC/B3I,OAAA,CAACtB,MAAM;kBACLkL,IAAI,EAAC,MAAM;kBACXW,OAAO,EAAE3E,sBAAuB;kBAChCyC,KAAK,EAAE;oBAAEU,OAAO,EAAE,CAAC;oBAAEI,YAAY,EAAE;kBAAE,CAAE;kBACvCqB,IAAI,eAAExK,OAAA,CAACV,cAAc;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAd,QAAA,EAExB5F,SAAS,GAAG,QAAQ,GAAG;gBAAQ;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAEL,CAAC1G,SAAS,gBACT/C,OAAA,CAAAE,SAAA;gBAAAyI,QAAA,gBAEE3I,OAAA,CAACxB,IAAI,CAACiM,IAAI;kBACRP,IAAI,EAAC,UAAU;kBACfQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE9L,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAA8J,QAAA,eAE/C3I,OAAA,CAAChB,MAAM;oBACL4L,WAAW,EAAC,gCAAO;oBACnBlK,OAAO,EAAEqB,gBAAiB;oBAC1BgI,QAAQ,EAAEzE,oBAAqB;oBAC/BuF,MAAM,eAAE7K,OAAA,CAACX,YAAY;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAd,QAAA,EAExBtH,SAAS,CAAC2C,GAAG,CAACC,QAAQ,iBACrBjE,OAAA,CAACI,MAAM;sBAAsB8D,KAAK,EAAED,QAAQ,CAACC,KAAM;sBAAAyE,QAAA,EAChD1E,QAAQ,CAACE;oBAAK,GADJF,QAAQ,CAACC,KAAK;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEnB,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;kBACRP,IAAI,EAAC,MAAM;kBACXQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE9L,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAA8J,QAAA,eAE/C3I,OAAA,CAAChB,MAAM;oBACL4L,WAAW,EAAC,gCAAO;oBACnBlK,OAAO,EAAEuB,aAAc;oBACvB8H,QAAQ,EAAErE,gBAAiB;oBAC3BoF,QAAQ,EAAE,CAACrI,gBAAiB;oBAAAkG,QAAA,EAE3BpH,MAAM,CAACyC,GAAG,CAACgB,IAAI,iBACdhF,OAAA,CAACI,MAAM;sBAAkB8D,KAAK,EAAEc,IAAI,CAACd,KAAM;sBAAAyE,QAAA,EACxC3D,IAAI,CAACb;oBAAK,GADAa,IAAI,CAACd,KAAK;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;kBAACP,IAAI,EAAC,UAAU;kBAAAvB,QAAA,eACxB3I,OAAA,CAAChB,MAAM;oBACL4L,WAAW,EAAC,wDAAW;oBACvBlK,OAAO,EAAEyB,gBAAiB;oBAC1B4H,QAAQ,EAAEpE,oBAAqB;oBAC/BmF,QAAQ,EAAE,CAACnI,YAAa;oBACxBoI,UAAU;oBAAApC,QAAA,EAETlH,SAAS,CAACuC,GAAG,CAACkB,QAAQ,iBACrBlF,OAAA,CAACI,MAAM;sBAAsB8D,KAAK,EAAEgB,QAAQ,CAAChB,KAAM;sBAAAyE,QAAA,EAChDzD,QAAQ,CAACf;oBAAK,GADJe,QAAQ,CAAChB,KAAK;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEnB,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;kBACRP,IAAI,EAAC,WAAW;kBAChBQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE9L,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAA8J,QAAA,eAE/C3I,OAAA,CAAChB,MAAM;oBACL4L,WAAW,EAAC,gCAAO;oBACnBlK,OAAO,EAAE2B,cAAe;oBACxByI,QAAQ,EAAE,CAACnI,YAAa;oBAAAgG,QAAA,EAEvBhH,OAAO,CAACqC,GAAG,CAACgH,MAAM,iBACjBhL,OAAA,CAACI,MAAM;sBAAoB8D,KAAK,EAAE8G,MAAM,CAAC9G,KAAM;sBAAAyE,QAAA,GAC5CqC,MAAM,CAAC7G,KAAK,EACZ6G,MAAM,CAACC,OAAO,iBACbjL,OAAA;wBAAKqI,KAAK,EAAE;0BAAE6C,QAAQ,EAAE,MAAM;0BAAE7B,KAAK,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAC7CqC,MAAM,CAACC;sBAAO;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CACN;oBAAA,GANUuB,MAAM,CAAC9G,KAAK;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAOjB,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,eACZ,CAAC,gBAEHzJ,OAAA,CAAAE,SAAA;gBAAAyI,QAAA,eAEE3I,OAAA,CAACxB,IAAI,CAACiM,IAAI;kBACRP,IAAI,EAAC,WAAW;kBAChBQ,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE9L,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAA8J,QAAA,eAE/C3I,OAAA,CAAChB,MAAM;oBACLmM,UAAU;oBACVP,WAAW,EAAC,wDAAW;oBACvBlK,OAAO,EAAE6B,aAAc;oBACvB6I,QAAQ,EAAEhG,mBAAoB;oBAC9BiG,YAAY,EAAE,KAAM;oBACpBC,eAAe,EAAE/I,aAAa,gBAAGvC,OAAA,CAAClB,IAAI;sBAACwL,IAAI,EAAC;oBAAO;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAAG,OAAQ;oBAAAd,QAAA,EAEhE9G,aAAa,CAACmC,GAAG,CAACgH,MAAM,iBACvBhL,OAAA,CAACI,MAAM;sBAAoB8D,KAAK,EAAE8G,MAAM,CAAC9G,KAAM;sBAAAyE,QAAA,eAC7C3I,OAAA;wBAAA2I,QAAA,gBACE3I,OAAA;0BAAA2I,QAAA,EAAMqC,MAAM,CAAC7G;wBAAK;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzBzJ,OAAA;0BAAKqI,KAAK,EAAE;4BAAE6C,QAAQ,EAAE,MAAM;4BAAE7B,KAAK,EAAE;0BAAO,CAAE;0BAAAV,QAAA,GAC7CqC,MAAM,CAAC/G,QAAQ,EAAC,GAAC,EAAC+G,MAAM,CAAChG,IAAI,EAAC,GAAC,EAACgG,MAAM,CAAC9F,QAAQ;wBAAA;0BAAAoE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GANKuB,MAAM,CAAC9G,KAAK;sBAAAoF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAOjB,CACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC,gBACZ,CACH,eAEDzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBACRP,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9L,OAAO,EAAE;gBAAU,CAAC,CAAE;gBAAA8J,QAAA,eAEhD3I,OAAA,CAACvB,KAAK;kBACJoM,MAAM,eAAE7K,OAAA,CAACb,YAAY;oBAAC8J,SAAS,EAAC;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDmB,WAAW,EAAC;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBACRP,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9L,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA8J,QAAA,eAE/C3I,OAAA,CAACvB,KAAK;kBACJoM,MAAM,eAAE7K,OAAA,CAACZ,YAAY;oBAAC6J,SAAS,EAAC;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDG,IAAI,EAAC,UAAU;kBACfgB,WAAW,EAAC;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBAAA9B,QAAA,eACR3I,OAAA,CAACtB,MAAM;kBACLkL,IAAI,EAAC,SAAS;kBACd2B,QAAQ,EAAC,QAAQ;kBACjBtC,SAAS,EAAC,mBAAmB;kBAC7BvI,OAAO,EAAEA,OAAQ;kBACjB2H,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAC1B;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC,GAjKC,QAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkKT,CAAC,eAEVzJ,OAAA,CAACK,OAAO;YACN4J,GAAG,eAAEjK,OAAA;cAAA2I,QAAA,gBAAM3I,OAAA,CAACT,aAAa;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAd,QAAA,eAGxC3I,OAAA,CAACxB,IAAI;cACHsC,IAAI,EAAEE,SAAU;cAChBkJ,IAAI,EAAC,kBAAkB;cACvBC,aAAa,EAAE;gBAAEC,QAAQ,EAAE;cAAK,CAAE;cAClCC,QAAQ,EAAE9D,YAAa;cACvB+D,IAAI,EAAC,OAAO;cACZrB,SAAS,EAAC,YAAY;cAAAN,QAAA,gBAEtB3I,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBACRP,IAAI,EAAC,OAAO;gBACZQ,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9L,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAE2M,OAAO,EAAE,eAAe;kBAAE3M,OAAO,EAAE;gBAAa,CAAC,CACnD;gBAAA8J,QAAA,eAEF3I,OAAA,CAACvB,KAAK;kBACJoM,MAAM,eAAE7K,OAAA,CAACT,aAAa;oBAAC0J,SAAS,EAAC;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC1DmB,WAAW,EAAC;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBACRP,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9L,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA8J,QAAA,eAE/C3I,OAAA,CAACvB,KAAK;kBACJoM,MAAM,eAAE7K,OAAA,CAACZ,YAAY;oBAAC6J,SAAS,EAAC;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDG,IAAI,EAAC,UAAU;kBACfgB,WAAW,EAAC;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBAAA9B,QAAA,eACR3I,OAAA,CAACtB,MAAM;kBACLkL,IAAI,EAAC,SAAS;kBACd2B,QAAQ,EAAC,QAAQ;kBACjBtC,SAAS,EAAC,mBAAmB;kBAC7BvI,OAAO,EAAEA,OAAQ;kBACjB2H,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAC1B;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC,GA7CH,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CJ,CAAC,eAEVzJ,OAAA,CAACK,OAAO;YACN4J,GAAG,eAAEjK,OAAA;cAAA2I,QAAA,gBAAM3I,OAAA,CAACR,YAAY;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAAAd,QAAA,eAGvC3I,OAAA,CAACxB,IAAI;cACHsC,IAAI,EAAEG,SAAU;cAChBiJ,IAAI,EAAC,kBAAkB;cACvBC,aAAa,EAAE;gBAAEC,QAAQ,EAAE;cAAK,CAAE;cAClCC,QAAQ,EAAE9D,YAAa;cACvB+D,IAAI,EAAC,OAAO;cACZrB,SAAS,EAAC,YAAY;cAAAN,QAAA,gBAEtB3I,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBACRP,IAAI,EAAC,OAAO;gBACZQ,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9L,OAAO,EAAE;gBAAS,CAAC,EACrC;kBAAE+K,IAAI,EAAE,OAAO;kBAAE/K,OAAO,EAAE;gBAAc,CAAC,CACzC;gBAAA8J,QAAA,eAEF3I,OAAA,CAACvB,KAAK;kBACJoM,MAAM,eAAE7K,OAAA,CAACR,YAAY;oBAACyJ,SAAS,EAAC;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDmB,WAAW,EAAC;gBAAM;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBACRP,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE9L,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA8J,QAAA,eAE/C3I,OAAA,CAACvB,KAAK;kBACJoM,MAAM,eAAE7K,OAAA,CAACZ,YAAY;oBAAC6J,SAAS,EAAC;kBAAqB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDG,IAAI,EAAC,UAAU;kBACfgB,WAAW,EAAC;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZzJ,OAAA,CAACxB,IAAI,CAACiM,IAAI;gBAAA9B,QAAA,eACR3I,OAAA,CAACtB,MAAM;kBACLkL,IAAI,EAAC,SAAS;kBACd2B,QAAQ,EAAC,QAAQ;kBACjBtC,SAAS,EAAC,mBAAmB;kBAC7BvI,OAAO,EAAEA,OAAQ;kBACjB2H,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO,CAAE;kBAAAD,QAAA,EAC1B;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC,GA7CH,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEHzJ,OAAA;UAAKiJ,SAAS,EAAC,eAAe;UAACZ,KAAK,EAAE;YAAEa,SAAS,EAAE;UAAS,CAAE;UAAAP,QAAA,eAC5D3I,OAAA,CAAC3B,IAAI;YAACoN,EAAE,EAAC,WAAW;YAAA9C,QAAA,EAAC;UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPzJ,OAAA;QAAKqI,KAAK,EAAE;UAAEqD,SAAS,EAAE,EAAE;UAAExC,SAAS,EAAE,QAAQ;UAAEG,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAChE3I,OAAA;UAAA2I,QAAA,EAAG;QAAU;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClJ,EAAA,CA3sBID,KAAK;EAAA,QAGM9B,IAAI,CAACuC,OAAO,EACPvC,IAAI,CAACuC,OAAO,EACZvC,IAAI,CAACuC,OAAO,EACfzC,WAAW,EACXC,WAAW,EACJqB,OAAO;AAAA;AAAA+L,EAAA,GAR3BrL,KAAK;AA6sBX,eAAeA,KAAK;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}