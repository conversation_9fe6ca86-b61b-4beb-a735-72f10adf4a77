{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport SkeletonAvatar from './Avatar';\nimport SkeletonButton from './Button';\nimport Element from './Element';\nimport SkeletonImage from './Image';\nimport SkeletonInput from './Input';\nimport SkeletonNode from './Node';\nimport Paragraph from './Paragraph';\nimport useStyle from './style';\nimport Title from './Title';\nfunction getComponentProps(prop) {\n  if (prop && typeof prop === 'object') {\n    return prop;\n  }\n  return {};\n}\nfunction getAvatarBasicProps(hasTitle, hasParagraph) {\n  if (hasTitle && !hasParagraph) {\n    // Square avatar\n    return {\n      size: 'large',\n      shape: 'square'\n    };\n  }\n  return {\n    size: 'large',\n    shape: 'circle'\n  };\n}\nfunction getTitleBasicProps(hasAvatar, hasParagraph) {\n  if (!hasAvatar && hasParagraph) {\n    return {\n      width: '38%'\n    };\n  }\n  if (hasAvatar && hasParagraph) {\n    return {\n      width: '50%'\n    };\n  }\n  return {};\n}\nfunction getParagraphBasicProps(hasAvatar, hasTitle) {\n  const basicProps = {};\n  // Width\n  if (!hasAvatar || !hasTitle) {\n    basicProps.width = '61%';\n  }\n  // Rows\n  if (!hasAvatar && hasTitle) {\n    basicProps.rows = 3;\n  } else {\n    basicProps.rows = 2;\n  }\n  return basicProps;\n}\nconst Skeleton = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    loading,\n    className,\n    rootClassName,\n    style,\n    children,\n    avatar = false,\n    title = true,\n    paragraph = true,\n    active,\n    round\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('skeleton');\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  if (loading || !('loading' in props)) {\n    const hasAvatar = !!avatar;\n    const hasTitle = !!title;\n    const hasParagraph = !!paragraph;\n    // Avatar\n    let avatarNode;\n    if (hasAvatar) {\n      const avatarProps = Object.assign(Object.assign({\n        prefixCls: `${prefixCls}-avatar`\n      }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));\n      // We direct use SkeletonElement as avatar in skeleton internal.\n      avatarNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-header`\n      }, /*#__PURE__*/React.createElement(Element, Object.assign({}, avatarProps)));\n    }\n    let contentNode;\n    if (hasTitle || hasParagraph) {\n      // Title\n      let $title;\n      if (hasTitle) {\n        const titleProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-title`\n        }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));\n        $title = /*#__PURE__*/React.createElement(Title, Object.assign({}, titleProps));\n      }\n      // Paragraph\n      let paragraphNode;\n      if (hasParagraph) {\n        const paragraphProps = Object.assign(Object.assign({\n          prefixCls: `${prefixCls}-paragraph`\n        }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));\n        paragraphNode = /*#__PURE__*/React.createElement(Paragraph, Object.assign({}, paragraphProps));\n      }\n      contentNode = /*#__PURE__*/React.createElement(\"div\", {\n        className: `${prefixCls}-content`\n      }, $title, paragraphNode);\n    }\n    const cls = classNames(prefixCls, {\n      [`${prefixCls}-with-avatar`]: hasAvatar,\n      [`${prefixCls}-active`]: active,\n      [`${prefixCls}-rtl`]: direction === 'rtl',\n      [`${prefixCls}-round`]: round\n    }, contextClassName, className, rootClassName, hashId, cssVarCls);\n    return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: Object.assign(Object.assign({}, contextStyle), style)\n    }, avatarNode, contentNode));\n  }\n  return children !== null && children !== void 0 ? children : null;\n};\nSkeleton.Button = SkeletonButton;\nSkeleton.Avatar = SkeletonAvatar;\nSkeleton.Input = SkeletonInput;\nSkeleton.Image = SkeletonImage;\nSkeleton.Node = SkeletonNode;\nif (process.env.NODE_ENV !== 'production') {\n  Skeleton.displayName = 'Skeleton';\n}\nexport default Skeleton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}