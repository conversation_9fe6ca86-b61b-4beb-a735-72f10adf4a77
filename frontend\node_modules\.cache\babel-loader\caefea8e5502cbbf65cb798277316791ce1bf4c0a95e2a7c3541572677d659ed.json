{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst UnitNumber = props => {\n  const {\n    prefixCls,\n    value,\n    current,\n    offset = 0\n  } = props;\n  let style;\n  if (offset) {\n    style = {\n      position: 'absolute',\n      top: `${offset}00%`,\n      left: 0\n    };\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    style: style,\n    className: classNames(`${prefixCls}-only-unit`, {\n      current\n    })\n  }, value);\n};\nfunction getOffset(start, end, unit) {\n  let index = start;\n  let offset = 0;\n  while ((index + 10) % 10 !== end) {\n    index += unit;\n    offset += unit;\n  }\n  return offset;\n}\nconst SingleNumber = props => {\n  const {\n    prefixCls,\n    count: originCount,\n    value: originValue\n  } = props;\n  const value = Number(originValue);\n  const count = Math.abs(originCount);\n  const [prevValue, setPrevValue] = React.useState(value);\n  const [prevCount, setPrevCount] = React.useState(count);\n  // ============================= Events =============================\n  const onTransitionEnd = () => {\n    setPrevValue(value);\n    setPrevCount(count);\n  };\n  // Fallback if transition events are not supported\n  React.useEffect(() => {\n    const timer = setTimeout(onTransitionEnd, 1000);\n    return () => clearTimeout(timer);\n  }, [value]);\n  // ============================= Render =============================\n  // Render unit list\n  let unitNodes;\n  let offsetStyle;\n  if (prevValue === value || Number.isNaN(value) || Number.isNaN(prevValue)) {\n    // Nothing to change\n    unitNodes = [/*#__PURE__*/React.createElement(UnitNumber, Object.assign({}, props, {\n      key: value,\n      current: true\n    }))];\n    offsetStyle = {\n      transition: 'none'\n    };\n  } else {\n    unitNodes = [];\n    // Fill basic number units\n    const end = value + 10;\n    const unitNumberList = [];\n    for (let index = value; index <= end; index += 1) {\n      unitNumberList.push(index);\n    }\n    const unit = prevCount < count ? 1 : -1;\n    // Fill with number unit nodes\n    const prevIndex = unitNumberList.findIndex(n => n % 10 === prevValue);\n    // Cut list\n    const cutUnitNumberList = unit < 0 ? unitNumberList.slice(0, prevIndex + 1) : unitNumberList.slice(prevIndex);\n    unitNodes = cutUnitNumberList.map((n, index) => {\n      const singleUnit = n % 10;\n      return /*#__PURE__*/React.createElement(UnitNumber, Object.assign({}, props, {\n        key: n,\n        value: singleUnit,\n        offset: unit < 0 ? index - prevIndex : index,\n        current: index === prevIndex\n      }));\n    });\n    // Calculate container offset value\n    offsetStyle = {\n      transform: `translateY(${-getOffset(prevValue, value, unit)}00%)`\n    };\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-only`,\n    style: offsetStyle,\n    onTransitionEnd: onTransitionEnd\n  }, unitNodes);\n};\nexport default SingleNumber;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}