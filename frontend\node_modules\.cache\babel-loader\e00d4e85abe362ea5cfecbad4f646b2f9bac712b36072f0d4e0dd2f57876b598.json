{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport * as React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport channelUpdate from \"./channelUpdate\";\n/**\n * Batcher for record any `useEffectState` need update.\n */\nexport function useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = React.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      channelUpdate(function () {\n        unstable_batchedUpdates(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nexport default function useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = useEvent(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}