{"ast": null, "code": "import { deserialize } from './deserialize.js';\nimport { serialize } from './serialize.js';\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns an array of serialized Records.\n * @param {any} any a serializable value.\n * @param {{transfer?: any[], json?: boolean, lossy?: boolean}?} options an object with\n * a transfer option (ignored when polyfilled) and/or non standard fields that\n * fallback to the polyfill if present.\n * @returns {Record[]}\n */\nexport default typeof structuredClone === \"function\" ? /* c8 ignore start */\n(any, options) => options && ('json' in options || 'lossy' in options) ? deserialize(serialize(any, options)) : structuredClone(any) : (any, options) => deserialize(serialize(any, options));\n/* c8 ignore stop */\n\nexport { deserialize, serialize };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}