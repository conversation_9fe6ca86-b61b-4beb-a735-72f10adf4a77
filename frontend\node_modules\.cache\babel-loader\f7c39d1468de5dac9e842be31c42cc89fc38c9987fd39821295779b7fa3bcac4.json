{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"prefixCls\", \"direction\", \"vertical\", \"options\", \"disabled\", \"defaultValue\", \"value\", \"name\", \"onChange\", \"className\", \"motionName\"];\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { composeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport MotionThumb from \"./MotionThumb\";\nfunction getValidTitle(option) {\n  if (typeof option.title !== 'undefined') {\n    return option.title;\n  }\n\n  // read `label` when title is `undefined`\n  if (_typeof(option.label) !== 'object') {\n    var _option$label;\n    return (_option$label = option.label) === null || _option$label === void 0 ? void 0 : _option$label.toString();\n  }\n}\nfunction normalizeOptions(options) {\n  return options.map(function (option) {\n    if (_typeof(option) === 'object' && option !== null) {\n      var validTitle = getValidTitle(option);\n      return _objectSpread(_objectSpread({}, option), {}, {\n        title: validTitle\n      });\n    }\n    return {\n      label: option === null || option === void 0 ? void 0 : option.toString(),\n      title: option === null || option === void 0 ? void 0 : option.toString(),\n      value: option\n    };\n  });\n}\nvar InternalSegmentedOption = function InternalSegmentedOption(_ref) {\n  var prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    disabled = _ref.disabled,\n    checked = _ref.checked,\n    label = _ref.label,\n    title = _ref.title,\n    value = _ref.value,\n    name = _ref.name,\n    onChange = _ref.onChange,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onKeyDown = _ref.onKeyDown,\n    onKeyUp = _ref.onKeyUp,\n    onMouseDown = _ref.onMouseDown;\n  var handleChange = function handleChange(event) {\n    if (disabled) {\n      return;\n    }\n    onChange(event, value);\n  };\n  return /*#__PURE__*/React.createElement(\"label\", {\n    className: classNames(className, _defineProperty({}, \"\".concat(prefixCls, \"-item-disabled\"), disabled)),\n    onMouseDown: onMouseDown\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    name: name,\n    className: \"\".concat(prefixCls, \"-item-input\"),\n    type: \"radio\",\n    disabled: disabled,\n    checked: checked,\n    onChange: handleChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-item-label\"),\n    title: title,\n    \"aria-selected\": checked\n  }, label));\n};\nvar Segmented = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _segmentedOptions$, _classNames2;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-segmented' : _props$prefixCls,\n    direction = props.direction,\n    vertical = props.vertical,\n    _props$options = props.options,\n    options = _props$options === void 0 ? [] : _props$options,\n    disabled = props.disabled,\n    defaultValue = props.defaultValue,\n    value = props.value,\n    name = props.name,\n    onChange = props.onChange,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    _props$motionName = props.motionName,\n    motionName = _props$motionName === void 0 ? 'thumb-motion' : _props$motionName,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var containerRef = React.useRef(null);\n  var mergedRef = React.useMemo(function () {\n    return composeRef(containerRef, ref);\n  }, [containerRef, ref]);\n  var segmentedOptions = React.useMemo(function () {\n    return normalizeOptions(options);\n  }, [options]);\n\n  // Note: We should not auto switch value when value not exist in options\n  // which may break single source of truth.\n  var _useMergedState = useMergedState((_segmentedOptions$ = segmentedOptions[0]) === null || _segmentedOptions$ === void 0 ? void 0 : _segmentedOptions$.value, {\n      value: value,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    rawValue = _useMergedState2[0],\n    setRawValue = _useMergedState2[1];\n\n  // ======================= Change ========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    thumbShow = _React$useState2[0],\n    setThumbShow = _React$useState2[1];\n  var handleChange = function handleChange(event, val) {\n    setRawValue(val);\n    onChange === null || onChange === void 0 || onChange(val);\n  };\n  var divProps = omit(restProps, ['children']);\n\n  // ======================= Focus ========================\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    isKeyboard = _React$useState4[0],\n    setIsKeyboard = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    isFocused = _React$useState6[0],\n    setIsFocused = _React$useState6[1];\n  var handleFocus = function handleFocus() {\n    setIsFocused(true);\n  };\n  var handleBlur = function handleBlur() {\n    setIsFocused(false);\n  };\n  var handleMouseDown = function handleMouseDown() {\n    setIsKeyboard(false);\n  };\n\n  // capture keyboard tab interaction for correct focus style\n  var handleKeyUp = function handleKeyUp(event) {\n    if (event.key === 'Tab') {\n      setIsKeyboard(true);\n    }\n  };\n\n  // ======================= Keyboard ========================\n  var onOffset = function onOffset(offset) {\n    var currentIndex = segmentedOptions.findIndex(function (option) {\n      return option.value === rawValue;\n    });\n    var total = segmentedOptions.length;\n    var nextIndex = (currentIndex + offset + total) % total;\n    var nextOption = segmentedOptions[nextIndex];\n    if (nextOption) {\n      setRawValue(nextOption.value);\n      onChange === null || onChange === void 0 || onChange(nextOption.value);\n    }\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.key) {\n      case 'ArrowLeft':\n      case 'ArrowUp':\n        onOffset(-1);\n        break;\n      case 'ArrowRight':\n      case 'ArrowDown':\n        onOffset(1);\n        break;\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    role: \"radiogroup\",\n    \"aria-label\": \"segmented control\",\n    tabIndex: disabled ? undefined : 0\n  }, divProps, {\n    className: classNames(prefixCls, (_classNames2 = {}, _defineProperty(_classNames2, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-disabled\"), disabled), _defineProperty(_classNames2, \"\".concat(prefixCls, \"-vertical\"), vertical), _classNames2), className),\n    ref: mergedRef\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-group\")\n  }, /*#__PURE__*/React.createElement(MotionThumb, {\n    vertical: vertical,\n    prefixCls: prefixCls,\n    value: rawValue,\n    containerRef: containerRef,\n    motionName: \"\".concat(prefixCls, \"-\").concat(motionName),\n    direction: direction,\n    getValueIndex: function getValueIndex(val) {\n      return segmentedOptions.findIndex(function (n) {\n        return n.value === val;\n      });\n    },\n    onMotionStart: function onMotionStart() {\n      setThumbShow(true);\n    },\n    onMotionEnd: function onMotionEnd() {\n      setThumbShow(false);\n    }\n  }), segmentedOptions.map(function (segmentedOption) {\n    var _classNames3;\n    return /*#__PURE__*/React.createElement(InternalSegmentedOption, _extends({}, segmentedOption, {\n      name: name,\n      key: segmentedOption.value,\n      prefixCls: prefixCls,\n      className: classNames(segmentedOption.className, \"\".concat(prefixCls, \"-item\"), (_classNames3 = {}, _defineProperty(_classNames3, \"\".concat(prefixCls, \"-item-selected\"), segmentedOption.value === rawValue && !thumbShow), _defineProperty(_classNames3, \"\".concat(prefixCls, \"-item-focused\"), isFocused && isKeyboard && segmentedOption.value === rawValue), _classNames3)),\n      checked: segmentedOption.value === rawValue,\n      onChange: handleChange,\n      onFocus: handleFocus,\n      onBlur: handleBlur,\n      onKeyDown: handleKeyDown,\n      onKeyUp: handleKeyUp,\n      onMouseDown: handleMouseDown,\n      disabled: !!disabled || !!segmentedOption.disabled\n    }));\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Segmented.displayName = 'Segmented';\n}\nvar TypedSegmented = Segmented;\nexport default TypedSegmented;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}