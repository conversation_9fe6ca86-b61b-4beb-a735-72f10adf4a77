{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factoryDestination } from 'micromark-factory-destination';\nimport { factoryLabel } from 'micromark-factory-label';\nimport { factoryTitle } from 'micromark-factory-title';\nimport { factoryWhitespace } from 'micromark-factory-whitespace';\nimport { markdownLineEndingOrSpace } from 'micromark-util-character';\nimport { push, splice } from 'micromark-util-chunked';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { resolveAll } from 'micromark-util-resolve-all';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n};\n\n/** @type {Construct} */\nconst resourceConstruct = {\n  tokenize: tokenizeResource\n};\n/** @type {Construct} */\nconst referenceFullConstruct = {\n  tokenize: tokenizeReferenceFull\n};\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {\n  tokenize: tokenizeReferenceCollapsed\n};\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1;\n  /** @type {Array<Event>} */\n  const newEvents = [];\n  while (++index < events.length) {\n    const token = events[index][1];\n    newEvents.push(events[index]);\n    if (token.type === types.labelImage || token.type === types.labelLink || token.type === types.labelEnd) {\n      // Remove the marker.\n      const offset = token.type === types.labelImage ? 4 : 2;\n      token.type = types.data;\n      index += offset;\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    splice(events, 0, events.length, newEvents);\n  }\n  return events;\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length;\n  let offset = 0;\n  /** @type {Token} */\n  let token;\n  /** @type {number | undefined} */\n  let open;\n  /** @type {number | undefined} */\n  let close;\n  /** @type {Array<Event>} */\n  let media;\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1];\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (token.type === types.link || token.type === types.labelLink && token._inactive) {\n        break;\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true;\n      }\n    } else if (close) {\n      if (events[index][0] === 'enter' && (token.type === types.labelImage || token.type === types.labelLink) && !token._balanced) {\n        open = index;\n        if (token.type !== types.labelLink) {\n          offset = 2;\n          break;\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index;\n    }\n  }\n  assert(open !== undefined, '`open` is supposed to be found');\n  assert(close !== undefined, '`close` is supposed to be found');\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: {\n      ...events[open][1].start\n    },\n    end: {\n      ...events[events.length - 1][1].end\n    }\n  };\n  const label = {\n    type: types.label,\n    start: {\n      ...events[open][1].start\n    },\n    end: {\n      ...events[close][1].end\n    }\n  };\n  const text = {\n    type: types.labelText,\n    start: {\n      ...events[open + offset + 2][1].end\n    },\n    end: {\n      ...events[close - 2][1].start\n    }\n  };\n  media = [['enter', group, context], ['enter', label, context]];\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3));\n\n  // Text open.\n  media = push(media, [['enter', text, context]]);\n\n  // Always populated by defaults.\n  assert(context.parser.constructs.insideSpan.null, 'expected `insideSpan.null` to be populated');\n  // Between.\n  media = push(media, resolveAll(context.parser.constructs.insideSpan.null, events.slice(open + offset + 4, close - 3), context));\n\n  // Text close, marker close, label close.\n  media = push(media, [['exit', text, context], events[close - 2], events[close - 1], ['exit', label, context]]);\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1));\n\n  // Media close.\n  media = push(media, [['exit', group, context]]);\n  splice(events, open, events.length, media);\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this;\n  let index = self.events.length;\n  /** @type {Token} */\n  let labelStart;\n  /** @type {boolean} */\n  let defined;\n\n  // Find an opening.\n  while (index--) {\n    if ((self.events[index][1].type === types.labelImage || self.events[index][1].type === types.labelLink) && !self.events[index][1]._balanced) {\n      labelStart = self.events[index][1];\n      break;\n    }\n  }\n  return start;\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`');\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code);\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code);\n    }\n    defined = self.parser.defined.includes(normalizeIdentifier(self.sliceSerialize({\n      start: labelStart.end,\n      end: self.now()\n    })));\n    effects.enter(types.labelEnd);\n    effects.enter(types.labelMarker);\n    effects.consume(code);\n    effects.exit(types.labelMarker);\n    effects.exit(types.labelEnd);\n    return after;\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(resourceConstruct, labelEndOk, defined ? labelEndOk : labelEndNok)(code);\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(referenceFullConstruct, labelEndOk, defined ? referenceNotFull : labelEndNok)(code);\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code);\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(referenceCollapsedConstruct, labelEndOk, labelEndNok)(code);\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code);\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true;\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart;\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren');\n    effects.enter(types.resource);\n    effects.enter(types.resourceMarker);\n    effects.consume(code);\n    effects.exit(types.resourceMarker);\n    return resourceBefore;\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceOpen)(code) : resourceOpen(code);\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code);\n    }\n    return factoryDestination(effects, resourceDestinationAfter, resourceDestinationMissing, types.resourceDestination, types.resourceDestinationLiteral, types.resourceDestinationLiteralMarker, types.resourceDestinationRaw, types.resourceDestinationString, constants.linkResourceDestinationBalanceMax)(code);\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceBetween)(code) : resourceEnd(code);\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code);\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (code === codes.quotationMark || code === codes.apostrophe || code === codes.leftParenthesis) {\n      return factoryTitle(effects, resourceTitleAfter, nok, types.resourceTitle, types.resourceTitleMarker, types.resourceTitleString)(code);\n    }\n    return resourceEnd(code);\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceEnd)(code) : resourceEnd(code);\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker);\n      effects.consume(code);\n      effects.exit(types.resourceMarker);\n      effects.exit(types.resource);\n      return ok;\n    }\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this;\n  return referenceFull;\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket');\n    return factoryLabel.call(self, effects, referenceFullAfter, referenceFullMissing, types.reference, types.referenceMarker, types.referenceString)(code);\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(normalizeIdentifier(self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1))) ? ok(code) : nok(code);\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart;\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket');\n    effects.enter(types.reference);\n    effects.enter(types.referenceMarker);\n    effects.consume(code);\n    effects.exit(types.referenceMarker);\n    return referenceCollapsedOpen;\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker);\n      effects.consume(code);\n      effects.exit(types.referenceMarker);\n      effects.exit(types.reference);\n      return ok;\n    }\n    return nok(code);\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}