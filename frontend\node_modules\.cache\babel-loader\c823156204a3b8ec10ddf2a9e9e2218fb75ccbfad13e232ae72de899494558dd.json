{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\nexport default function usePresets(presets, legacyRanges) {\n  return React.useMemo(function () {\n    if (presets) {\n      return presets;\n    }\n    if (legacyRanges) {\n      warning(false, '`ranges` is deprecated. Please use `presets` instead.');\n      return Object.entries(legacyRanges).map(function (_ref) {\n        var _ref2 = _slicedToArray(_ref, 2),\n          label = _ref2[0],\n          value = _ref2[1];\n        return {\n          label: label,\n          value: value\n        };\n      });\n    }\n    return [];\n  }, [presets, legacyRanges]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}