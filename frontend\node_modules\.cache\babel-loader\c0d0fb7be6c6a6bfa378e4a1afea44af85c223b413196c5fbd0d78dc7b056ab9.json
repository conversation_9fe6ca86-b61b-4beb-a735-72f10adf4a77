{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CarOutlinedSvg from \"@ant-design/icons-svg/es/asn/CarOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CarOutlined = function CarOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CarOutlinedSvg\n  }));\n};\n\n/**![car](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM4MCA3MDRoMjY0YzQuNCAwIDgtMy42IDgtOHYtODRjMC00LjQtMy42LTgtOC04aC00MGMtNC40IDAtOCAzLjYtOCA4djM2SDQyOHYtMzZjMC00LjQtMy42LTgtOC04aC00MGMtNC40IDAtOCAzLjYtOCA4djg0YzAgNC40IDMuNiA4IDggOHptMzQwLTEyM2E0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6bTIzOS0xNjcuNkw5MzUuMyAzNzJhOCA4IDAgMDAtMTAuOS0yLjlsLTUwLjcgMjkuNi03OC4zLTIxNi4yYTYzLjkgNjMuOSAwIDAwLTYwLjktNDQuNEgzMDEuMmMtMzQuNyAwLTY1LjUgMjIuNC03Ni4yIDU1LjVsLTc0LjYgMjA1LjItNTAuOC0yOS42YTggOCAwIDAwLTEwLjkgMi45TDY1IDQxMy40Yy0yLjIgMy44LS45IDguNiAyLjkgMTAuOGw2MC40IDM1LjItMTQuNSA0MGMtMS4yIDMuMi0xLjggNi42LTEuOCAxMHYzNDguMmMwIDE1LjcgMTEuOCAyOC40IDI2LjMgMjguNGg2Ny42YzEyLjMgMCAyMy05LjMgMjUuNi0yMi4zbDcuNy0zNy43aDU0NS42bDcuNyAzNy43YzIuNyAxMyAxMy4zIDIyLjMgMjUuNiAyMi4zaDY3LjZjMTQuNSAwIDI2LjMtMTIuNyAyNi4zLTI4LjRWNTA5LjRjMC0zLjQtLjYtNi44LTEuOC0xMGwtMTQuNS00MCA2MC4zLTM1LjJhOCA4IDAgMDAzLTEwLjh6TTg0MCA1MTd2MjM3SDE4NFY1MTdsMTUuNi00M2g2MjQuOGwxNS42IDQzek0yOTIuNyAyMTguMWwuNS0xLjMuNC0xLjNjMS4xLTMuMyA0LjEtNS41IDcuNi01LjVoNDI3LjZsNzUuNCAyMDhIMjIwbDcyLjctMTk5Ljl6TTIyNCA1ODFhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CarOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CarOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}