{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport TimelineItem from './TimelineItem';\nconst TimelineItemList = _a => {\n  var {\n      prefixCls,\n      className,\n      pending = false,\n      children,\n      items,\n      rootClassName,\n      reverse = false,\n      direction,\n      hashId,\n      pendingDot,\n      mode = ''\n    } = _a,\n    restProps = __rest(_a, [\"prefixCls\", \"className\", \"pending\", \"children\", \"items\", \"rootClassName\", \"reverse\", \"direction\", \"hashId\", \"pendingDot\", \"mode\"]);\n  const getPositionCls = (position, idx) => {\n    if (mode === 'alternate') {\n      if (position === 'right') return `${prefixCls}-item-right`;\n      if (position === 'left') return `${prefixCls}-item-left`;\n      return idx % 2 === 0 ? `${prefixCls}-item-left` : `${prefixCls}-item-right`;\n    }\n    if (mode === 'left') return `${prefixCls}-item-left`;\n    if (mode === 'right') return `${prefixCls}-item-right`;\n    if (position === 'right') return `${prefixCls}-item-right`;\n    return '';\n  };\n  const mergedItems = _toConsumableArray(items || []);\n  const pendingNode = typeof pending === 'boolean' ? null : pending;\n  if (pending) {\n    mergedItems.push({\n      pending: !!pending,\n      dot: pendingDot || /*#__PURE__*/React.createElement(LoadingOutlined, null),\n      children: pendingNode\n    });\n  }\n  if (reverse) {\n    mergedItems.reverse();\n  }\n  const itemsCount = mergedItems.length;\n  const lastCls = `${prefixCls}-item-last`;\n  const itemsList = mergedItems.filter(item => !!item).map((item, idx) => {\n    var _a;\n    const pendingClass = idx === itemsCount - 2 ? lastCls : '';\n    const readyClass = idx === itemsCount - 1 ? lastCls : '';\n    const {\n        className: itemClassName\n      } = item,\n      itemProps = __rest(item, [\"className\"]);\n    return /*#__PURE__*/React.createElement(TimelineItem, Object.assign({}, itemProps, {\n      className: classNames([itemClassName, !reverse && !!pending ? pendingClass : readyClass, getPositionCls((_a = item === null || item === void 0 ? void 0 : item.position) !== null && _a !== void 0 ? _a : '', idx)]),\n      key: (item === null || item === void 0 ? void 0 : item.key) || idx\n    }));\n  });\n  const hasLabelItem = mergedItems.some(item => !!(item === null || item === void 0 ? void 0 : item.label));\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-pending`]: !!pending,\n    [`${prefixCls}-reverse`]: !!reverse,\n    [`${prefixCls}-${mode}`]: !!mode && !hasLabelItem,\n    [`${prefixCls}-label`]: hasLabelItem,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId);\n  return /*#__PURE__*/React.createElement(\"ol\", Object.assign({}, restProps, {\n    className: classString\n  }), itemsList);\n};\nexport default TimelineItemList;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}