{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport RcDrawer from 'rc-drawer';\nimport ContextIsolator from '../_util/ContextIsolator';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport { devUseWarning } from '../_util/warning';\nimport zIndexContext from '../_util/zindexContext';\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport { usePanelRef } from '../watermark/context';\nimport DrawerPanel from './DrawerPanel';\nimport useStyle from './style';\nconst _SizeTypes = ['default', 'large'];\nconst defaultPushState = {\n  distance: 180\n};\nconst Drawer = props => {\n  var _a;\n  const {\n      rootClassName,\n      width,\n      height,\n      size = 'default',\n      mask = true,\n      push = defaultPushState,\n      open,\n      afterOpenChange,\n      onClose,\n      prefixCls: customizePrefixCls,\n      getContainer: customizeGetContainer,\n      style,\n      className,\n      // Deprecated\n      visible,\n      afterVisibleChange,\n      maskStyle,\n      drawerStyle,\n      contentWrapperStyle,\n      destroyOnClose,\n      destroyOnHidden\n    } = props,\n    rest = __rest(props, [\"rootClassName\", \"width\", \"height\", \"size\", \"mask\", \"push\", \"open\", \"afterOpenChange\", \"onClose\", \"prefixCls\", \"getContainer\", \"style\", \"className\", \"visible\", \"afterVisibleChange\", \"maskStyle\", \"drawerStyle\", \"contentWrapperStyle\", \"destroyOnClose\", \"destroyOnHidden\"]);\n  const {\n    getPopupContainer,\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('drawer');\n  const prefixCls = getPrefixCls('drawer', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const getContainer =\n  // 有可能为 false，所以不能直接判断\n  customizeGetContainer === undefined && getPopupContainer ? () => getPopupContainer(document.body) : customizeGetContainer;\n  const drawerClassName = classNames({\n    'no-mask': !mask,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, rootClassName, hashId, cssVarCls);\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Drawer');\n    [['visible', 'open'], ['afterVisibleChange', 'afterOpenChange'], ['headerStyle', 'styles.header'], ['bodyStyle', 'styles.body'], ['footerStyle', 'styles.footer'], ['contentWrapperStyle', 'styles.wrapper'], ['maskStyle', 'styles.mask'], ['drawerStyle', 'styles.content'], ['destroyInactivePanel', 'destroyOnHidden']].forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, newName);\n    });\n    if (getContainer !== undefined && ((_a = props.style) === null || _a === void 0 ? void 0 : _a.position) === 'absolute') {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'breaking', '`style` is replaced by `rootStyle` in v5. Please check that `position: absolute` is necessary.') : void 0;\n    }\n  }\n  // ============================ Size ============================\n  const mergedWidth = React.useMemo(() => width !== null && width !== void 0 ? width : size === 'large' ? 736 : 378, [width, size]);\n  const mergedHeight = React.useMemo(() => height !== null && height !== void 0 ? height : size === 'large' ? 736 : 378, [height, size]);\n  // =========================== Motion ===========================\n  const maskMotion = {\n    motionName: getTransitionName(prefixCls, 'mask-motion'),\n    motionAppear: true,\n    motionEnter: true,\n    motionLeave: true,\n    motionDeadline: 500\n  };\n  const panelMotion = motionPlacement => ({\n    motionName: getTransitionName(prefixCls, `panel-motion-${motionPlacement}`),\n    motionAppear: true,\n    motionEnter: true,\n    motionLeave: true,\n    motionDeadline: 500\n  });\n  // ============================ Refs ============================\n  // Select `ant-drawer-content` by `panelRef`\n  const panelRef = usePanelRef();\n  // ============================ zIndex ============================\n  const [zIndex, contextZIndex] = useZIndex('Drawer', rest.zIndex);\n  // =========================== Render ===========================\n  const {\n    classNames: propClassNames = {},\n    styles: propStyles = {}\n  } = rest;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ContextIsolator, {\n    form: true,\n    space: true\n  }, /*#__PURE__*/React.createElement(zIndexContext.Provider, {\n    value: contextZIndex\n  }, /*#__PURE__*/React.createElement(RcDrawer, Object.assign({\n    prefixCls: prefixCls,\n    onClose: onClose,\n    maskMotion: maskMotion,\n    motion: panelMotion\n  }, rest, {\n    classNames: {\n      mask: classNames(propClassNames.mask, contextClassNames.mask),\n      content: classNames(propClassNames.content, contextClassNames.content),\n      wrapper: classNames(propClassNames.wrapper, contextClassNames.wrapper)\n    },\n    styles: {\n      mask: Object.assign(Object.assign(Object.assign({}, propStyles.mask), maskStyle), contextStyles.mask),\n      content: Object.assign(Object.assign(Object.assign({}, propStyles.content), drawerStyle), contextStyles.content),\n      wrapper: Object.assign(Object.assign(Object.assign({}, propStyles.wrapper), contentWrapperStyle), contextStyles.wrapper)\n    },\n    open: open !== null && open !== void 0 ? open : visible,\n    mask: mask,\n    push: push,\n    width: mergedWidth,\n    height: mergedHeight,\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    className: classNames(contextClassName, className),\n    rootClassName: drawerClassName,\n    getContainer: getContainer,\n    afterOpenChange: afterOpenChange !== null && afterOpenChange !== void 0 ? afterOpenChange : afterVisibleChange,\n    panelRef: panelRef,\n    zIndex: zIndex,\n    // TODO: In the future, destroyOnClose in rc-drawer needs to be upgrade to destroyOnHidden\n    destroyOnClose: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyOnClose\n  }), /*#__PURE__*/React.createElement(DrawerPanel, Object.assign({\n    prefixCls: prefixCls\n  }, rest, {\n    onClose: onClose\n  }))))));\n};\n/** @private Internal Component. Do not use in your production. */\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      style,\n      className,\n      placement = 'right'\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"style\", \"className\", \"placement\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('drawer', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-pure`, `${prefixCls}-${placement}`, hashId, cssVarCls, className);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, /*#__PURE__*/React.createElement(DrawerPanel, Object.assign({\n    prefixCls: prefixCls\n  }, restProps))));\n};\nDrawer._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Drawer.displayName = 'Drawer';\n}\nexport default Drawer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}