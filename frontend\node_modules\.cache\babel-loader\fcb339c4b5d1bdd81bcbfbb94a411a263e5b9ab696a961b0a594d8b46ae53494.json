{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\HomeworkAnalysis\\\\AssignmentSelector.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Card, Row, Col, Button, Table, Tag, Space, Typography, Alert, Input, Select, Spin } from 'antd';\nimport { SearchOutlined, BarChartOutlined, CalendarOutlined, BookOutlined, TeamOutlined, ClearOutlined } from '@ant-design/icons';\nimport api from '../../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst AssignmentSelector = ({\n  user\n}) => {\n  _s();\n  var _classes$find, _assignments$find;\n  const navigate = useNavigate();\n  const [assignments, setAssignments] = useState([]);\n  const [filteredAssignments, setFilteredAssignments] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // 筛选数据\n  const [grades, setGrades] = useState([]);\n  const [classes, setClasses] = useState([]);\n  const [allClasses, setAllClasses] = useState([]);\n\n  // 从localStorage恢复筛选条件\n  const getInitialFilters = () => {\n    try {\n      const savedFilters = localStorage.getItem('homeworkAnalysisFilters');\n      if (savedFilters) {\n        const parsed = JSON.parse(savedFilters);\n        console.log('🔄 从localStorage恢复筛选条件:', parsed);\n        return {\n          selectedGrade: parsed.selectedGrade || '',\n          selectedClass: parsed.selectedClass || '',\n          selectedAssignment: parsed.selectedAssignment || '',\n          searchText: parsed.searchText || ''\n        };\n      }\n    } catch (error) {\n      console.error('恢复筛选条件失败:', error);\n    }\n    return {\n      selectedGrade: '',\n      selectedClass: '',\n      selectedAssignment: '',\n      searchText: ''\n    };\n  };\n\n  // 筛选条件\n  const [filters, setFilters] = useState(getInitialFilters);\n\n  // 获取基础数据\n  const fetchInitialData = async () => {\n    try {\n      setLoading(true);\n\n      // 并行获取作业分配列表和班级列表\n      const [assignmentsResponse, classesResponse] = await Promise.all([api.get('/homework-assignment'),\n      // 获取作业分配而不是作业提交\n      api.get('/admin/classes') // 使用管理员班级API\n      ]);\n\n      // 处理作业分配数据\n      const assignmentsData = assignmentsResponse;\n      console.log('📝 获取到的作业分配数据:', assignmentsData === null || assignmentsData === void 0 ? void 0 : assignmentsData.length, '个作业分配');\n      if (Array.isArray(assignmentsData)) {\n        // 过滤出有效的作业分配（可以进行分析的）\n        const validAssignments = assignmentsData.filter(assignment => {\n          // 检查是否有基本信息\n          return assignment.id && assignment.title;\n        });\n        console.log('📝 筛选后的作业分配数据:', validAssignments === null || validAssignments === void 0 ? void 0 : validAssignments.length, '个可分析作业分配');\n        setAssignments(validAssignments);\n        setFilteredAssignments(validAssignments);\n      } else {\n        console.error('❌ 作业分配数据格式错误:', assignmentsData);\n        setAssignments([]);\n        setFilteredAssignments([]);\n      }\n\n      // 处理班级数据\n      // API直接返回数据数组，不需要访问.data属性\n      const classesData = classesResponse;\n      console.log('🏫 获取到的班级数据:', classesData === null || classesData === void 0 ? void 0 : classesData.length, '个班级');\n      if (Array.isArray(classesData)) {\n        setAllClasses(classesData);\n\n        // 提取年级信息\n        const gradeSet = new Set();\n        let matchedCount = 0;\n        classesData.forEach(cls => {\n          if (cls.name) {\n            // 多种年级匹配模式\n            let grade = null;\n\n            // 匹配：七年级、八年级、九年级\n            if (cls.name.includes('七年级')) {\n              grade = '七年级';\n            } else if (cls.name.includes('八年级')) {\n              grade = '八年级';\n            } else if (cls.name.includes('九年级')) {\n              grade = '九年级';\n            }\n            // 匹配：高一、高二、高三\n            else if (cls.name.includes('高一') || cls.name.includes('高1')) {\n              grade = '高一';\n            } else if (cls.name.includes('高二') || cls.name.includes('高2')) {\n              grade = '高二';\n            } else if (cls.name.includes('高三') || cls.name.includes('高3')) {\n              grade = '高三';\n            }\n            // 匹配：初一、初二、初三\n            else if (cls.name.includes('初一') || cls.name.includes('初1')) {\n              grade = '初一';\n            } else if (cls.name.includes('初二') || cls.name.includes('初2')) {\n              grade = '初二';\n            } else if (cls.name.includes('初三') || cls.name.includes('初3')) {\n              grade = '初三';\n            }\n            if (grade) {\n              gradeSet.add(grade);\n              matchedCount++;\n            }\n          }\n        });\n        const gradesList = Array.from(gradeSet).sort();\n        console.log('📊 提取到的年级列表:', gradesList, `(匹配了${matchedCount}个班级)`);\n        setGrades(gradesList);\n      } else {\n        console.error('❌ 班级数据格式错误:', classesData);\n        setAllClasses([]);\n        setGrades([]);\n      }\n    } catch (error) {\n      console.error('获取数据失败:', error);\n      setAssignments([]);\n      setFilteredAssignments([]);\n      setAllClasses([]);\n      setGrades([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchInitialData();\n  }, []);\n\n  // 调试状态变化\n  useEffect(() => {\n    console.log('🔄 状态更新 - grades:', grades.length, 'classes:', classes.length, 'assignments:', assignments.length);\n  }, [grades, classes, assignments]);\n\n  // 年级变化时更新班级列表\n  useEffect(() => {\n    if (filters.selectedGrade) {\n      console.log('🔄 筛选年级:', filters.selectedGrade);\n      const gradeClasses = allClasses.filter(cls => {\n        if (!cls.name) return false;\n\n        // 使用与年级提取相同的逻辑\n        const name = cls.name;\n        let matchesGrade = false;\n        switch (filters.selectedGrade) {\n          case '七年级':\n            matchesGrade = name.includes('七年级');\n            break;\n          case '八年级':\n            matchesGrade = name.includes('八年级');\n            break;\n          case '九年级':\n            matchesGrade = name.includes('九年级');\n            break;\n          case '高一':\n            matchesGrade = name.includes('高一') || name.includes('高1');\n            break;\n          case '高二':\n            matchesGrade = name.includes('高二') || name.includes('高2');\n            break;\n          case '高三':\n            matchesGrade = name.includes('高三') || name.includes('高3');\n            break;\n          case '初一':\n            matchesGrade = name.includes('初一') || name.includes('初1');\n            break;\n          case '初二':\n            matchesGrade = name.includes('初二') || name.includes('初2');\n            break;\n          case '初三':\n            matchesGrade = name.includes('初三') || name.includes('初3');\n            break;\n          default:\n            matchesGrade = false;\n        }\n        return matchesGrade;\n      });\n      console.log('🏫 筛选出的班级数量:', gradeClasses.length);\n      setClasses(gradeClasses);\n\n      // 清空班级和作业选择\n      setFilters(prev => ({\n        ...prev,\n        selectedClass: '',\n        selectedAssignment: ''\n      }));\n    } else {\n      setClasses([]);\n      setFilters(prev => ({\n        ...prev,\n        selectedClass: '',\n        selectedAssignment: ''\n      }));\n    }\n  }, [filters.selectedGrade, allClasses]);\n\n  // 应用筛选条件\n  useEffect(() => {\n    let filtered = [...assignments];\n\n    // 班级筛选 - 通过class_id匹配\n    if (filters.selectedClass) {\n      const selectedClassId = parseInt(filters.selectedClass);\n      filtered = filtered.filter(assignment => assignment.class_id === selectedClassId);\n    }\n\n    // 作业筛选（按作业分配ID筛选）\n    if (filters.selectedAssignment) {\n      filtered = filtered.filter(assignment => assignment.id === parseInt(filters.selectedAssignment));\n    }\n\n    // 搜索筛选\n    if (filters.searchText) {\n      filtered = filtered.filter(assignment => {\n        var _assignment$title, _assignment$descripti;\n        return ((_assignment$title = assignment.title) === null || _assignment$title === void 0 ? void 0 : _assignment$title.toLowerCase().includes(filters.searchText.toLowerCase())) || ((_assignment$descripti = assignment.description) === null || _assignment$descripti === void 0 ? void 0 : _assignment$descripti.toLowerCase().includes(filters.searchText.toLowerCase()));\n      });\n    }\n    setFilteredAssignments(filtered);\n  }, [assignments, filters, classes]);\n\n  // 处理筛选条件变化\n  const handleFilterChange = (key, value) => {\n    const newFilters = {\n      ...filters,\n      [key]: value\n    };\n    setFilters(newFilters);\n\n    // 保存到localStorage\n    try {\n      localStorage.setItem('homeworkAnalysisFilters', JSON.stringify(newFilters));\n      console.log('💾 筛选条件已保存到localStorage:', newFilters);\n    } catch (error) {\n      console.error('保存筛选条件失败:', error);\n    }\n  };\n\n  // 清除所有筛选条件\n  const clearAllFilters = () => {\n    const emptyFilters = {\n      selectedGrade: '',\n      selectedClass: '',\n      selectedAssignment: '',\n      searchText: ''\n    };\n    setFilters(emptyFilters);\n\n    // 清除localStorage\n    try {\n      localStorage.removeItem('homeworkAnalysisFilters');\n      localStorage.removeItem('lastAssignmentId');\n      console.log('🗑️ 已清除所有筛选条件');\n    } catch (error) {\n      console.error('清除筛选条件失败:', error);\n    }\n  };\n\n  // 获取当前班级的作业分配\n  const getCurrentClassAssignments = () => {\n    if (!filters.selectedClass) return [];\n    const selectedClassId = parseInt(filters.selectedClass);\n\n    // 筛选出属于当前班级的作业分配\n    return assignments.filter(assignment => assignment.class_id === selectedClassId);\n  };\n\n  // 跳转到作业分析页面\n  const handleAnalyzeAssignment = assignmentId => {\n    navigate(`/homework-analysis/overview?assignmentId=${assignmentId}`);\n  };\n\n  // 获取状态标签\n  const getStatusTag = status => {\n    switch (status) {\n      case 'published':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          children: \"\\u5DF2\\u53D1\\u5E03\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 16\n        }, this);\n      case 'draft':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"orange\",\n          children: \"\\u8349\\u7A3F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          children: status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '作业标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (title, record) => /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), record.description && /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: record.description.length > 50 ? `${record.description.substring(0, 50)}...` : record.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '班级',\n    dataIndex: 'class_name',\n    key: 'class_name',\n    width: 120,\n    render: className => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: className || '未知班级'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '科目',\n    dataIndex: 'subject_name',\n    key: 'subject_name',\n    width: 100,\n    render: subjectName => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: subjectName || '未知科目'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => getStatusTag(status)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    width: 120,\n    render: createdAt => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: createdAt ? new Date(createdAt).toLocaleDateString() : '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 17\n      }, this),\n      onClick: () => handleAnalyzeAssignment(record.id),\n      children: \"\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this), \" \\u4F5C\\u4E1A\\u5206\\u6790 - \\u9009\\u62E9\\u4F5C\\u4E1A\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"large\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        title: `筛选条件 (年级: ${grades.length}, 班级: ${classes.length}, 作业: ${assignments.length})`,\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: `选择年级 (${grades.length}个可选)`,\n              style: {\n                width: '100%'\n              },\n              value: filters.selectedGrade,\n              onChange: value => handleFilterChange('selectedGrade', value),\n              allowClear: true,\n              notFoundContent: grades.length === 0 ? \"正在加载年级...\" : \"没有找到年级\",\n              children: grades.map(grade => /*#__PURE__*/_jsxDEV(Option, {\n                value: grade,\n                children: grade\n              }, grade, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: `选择班级 (${classes.length}个可选)`,\n              style: {\n                width: '100%'\n              },\n              value: filters.selectedClass,\n              onChange: value => handleFilterChange('selectedClass', value),\n              disabled: !filters.selectedGrade,\n              allowClear: true,\n              notFoundContent: classes.length === 0 ? \"请先选择年级\" : \"没有找到班级\",\n              children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls.id,\n                children: cls.name\n              }, cls.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u9009\\u62E9\\u4F5C\\u4E1A\",\n              style: {\n                width: '100%'\n              },\n              value: filters.selectedAssignment,\n              onChange: value => handleFilterChange('selectedAssignment', value),\n              disabled: !filters.selectedClass,\n              allowClear: true,\n              children: getCurrentClassAssignments().map(assignment => /*#__PURE__*/_jsxDEV(Option, {\n                value: assignment.id,\n                children: assignment.title\n              }, assignment.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u641C\\u7D22\\u4F5C\\u4E1A\\u6807\\u9898\",\n              prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 25\n              }, this),\n              value: filters.searchText,\n              onChange: e => handleFilterChange('searchText', e.target.value),\n              allowClear: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 1,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 23\n              }, this),\n              disabled: !filters.selectedAssignment,\n              onClick: () => handleAnalyzeAssignment(filters.selectedAssignment),\n              style: {\n                width: '100%'\n              },\n              children: \"\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 1,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 23\n              }, this),\n              onClick: clearAllFilters,\n              title: \"\\u6E05\\u9664\\u6240\\u6709\\u7B5B\\u9009\\u6761\\u4EF6\",\n              style: {\n                width: '100%'\n              },\n              children: \"\\u6E05\\u9664\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), (filters.selectedGrade || filters.selectedClass || filters.selectedAssignment) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '12px',\n            padding: '8px',\n            background: '#f6ffed',\n            borderRadius: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [\"\\u5F53\\u524D\\u7B5B\\u9009:\", filters.selectedGrade && /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: filters.selectedGrade\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 43\n            }, this), filters.selectedClass && /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: (_classes$find = classes.find(c => c.id === parseInt(filters.selectedClass))) === null || _classes$find === void 0 ? void 0 : _classes$find.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 43\n            }, this), filters.selectedAssignment && /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"orange\",\n              children: (_assignments$find = assignments.find(a => a.assignment_id === parseInt(filters.selectedAssignment))) === null || _assignments$find === void 0 ? void 0 : _assignments$find.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 48\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: `作业列表 (${filteredAssignments.length} 个可分析的作业)`,\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          spinning: loading,\n          children: filteredAssignments.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n            columns: columns,\n            dataSource: filteredAssignments,\n            rowKey: \"id\",\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 个作业`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u6682\\u65E0\\u53EF\\u5206\\u6790\\u7684\\u4F5C\\u4E1A\",\n            description: loading ? \"正在加载作业列表...\" : \"没有找到符合条件的作业，请检查筛选条件或确保有已发布的作业。\",\n            type: \"info\",\n            showIcon: true,\n            action: !loading && /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              onClick: fetchInitialData,\n              children: \"\\u5237\\u65B0\\u5217\\u8868\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u4F7F\\u7528\\u8BF4\\u660E\",\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u2022 \\u9009\\u62E9\\u4E00\\u4E2A\\u5DF2\\u53D1\\u5E03\\u6216\\u5DF2\\u5B8C\\u6210\\u7684\\u4F5C\\u4E1A\\u8FDB\\u884C\\u5206\\u6790\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u2022 \\u4F5C\\u4E1A\\u5206\\u6790\\u5305\\u542B\\u6982\\u89C8\\u3001\\u9010\\u9898\\u5206\\u6790\\u3001\\u5B66\\u751F\\u8BE6\\u60C5\\u3001\\u667A\\u80FD\\u5EFA\\u8BAE\\u7B49\\u529F\\u80FD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u2022 \\u53EF\\u4EE5\\u751F\\u6210\\u5BB6\\u957F\\u62A5\\u544A\\u548C\\u5BFC\\u51FA\\u5206\\u6790\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u2022 \\u53EA\\u6709\\u6559\\u5E08\\u548C\\u7BA1\\u7406\\u5458\\u53EF\\u4EE5\\u8BBF\\u95EE\\u4F5C\\u4E1A\\u5206\\u6790\\u529F\\u80FD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 413,\n    columnNumber: 5\n  }, this);\n};\n_s(AssignmentSelector, \"OYudOsKulxuH9AO5igPHJDMjNiw=\", false, function () {\n  return [useNavigate];\n});\n_c = AssignmentSelector;\nexport default AssignmentSelector;\nvar _c;\n$RefreshReg$(_c, \"AssignmentSelector\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Card", "Row", "Col", "<PERSON><PERSON>", "Table", "Tag", "Space", "Typography", "<PERSON><PERSON>", "Input", "Select", "Spin", "SearchOutlined", "BarChartOutlined", "CalendarOutlined", "BookOutlined", "TeamOutlined", "ClearOutlined", "api", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "AssignmentSelector", "user", "_s", "_classes$find", "_assignments$find", "navigate", "assignments", "setAssignments", "filteredAssignments", "setFilteredAssignments", "loading", "setLoading", "grades", "setGrades", "classes", "setClasses", "allClasses", "setAllClasses", "getInitialFilters", "savedFilters", "localStorage", "getItem", "parsed", "JSON", "parse", "console", "log", "selected<PERSON><PERSON>", "selectedClass", "selectedAssignment", "searchText", "error", "filters", "setFilters", "fetchInitialData", "assignmentsResponse", "classesResponse", "Promise", "all", "get", "assignmentsData", "length", "Array", "isArray", "validAssignments", "filter", "assignment", "id", "title", "classesData", "gradeSet", "Set", "matchedCount", "for<PERSON>ach", "cls", "name", "grade", "includes", "add", "gradesList", "from", "sort", "gradeClasses", "matchesGrade", "prev", "filtered", "selectedClassId", "parseInt", "class_id", "_assignment$title", "_assignment$descripti", "toLowerCase", "description", "handleFilterChange", "key", "value", "newFilters", "setItem", "stringify", "clearAllFilters", "emptyFilters", "removeItem", "getCurrentClassAssignments", "handleAnalyzeAssignment", "assignmentId", "getStatusTag", "status", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "columns", "dataIndex", "render", "record", "direction", "size", "strong", "type", "style", "fontSize", "substring", "width", "className", "subjectName", "createdAt", "Date", "toLocaleDateString", "_", "icon", "onClick", "padding", "level", "gutter", "xs", "sm", "placeholder", "onChange", "allowClear", "notFoundContent", "map", "disabled", "prefix", "e", "target", "marginTop", "background", "borderRadius", "find", "c", "a", "assignment_id", "spinning", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "message", "showIcon", "action", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/HomeworkAnalysis/AssignmentSelector.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Card, Row, Col, Button, Table, Tag, Space, Typography,\n  Alert, Input, Select, Spin\n} from 'antd';\nimport {\n  SearchOutlined,\n  BarChartOutlined,\n  CalendarOutlined,\n  BookOutlined,\n  TeamOutlined,\n  ClearOutlined\n} from '@ant-design/icons';\nimport api from '../../utils/api';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\n\nconst AssignmentSelector = ({ user }) => {\n  const navigate = useNavigate();\n  const [assignments, setAssignments] = useState([]);\n  const [filteredAssignments, setFilteredAssignments] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // 筛选数据\n  const [grades, setGrades] = useState([]);\n  const [classes, setClasses] = useState([]);\n  const [allClasses, setAllClasses] = useState([]);\n\n  // 从localStorage恢复筛选条件\n  const getInitialFilters = () => {\n    try {\n      const savedFilters = localStorage.getItem('homeworkAnalysisFilters');\n      if (savedFilters) {\n        const parsed = JSON.parse(savedFilters);\n        console.log('🔄 从localStorage恢复筛选条件:', parsed);\n        return {\n          selectedGrade: parsed.selectedGrade || '',\n          selectedClass: parsed.selectedClass || '',\n          selectedAssignment: parsed.selectedAssignment || '',\n          searchText: parsed.searchText || ''\n        };\n      }\n    } catch (error) {\n      console.error('恢复筛选条件失败:', error);\n    }\n    return {\n      selectedGrade: '',\n      selectedClass: '',\n      selectedAssignment: '',\n      searchText: ''\n    };\n  };\n\n  // 筛选条件\n  const [filters, setFilters] = useState(getInitialFilters);\n\n  // 获取基础数据\n  const fetchInitialData = async () => {\n    try {\n      setLoading(true);\n\n      // 并行获取作业分配列表和班级列表\n      const [assignmentsResponse, classesResponse] = await Promise.all([\n        api.get('/homework-assignment'),  // 获取作业分配而不是作业提交\n        api.get('/admin/classes')  // 使用管理员班级API\n      ]);\n\n      // 处理作业分配数据\n      const assignmentsData = assignmentsResponse;\n      console.log('📝 获取到的作业分配数据:', assignmentsData?.length, '个作业分配');\n\n      if (Array.isArray(assignmentsData)) {\n        // 过滤出有效的作业分配（可以进行分析的）\n        const validAssignments = assignmentsData.filter(assignment => {\n          // 检查是否有基本信息\n          return assignment.id && assignment.title;\n        });\n        console.log('📝 筛选后的作业分配数据:', validAssignments?.length, '个可分析作业分配');\n        setAssignments(validAssignments);\n        setFilteredAssignments(validAssignments);\n      } else {\n        console.error('❌ 作业分配数据格式错误:', assignmentsData);\n        setAssignments([]);\n        setFilteredAssignments([]);\n      }\n\n      // 处理班级数据\n      // API直接返回数据数组，不需要访问.data属性\n      const classesData = classesResponse;\n      console.log('🏫 获取到的班级数据:', classesData?.length, '个班级');\n\n      if (Array.isArray(classesData)) {\n        setAllClasses(classesData);\n\n        // 提取年级信息\n        const gradeSet = new Set();\n        let matchedCount = 0;\n\n        classesData.forEach(cls => {\n          if (cls.name) {\n            // 多种年级匹配模式\n            let grade = null;\n\n            // 匹配：七年级、八年级、九年级\n            if (cls.name.includes('七年级')) {\n              grade = '七年级';\n            } else if (cls.name.includes('八年级')) {\n              grade = '八年级';\n            } else if (cls.name.includes('九年级')) {\n              grade = '九年级';\n            }\n            // 匹配：高一、高二、高三\n            else if (cls.name.includes('高一') || cls.name.includes('高1')) {\n              grade = '高一';\n            } else if (cls.name.includes('高二') || cls.name.includes('高2')) {\n              grade = '高二';\n            } else if (cls.name.includes('高三') || cls.name.includes('高3')) {\n              grade = '高三';\n            }\n            // 匹配：初一、初二、初三\n            else if (cls.name.includes('初一') || cls.name.includes('初1')) {\n              grade = '初一';\n            } else if (cls.name.includes('初二') || cls.name.includes('初2')) {\n              grade = '初二';\n            } else if (cls.name.includes('初三') || cls.name.includes('初3')) {\n              grade = '初三';\n            }\n\n            if (grade) {\n              gradeSet.add(grade);\n              matchedCount++;\n            }\n          }\n        });\n\n        const gradesList = Array.from(gradeSet).sort();\n        console.log('📊 提取到的年级列表:', gradesList, `(匹配了${matchedCount}个班级)`);\n        setGrades(gradesList);\n      } else {\n        console.error('❌ 班级数据格式错误:', classesData);\n        setAllClasses([]);\n        setGrades([]);\n      }\n\n    } catch (error) {\n      console.error('获取数据失败:', error);\n      setAssignments([]);\n      setFilteredAssignments([]);\n      setAllClasses([]);\n      setGrades([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchInitialData();\n  }, []);\n\n  // 调试状态变化\n  useEffect(() => {\n    console.log('🔄 状态更新 - grades:', grades.length, 'classes:', classes.length, 'assignments:', assignments.length);\n  }, [grades, classes, assignments]);\n\n  // 年级变化时更新班级列表\n  useEffect(() => {\n    if (filters.selectedGrade) {\n      console.log('🔄 筛选年级:', filters.selectedGrade);\n\n      const gradeClasses = allClasses.filter(cls => {\n        if (!cls.name) return false;\n\n        // 使用与年级提取相同的逻辑\n        const name = cls.name;\n        let matchesGrade = false;\n\n        switch (filters.selectedGrade) {\n          case '七年级':\n            matchesGrade = name.includes('七年级');\n            break;\n          case '八年级':\n            matchesGrade = name.includes('八年级');\n            break;\n          case '九年级':\n            matchesGrade = name.includes('九年级');\n            break;\n          case '高一':\n            matchesGrade = name.includes('高一') || name.includes('高1');\n            break;\n          case '高二':\n            matchesGrade = name.includes('高二') || name.includes('高2');\n            break;\n          case '高三':\n            matchesGrade = name.includes('高三') || name.includes('高3');\n            break;\n          case '初一':\n            matchesGrade = name.includes('初一') || name.includes('初1');\n            break;\n          case '初二':\n            matchesGrade = name.includes('初二') || name.includes('初2');\n            break;\n          case '初三':\n            matchesGrade = name.includes('初三') || name.includes('初3');\n            break;\n          default:\n            matchesGrade = false;\n        }\n\n        return matchesGrade;\n      });\n\n      console.log('🏫 筛选出的班级数量:', gradeClasses.length);\n      setClasses(gradeClasses);\n\n      // 清空班级和作业选择\n      setFilters(prev => ({\n        ...prev,\n        selectedClass: '',\n        selectedAssignment: ''\n      }));\n    } else {\n      setClasses([]);\n      setFilters(prev => ({\n        ...prev,\n        selectedClass: '',\n        selectedAssignment: ''\n      }));\n    }\n  }, [filters.selectedGrade, allClasses]);\n\n  // 应用筛选条件\n  useEffect(() => {\n    let filtered = [...assignments];\n\n    // 班级筛选 - 通过class_id匹配\n    if (filters.selectedClass) {\n      const selectedClassId = parseInt(filters.selectedClass);\n      filtered = filtered.filter(assignment =>\n        assignment.class_id === selectedClassId\n      );\n    }\n\n    // 作业筛选（按作业分配ID筛选）\n    if (filters.selectedAssignment) {\n      filtered = filtered.filter(assignment =>\n        assignment.id === parseInt(filters.selectedAssignment)\n      );\n    }\n\n    // 搜索筛选\n    if (filters.searchText) {\n      filtered = filtered.filter(assignment =>\n        assignment.title?.toLowerCase().includes(filters.searchText.toLowerCase()) ||\n        assignment.description?.toLowerCase().includes(filters.searchText.toLowerCase())\n      );\n    }\n\n    setFilteredAssignments(filtered);\n  }, [assignments, filters, classes]);\n\n  // 处理筛选条件变化\n  const handleFilterChange = (key, value) => {\n    const newFilters = {\n      ...filters,\n      [key]: value\n    };\n\n    setFilters(newFilters);\n\n    // 保存到localStorage\n    try {\n      localStorage.setItem('homeworkAnalysisFilters', JSON.stringify(newFilters));\n      console.log('💾 筛选条件已保存到localStorage:', newFilters);\n    } catch (error) {\n      console.error('保存筛选条件失败:', error);\n    }\n  };\n\n  // 清除所有筛选条件\n  const clearAllFilters = () => {\n    const emptyFilters = {\n      selectedGrade: '',\n      selectedClass: '',\n      selectedAssignment: '',\n      searchText: ''\n    };\n\n    setFilters(emptyFilters);\n\n    // 清除localStorage\n    try {\n      localStorage.removeItem('homeworkAnalysisFilters');\n      localStorage.removeItem('lastAssignmentId');\n      console.log('🗑️ 已清除所有筛选条件');\n    } catch (error) {\n      console.error('清除筛选条件失败:', error);\n    }\n  };\n\n  // 获取当前班级的作业分配\n  const getCurrentClassAssignments = () => {\n    if (!filters.selectedClass) return [];\n\n    const selectedClassId = parseInt(filters.selectedClass);\n\n    // 筛选出属于当前班级的作业分配\n    return assignments.filter(assignment =>\n      assignment.class_id === selectedClassId\n    );\n  };\n\n  // 跳转到作业分析页面\n  const handleAnalyzeAssignment = (assignmentId) => {\n    navigate(`/homework-analysis/overview?assignmentId=${assignmentId}`);\n  };\n\n  // 获取状态标签\n  const getStatusTag = (status) => {\n    switch (status) {\n      case 'published':\n        return <Tag color=\"green\">已发布</Tag>;\n      case 'completed':\n        return <Tag color=\"blue\">已完成</Tag>;\n      case 'draft':\n        return <Tag color=\"orange\">草稿</Tag>;\n      default:\n        return <Tag>{status}</Tag>;\n    }\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '作业标题',\n      dataIndex: 'title',\n      key: 'title',\n      render: (title, record) => (\n        <Space direction=\"vertical\" size=\"small\">\n          <Text strong>{title}</Text>\n          {record.description && (\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              {record.description.length > 50 \n                ? `${record.description.substring(0, 50)}...` \n                : record.description\n              }\n            </Text>\n          )}\n        </Space>\n      )\n    },\n    {\n      title: '班级',\n      dataIndex: 'class_name',\n      key: 'class_name',\n      width: 120,\n      render: (className) => (\n        <Space>\n          <TeamOutlined />\n          <Text>{className || '未知班级'}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '科目',\n      dataIndex: 'subject_name',\n      key: 'subject_name',\n      width: 100,\n      render: (subjectName) => (\n        <Space>\n          <BookOutlined />\n          <Text>{subjectName || '未知科目'}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status) => getStatusTag(status)\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      width: 120,\n      render: (createdAt) => (\n        <Space>\n          <CalendarOutlined />\n          <Text>{createdAt ? new Date(createdAt).toLocaleDateString() : '未知'}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => (\n        <Button\n          type=\"primary\"\n          icon={<BarChartOutlined />}\n          onClick={() => handleAnalyzeAssignment(record.id)}\n        >\n          分析\n        </Button>\n      )\n    }\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>\n        <BarChartOutlined /> 作业分析 - 选择作业\n      </Title>\n      \n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        {/* 筛选条件 */}\n        <Card title={`筛选条件 (年级: ${grades.length}, 班级: ${classes.length}, 作业: ${assignments.length})`} size=\"small\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} sm={6}>\n              <Select\n                placeholder={`选择年级 (${grades.length}个可选)`}\n                style={{ width: '100%' }}\n                value={filters.selectedGrade}\n                onChange={(value) => handleFilterChange('selectedGrade', value)}\n                allowClear\n                notFoundContent={grades.length === 0 ? \"正在加载年级...\" : \"没有找到年级\"}\n              >\n                {grades.map(grade => (\n                  <Option key={grade} value={grade}>\n                    {grade}\n                  </Option>\n                ))}\n              </Select>\n            </Col>\n\n            <Col xs={24} sm={6}>\n              <Select\n                placeholder={`选择班级 (${classes.length}个可选)`}\n                style={{ width: '100%' }}\n                value={filters.selectedClass}\n                onChange={(value) => handleFilterChange('selectedClass', value)}\n                disabled={!filters.selectedGrade}\n                allowClear\n                notFoundContent={classes.length === 0 ? \"请先选择年级\" : \"没有找到班级\"}\n              >\n                {classes.map(cls => (\n                  <Option key={cls.id} value={cls.id}>\n                    {cls.name}\n                  </Option>\n                ))}\n              </Select>\n            </Col>\n\n            <Col xs={24} sm={6}>\n              <Select\n                placeholder=\"选择作业\"\n                style={{ width: '100%' }}\n                value={filters.selectedAssignment}\n                onChange={(value) => handleFilterChange('selectedAssignment', value)}\n                disabled={!filters.selectedClass}\n                allowClear\n              >\n                {getCurrentClassAssignments().map(assignment => (\n                  <Option key={assignment.id} value={assignment.id}>\n                    {assignment.title}\n                  </Option>\n                ))}\n              </Select>\n            </Col>\n\n            <Col xs={24} sm={4}>\n              <Input\n                placeholder=\"搜索作业标题\"\n                prefix={<SearchOutlined />}\n                value={filters.searchText}\n                onChange={(e) => handleFilterChange('searchText', e.target.value)}\n                allowClear\n              />\n            </Col>\n\n            <Col xs={24} sm={1}>\n              <Button\n                type=\"primary\"\n                icon={<BarChartOutlined />}\n                disabled={!filters.selectedAssignment}\n                onClick={() => handleAnalyzeAssignment(filters.selectedAssignment)}\n                style={{ width: '100%' }}\n              >\n                分析\n              </Button>\n            </Col>\n\n            <Col xs={24} sm={1}>\n              <Button\n                icon={<ClearOutlined />}\n                onClick={clearAllFilters}\n                title=\"清除所有筛选条件\"\n                style={{ width: '100%' }}\n              >\n                清除\n              </Button>\n            </Col>\n          </Row>\n\n          {/* 筛选状态提示 */}\n          {(filters.selectedGrade || filters.selectedClass || filters.selectedAssignment) && (\n            <div style={{ marginTop: '12px', padding: '8px', background: '#f6ffed', borderRadius: '4px' }}>\n              <Text type=\"secondary\">\n                当前筛选:\n                {filters.selectedGrade && <Tag color=\"blue\">{filters.selectedGrade}</Tag>}\n                {filters.selectedClass && <Tag color=\"green\">{classes.find(c => c.id === parseInt(filters.selectedClass))?.name}</Tag>}\n                {filters.selectedAssignment && <Tag color=\"orange\">{assignments.find(a => a.assignment_id === parseInt(filters.selectedAssignment))?.title}</Tag>}\n              </Text>\n            </div>\n          )}\n        </Card>\n        \n        {/* 作业列表 */}\n        <Card title={`作业列表 (${filteredAssignments.length} 个可分析的作业)`}>\n          <Spin spinning={loading}>\n            {filteredAssignments.length > 0 ? (\n              <Table\n                columns={columns}\n                dataSource={filteredAssignments}\n                rowKey=\"id\"\n                pagination={{\n                  pageSize: 10,\n                  showSizeChanger: true,\n                  showQuickJumper: true,\n                  showTotal: (total) => `共 ${total} 个作业`\n                }}\n              />\n            ) : (\n              <Alert\n                message=\"暂无可分析的作业\"\n                description={\n                  loading \n                    ? \"正在加载作业列表...\" \n                    : \"没有找到符合条件的作业，请检查筛选条件或确保有已发布的作业。\"\n                }\n                type=\"info\"\n                showIcon\n                action={\n                  !loading && (\n                    <Button size=\"small\" onClick={fetchInitialData}>\n                      刷新列表\n                    </Button>\n                  )\n                }\n              />\n            )}\n          </Spin>\n        </Card>\n        \n        {/* 使用说明 */}\n        <Card title=\"使用说明\" size=\"small\">\n          <Space direction=\"vertical\">\n            <Text>• 选择一个已发布或已完成的作业进行分析</Text>\n            <Text>• 作业分析包含概览、逐题分析、学生详情、智能建议等功能</Text>\n            <Text>• 可以生成家长报告和导出分析数据</Text>\n            <Text>• 只有教师和管理员可以访问作业分析功能</Text>\n          </Space>\n        </Card>\n      </Space>\n    </div>\n  );\n};\n\nexport default AssignmentSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EACrDC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QACrB,MAAM;AACb,SACEC,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,EACZC,aAAa,QACR,mBAAmB;AAC1B,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGf,UAAU;AAClC,MAAM;EAAEgB;AAAO,CAAC,GAAGb,MAAM;AAEzB,MAAMc,kBAAkB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,iBAAA;EACvC,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM6C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI;MACF,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,yBAAyB,CAAC;MACpE,IAAIF,YAAY,EAAE;QAChB,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC;QACvCM,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,MAAM,CAAC;QAC9C,OAAO;UACLK,aAAa,EAAEL,MAAM,CAACK,aAAa,IAAI,EAAE;UACzCC,aAAa,EAAEN,MAAM,CAACM,aAAa,IAAI,EAAE;UACzCC,kBAAkB,EAAEP,MAAM,CAACO,kBAAkB,IAAI,EAAE;UACnDC,UAAU,EAAER,MAAM,CAACQ,UAAU,IAAI;QACnC,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;IACA,OAAO;MACLJ,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE,EAAE;MACtBC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;;EAED;EACA,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC6C,iBAAiB,CAAC;;EAEzD;EACA,MAAMgB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACwB,mBAAmB,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/D5C,GAAG,CAAC6C,GAAG,CAAC,sBAAsB,CAAC;MAAG;MAClC7C,GAAG,CAAC6C,GAAG,CAAC,gBAAgB,CAAC,CAAE;MAAA,CAC5B,CAAC;;MAEF;MACA,MAAMC,eAAe,GAAGL,mBAAmB;MAC3CV,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEc,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEC,MAAM,EAAE,OAAO,CAAC;MAE/D,IAAIC,KAAK,CAACC,OAAO,CAACH,eAAe,CAAC,EAAE;QAClC;QACA,MAAMI,gBAAgB,GAAGJ,eAAe,CAACK,MAAM,CAACC,UAAU,IAAI;UAC5D;UACA,OAAOA,UAAU,CAACC,EAAE,IAAID,UAAU,CAACE,KAAK;QAC1C,CAAC,CAAC;QACFvB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEH,MAAM,EAAE,UAAU,CAAC;QACnElC,cAAc,CAACqC,gBAAgB,CAAC;QAChCnC,sBAAsB,CAACmC,gBAAgB,CAAC;MAC1C,CAAC,MAAM;QACLnB,OAAO,CAACM,KAAK,CAAC,eAAe,EAAES,eAAe,CAAC;QAC/CjC,cAAc,CAAC,EAAE,CAAC;QAClBE,sBAAsB,CAAC,EAAE,CAAC;MAC5B;;MAEA;MACA;MACA,MAAMwC,WAAW,GAAGb,eAAe;MACnCX,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEuB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAER,MAAM,EAAE,KAAK,CAAC;MAEvD,IAAIC,KAAK,CAACC,OAAO,CAACM,WAAW,CAAC,EAAE;QAC9BhC,aAAa,CAACgC,WAAW,CAAC;;QAE1B;QACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;QAC1B,IAAIC,YAAY,GAAG,CAAC;QAEpBH,WAAW,CAACI,OAAO,CAACC,GAAG,IAAI;UACzB,IAAIA,GAAG,CAACC,IAAI,EAAE;YACZ;YACA,IAAIC,KAAK,GAAG,IAAI;;YAEhB;YACA,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;cAC5BD,KAAK,GAAG,KAAK;YACf,CAAC,MAAM,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;cACnCD,KAAK,GAAG,KAAK;YACf,CAAC,MAAM,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE;cACnCD,KAAK,GAAG,KAAK;YACf;YACA;YAAA,KACK,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC3DD,KAAK,GAAG,IAAI;YACd,CAAC,MAAM,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC7DD,KAAK,GAAG,IAAI;YACd,CAAC,MAAM,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC7DD,KAAK,GAAG,IAAI;YACd;YACA;YAAA,KACK,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC3DD,KAAK,GAAG,IAAI;YACd,CAAC,MAAM,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC7DD,KAAK,GAAG,IAAI;YACd,CAAC,MAAM,IAAIF,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACC,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC7DD,KAAK,GAAG,IAAI;YACd;YAEA,IAAIA,KAAK,EAAE;cACTN,QAAQ,CAACQ,GAAG,CAACF,KAAK,CAAC;cACnBJ,YAAY,EAAE;YAChB;UACF;QACF,CAAC,CAAC;QAEF,MAAMO,UAAU,GAAGjB,KAAK,CAACkB,IAAI,CAACV,QAAQ,CAAC,CAACW,IAAI,CAAC,CAAC;QAC9CpC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiC,UAAU,EAAE,OAAOP,YAAY,MAAM,CAAC;QAClEvC,SAAS,CAAC8C,UAAU,CAAC;MACvB,CAAC,MAAM;QACLlC,OAAO,CAACM,KAAK,CAAC,aAAa,EAAEkB,WAAW,CAAC;QACzChC,aAAa,CAAC,EAAE,CAAC;QACjBJ,SAAS,CAAC,EAAE,CAAC;MACf;IAEF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxB,cAAc,CAAC,EAAE,CAAC;MAClBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BQ,aAAa,CAAC,EAAE,CAAC;MACjBJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDrC,SAAS,CAAC,MAAM;IACd4D,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5D,SAAS,CAAC,MAAM;IACdmD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEd,MAAM,CAAC6B,MAAM,EAAE,UAAU,EAAE3B,OAAO,CAAC2B,MAAM,EAAE,cAAc,EAAEnC,WAAW,CAACmC,MAAM,CAAC;EACjH,CAAC,EAAE,CAAC7B,MAAM,EAAEE,OAAO,EAAER,WAAW,CAAC,CAAC;;EAElC;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI0D,OAAO,CAACL,aAAa,EAAE;MACzBF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEM,OAAO,CAACL,aAAa,CAAC;MAE9C,MAAMmC,YAAY,GAAG9C,UAAU,CAAC6B,MAAM,CAACS,GAAG,IAAI;QAC5C,IAAI,CAACA,GAAG,CAACC,IAAI,EAAE,OAAO,KAAK;;QAE3B;QACA,MAAMA,IAAI,GAAGD,GAAG,CAACC,IAAI;QACrB,IAAIQ,YAAY,GAAG,KAAK;QAExB,QAAQ/B,OAAO,CAACL,aAAa;UAC3B,KAAK,KAAK;YACRoC,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC;YACnC;UACF,KAAK,KAAK;YACRM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC;YACnC;UACF,KAAK,KAAK;YACRM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,KAAK,CAAC;YACnC;UACF,KAAK,IAAI;YACPM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC;YACzD;UACF,KAAK,IAAI;YACPM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC;YACzD;UACF,KAAK,IAAI;YACPM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC;YACzD;UACF,KAAK,IAAI;YACPM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC;YACzD;UACF,KAAK,IAAI;YACPM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC;YACzD;UACF,KAAK,IAAI;YACPM,YAAY,GAAGR,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACE,QAAQ,CAAC,IAAI,CAAC;YACzD;UACF;YACEM,YAAY,GAAG,KAAK;QACxB;QAEA,OAAOA,YAAY;MACrB,CAAC,CAAC;MAEFtC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoC,YAAY,CAACrB,MAAM,CAAC;MAChD1B,UAAU,CAAC+C,YAAY,CAAC;;MAExB;MACA7B,UAAU,CAAC+B,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPpC,aAAa,EAAE,EAAE;QACjBC,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLd,UAAU,CAAC,EAAE,CAAC;MACdkB,UAAU,CAAC+B,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPpC,aAAa,EAAE,EAAE;QACjBC,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACG,OAAO,CAACL,aAAa,EAAEX,UAAU,CAAC,CAAC;;EAEvC;EACA1C,SAAS,CAAC,MAAM;IACd,IAAI2F,QAAQ,GAAG,CAAC,GAAG3D,WAAW,CAAC;;IAE/B;IACA,IAAI0B,OAAO,CAACJ,aAAa,EAAE;MACzB,MAAMsC,eAAe,GAAGC,QAAQ,CAACnC,OAAO,CAACJ,aAAa,CAAC;MACvDqC,QAAQ,GAAGA,QAAQ,CAACpB,MAAM,CAACC,UAAU,IACnCA,UAAU,CAACsB,QAAQ,KAAKF,eAC1B,CAAC;IACH;;IAEA;IACA,IAAIlC,OAAO,CAACH,kBAAkB,EAAE;MAC9BoC,QAAQ,GAAGA,QAAQ,CAACpB,MAAM,CAACC,UAAU,IACnCA,UAAU,CAACC,EAAE,KAAKoB,QAAQ,CAACnC,OAAO,CAACH,kBAAkB,CACvD,CAAC;IACH;;IAEA;IACA,IAAIG,OAAO,CAACF,UAAU,EAAE;MACtBmC,QAAQ,GAAGA,QAAQ,CAACpB,MAAM,CAACC,UAAU;QAAA,IAAAuB,iBAAA,EAAAC,qBAAA;QAAA,OACnC,EAAAD,iBAAA,GAAAvB,UAAU,CAACE,KAAK,cAAAqB,iBAAA,uBAAhBA,iBAAA,CAAkBE,WAAW,CAAC,CAAC,CAACd,QAAQ,CAACzB,OAAO,CAACF,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC,OAAAD,qBAAA,GAC1ExB,UAAU,CAAC0B,WAAW,cAAAF,qBAAA,uBAAtBA,qBAAA,CAAwBC,WAAW,CAAC,CAAC,CAACd,QAAQ,CAACzB,OAAO,CAACF,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC;MAAA,CAClF,CAAC;IACH;IAEA9D,sBAAsB,CAACwD,QAAQ,CAAC;EAClC,CAAC,EAAE,CAAC3D,WAAW,EAAE0B,OAAO,EAAElB,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAM2D,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzC,MAAMC,UAAU,GAAG;MACjB,GAAG5C,OAAO;MACV,CAAC0C,GAAG,GAAGC;IACT,CAAC;IAED1C,UAAU,CAAC2C,UAAU,CAAC;;IAEtB;IACA,IAAI;MACFxD,YAAY,CAACyD,OAAO,CAAC,yBAAyB,EAAEtD,IAAI,CAACuD,SAAS,CAACF,UAAU,CAAC,CAAC;MAC3EnD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkD,UAAU,CAAC;IACrD,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMgD,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG;MACnBrD,aAAa,EAAE,EAAE;MACjBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE,EAAE;MACtBC,UAAU,EAAE;IACd,CAAC;IAEDG,UAAU,CAAC+C,YAAY,CAAC;;IAExB;IACA,IAAI;MACF5D,YAAY,CAAC6D,UAAU,CAAC,yBAAyB,CAAC;MAClD7D,YAAY,CAAC6D,UAAU,CAAC,kBAAkB,CAAC;MAC3CxD,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMmD,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAAClD,OAAO,CAACJ,aAAa,EAAE,OAAO,EAAE;IAErC,MAAMsC,eAAe,GAAGC,QAAQ,CAACnC,OAAO,CAACJ,aAAa,CAAC;;IAEvD;IACA,OAAOtB,WAAW,CAACuC,MAAM,CAACC,UAAU,IAClCA,UAAU,CAACsB,QAAQ,KAAKF,eAC1B,CAAC;EACH,CAAC;;EAED;EACA,MAAMiB,uBAAuB,GAAIC,YAAY,IAAK;IAChD/E,QAAQ,CAAC,4CAA4C+E,YAAY,EAAE,CAAC;EACtE,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAO1F,OAAA,CAACf,GAAG;UAAC0G,KAAK,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrC,KAAK,WAAW;QACd,oBAAOhG,OAAA,CAACf,GAAG;UAAC0G,KAAK,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACpC,KAAK,OAAO;QACV,oBAAOhG,OAAA,CAACf,GAAG;UAAC0G,KAAK,EAAC,QAAQ;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrC;QACE,oBAAOhG,OAAA,CAACf,GAAG;UAAA2G,QAAA,EAAEF;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACE7C,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,OAAO;IAClBpB,GAAG,EAAE,OAAO;IACZqB,MAAM,EAAEA,CAAC/C,KAAK,EAAEgD,MAAM,kBACpBpG,OAAA,CAACd,KAAK;MAACmH,SAAS,EAAC,UAAU;MAACC,IAAI,EAAC,OAAO;MAAAV,QAAA,gBACtC5F,OAAA,CAACE,IAAI;QAACqG,MAAM;QAAAX,QAAA,EAAExC;MAAK;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAC1BI,MAAM,CAACxB,WAAW,iBACjB5E,OAAA,CAACE,IAAI;QAACsG,IAAI,EAAC,WAAW;QAACC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAd,QAAA,EAChDQ,MAAM,CAACxB,WAAW,CAAC/B,MAAM,GAAG,EAAE,GAC3B,GAAGuD,MAAM,CAACxB,WAAW,CAAC+B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC3CP,MAAM,CAACxB;MAAW;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAElB,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,EACD;IACE5C,KAAK,EAAE,IAAI;IACX8C,SAAS,EAAE,YAAY;IACvBpB,GAAG,EAAE,YAAY;IACjB8B,KAAK,EAAE,GAAG;IACVT,MAAM,EAAGU,SAAS,iBAChB7G,OAAA,CAACd,KAAK;MAAA0G,QAAA,gBACJ5F,OAAA,CAACJ,YAAY;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBhG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAEiB,SAAS,IAAI;MAAM;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAEX,CAAC,EACD;IACE5C,KAAK,EAAE,IAAI;IACX8C,SAAS,EAAE,cAAc;IACzBpB,GAAG,EAAE,cAAc;IACnB8B,KAAK,EAAE,GAAG;IACVT,MAAM,EAAGW,WAAW,iBAClB9G,OAAA,CAACd,KAAK;MAAA0G,QAAA,gBACJ5F,OAAA,CAACL,YAAY;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBhG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAEkB,WAAW,IAAI;MAAM;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAEX,CAAC,EACD;IACE5C,KAAK,EAAE,IAAI;IACX8C,SAAS,EAAE,QAAQ;IACnBpB,GAAG,EAAE,QAAQ;IACb8B,KAAK,EAAE,GAAG;IACVT,MAAM,EAAGT,MAAM,IAAKD,YAAY,CAACC,MAAM;EACzC,CAAC,EACD;IACEtC,KAAK,EAAE,MAAM;IACb8C,SAAS,EAAE,YAAY;IACvBpB,GAAG,EAAE,YAAY;IACjB8B,KAAK,EAAE,GAAG;IACVT,MAAM,EAAGY,SAAS,iBAChB/G,OAAA,CAACd,KAAK;MAAA0G,QAAA,gBACJ5F,OAAA,CAACN,gBAAgB;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpBhG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAEmB,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;MAAI;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE;EAEX,CAAC,EACD;IACE5C,KAAK,EAAE,IAAI;IACX0B,GAAG,EAAE,QAAQ;IACb8B,KAAK,EAAE,GAAG;IACVT,MAAM,EAAEA,CAACe,CAAC,EAAEd,MAAM,kBAChBpG,OAAA,CAACjB,MAAM;MACLyH,IAAI,EAAC,SAAS;MACdW,IAAI,eAAEnH,OAAA,CAACP,gBAAgB;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC3BoB,OAAO,EAAEA,CAAA,KAAM7B,uBAAuB,CAACa,MAAM,CAACjD,EAAE,CAAE;MAAAyC,QAAA,EACnD;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAEZ,CAAC,CACF;EAED,oBACEhG,OAAA;IAAKyG,KAAK,EAAE;MAAEY,OAAO,EAAE;IAAO,CAAE;IAAAzB,QAAA,gBAC9B5F,OAAA,CAACC,KAAK;MAACqH,KAAK,EAAE,CAAE;MAAA1B,QAAA,gBACd5F,OAAA,CAACP,gBAAgB;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,wDACtB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERhG,OAAA,CAACd,KAAK;MAACmH,SAAS,EAAC,UAAU;MAACC,IAAI,EAAC,OAAO;MAACG,KAAK,EAAE;QAAEG,KAAK,EAAE;MAAO,CAAE;MAAAhB,QAAA,gBAEhE5F,OAAA,CAACpB,IAAI;QAACwE,KAAK,EAAE,aAAapC,MAAM,CAAC6B,MAAM,SAAS3B,OAAO,CAAC2B,MAAM,SAASnC,WAAW,CAACmC,MAAM,GAAI;QAACyD,IAAI,EAAC,OAAO;QAAAV,QAAA,gBACxG5F,OAAA,CAACnB,GAAG;UAAC0I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAA3B,QAAA,gBACpB5F,OAAA,CAAClB,GAAG;YAAC0I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACjB5F,OAAA,CAACV,MAAM;cACLoI,WAAW,EAAE,SAAS1G,MAAM,CAAC6B,MAAM,MAAO;cAC1C4D,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAO,CAAE;cACzB7B,KAAK,EAAE3C,OAAO,CAACL,aAAc;cAC7B4F,QAAQ,EAAG5C,KAAK,IAAKF,kBAAkB,CAAC,eAAe,EAAEE,KAAK,CAAE;cAChE6C,UAAU;cACVC,eAAe,EAAE7G,MAAM,CAAC6B,MAAM,KAAK,CAAC,GAAG,WAAW,GAAG,QAAS;cAAA+C,QAAA,EAE7D5E,MAAM,CAAC8G,GAAG,CAAClE,KAAK,iBACf5D,OAAA,CAACG,MAAM;gBAAa4E,KAAK,EAAEnB,KAAM;gBAAAgC,QAAA,EAC9BhC;cAAK,GADKA,KAAK;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhG,OAAA,CAAClB,GAAG;YAAC0I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACjB5F,OAAA,CAACV,MAAM;cACLoI,WAAW,EAAE,SAASxG,OAAO,CAAC2B,MAAM,MAAO;cAC3C4D,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAO,CAAE;cACzB7B,KAAK,EAAE3C,OAAO,CAACJ,aAAc;cAC7B2F,QAAQ,EAAG5C,KAAK,IAAKF,kBAAkB,CAAC,eAAe,EAAEE,KAAK,CAAE;cAChEgD,QAAQ,EAAE,CAAC3F,OAAO,CAACL,aAAc;cACjC6F,UAAU;cACVC,eAAe,EAAE3G,OAAO,CAAC2B,MAAM,KAAK,CAAC,GAAG,QAAQ,GAAG,QAAS;cAAA+C,QAAA,EAE3D1E,OAAO,CAAC4G,GAAG,CAACpE,GAAG,iBACd1D,OAAA,CAACG,MAAM;gBAAc4E,KAAK,EAAErB,GAAG,CAACP,EAAG;gBAAAyC,QAAA,EAChClC,GAAG,CAACC;cAAI,GADED,GAAG,CAACP,EAAE;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEX,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhG,OAAA,CAAClB,GAAG;YAAC0I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACjB5F,OAAA,CAACV,MAAM;cACLoI,WAAW,EAAC,0BAAM;cAClBjB,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAO,CAAE;cACzB7B,KAAK,EAAE3C,OAAO,CAACH,kBAAmB;cAClC0F,QAAQ,EAAG5C,KAAK,IAAKF,kBAAkB,CAAC,oBAAoB,EAAEE,KAAK,CAAE;cACrEgD,QAAQ,EAAE,CAAC3F,OAAO,CAACJ,aAAc;cACjC4F,UAAU;cAAAhC,QAAA,EAETN,0BAA0B,CAAC,CAAC,CAACwC,GAAG,CAAC5E,UAAU,iBAC1ClD,OAAA,CAACG,MAAM;gBAAqB4E,KAAK,EAAE7B,UAAU,CAACC,EAAG;gBAAAyC,QAAA,EAC9C1C,UAAU,CAACE;cAAK,GADNF,UAAU,CAACC,EAAE;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhG,OAAA,CAAClB,GAAG;YAAC0I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACjB5F,OAAA,CAACX,KAAK;cACJqI,WAAW,EAAC,sCAAQ;cACpBM,MAAM,eAAEhI,OAAA,CAACR,cAAc;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BjB,KAAK,EAAE3C,OAAO,CAACF,UAAW;cAC1ByF,QAAQ,EAAGM,CAAC,IAAKpD,kBAAkB,CAAC,YAAY,EAAEoD,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;cAClE6C,UAAU;YAAA;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhG,OAAA,CAAClB,GAAG;YAAC0I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACjB5F,OAAA,CAACjB,MAAM;cACLyH,IAAI,EAAC,SAAS;cACdW,IAAI,eAAEnH,OAAA,CAACP,gBAAgB;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3B+B,QAAQ,EAAE,CAAC3F,OAAO,CAACH,kBAAmB;cACtCmF,OAAO,EAAEA,CAAA,KAAM7B,uBAAuB,CAACnD,OAAO,CAACH,kBAAkB,CAAE;cACnEwE,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAO,CAAE;cAAAhB,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhG,OAAA,CAAClB,GAAG;YAAC0I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACjB5F,OAAA,CAACjB,MAAM;cACLoI,IAAI,eAAEnH,OAAA,CAACH,aAAa;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBoB,OAAO,EAAEjC,eAAgB;cACzB/B,KAAK,EAAC,kDAAU;cAChBqD,KAAK,EAAE;gBAAEG,KAAK,EAAE;cAAO,CAAE;cAAAhB,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC5D,OAAO,CAACL,aAAa,IAAIK,OAAO,CAACJ,aAAa,IAAII,OAAO,CAACH,kBAAkB,kBAC5EjC,OAAA;UAAKyG,KAAK,EAAE;YAAE0B,SAAS,EAAE,MAAM;YAAEd,OAAO,EAAE,KAAK;YAAEe,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAzC,QAAA,eAC5F5F,OAAA,CAACE,IAAI;YAACsG,IAAI,EAAC,WAAW;YAAAZ,QAAA,GAAC,2BAErB,EAACxD,OAAO,CAACL,aAAa,iBAAI/B,OAAA,CAACf,GAAG;cAAC0G,KAAK,EAAC,MAAM;cAAAC,QAAA,EAAExD,OAAO,CAACL;YAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACxE5D,OAAO,CAACJ,aAAa,iBAAIhC,OAAA,CAACf,GAAG;cAAC0G,KAAK,EAAC,OAAO;cAAAC,QAAA,GAAArF,aAAA,GAAEW,OAAO,CAACoH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpF,EAAE,KAAKoB,QAAQ,CAACnC,OAAO,CAACJ,aAAa,CAAC,CAAC,cAAAzB,aAAA,uBAA3DA,aAAA,CAA6DoD;YAAI;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrH5D,OAAO,CAACH,kBAAkB,iBAAIjC,OAAA,CAACf,GAAG;cAAC0G,KAAK,EAAC,QAAQ;cAAAC,QAAA,GAAApF,iBAAA,GAAEE,WAAW,CAAC4H,IAAI,CAACE,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAKlE,QAAQ,CAACnC,OAAO,CAACH,kBAAkB,CAAC,CAAC,cAAAzB,iBAAA,uBAA/EA,iBAAA,CAAiF4C;YAAK;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7I;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGPhG,OAAA,CAACpB,IAAI;QAACwE,KAAK,EAAE,SAASxC,mBAAmB,CAACiC,MAAM,WAAY;QAAA+C,QAAA,eAC1D5F,OAAA,CAACT,IAAI;UAACmJ,QAAQ,EAAE5H,OAAQ;UAAA8E,QAAA,EACrBhF,mBAAmB,CAACiC,MAAM,GAAG,CAAC,gBAC7B7C,OAAA,CAAChB,KAAK;YACJiH,OAAO,EAAEA,OAAQ;YACjB0C,UAAU,EAAE/H,mBAAoB;YAChCgI,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC;UAAE;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFhG,OAAA,CAACZ,KAAK;YACJ+J,OAAO,EAAC,kDAAU;YAClBvE,WAAW,EACT9D,OAAO,GACH,aAAa,GACb,gCACL;YACD0F,IAAI,EAAC,MAAM;YACX4C,QAAQ;YACRC,MAAM,EACJ,CAACvI,OAAO,iBACNd,OAAA,CAACjB,MAAM;cAACuH,IAAI,EAAC,OAAO;cAACc,OAAO,EAAE9E,gBAAiB;cAAAsD,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAEX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPhG,OAAA,CAACpB,IAAI;QAACwE,KAAK,EAAC,0BAAM;QAACkD,IAAI,EAAC,OAAO;QAAAV,QAAA,eAC7B5F,OAAA,CAACd,KAAK;UAACmH,SAAS,EAAC,UAAU;UAAAT,QAAA,gBACzB5F,OAAA,CAACE,IAAI;YAAA0F,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjChG,OAAA,CAACE,IAAI;YAAA0F,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzChG,OAAA,CAACE,IAAI;YAAA0F,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9BhG,OAAA,CAACE,IAAI;YAAA0F,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1F,EAAA,CAtiBIF,kBAAkB;EAAA,QACLzB,WAAW;AAAA;AAAA2K,EAAA,GADxBlJ,kBAAkB;AAwiBxB,eAAeA,kBAAkB;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}