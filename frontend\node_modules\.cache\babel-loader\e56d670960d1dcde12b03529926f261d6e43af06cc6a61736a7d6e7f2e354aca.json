{"ast": null, "code": "const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}