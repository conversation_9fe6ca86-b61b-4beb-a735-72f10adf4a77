{"ast": null, "code": "import genFontSizes from './genFontSizes';\nconst genFontMapToken = fontSize => {\n  const fontSizePairs = genFontSizes(fontSize);\n  const fontSizes = fontSizePairs.map(pair => pair.size);\n  const lineHeights = fontSizePairs.map(pair => pair.lineHeight);\n  const fontSizeMD = fontSizes[1];\n  const fontSizeSM = fontSizes[0];\n  const fontSizeLG = fontSizes[2];\n  const lineHeight = lineHeights[1];\n  const lineHeightSM = lineHeights[0];\n  const lineHeightLG = lineHeights[2];\n  return {\n    fontSizeSM,\n    fontSize: fontSizeMD,\n    fontSizeLG,\n    fontSizeXL: fontSizes[3],\n    fontSizeHeading1: fontSizes[6],\n    fontSizeHeading2: fontSizes[5],\n    fontSizeHeading3: fontSizes[4],\n    fontSizeHeading4: fontSizes[3],\n    fontSizeHeading5: fontSizes[2],\n    lineHeight,\n    lineHeightLG,\n    lineHeightSM,\n    fontHeight: Math.round(lineHeight * fontSizeMD),\n    fontHeightLG: Math.round(lineHeightLG * fontSizeLG),\n    fontHeightSM: Math.round(lineHeightSM * fontSizeSM),\n    lineHeightHeading1: lineHeights[6],\n    lineHeightHeading2: lineHeights[5],\n    lineHeightHeading3: lineHeights[4],\n    lineHeightHeading4: lineHeights[3],\n    lineHeightHeading5: lineHeights[2]\n  };\n};\nexport default genFontMapToken;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}