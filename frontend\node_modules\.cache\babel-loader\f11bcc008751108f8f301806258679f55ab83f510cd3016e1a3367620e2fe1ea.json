{"ast": null, "code": "const genSpaceCompactStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-block': {\n        display: 'flex',\n        width: '100%'\n      },\n      '&-vertical': {\n        flexDirection: 'column'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSpaceCompactStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}