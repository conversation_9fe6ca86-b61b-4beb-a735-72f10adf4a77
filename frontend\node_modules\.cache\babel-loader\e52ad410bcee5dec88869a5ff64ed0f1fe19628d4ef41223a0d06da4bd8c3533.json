{"ast": null, "code": "import * as React from 'react';\nexport default function useResizable(items, pxSizes, isRTL) {\n  return React.useMemo(() => {\n    const resizeInfos = [];\n    for (let i = 0; i < items.length - 1; i += 1) {\n      const prevItem = items[i];\n      const nextItem = items[i + 1];\n      const prevSize = pxSizes[i];\n      const nextSize = pxSizes[i + 1];\n      const {\n        resizable: prevResizable = true,\n        min: prevMin,\n        collapsible: prevCollapsible\n      } = prevItem;\n      const {\n        resizable: nextResizable = true,\n        min: nextMin,\n        collapsible: nextCollapsible\n      } = nextItem;\n      const mergedResizable =\n      // Both need to be resizable\n      prevResizable && nextResizable && (\n      // Prev is not collapsed and limit min size\n      prevSize !== 0 || !prevMin) && (\n      // Next is not collapsed and limit min size\n      nextSize !== 0 || !nextMin);\n      const startCollapsible =\n      // Self is collapsible\n      prevCollapsible.end && prevSize > 0 ||\n      // Collapsed and can be collapsed\n      nextCollapsible.start && nextSize === 0 && prevSize > 0;\n      const endCollapsible =\n      // Self is collapsible\n      nextCollapsible.start && nextSize > 0 ||\n      // Collapsed and can be collapsed\n      prevCollapsible.end && prevSize === 0 && nextSize > 0;\n      resizeInfos[i] = {\n        resizable: mergedResizable,\n        startCollapsible: !!(isRTL ? endCollapsible : startCollapsible),\n        endCollapsible: !!(isRTL ? startCollapsible : endCollapsible)\n      };\n    }\n    return resizeInfos;\n  }, [pxSizes, items]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}