{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\HomeworkAnalysis\\\\ParentReport.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Button, Select, Space, Typography, Alert, Descriptions, List, Progress, Tag, Modal } from 'antd';\nimport api from '../../utils/api';\nimport { UserOutlined, TrophyOutlined, HeartOutlined, BookOutlined, LineChartOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Option\n} = Select;\nconst ParentReport = ({\n  assignmentId,\n  user,\n  onLoading\n}) => {\n  _s();\n  const [studentsData, setStudentsData] = useState([]);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [error, setError] = useState(null);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // 获取学生列表\n  const fetchStudentsData = async () => {\n    if (!assignmentId) return;\n    try {\n      onLoading(true);\n      const response = await api.get(`/homework-analysis/students/${assignmentId}`);\n      if (response.success) {\n        setStudentsData(response.data.students || []);\n        if (response.data.students && response.data.students.length > 0) {\n          setSelectedStudent(response.data.students[0].student_id);\n        }\n        setError(null);\n      } else {\n        throw new Error(response.message || '获取数据失败');\n      }\n    } catch (err) {\n      console.error('获取学生列表失败:', err);\n      setError(err.message);\n    } finally {\n      onLoading(false);\n    }\n  };\n\n  // 获取家长报告\n  const fetchParentReport = async studentId => {\n    if (!studentId || !assignmentId) return;\n    try {\n      onLoading(true);\n      const response = await api.get(`/homework-analysis/parent-report/${studentId}/${assignmentId}`);\n      if (response.success) {\n        setReportData(response.data);\n        setError(null);\n      } else {\n        throw new Error(response.message || '获取家长报告失败');\n      }\n    } catch (err) {\n      console.error('获取家长报告失败:', err);\n      setError(err.message);\n    } finally {\n      onLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchStudentsData();\n  }, [assignmentId]);\n  useEffect(() => {\n    if (selectedStudent) {\n      fetchParentReport(selectedStudent);\n    }\n  }, [selectedStudent, assignmentId]);\n\n  // 渲染作业表现\n  const renderAssignmentPerformance = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.assignment_performance)) return null;\n    const {\n      assignment_performance\n    } = reportData;\n    const {\n      class_comparison\n    } = assignment_performance;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this), \"\\u4F5C\\u4E1A\\u8868\\u73B0\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            column: 1,\n            bordered: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u603B\\u5206\",\n              children: [assignment_performance.total_score, \"\\u5206\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6B63\\u786E\\u7387\",\n              children: [(assignment_performance.accuracy_rate * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u63D0\\u4EA4\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: assignment_performance.submit_status === '已提交' ? 'green' : 'red',\n                children: assignment_performance.submit_status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u63D0\\u4EA4\\u65F6\\u95F4\",\n              children: assignment_performance.submit_time || '未提交'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            title: \"\\u73ED\\u7EA7\\u5BF9\\u6BD4\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5B69\\u5B50\\u5F97\\u5206: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  style: {\n                    fontSize: '18px',\n                    color: '#1890ff'\n                  },\n                  children: [class_comparison.student_score, \"\\u5206\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u73ED\\u7EA7\\u5E73\\u5747: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: [class_comparison.class_average, \"\\u5206\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: class_comparison.student_score / class_comparison.class_average * 100,\n                format: () => class_comparison.above_average ? '高于平均' : '低于平均',\n                strokeColor: class_comparison.above_average ? '#52c41a' : '#faad14'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染优势和问题\n  const renderStrengthsAndProblems = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.strengths_and_problems)) return null;\n    const {\n      strengths_and_problems\n    } = reportData;\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), \"\\u4F18\\u52BF\\u8868\\u73B0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this),\n          size: \"small\",\n          children: strengths_and_problems.strengths.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n            dataSource: strengths_and_problems.strengths,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                children: [\"\\u2713 \", item]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u6682\\u65E0\\u7279\\u522B\\u4F18\\u52BF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), \"\\u9700\\u8981\\u6539\\u8FDB\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this),\n          size: \"small\",\n          children: strengths_and_problems.problems.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n            dataSource: strengths_and_problems.problems,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                children: [\"\\u2022 \", item]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              color: '#52c41a'\n            },\n            children: \"\\u8868\\u73B0\\u826F\\u597D\\uFF0C\\u65E0\\u660E\\u663E\\u95EE\\u9898\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染家庭辅导建议\n  const renderHomeGuidance = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.home_guidance)) return null;\n    const {\n      home_guidance\n    } = reportData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this), \"\\u5BB6\\u5EAD\\u8F85\\u5BFC\\u5EFA\\u8BAE\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                color: '#1890ff'\n              },\n              children: \"\\u77E5\\u8BC6\\u8F85\\u5BFC:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              size: \"small\",\n              dataSource: home_guidance.knowledge_guidance,\n              renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n                children: [\"\\u2022 \", item]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                color: '#52c41a'\n              },\n              children: \"\\u4E60\\u60EF\\u57F9\\u517B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              size: \"small\",\n              dataSource: home_guidance.habit_guidance,\n              renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n                children: [\"\\u2022 \", item]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染进步趋势\n  const renderProgressTrend = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.progress_trend)) return null;\n    const {\n      progress_trend\n    } = reportData;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), \"\\u8FDB\\u6B65\\u8D8B\\u52BF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u6700\\u8FD1\\u8868\\u73B0: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: progress_trend.trend\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            children: \"\\u6700\\u8FD15\\u6B21\\u4F5C\\u4E1A\\u5206\\u6570: \"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            children: progress_trend.recent_scores.map((score, index) => /*#__PURE__*/_jsxDEV(Tag, {\n              color: score >= 90 ? 'green' : score >= 80 ? 'blue' : 'orange',\n              children: [score, \"\\u5206\"]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          type: \"secondary\",\n          children: progress_trend.trend_description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染老师寄语\n  const renderTeacherMessage = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.teacher_message)) return null;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this), \"\\u8001\\u5E08\\u5BC4\\u8BED\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Paragraph, {\n        style: {\n          fontSize: '16px',\n          lineHeight: '1.8',\n          padding: '16px',\n          background: '#f6ffed',\n          border: '1px solid #b7eb8f',\n          borderRadius: '6px'\n        },\n        children: reportData.teacher_message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染报告预览模态框\n  const renderReportModal = () => {\n    return /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5BB6\\u957F\\u62A5\\u544A\\u9884\\u89C8\",\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 55\n        }, this),\n        children: \"\\u4E0B\\u8F7DPDF\\u62A5\\u544A\"\n      }, \"download\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: '600px',\n          overflowY: 'auto'\n        },\n        children: reportData && /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          style: {\n            width: '100%'\n          },\n          children: [renderAssignmentPerformance(), renderStrengthsAndProblems(), renderHomeGuidance(), renderProgressTrend(), renderTeacherMessage()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this);\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u52A0\\u8F7D\\u5931\\u8D25\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      action: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        onClick: fetchStudentsData,\n        children: \"\\u91CD\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `parent-report-page ${isMobile ? 'mobile' : 'desktop'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"report-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: isMobile ? 3 : 2,\n        className: \"report-title\",\n        children: \"\\uD83D\\uDCCA \\u5BB6\\u957F\\u62A5\\u544A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-controls\",\n        children: [/*#__PURE__*/_jsxDEV(Select, {\n          className: \"student-selector\",\n          style: {\n            width: isMobile ? '100%' : '200px'\n          },\n          placeholder: \"\\u9009\\u62E9\\u5B66\\u751F\",\n          value: selectedStudent,\n          onChange: setSelectedStudent,\n          size: isMobile ? \"large\" : \"default\",\n          children: studentsData.map(student => /*#__PURE__*/_jsxDEV(Option, {\n            value: student.student_id,\n            children: student.student_name\n          }, student.student_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 21\n            }, this),\n            onClick: () => setModalVisible(true),\n            disabled: !reportData,\n            size: isMobile ? \"large\" : \"default\",\n            children: isMobile ? '预览' : '预览报告'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 21\n            }, this),\n            disabled: !reportData,\n            size: isMobile ? \"large\" : \"default\",\n            children: isMobile ? '下载' : '下载PDF'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), reportData ? /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: isMobile ? \"middle\" : \"large\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-title\",\n          children: \"\\uD83D\\uDCC8 \\u4F5C\\u4E1A\\u8868\\u73B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-content\",\n          children: renderAssignmentPerformance()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-title\",\n          children: \"\\uD83D\\uDCAA \\u4F18\\u52BF\\u548C\\u95EE\\u9898\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-content\",\n          children: renderStrengthsAndProblems()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-title\",\n          children: \"\\uD83C\\uDFE0 \\u5BB6\\u5EAD\\u8F85\\u5BFC\\u5EFA\\u8BAE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-content\",\n          children: renderHomeGuidance()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-title\",\n          children: \"\\uD83D\\uDCCA \\u8FDB\\u6B65\\u8D8B\\u52BF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-content\",\n          children: renderProgressTrend()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-title\",\n          children: \"\\uD83D\\uDC8C \\u8001\\u5E08\\u5BC4\\u8BED\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-content\",\n          children: renderTeacherMessage()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        textAlign: 'center',\n        padding: isMobile ? '40px 20px' : '60px 40px',\n        borderRadius: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: isMobile ? '48px' : '64px',\n          marginBottom: '16px',\n          color: '#d9d9d9'\n        },\n        children: \"\\uD83D\\uDCCA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: isMobile ? '14px' : '16px'\n        },\n        children: \"\\u8BF7\\u9009\\u62E9\\u5B66\\u751F\\u67E5\\u770B\\u5BB6\\u957F\\u62A5\\u544A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 9\n    }, this), renderReportModal()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 370,\n    columnNumber: 5\n  }, this);\n};\n_s(ParentReport, \"5hJlzEVsrSR4Dfjg/zMXUEa8rhA=\");\n_c = ParentReport;\nexport default ParentReport;\nvar _c;\n$RefreshReg$(_c, \"ParentReport\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "<PERSON><PERSON>", "Select", "Space", "Typography", "<PERSON><PERSON>", "Descriptions", "List", "Progress", "Tag", "Modal", "api", "UserOutlined", "TrophyOutlined", "HeartOutlined", "BookOutlined", "LineChartOutlined", "DownloadOutlined", "EyeOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Option", "ParentReport", "assignmentId", "user", "onLoading", "_s", "studentsData", "setStudentsData", "selectedStudent", "setSelectedStudent", "reportData", "setReportData", "modalVisible", "setModalVisible", "error", "setError", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "fetchStudentsData", "response", "get", "success", "data", "students", "length", "student_id", "Error", "message", "err", "console", "fetchParentReport", "studentId", "renderAssignmentPerformance", "assignment_performance", "class_comparison", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "md", "column", "bordered", "size", "<PERSON><PERSON>", "label", "total_score", "accuracy_rate", "toFixed", "color", "submit_status", "submit_time", "direction", "style", "width", "strong", "fontSize", "student_score", "class_average", "percent", "format", "above_average", "strokeColor", "renderStrengthsAndProblems", "strengths_and_problems", "strengths", "dataSource", "renderItem", "item", "type", "problems", "renderHomeGuidance", "home_guidance", "knowledge_guidance", "habit_guidance", "renderProgressTrend", "progress_trend", "trend", "recent_scores", "map", "score", "index", "trend_description", "renderTeacherMessage", "teacher_message", "lineHeight", "padding", "background", "border", "borderRadius", "renderReportModal", "open", "onCancel", "footer", "icon", "onClick", "maxHeight", "overflowY", "description", "showIcon", "action", "className", "level", "placeholder", "value", "onChange", "student", "student_name", "disabled", "textAlign", "marginBottom", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/HomeworkAnalysis/ParentReport.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card, Row, Col, Button, Select, Space, Typography,\n  Alert, Descriptions, List, Progress, Tag, Modal\n} from 'antd';\nimport api from '../../utils/api';\nimport {\n  UserOutlined,\n  TrophyOutlined,\n  HeartOutlined,\n  BookOutlined,\n  LineChartOutlined,\n  DownloadOutlined,\n  EyeOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\nconst ParentReport = ({ assignmentId, user, onLoading }) => {\n  const [studentsData, setStudentsData] = useState([]);\n  const [selectedStudent, setSelectedStudent] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [error, setError] = useState(null);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // 获取学生列表\n  const fetchStudentsData = async () => {\n    if (!assignmentId) return;\n    \n    try {\n      onLoading(true);\n      const response = await api.get(`/homework-analysis/students/${assignmentId}`);\n\n      if (response.success) {\n        setStudentsData(response.data.students || []);\n        if (response.data.students && response.data.students.length > 0) {\n          setSelectedStudent(response.data.students[0].student_id);\n        }\n        setError(null);\n      } else {\n        throw new Error(response.message || '获取数据失败');\n      }\n    } catch (err) {\n      console.error('获取学生列表失败:', err);\n      setError(err.message);\n    } finally {\n      onLoading(false);\n    }\n  };\n\n  // 获取家长报告\n  const fetchParentReport = async (studentId) => {\n    if (!studentId || !assignmentId) return;\n    \n    try {\n      onLoading(true);\n      const response = await api.get(`/homework-analysis/parent-report/${studentId}/${assignmentId}`);\n\n      if (response.success) {\n        setReportData(response.data);\n        setError(null);\n      } else {\n        throw new Error(response.message || '获取家长报告失败');\n      }\n    } catch (err) {\n      console.error('获取家长报告失败:', err);\n      setError(err.message);\n    } finally {\n      onLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchStudentsData();\n  }, [assignmentId]);\n\n  useEffect(() => {\n    if (selectedStudent) {\n      fetchParentReport(selectedStudent);\n    }\n  }, [selectedStudent, assignmentId]);\n\n  // 渲染作业表现\n  const renderAssignmentPerformance = () => {\n    if (!reportData?.assignment_performance) return null;\n    \n    const { assignment_performance } = reportData;\n    const { class_comparison } = assignment_performance;\n    \n    return (\n      <Card \n        title={\n          <Space>\n            <TrophyOutlined />\n            作业表现\n          </Space>\n        }\n      >\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <Descriptions column={1} bordered size=\"small\">\n              <Descriptions.Item label=\"总分\">{assignment_performance.total_score}分</Descriptions.Item>\n              <Descriptions.Item label=\"正确率\">{(assignment_performance.accuracy_rate * 100).toFixed(1)}%</Descriptions.Item>\n              <Descriptions.Item label=\"提交状态\">\n                <Tag color={assignment_performance.submit_status === '已提交' ? 'green' : 'red'}>\n                  {assignment_performance.submit_status}\n                </Tag>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"提交时间\">{assignment_performance.submit_time || '未提交'}</Descriptions.Item>\n            </Descriptions>\n          </Col>\n          \n          <Col xs={24} md={12}>\n            <Card size=\"small\" title=\"班级对比\">\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text>孩子得分: </Text>\n                  <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>\n                    {class_comparison.student_score}分\n                  </Text>\n                </div>\n                <div>\n                  <Text>班级平均: </Text>\n                  <Text strong>{class_comparison.class_average}分</Text>\n                </div>\n                <Progress \n                  percent={class_comparison.student_score / class_comparison.class_average * 100}\n                  format={() => class_comparison.above_average ? '高于平均' : '低于平均'}\n                  strokeColor={class_comparison.above_average ? '#52c41a' : '#faad14'}\n                />\n              </Space>\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n    );\n  };\n\n  // 渲染优势和问题\n  const renderStrengthsAndProblems = () => {\n    if (!reportData?.strengths_and_problems) return null;\n    \n    const { strengths_and_problems } = reportData;\n    \n    return (\n      <Row gutter={[16, 16]}>\n        <Col xs={24} md={12}>\n          <Card \n            title={\n              <Space>\n                <TrophyOutlined style={{ color: '#52c41a' }} />\n                优势表现\n              </Space>\n            }\n            size=\"small\"\n          >\n            {strengths_and_problems.strengths.length > 0 ? (\n              <List\n                dataSource={strengths_and_problems.strengths}\n                renderItem={item => (\n                  <List.Item>\n                    <Text>✓ {item}</Text>\n                  </List.Item>\n                )}\n              />\n            ) : (\n              <Text type=\"secondary\">暂无特别优势</Text>\n            )}\n          </Card>\n        </Col>\n        \n        <Col xs={24} md={12}>\n          <Card \n            title={\n              <Space>\n                <BookOutlined style={{ color: '#faad14' }} />\n                需要改进\n              </Space>\n            }\n            size=\"small\"\n          >\n            {strengths_and_problems.problems.length > 0 ? (\n              <List\n                dataSource={strengths_and_problems.problems}\n                renderItem={item => (\n                  <List.Item>\n                    <Text>• {item}</Text>\n                  </List.Item>\n                )}\n              />\n            ) : (\n              <Text type=\"secondary\" style={{ color: '#52c41a' }}>表现良好，无明显问题</Text>\n            )}\n          </Card>\n        </Col>\n      </Row>\n    );\n  };\n\n  // 渲染家庭辅导建议\n  const renderHomeGuidance = () => {\n    if (!reportData?.home_guidance) return null;\n    \n    const { home_guidance } = reportData;\n    \n    return (\n      <Card \n        title={\n          <Space>\n            <HeartOutlined />\n            家庭辅导建议\n          </Space>\n        }\n      >\n        <Row gutter={[16, 16]}>\n          <Col xs={24} md={12}>\n            <div>\n              <Text strong style={{ color: '#1890ff' }}>知识辅导:</Text>\n              <List\n                size=\"small\"\n                dataSource={home_guidance.knowledge_guidance}\n                renderItem={item => <List.Item>• {item}</List.Item>}\n              />\n            </div>\n          </Col>\n          \n          <Col xs={24} md={12}>\n            <div>\n              <Text strong style={{ color: '#52c41a' }}>习惯培养:</Text>\n              <List\n                size=\"small\"\n                dataSource={home_guidance.habit_guidance}\n                renderItem={item => <List.Item>• {item}</List.Item>}\n              />\n            </div>\n          </Col>\n        </Row>\n      </Card>\n    );\n  };\n\n  // 渲染进步趋势\n  const renderProgressTrend = () => {\n    if (!reportData?.progress_trend) return null;\n    \n    const { progress_trend } = reportData;\n    \n    return (\n      <Card \n        title={\n          <Space>\n            <LineChartOutlined />\n            进步趋势\n          </Space>\n        }\n      >\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <Text strong>最近表现: </Text>\n            <Tag color=\"blue\">{progress_trend.trend}</Tag>\n          </div>\n          \n          <div>\n            <Text>最近5次作业分数: </Text>\n            <Space>\n              {progress_trend.recent_scores.map((score, index) => (\n                <Tag key={index} color={score >= 90 ? 'green' : score >= 80 ? 'blue' : 'orange'}>\n                  {score}分\n                </Tag>\n              ))}\n            </Space>\n          </div>\n          \n          <Paragraph type=\"secondary\">\n            {progress_trend.trend_description}\n          </Paragraph>\n        </Space>\n      </Card>\n    );\n  };\n\n  // 渲染老师寄语\n  const renderTeacherMessage = () => {\n    if (!reportData?.teacher_message) return null;\n    \n    return (\n      <Card \n        title={\n          <Space>\n            <UserOutlined />\n            老师寄语\n          </Space>\n        }\n      >\n        <Paragraph style={{ \n          fontSize: '16px', \n          lineHeight: '1.8',\n          padding: '16px',\n          background: '#f6ffed',\n          border: '1px solid #b7eb8f',\n          borderRadius: '6px'\n        }}>\n          {reportData.teacher_message}\n        </Paragraph>\n      </Card>\n    );\n  };\n\n  // 渲染报告预览模态框\n  const renderReportModal = () => {\n    return (\n      <Modal\n        title=\"家长报告预览\"\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={[\n          <Button key=\"download\" type=\"primary\" icon={<DownloadOutlined />}>\n            下载PDF报告\n          </Button>,\n          <Button key=\"close\" onClick={() => setModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        <div style={{ maxHeight: '600px', overflowY: 'auto' }}>\n          {reportData && (\n            <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n              {renderAssignmentPerformance()}\n              {renderStrengthsAndProblems()}\n              {renderHomeGuidance()}\n              {renderProgressTrend()}\n              {renderTeacherMessage()}\n            </Space>\n          )}\n        </div>\n      </Modal>\n    );\n  };\n\n  if (error) {\n    return (\n      <Alert\n        message=\"加载失败\"\n        description={error}\n        type=\"error\"\n        showIcon\n        action={\n          <Button size=\"small\" onClick={fetchStudentsData}>\n            重试\n          </Button>\n        }\n      />\n    );\n  }\n\n  return (\n    <div className={`parent-report-page ${isMobile ? 'mobile' : 'desktop'}`}>\n      <div className=\"report-header\">\n        <Title level={isMobile ? 3 : 2} className=\"report-title\">\n          📊 家长报告\n        </Title>\n\n        <div className=\"report-controls\">\n          <Select\n            className=\"student-selector\"\n            style={{ width: isMobile ? '100%' : '200px' }}\n            placeholder=\"选择学生\"\n            value={selectedStudent}\n            onChange={setSelectedStudent}\n            size={isMobile ? \"large\" : \"default\"}\n          >\n            {studentsData.map(student => (\n              <Option key={student.student_id} value={student.student_id}>\n                {student.student_name}\n              </Option>\n            ))}\n          </Select>\n\n          <div className=\"report-actions\">\n            <Button\n              type=\"primary\"\n              icon={<EyeOutlined />}\n              onClick={() => setModalVisible(true)}\n              disabled={!reportData}\n              size={isMobile ? \"large\" : \"default\"}\n            >\n              {isMobile ? '预览' : '预览报告'}\n            </Button>\n            <Button\n              icon={<DownloadOutlined />}\n              disabled={!reportData}\n              size={isMobile ? \"large\" : \"default\"}\n            >\n              {isMobile ? '下载' : '下载PDF'}\n            </Button>\n          </div>\n        </div>\n      </div>\n      \n      {reportData ? (\n        <Space\n          direction=\"vertical\"\n          size={isMobile ? \"middle\" : \"large\"}\n          style={{ width: '100%' }}\n        >\n          {/* 作业表现 */}\n          <div className=\"report-section\">\n            <div className=\"section-title\">📈 作业表现</div>\n            <div className=\"section-content\">\n              {renderAssignmentPerformance()}\n            </div>\n          </div>\n\n          {/* 优势和问题 */}\n          <div className=\"report-section\">\n            <div className=\"section-title\">💪 优势和问题</div>\n            <div className=\"section-content\">\n              {renderStrengthsAndProblems()}\n            </div>\n          </div>\n\n          {/* 家庭辅导建议 */}\n          <div className=\"report-section\">\n            <div className=\"section-title\">🏠 家庭辅导建议</div>\n            <div className=\"section-content\">\n              {renderHomeGuidance()}\n            </div>\n          </div>\n\n          {/* 进步趋势 */}\n          <div className=\"report-section\">\n            <div className=\"section-title\">📊 进步趋势</div>\n            <div className=\"section-content\">\n              {renderProgressTrend()}\n            </div>\n          </div>\n\n          {/* 老师寄语 */}\n          <div className=\"report-section\">\n            <div className=\"section-title\">💌 老师寄语</div>\n            <div className=\"section-content\">\n              {renderTeacherMessage()}\n            </div>\n          </div>\n        </Space>\n      ) : (\n        <Card\n          style={{\n            textAlign: 'center',\n            padding: isMobile ? '40px 20px' : '60px 40px',\n            borderRadius: '12px'\n          }}\n        >\n          <div style={{\n            fontSize: isMobile ? '48px' : '64px',\n            marginBottom: '16px',\n            color: '#d9d9d9'\n          }}>\n            📊\n          </div>\n          <Text type=\"secondary\" style={{ fontSize: isMobile ? '14px' : '16px' }}>\n            请选择学生查看家长报告\n          </Text>\n        </Card>\n      )}\n      \n      {/* 报告预览模态框 */}\n      {renderReportModal()}\n    </div>\n  );\n};\n\nexport default ParentReport;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EACjDC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,QAC1C,MAAM;AACb,OAAOC,GAAG,MAAM,iBAAiB;AACjC,SACEC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,EAChBC,WAAW,QACN,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGnB,UAAU;AAC7C,MAAM;EAAEoB;AAAO,CAAC,GAAGtB,MAAM;AAEzB,MAAMuB,YAAY,GAAGA,CAAC;EAAEC,YAAY;EAAEC,IAAI;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC8C,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;;EAElE;EACA9C,SAAS,CAAC,MAAM;IACd,MAAM+C,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACrB,YAAY,EAAE;IAEnB,IAAI;MACFE,SAAS,CAAC,IAAI,CAAC;MACf,MAAMoB,QAAQ,GAAG,MAAMrC,GAAG,CAACsC,GAAG,CAAC,+BAA+BvB,YAAY,EAAE,CAAC;MAE7E,IAAIsB,QAAQ,CAACE,OAAO,EAAE;QACpBnB,eAAe,CAACiB,QAAQ,CAACG,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC;QAC7C,IAAIJ,QAAQ,CAACG,IAAI,CAACC,QAAQ,IAAIJ,QAAQ,CAACG,IAAI,CAACC,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC/DpB,kBAAkB,CAACe,QAAQ,CAACG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACE,UAAU,CAAC;QAC1D;QACAf,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL,MAAM,IAAIgB,KAAK,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEmB,GAAG,CAAC;MAC/BlB,QAAQ,CAACkB,GAAG,CAACD,OAAO,CAAC;IACvB,CAAC,SAAS;MACR5B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM+B,iBAAiB,GAAG,MAAOC,SAAS,IAAK;IAC7C,IAAI,CAACA,SAAS,IAAI,CAAClC,YAAY,EAAE;IAEjC,IAAI;MACFE,SAAS,CAAC,IAAI,CAAC;MACf,MAAMoB,QAAQ,GAAG,MAAMrC,GAAG,CAACsC,GAAG,CAAC,oCAAoCW,SAAS,IAAIlC,YAAY,EAAE,CAAC;MAE/F,IAAIsB,QAAQ,CAACE,OAAO,EAAE;QACpBf,aAAa,CAACa,QAAQ,CAACG,IAAI,CAAC;QAC5BZ,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,MAAM;QACL,MAAM,IAAIgB,KAAK,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MACjD;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEmB,GAAG,CAAC;MAC/BlB,QAAQ,CAACkB,GAAG,CAACD,OAAO,CAAC;IACvB,CAAC,SAAS;MACR5B,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED/B,SAAS,CAAC,MAAM;IACdkD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACrB,YAAY,CAAC,CAAC;EAElB7B,SAAS,CAAC,MAAM;IACd,IAAImC,eAAe,EAAE;MACnB2B,iBAAiB,CAAC3B,eAAe,CAAC;IACpC;EACF,CAAC,EAAE,CAACA,eAAe,EAAEN,YAAY,CAAC,CAAC;;EAEnC;EACA,MAAMmC,2BAA2B,GAAGA,CAAA,KAAM;IACxC,IAAI,EAAC3B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE4B,sBAAsB,GAAE,OAAO,IAAI;IAEpD,MAAM;MAAEA;IAAuB,CAAC,GAAG5B,UAAU;IAC7C,MAAM;MAAE6B;IAAiB,CAAC,GAAGD,sBAAsB;IAEnD,oBACE1C,OAAA,CAACtB,IAAI;MACHkE,KAAK,eACH5C,OAAA,CAACjB,KAAK;QAAA8D,QAAA,gBACJ7C,OAAA,CAACP,cAAc;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MAAAJ,QAAA,eAED7C,OAAA,CAACrB,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACpB7C,OAAA,CAACpB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAP,QAAA,eAClB7C,OAAA,CAACd,YAAY;YAACmE,MAAM,EAAE,CAAE;YAACC,QAAQ;YAACC,IAAI,EAAC,OAAO;YAAAV,QAAA,gBAC5C7C,OAAA,CAACd,YAAY,CAACsE,IAAI;cAACC,KAAK,EAAC,cAAI;cAAAZ,QAAA,GAAEH,sBAAsB,CAACgB,WAAW,EAAC,QAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACvFjD,OAAA,CAACd,YAAY,CAACsE,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAAZ,QAAA,GAAE,CAACH,sBAAsB,CAACiB,aAAa,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC7GjD,OAAA,CAACd,YAAY,CAACsE,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAZ,QAAA,eAC7B7C,OAAA,CAACX,GAAG;gBAACwE,KAAK,EAAEnB,sBAAsB,CAACoB,aAAa,KAAK,KAAK,GAAG,OAAO,GAAG,KAAM;gBAAAjB,QAAA,EAC1EH,sBAAsB,CAACoB;cAAa;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpBjD,OAAA,CAACd,YAAY,CAACsE,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAZ,QAAA,EAAEH,sBAAsB,CAACqB,WAAW,IAAI;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAENjD,OAAA,CAACpB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAP,QAAA,eAClB7C,OAAA,CAACtB,IAAI;YAAC6E,IAAI,EAAC,OAAO;YAACX,KAAK,EAAC,0BAAM;YAAAC,QAAA,eAC7B7C,OAAA,CAACjB,KAAK;cAACiF,SAAS,EAAC,UAAU;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAArB,QAAA,gBACnD7C,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;kBAAA2C,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBjD,OAAA,CAACE,IAAI;kBAACiE,MAAM;kBAACF,KAAK,EAAE;oBAAEG,QAAQ,EAAE,MAAM;oBAAEP,KAAK,EAAE;kBAAU,CAAE;kBAAAhB,QAAA,GACxDF,gBAAgB,CAAC0B,aAAa,EAAC,QAClC;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjD,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;kBAAA2C,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBjD,OAAA,CAACE,IAAI;kBAACiE,MAAM;kBAAAtB,QAAA,GAAEF,gBAAgB,CAAC2B,aAAa,EAAC,QAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACNjD,OAAA,CAACZ,QAAQ;gBACPmF,OAAO,EAAE5B,gBAAgB,CAAC0B,aAAa,GAAG1B,gBAAgB,CAAC2B,aAAa,GAAG,GAAI;gBAC/EE,MAAM,EAAEA,CAAA,KAAM7B,gBAAgB,CAAC8B,aAAa,GAAG,MAAM,GAAG,MAAO;gBAC/DC,WAAW,EAAE/B,gBAAgB,CAAC8B,aAAa,GAAG,SAAS,GAAG;cAAU;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;;EAED;EACA,MAAM0B,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,EAAC7D,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE8D,sBAAsB,GAAE,OAAO,IAAI;IAEpD,MAAM;MAAEA;IAAuB,CAAC,GAAG9D,UAAU;IAE7C,oBACEd,OAAA,CAACrB,GAAG;MAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAL,QAAA,gBACpB7C,OAAA,CAACpB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAP,QAAA,eAClB7C,OAAA,CAACtB,IAAI;UACHkE,KAAK,eACH5C,OAAA,CAACjB,KAAK;YAAA8D,QAAA,gBACJ7C,OAAA,CAACP,cAAc;cAACwE,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEjD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACDM,IAAI,EAAC,OAAO;UAAAV,QAAA,EAEX+B,sBAAsB,CAACC,SAAS,CAAC5C,MAAM,GAAG,CAAC,gBAC1CjC,OAAA,CAACb,IAAI;YACH2F,UAAU,EAAEF,sBAAsB,CAACC,SAAU;YAC7CE,UAAU,EAAEC,IAAI,iBACdhF,OAAA,CAACb,IAAI,CAACqE,IAAI;cAAAX,QAAA,eACR7C,OAAA,CAACE,IAAI;gBAAA2C,QAAA,GAAC,SAAE,EAACmC,IAAI;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFjD,OAAA,CAACE,IAAI;YAAC+E,IAAI,EAAC,WAAW;YAAApC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACpC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENjD,OAAA,CAACpB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAP,QAAA,eAClB7C,OAAA,CAACtB,IAAI;UACHkE,KAAK,eACH5C,OAAA,CAACjB,KAAK;YAAA8D,QAAA,gBACJ7C,OAAA,CAACL,YAAY;cAACsE,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAE/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UACDM,IAAI,EAAC,OAAO;UAAAV,QAAA,EAEX+B,sBAAsB,CAACM,QAAQ,CAACjD,MAAM,GAAG,CAAC,gBACzCjC,OAAA,CAACb,IAAI;YACH2F,UAAU,EAAEF,sBAAsB,CAACM,QAAS;YAC5CH,UAAU,EAAEC,IAAI,iBACdhF,OAAA,CAACb,IAAI,CAACqE,IAAI;cAAAX,QAAA,eACR7C,OAAA,CAACE,IAAI;gBAAA2C,QAAA,GAAC,SAAE,EAACmC,IAAI;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFjD,OAAA,CAACE,IAAI;YAAC+E,IAAI,EAAC,WAAW;YAAChB,KAAK,EAAE;cAAEJ,KAAK,EAAE;YAAU,CAAE;YAAAhB,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QACrE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAACrE,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEsE,aAAa,GAAE,OAAO,IAAI;IAE3C,MAAM;MAAEA;IAAc,CAAC,GAAGtE,UAAU;IAEpC,oBACEd,OAAA,CAACtB,IAAI;MACHkE,KAAK,eACH5C,OAAA,CAACjB,KAAK;QAAA8D,QAAA,gBACJ7C,OAAA,CAACN,aAAa;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,wCAEnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MAAAJ,QAAA,eAED7C,OAAA,CAACrB,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAL,QAAA,gBACpB7C,OAAA,CAACpB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAP,QAAA,eAClB7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;cAACiE,MAAM;cAACF,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDjD,OAAA,CAACb,IAAI;cACHoE,IAAI,EAAC,OAAO;cACZuB,UAAU,EAAEM,aAAa,CAACC,kBAAmB;cAC7CN,UAAU,EAAEC,IAAI,iBAAIhF,OAAA,CAACb,IAAI,CAACqE,IAAI;gBAAAX,QAAA,GAAC,SAAE,EAACmC,IAAI;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjD,OAAA,CAACpB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAP,QAAA,eAClB7C,OAAA;YAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;cAACiE,MAAM;cAACF,KAAK,EAAE;gBAAEJ,KAAK,EAAE;cAAU,CAAE;cAAAhB,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDjD,OAAA,CAACb,IAAI;cACHoE,IAAI,EAAC,OAAO;cACZuB,UAAU,EAAEM,aAAa,CAACE,cAAe;cACzCP,UAAU,EAAEC,IAAI,iBAAIhF,OAAA,CAACb,IAAI,CAACqE,IAAI;gBAAAX,QAAA,GAAC,SAAE,EAACmC,IAAI;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX,CAAC;;EAED;EACA,MAAMsC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,EAACzE,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE0E,cAAc,GAAE,OAAO,IAAI;IAE5C,MAAM;MAAEA;IAAe,CAAC,GAAG1E,UAAU;IAErC,oBACEd,OAAA,CAACtB,IAAI;MACHkE,KAAK,eACH5C,OAAA,CAACjB,KAAK;QAAA8D,QAAA,gBACJ7C,OAAA,CAACJ,iBAAiB;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAEvB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MAAAJ,QAAA,eAED7C,OAAA,CAACjB,KAAK;QAACiF,SAAS,EAAC,UAAU;QAACC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAArB,QAAA,gBACnD7C,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;YAACiE,MAAM;YAAAtB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1BjD,OAAA,CAACX,GAAG;YAACwE,KAAK,EAAC,MAAM;YAAAhB,QAAA,EAAE2C,cAAc,CAACC;UAAK;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAENjD,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACE,IAAI;YAAA2C,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvBjD,OAAA,CAACjB,KAAK;YAAA8D,QAAA,EACH2C,cAAc,CAACE,aAAa,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC7C7F,OAAA,CAACX,GAAG;cAAawE,KAAK,EAAE+B,KAAK,IAAI,EAAE,GAAG,OAAO,GAAGA,KAAK,IAAI,EAAE,GAAG,MAAM,GAAG,QAAS;cAAA/C,QAAA,GAC7E+C,KAAK,EAAC,QACT;YAAA,GAFUC,KAAK;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENjD,OAAA,CAACG,SAAS;UAAC8E,IAAI,EAAC,WAAW;UAAApC,QAAA,EACxB2C,cAAc,CAACM;QAAiB;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEX,CAAC;;EAED;EACA,MAAM8C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,EAACjF,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEkF,eAAe,GAAE,OAAO,IAAI;IAE7C,oBACEhG,OAAA,CAACtB,IAAI;MACHkE,KAAK,eACH5C,OAAA,CAACjB,KAAK;QAAA8D,QAAA,gBACJ7C,OAAA,CAACR,YAAY;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAElB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;MAAAJ,QAAA,eAED7C,OAAA,CAACG,SAAS;QAAC8D,KAAK,EAAE;UAChBG,QAAQ,EAAE,MAAM;UAChB6B,UAAU,EAAE,KAAK;UACjBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,SAAS;UACrBC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE;QAChB,CAAE;QAAAxD,QAAA,EACC/B,UAAU,CAACkF;MAAe;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEX,CAAC;;EAED;EACA,MAAMqD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,oBACEtG,OAAA,CAACV,KAAK;MACJsD,KAAK,EAAC,sCAAQ;MACd2D,IAAI,EAAEvF,YAAa;MACnBwF,QAAQ,EAAEA,CAAA,KAAMvF,eAAe,CAAC,KAAK,CAAE;MACvCwF,MAAM,EAAE,cACNzG,OAAA,CAACnB,MAAM;QAAgBoG,IAAI,EAAC,SAAS;QAACyB,IAAI,eAAE1G,OAAA,CAACH,gBAAgB;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAAC;MAElE,GAFY,UAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEd,CAAC,eACTjD,OAAA,CAACnB,MAAM;QAAa8H,OAAO,EAAEA,CAAA,KAAM1F,eAAe,CAAC,KAAK,CAAE;QAAA4B,QAAA,EAAC;MAE3D,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFiB,KAAK,EAAE,GAAI;MAAArB,QAAA,eAEX7C,OAAA;QAAKiE,KAAK,EAAE;UAAE2C,SAAS,EAAE,OAAO;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAhE,QAAA,EACnD/B,UAAU,iBACTd,OAAA,CAACjB,KAAK;UAACiF,SAAS,EAAC,UAAU;UAACT,IAAI,EAAC,OAAO;UAACU,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAArB,QAAA,GAC/DJ,2BAA2B,CAAC,CAAC,EAC7BkC,0BAA0B,CAAC,CAAC,EAC5BQ,kBAAkB,CAAC,CAAC,EACpBI,mBAAmB,CAAC,CAAC,EACrBQ,oBAAoB,CAAC,CAAC;QAAA;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;EAED,IAAI/B,KAAK,EAAE;IACT,oBACElB,OAAA,CAACf,KAAK;MACJmD,OAAO,EAAC,0BAAM;MACd0E,WAAW,EAAE5F,KAAM;MACnB+D,IAAI,EAAC,OAAO;MACZ8B,QAAQ;MACRC,MAAM,eACJhH,OAAA,CAACnB,MAAM;QAAC0E,IAAI,EAAC,OAAO;QAACoD,OAAO,EAAEhF,iBAAkB;QAAAkB,QAAA,EAAC;MAEjD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEN;EAEA,oBACEjD,OAAA;IAAKiH,SAAS,EAAE,sBAAsB7F,QAAQ,GAAG,QAAQ,GAAG,SAAS,EAAG;IAAAyB,QAAA,gBACtE7C,OAAA;MAAKiH,SAAS,EAAC,eAAe;MAAApE,QAAA,gBAC5B7C,OAAA,CAACC,KAAK;QAACiH,KAAK,EAAE9F,QAAQ,GAAG,CAAC,GAAG,CAAE;QAAC6F,SAAS,EAAC,cAAc;QAAApE,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERjD,OAAA;QAAKiH,SAAS,EAAC,iBAAiB;QAAApE,QAAA,gBAC9B7C,OAAA,CAAClB,MAAM;UACLmI,SAAS,EAAC,kBAAkB;UAC5BhD,KAAK,EAAE;YAAEC,KAAK,EAAE9C,QAAQ,GAAG,MAAM,GAAG;UAAQ,CAAE;UAC9C+F,WAAW,EAAC,0BAAM;UAClBC,KAAK,EAAExG,eAAgB;UACvByG,QAAQ,EAAExG,kBAAmB;UAC7B0C,IAAI,EAAEnC,QAAQ,GAAG,OAAO,GAAG,SAAU;UAAAyB,QAAA,EAEpCnC,YAAY,CAACiF,GAAG,CAAC2B,OAAO,iBACvBtH,OAAA,CAACI,MAAM;YAA0BgH,KAAK,EAAEE,OAAO,CAACpF,UAAW;YAAAW,QAAA,EACxDyE,OAAO,CAACC;UAAY,GADVD,OAAO,CAACpF,UAAU;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAETjD,OAAA;UAAKiH,SAAS,EAAC,gBAAgB;UAAApE,QAAA,gBAC7B7C,OAAA,CAACnB,MAAM;YACLoG,IAAI,EAAC,SAAS;YACdyB,IAAI,eAAE1G,OAAA,CAACF,WAAW;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtB0D,OAAO,EAAEA,CAAA,KAAM1F,eAAe,CAAC,IAAI,CAAE;YACrCuG,QAAQ,EAAE,CAAC1G,UAAW;YACtByC,IAAI,EAAEnC,QAAQ,GAAG,OAAO,GAAG,SAAU;YAAAyB,QAAA,EAEpCzB,QAAQ,GAAG,IAAI,GAAG;UAAM;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACTjD,OAAA,CAACnB,MAAM;YACL6H,IAAI,eAAE1G,OAAA,CAACH,gBAAgB;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuE,QAAQ,EAAE,CAAC1G,UAAW;YACtByC,IAAI,EAAEnC,QAAQ,GAAG,OAAO,GAAG,SAAU;YAAAyB,QAAA,EAEpCzB,QAAQ,GAAG,IAAI,GAAG;UAAO;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELnC,UAAU,gBACTd,OAAA,CAACjB,KAAK;MACJiF,SAAS,EAAC,UAAU;MACpBT,IAAI,EAAEnC,QAAQ,GAAG,QAAQ,GAAG,OAAQ;MACpC6C,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAArB,QAAA,gBAGzB7C,OAAA;QAAKiH,SAAS,EAAC,gBAAgB;QAAApE,QAAA,gBAC7B7C,OAAA;UAAKiH,SAAS,EAAC,eAAe;UAAApE,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5CjD,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAApE,QAAA,EAC7BJ,2BAA2B,CAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA;QAAKiH,SAAS,EAAC,gBAAgB;QAAApE,QAAA,gBAC7B7C,OAAA;UAAKiH,SAAS,EAAC,eAAe;UAAApE,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7CjD,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAApE,QAAA,EAC7B8B,0BAA0B,CAAC;QAAC;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA;QAAKiH,SAAS,EAAC,gBAAgB;QAAApE,QAAA,gBAC7B7C,OAAA;UAAKiH,SAAS,EAAC,eAAe;UAAApE,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9CjD,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAApE,QAAA,EAC7BsC,kBAAkB,CAAC;QAAC;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA;QAAKiH,SAAS,EAAC,gBAAgB;QAAApE,QAAA,gBAC7B7C,OAAA;UAAKiH,SAAS,EAAC,eAAe;UAAApE,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5CjD,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAApE,QAAA,EAC7B0C,mBAAmB,CAAC;QAAC;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjD,OAAA;QAAKiH,SAAS,EAAC,gBAAgB;QAAApE,QAAA,gBAC7B7C,OAAA;UAAKiH,SAAS,EAAC,eAAe;UAAApE,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5CjD,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAApE,QAAA,EAC7BkD,oBAAoB,CAAC;QAAC;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,gBAERjD,OAAA,CAACtB,IAAI;MACHuF,KAAK,EAAE;QACLwD,SAAS,EAAE,QAAQ;QACnBvB,OAAO,EAAE9E,QAAQ,GAAG,WAAW,GAAG,WAAW;QAC7CiF,YAAY,EAAE;MAChB,CAAE;MAAAxD,QAAA,gBAEF7C,OAAA;QAAKiE,KAAK,EAAE;UACVG,QAAQ,EAAEhD,QAAQ,GAAG,MAAM,GAAG,MAAM;UACpCsG,YAAY,EAAE,MAAM;UACpB7D,KAAK,EAAE;QACT,CAAE;QAAAhB,QAAA,EAAC;MAEH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjD,OAAA,CAACE,IAAI;QAAC+E,IAAI,EAAC,WAAW;QAAChB,KAAK,EAAE;UAAEG,QAAQ,EAAEhD,QAAQ,GAAG,MAAM,GAAG;QAAO,CAAE;QAAAyB,QAAA,EAAC;MAExE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP,EAGAqD,iBAAiB,CAAC,CAAC;EAAA;IAAAxD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjB,CAAC;AAEV,CAAC;AAACxC,EAAA,CAhdIJ,YAAY;AAAAsH,EAAA,GAAZtH,YAAY;AAkdlB,eAAeA,YAAY;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}