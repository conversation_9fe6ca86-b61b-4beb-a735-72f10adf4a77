{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nvar BEAT_LIMIT = 1000 * 60 * 10;\n\n/**\n * A helper class to map keys to values.\n * It supports both primitive keys and object keys.\n */\nvar ArrayKeyMap = /*#__PURE__*/function () {\n  function ArrayKeyMap() {\n    _classCallCheck(this, ArrayKeyMap);\n    _defineProperty(this, \"map\", new Map());\n    // Use WeakMap to avoid memory leak\n    _defineProperty(this, \"objectIDMap\", new WeakMap());\n    _defineProperty(this, \"nextID\", 0);\n    _defineProperty(this, \"lastAccessBeat\", new Map());\n    // We will clean up the cache when reach the limit\n    _defineProperty(this, \"accessBeat\", 0);\n  }\n  _createClass(ArrayKeyMap, [{\n    key: \"set\",\n    value: function set(keys, value) {\n      // New set will trigger clear\n      this.clear();\n\n      // Set logic\n      var compositeKey = this.getCompositeKey(keys);\n      this.map.set(compositeKey, value);\n      this.lastAccessBeat.set(compositeKey, Date.now());\n    }\n  }, {\n    key: \"get\",\n    value: function get(keys) {\n      var compositeKey = this.getCompositeKey(keys);\n      var cache = this.map.get(compositeKey);\n      this.lastAccessBeat.set(compositeKey, Date.now());\n      this.accessBeat += 1;\n      return cache;\n    }\n  }, {\n    key: \"getCompositeKey\",\n    value: function getCompositeKey(keys) {\n      var _this = this;\n      var ids = keys.map(function (key) {\n        if (key && _typeof(key) === 'object') {\n          return \"obj_\".concat(_this.getObjectID(key));\n        }\n        return \"\".concat(_typeof(key), \"_\").concat(key);\n      });\n      return ids.join('|');\n    }\n  }, {\n    key: \"getObjectID\",\n    value: function getObjectID(obj) {\n      if (this.objectIDMap.has(obj)) {\n        return this.objectIDMap.get(obj);\n      }\n      var id = this.nextID;\n      this.objectIDMap.set(obj, id);\n      this.nextID += 1;\n      return id;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      var _this2 = this;\n      if (this.accessBeat > 10000) {\n        var now = Date.now();\n        this.lastAccessBeat.forEach(function (beat, key) {\n          if (now - beat > BEAT_LIMIT) {\n            _this2.map.delete(key);\n            _this2.lastAccessBeat.delete(key);\n          }\n        });\n        this.accessBeat = 0;\n      }\n    }\n  }]);\n  return ArrayKeyMap;\n}();\nvar uniqueMap = new ArrayKeyMap();\n\n/**\n * Like `useMemo`, but this hook result will be shared across all instances.\n */\nfunction useUniqueMemo(memoFn, deps) {\n  return React.useMemo(function () {\n    var cachedValue = uniqueMap.get(deps);\n    if (cachedValue) {\n      return cachedValue;\n    }\n    var newValue = memoFn();\n    uniqueMap.set(deps, newValue);\n    return newValue;\n  }, deps);\n}\nexport default useUniqueMemo;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}