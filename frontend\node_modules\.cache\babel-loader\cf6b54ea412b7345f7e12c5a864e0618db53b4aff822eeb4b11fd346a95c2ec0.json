{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/buttonHelpers';\nfunction isThenable(thing) {\n  return !!(thing === null || thing === void 0 ? void 0 : thing.then);\n}\nconst ActionButton = props => {\n  const {\n    type,\n    children,\n    prefixCls,\n    buttonProps,\n    close,\n    autoFocus,\n    emitEvent,\n    isSilent,\n    quitOnNullishReturnValue,\n    actionFn\n  } = props;\n  const clickedRef = React.useRef(false);\n  const buttonRef = React.useRef(null);\n  const [loading, setLoading] = useState(false);\n  const onInternalClose = (...args) => {\n    close === null || close === void 0 ? void 0 : close.apply(void 0, args);\n  };\n  React.useEffect(() => {\n    let timeoutId = null;\n    if (autoFocus) {\n      timeoutId = setTimeout(() => {\n        var _a;\n        (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus({\n          preventScroll: true\n        });\n      });\n    }\n    return () => {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n  const handlePromiseOnOk = returnValueOfOnOk => {\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n    setLoading(true);\n    returnValueOfOnOk.then((...args) => {\n      setLoading(false, true);\n      onInternalClose.apply(void 0, args);\n      clickedRef.current = false;\n    }, e => {\n      // See: https://github.com/ant-design/ant-design/issues/6183\n      setLoading(false, true);\n      clickedRef.current = false;\n      // Do not throw if is `await` mode\n      if (isSilent === null || isSilent === void 0 ? void 0 : isSilent()) {\n        return;\n      }\n      return Promise.reject(e);\n    });\n  };\n  const onClick = e => {\n    if (clickedRef.current) {\n      return;\n    }\n    clickedRef.current = true;\n    if (!actionFn) {\n      onInternalClose();\n      return;\n    }\n    let returnValueOfOnOk;\n    if (emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n      if (quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        onInternalClose(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close);\n      // https://github.com/ant-design/ant-design/issues/23358\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n      if (!isThenable(returnValueOfOnOk)) {\n        onInternalClose();\n        return;\n      }\n    }\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n  return /*#__PURE__*/React.createElement(Button, Object.assign({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: buttonRef\n  }), children);\n};\nexport default ActionButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}