{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport RotateLeftOutlined from \"@ant-design/icons/es/icons/RotateLeftOutlined\";\nimport RotateRightOutlined from \"@ant-design/icons/es/icons/RotateRightOutlined\";\nimport SwapOutlined from \"@ant-design/icons/es/icons/SwapOutlined\";\nimport ZoomInOutlined from \"@ant-design/icons/es/icons/ZoomInOutlined\";\nimport ZoomOutOutlined from \"@ant-design/icons/es/icons/ZoomOutOutlined\";\nimport classNames from 'classnames';\nimport RcImage from 'rc-image';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useStyle from './style';\nexport const icons = {\n  rotateLeft: /*#__PURE__*/React.createElement(RotateLeftOutlined, null),\n  rotateRight: /*#__PURE__*/React.createElement(RotateRightOutlined, null),\n  zoomIn: /*#__PURE__*/React.createElement(ZoomInOutlined, null),\n  zoomOut: /*#__PURE__*/React.createElement(ZoomOutOutlined, null),\n  close: /*#__PURE__*/React.createElement(CloseOutlined, null),\n  left: /*#__PURE__*/React.createElement(LeftOutlined, null),\n  right: /*#__PURE__*/React.createElement(RightOutlined, null),\n  flipX: /*#__PURE__*/React.createElement(SwapOutlined, null),\n  flipY: /*#__PURE__*/React.createElement(SwapOutlined, {\n    rotate: 90\n  })\n};\nconst InternalPreviewGroup = _a => {\n  var {\n      previewPrefixCls: customizePrefixCls,\n      preview\n    } = _a,\n    otherProps = __rest(_a, [\"previewPrefixCls\", \"preview\"]);\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('image', customizePrefixCls);\n  const previewPrefixCls = `${prefixCls}-preview`;\n  const rootPrefixCls = getPrefixCls();\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const [zIndex] = useZIndex('ImagePreview', typeof preview === 'object' ? preview.zIndex : undefined);\n  const memoizedIcons = React.useMemo(() => Object.assign(Object.assign({}, icons), {\n    left: direction === 'rtl' ? /*#__PURE__*/React.createElement(RightOutlined, null) : /*#__PURE__*/React.createElement(LeftOutlined, null),\n    right: direction === 'rtl' ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null)\n  }), [direction]);\n  const mergedPreview = React.useMemo(() => {\n    var _a;\n    if (preview === false) {\n      return preview;\n    }\n    const _preview = typeof preview === 'object' ? preview : {};\n    const mergedRootClassName = classNames(hashId, cssVarCls, rootCls, (_a = _preview.rootClassName) !== null && _a !== void 0 ? _a : '');\n    return Object.assign(Object.assign({}, _preview), {\n      transitionName: getTransitionName(rootPrefixCls, 'zoom', _preview.transitionName),\n      maskTransitionName: getTransitionName(rootPrefixCls, 'fade', _preview.maskTransitionName),\n      rootClassName: mergedRootClassName,\n      zIndex\n    });\n  }, [preview]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RcImage.PreviewGroup, Object.assign({\n    preview: mergedPreview,\n    previewPrefixCls: previewPrefixCls,\n    icons: memoizedIcons\n  }, otherProps)));\n};\nexport default InternalPreviewGroup;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}