{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nvar useCheckedKeys = function useCheckedKeys(rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var extractValues = function extractValues(values) {\n      return values.map(function (_ref) {\n        var value = _ref.value;\n        return value;\n      });\n    };\n    var checkedKeys = extractValues(rawLabeledValues);\n    var halfCheckedKeys = extractValues(rawHalfCheckedValues);\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n    var finalCheckedKeys = checkedKeys;\n    var finalHalfCheckedKeys = halfCheckedKeys;\n    if (treeConduction) {\n      var conductResult = conductCheck(checkedKeys, true, keyEntities);\n      finalCheckedKeys = conductResult.checkedKeys;\n      finalHalfCheckedKeys = conductResult.halfCheckedKeys;\n    }\n    return [Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(finalCheckedKeys)))), finalHalfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n};\nexport default useCheckedKeys;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}