{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { isPresetSize } from '../_util/gapSize';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport createFlexClassNames from './utils';\nconst Flex = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      rootClassName,\n      className,\n      style,\n      flex,\n      gap,\n      children,\n      vertical = false,\n      component: Component = 'div'\n    } = props,\n    othersProps = __rest(props, [\"prefixCls\", \"rootClassName\", \"className\", \"style\", \"flex\", \"gap\", \"children\", \"vertical\", \"component\"]);\n  const {\n    flex: ctxFlex,\n    direction: ctxDirection,\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('flex', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const mergedVertical = vertical !== null && vertical !== void 0 ? vertical : ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.vertical;\n  const mergedCls = classNames(className, rootClassName, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.className, prefixCls, hashId, cssVarCls, createFlexClassNames(prefixCls, props), {\n    [`${prefixCls}-rtl`]: ctxDirection === 'rtl',\n    [`${prefixCls}-gap-${gap}`]: isPresetSize(gap),\n    [`${prefixCls}-vertical`]: mergedVertical\n  });\n  const mergedStyle = Object.assign(Object.assign({}, ctxFlex === null || ctxFlex === void 0 ? void 0 : ctxFlex.style), style);\n  if (flex) {\n    mergedStyle.flex = flex;\n  }\n  if (gap && !isPresetSize(gap)) {\n    mergedStyle.gap = gap;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Component, Object.assign({\n    ref: ref,\n    className: mergedCls,\n    style: mergedStyle\n  }, omit(othersProps, ['justify', 'wrap', 'align'])), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Flex.displayName = 'Flex';\n}\nexport default Flex;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}