{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport * as React from 'react';\nimport { toPathKeys } from \"../utils/commonUtil\";\nexport default function useValues(multiple, rawValues, getPathKeyEntities, getValueByKeyPath, getMissingValues) {\n  // Fill `rawValues` with checked conduction values\n  return React.useMemo(function () {\n    var _getMissingValues = getMissingValues(rawValues),\n      _getMissingValues2 = _slicedToArray(_getMissingValues, 2),\n      existValues = _getMissingValues2[0],\n      missingValues = _getMissingValues2[1];\n    if (!multiple || !rawValues.length) {\n      return [existValues, [], missingValues];\n    }\n    var keyPathValues = toPathKeys(existValues);\n    var keyPathEntities = getPathKeyEntities();\n    var _conductCheck = conductCheck(keyPathValues, true, keyPathEntities),\n      checkedKeys = _conductCheck.checkedKeys,\n      halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n    // Convert key back to value cells\n    return [getValueByKeyPath(checkedKeys), getValueByKeyPath(halfCheckedKeys), missingValues];\n  }, [multiple, rawValues, getPathKeyEntities, getValueByKeyPath, getMissingValues]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}