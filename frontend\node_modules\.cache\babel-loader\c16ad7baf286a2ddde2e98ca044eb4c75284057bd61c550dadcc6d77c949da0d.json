{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline, resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\n// styles from RadioGroup only\nconst getGroupRadioStyle = token => {\n  const {\n    componentCls,\n    antCls\n  } = token;\n  const groupPrefixCls = `${componentCls}-group`;\n  return {\n    [groupPrefixCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'inline-block',\n      fontSize: 0,\n      // RTL\n      [`&${groupPrefixCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [`&${groupPrefixCls}-block`]: {\n        display: 'flex'\n      },\n      [`${antCls}-badge ${antCls}-badge-count`]: {\n        zIndex: 1\n      },\n      [`> ${antCls}-badge:not(:first-child) > ${antCls}-button-wrapper`]: {\n        borderInlineStart: 'none'\n      }\n    })\n  };\n};\n// Styles from radio-wrapper\nconst getRadioBasicStyle = token => {\n  const {\n    componentCls,\n    wrapperMarginInlineEnd,\n    colorPrimary,\n    radioSize,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOutCirc,\n    colorBgContainer,\n    colorBorder,\n    lineWidth,\n    colorBgContainerDisabled,\n    colorTextDisabled,\n    paddingXS,\n    dotColorDisabled,\n    lineType,\n    radioColor,\n    radioBgColor,\n    calc\n  } = token;\n  const radioInnerPrefixCls = `${componentCls}-inner`;\n  const dotPadding = 4;\n  const radioDotDisabledSize = calc(radioSize).sub(calc(dotPadding).mul(2));\n  const radioSizeCalc = calc(1).mul(radioSize).equal({\n    unit: true\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'inline-flex',\n      alignItems: 'baseline',\n      marginInlineStart: 0,\n      marginInlineEnd: wrapperMarginInlineEnd,\n      cursor: 'pointer',\n      '&:last-child': {\n        marginInlineEnd: 0\n      },\n      // RTL\n      [`&${componentCls}-wrapper-rtl`]: {\n        direction: 'rtl'\n      },\n      '&-disabled': {\n        cursor: 'not-allowed',\n        color: token.colorTextDisabled\n      },\n      '&::after': {\n        display: 'inline-block',\n        width: 0,\n        overflow: 'hidden',\n        content: '\"\\\\a0\"'\n      },\n      '&-block': {\n        flex: 1,\n        justifyContent: 'center'\n      },\n      // hashId 在 wrapper 上，只能铺平\n      [`${componentCls}-checked::after`]: {\n        position: 'absolute',\n        insetBlockStart: 0,\n        insetInlineStart: 0,\n        width: '100%',\n        height: '100%',\n        border: `${unit(lineWidth)} ${lineType} ${colorPrimary}`,\n        borderRadius: '50%',\n        visibility: 'hidden',\n        opacity: 0,\n        content: '\"\"'\n      },\n      [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n        position: 'relative',\n        display: 'inline-block',\n        outline: 'none',\n        cursor: 'pointer',\n        alignSelf: 'center',\n        borderRadius: '50%'\n      }),\n      [`${componentCls}-wrapper:hover &,\n        &:hover ${radioInnerPrefixCls}`]: {\n        borderColor: colorPrimary\n      },\n      [`${componentCls}-input:focus-visible + ${radioInnerPrefixCls}`]: Object.assign({}, genFocusOutline(token)),\n      [`${componentCls}:hover::after, ${componentCls}-wrapper:hover &::after`]: {\n        visibility: 'visible'\n      },\n      [`${componentCls}-inner`]: {\n        '&::after': {\n          boxSizing: 'border-box',\n          position: 'absolute',\n          insetBlockStart: '50%',\n          insetInlineStart: '50%',\n          display: 'block',\n          width: radioSizeCalc,\n          height: radioSizeCalc,\n          marginBlockStart: calc(1).mul(radioSize).div(-2).equal({\n            unit: true\n          }),\n          marginInlineStart: calc(1).mul(radioSize).div(-2).equal({\n            unit: true\n          }),\n          backgroundColor: radioColor,\n          borderBlockStart: 0,\n          borderInlineStart: 0,\n          borderRadius: radioSizeCalc,\n          transform: 'scale(0)',\n          opacity: 0,\n          transition: `all ${motionDurationSlow} ${motionEaseInOutCirc}`,\n          content: '\"\"'\n        },\n        boxSizing: 'border-box',\n        position: 'relative',\n        insetBlockStart: 0,\n        insetInlineStart: 0,\n        display: 'block',\n        width: radioSizeCalc,\n        height: radioSizeCalc,\n        backgroundColor: colorBgContainer,\n        borderColor: colorBorder,\n        borderStyle: 'solid',\n        borderWidth: lineWidth,\n        borderRadius: '50%',\n        transition: `all ${motionDurationMid}`\n      },\n      [`${componentCls}-input`]: {\n        position: 'absolute',\n        inset: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        opacity: 0\n      },\n      // 选中状态\n      [`${componentCls}-checked`]: {\n        [radioInnerPrefixCls]: {\n          borderColor: colorPrimary,\n          backgroundColor: radioBgColor,\n          '&::after': {\n            transform: `scale(${token.calc(token.dotSize).div(radioSize).equal()})`,\n            opacity: 1,\n            transition: `all ${motionDurationSlow} ${motionEaseInOutCirc}`\n          }\n        }\n      },\n      [`${componentCls}-disabled`]: {\n        cursor: 'not-allowed',\n        [radioInnerPrefixCls]: {\n          backgroundColor: colorBgContainerDisabled,\n          borderColor: colorBorder,\n          cursor: 'not-allowed',\n          '&::after': {\n            backgroundColor: dotColorDisabled\n          }\n        },\n        [`${componentCls}-input`]: {\n          cursor: 'not-allowed'\n        },\n        [`${componentCls}-disabled + span`]: {\n          color: colorTextDisabled,\n          cursor: 'not-allowed'\n        },\n        [`&${componentCls}-checked`]: {\n          [radioInnerPrefixCls]: {\n            '&::after': {\n              transform: `scale(${calc(radioDotDisabledSize).div(radioSize).equal()})`\n            }\n          }\n        }\n      },\n      [`span${componentCls} + *`]: {\n        paddingInlineStart: paddingXS,\n        paddingInlineEnd: paddingXS\n      }\n    })\n  };\n};\n// Styles from radio-button\nconst getRadioButtonStyle = token => {\n  const {\n    buttonColor,\n    controlHeight,\n    componentCls,\n    lineWidth,\n    lineType,\n    colorBorder,\n    motionDurationSlow,\n    motionDurationMid,\n    buttonPaddingInline,\n    fontSize,\n    buttonBg,\n    fontSizeLG,\n    controlHeightLG,\n    controlHeightSM,\n    paddingXS,\n    borderRadius,\n    borderRadiusSM,\n    borderRadiusLG,\n    buttonCheckedBg,\n    buttonSolidCheckedColor,\n    colorTextDisabled,\n    colorBgContainerDisabled,\n    buttonCheckedBgDisabled,\n    buttonCheckedColorDisabled,\n    colorPrimary,\n    colorPrimaryHover,\n    colorPrimaryActive,\n    buttonSolidCheckedBg,\n    buttonSolidCheckedHoverBg,\n    buttonSolidCheckedActiveBg,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-button-wrapper`]: {\n      position: 'relative',\n      display: 'inline-block',\n      height: controlHeight,\n      margin: 0,\n      paddingInline: buttonPaddingInline,\n      paddingBlock: 0,\n      color: buttonColor,\n      fontSize,\n      lineHeight: unit(calc(controlHeight).sub(calc(lineWidth).mul(2)).equal()),\n      background: buttonBg,\n      border: `${unit(lineWidth)} ${lineType} ${colorBorder}`,\n      // strange align fix for chrome but works\n      // https://gw.alipayobjects.com/zos/rmsportal/VFTfKXJuogBAXcvfAUWJ.gif\n      borderBlockStartWidth: calc(lineWidth).add(0.02).equal(),\n      borderInlineStartWidth: 0,\n      borderInlineEndWidth: lineWidth,\n      cursor: 'pointer',\n      transition: [`color ${motionDurationMid}`, `background ${motionDurationMid}`, `box-shadow ${motionDurationMid}`].join(','),\n      a: {\n        color: buttonColor\n      },\n      [`> ${componentCls}-button`]: {\n        position: 'absolute',\n        insetBlockStart: 0,\n        insetInlineStart: 0,\n        zIndex: -1,\n        width: '100%',\n        height: '100%'\n      },\n      '&:not(:first-child)': {\n        '&::before': {\n          position: 'absolute',\n          insetBlockStart: calc(lineWidth).mul(-1).equal(),\n          insetInlineStart: calc(lineWidth).mul(-1).equal(),\n          display: 'block',\n          boxSizing: 'content-box',\n          width: 1,\n          height: '100%',\n          paddingBlock: lineWidth,\n          paddingInline: 0,\n          backgroundColor: colorBorder,\n          transition: `background-color ${motionDurationSlow}`,\n          content: '\"\"'\n        }\n      },\n      '&:first-child': {\n        borderInlineStart: `${unit(lineWidth)} ${lineType} ${colorBorder}`,\n        borderStartStartRadius: borderRadius,\n        borderEndStartRadius: borderRadius\n      },\n      '&:last-child': {\n        borderStartEndRadius: borderRadius,\n        borderEndEndRadius: borderRadius\n      },\n      '&:first-child:last-child': {\n        borderRadius\n      },\n      [`${componentCls}-group-large &`]: {\n        height: controlHeightLG,\n        fontSize: fontSizeLG,\n        lineHeight: unit(calc(controlHeightLG).sub(calc(lineWidth).mul(2)).equal()),\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderEndStartRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderStartEndRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        }\n      },\n      [`${componentCls}-group-small &`]: {\n        height: controlHeightSM,\n        paddingInline: calc(paddingXS).sub(lineWidth).equal(),\n        paddingBlock: 0,\n        lineHeight: unit(calc(controlHeightSM).sub(calc(lineWidth).mul(2)).equal()),\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusSM,\n          borderEndStartRadius: borderRadiusSM\n        },\n        '&:last-child': {\n          borderStartEndRadius: borderRadiusSM,\n          borderEndEndRadius: borderRadiusSM\n        }\n      },\n      '&:hover': {\n        position: 'relative',\n        color: colorPrimary\n      },\n      '&:has(:focus-visible)': Object.assign({}, genFocusOutline(token)),\n      [`${componentCls}-inner, input[type='checkbox'], input[type='radio']`]: {\n        width: 0,\n        height: 0,\n        opacity: 0,\n        pointerEvents: 'none'\n      },\n      [`&-checked:not(${componentCls}-button-wrapper-disabled)`]: {\n        zIndex: 1,\n        color: colorPrimary,\n        background: buttonCheckedBg,\n        borderColor: colorPrimary,\n        '&::before': {\n          backgroundColor: colorPrimary\n        },\n        '&:first-child': {\n          borderColor: colorPrimary\n        },\n        '&:hover': {\n          color: colorPrimaryHover,\n          borderColor: colorPrimaryHover,\n          '&::before': {\n            backgroundColor: colorPrimaryHover\n          }\n        },\n        '&:active': {\n          color: colorPrimaryActive,\n          borderColor: colorPrimaryActive,\n          '&::before': {\n            backgroundColor: colorPrimaryActive\n          }\n        }\n      },\n      [`${componentCls}-group-solid &-checked:not(${componentCls}-button-wrapper-disabled)`]: {\n        color: buttonSolidCheckedColor,\n        background: buttonSolidCheckedBg,\n        borderColor: buttonSolidCheckedBg,\n        '&:hover': {\n          color: buttonSolidCheckedColor,\n          background: buttonSolidCheckedHoverBg,\n          borderColor: buttonSolidCheckedHoverBg\n        },\n        '&:active': {\n          color: buttonSolidCheckedColor,\n          background: buttonSolidCheckedActiveBg,\n          borderColor: buttonSolidCheckedActiveBg\n        }\n      },\n      '&-disabled': {\n        color: colorTextDisabled,\n        backgroundColor: colorBgContainerDisabled,\n        borderColor: colorBorder,\n        cursor: 'not-allowed',\n        '&:first-child, &:hover': {\n          color: colorTextDisabled,\n          backgroundColor: colorBgContainerDisabled,\n          borderColor: colorBorder\n        }\n      },\n      [`&-disabled${componentCls}-button-wrapper-checked`]: {\n        color: buttonCheckedColorDisabled,\n        backgroundColor: buttonCheckedBgDisabled,\n        borderColor: colorBorder,\n        boxShadow: 'none'\n      },\n      '&-block': {\n        flex: 1,\n        textAlign: 'center'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    wireframe,\n    padding,\n    marginXS,\n    lineWidth,\n    fontSizeLG,\n    colorText,\n    colorBgContainer,\n    colorTextDisabled,\n    controlItemBgActiveDisabled,\n    colorTextLightSolid,\n    colorPrimary,\n    colorPrimaryHover,\n    colorPrimaryActive,\n    colorWhite\n  } = token;\n  const dotPadding = 4; // Fixed value\n  const radioSize = fontSizeLG;\n  const radioDotSize = wireframe ? radioSize - dotPadding * 2 : radioSize - (dotPadding + lineWidth) * 2;\n  return {\n    // Radio\n    radioSize,\n    dotSize: radioDotSize,\n    dotColorDisabled: colorTextDisabled,\n    // Radio buttons\n    buttonSolidCheckedColor: colorTextLightSolid,\n    buttonSolidCheckedBg: colorPrimary,\n    buttonSolidCheckedHoverBg: colorPrimaryHover,\n    buttonSolidCheckedActiveBg: colorPrimaryActive,\n    buttonBg: colorBgContainer,\n    buttonCheckedBg: colorBgContainer,\n    buttonColor: colorText,\n    buttonCheckedBgDisabled: controlItemBgActiveDisabled,\n    buttonCheckedColorDisabled: colorTextDisabled,\n    buttonPaddingInline: padding - lineWidth,\n    wrapperMarginInlineEnd: marginXS,\n    // internal\n    radioColor: wireframe ? colorPrimary : colorWhite,\n    radioBgColor: wireframe ? colorBgContainer : colorPrimary\n  };\n};\nexport default genStyleHooks('Radio', token => {\n  const {\n    controlOutline,\n    controlOutlineWidth\n  } = token;\n  const radioFocusShadow = `0 0 0 ${unit(controlOutlineWidth)} ${controlOutline}`;\n  const radioButtonFocusShadow = radioFocusShadow;\n  const radioToken = mergeToken(token, {\n    radioFocusShadow,\n    radioButtonFocusShadow\n  });\n  return [getGroupRadioStyle(radioToken), getRadioBasicStyle(radioToken), getRadioButtonStyle(radioToken)];\n}, prepareComponentToken, {\n  unitless: {\n    radioSize: true,\n    dotSize: true\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}