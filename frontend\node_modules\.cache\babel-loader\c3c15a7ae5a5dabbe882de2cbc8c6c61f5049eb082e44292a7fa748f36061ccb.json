{"ast": null, "code": "/**\n * @import {Event} from 'micromark-util-types'\n */\n\nimport { subtokenize } from 'micromark-util-subtokenize';\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n  return events;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}