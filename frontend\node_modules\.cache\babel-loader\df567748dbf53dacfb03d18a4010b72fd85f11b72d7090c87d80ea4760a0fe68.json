{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nimport classNames from 'classnames';\nimport RcDropdown from 'rc-dropdown';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport isPrimitive from '../_util/isPrimitive';\nimport getPlacements from '../_util/placements';\nimport genPurePanel from '../_util/PurePanel';\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport zIndexContext from '../_util/zindexContext';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Menu from '../menu';\nimport { OverrideProvider } from '../menu/OverrideContext';\nimport { useToken } from '../theme/internal';\nimport useStyle from './style';\nconst _Placements = ['topLeft', 'topCenter', 'topRight', 'bottomLeft', 'bottomCenter', 'bottomRight', 'top', 'bottom'];\nconst Dropdown = props => {\n  var _a;\n  const {\n    menu,\n    arrow,\n    prefixCls: customizePrefixCls,\n    children,\n    trigger,\n    disabled,\n    dropdownRender,\n    popupRender,\n    getPopupContainer,\n    overlayClassName,\n    rootClassName,\n    overlayStyle,\n    open,\n    onOpenChange,\n    // Deprecated\n    visible,\n    onVisibleChange,\n    mouseEnterDelay = 0.15,\n    mouseLeaveDelay = 0.1,\n    autoAdjustOverflow = true,\n    placement = '',\n    overlay,\n    transitionName,\n    destroyOnHidden,\n    destroyPopupOnHide\n  } = props;\n  const {\n    getPopupContainer: getContextPopupContainer,\n    getPrefixCls,\n    direction,\n    dropdown\n  } = React.useContext(ConfigContext);\n  const mergedPopupRender = popupRender || dropdownRender;\n  // Warning for deprecated usage\n  const warning = devUseWarning('Dropdown');\n  if (process.env.NODE_ENV !== 'production') {\n    const deprecatedProps = {\n      visible: 'open',\n      onVisibleChange: 'onOpenChange',\n      overlay: 'menu',\n      dropdownRender: 'popupRender',\n      destroyPopupOnHide: 'destroyOnHidden'\n    };\n    Object.entries(deprecatedProps).forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, newName);\n    });\n    if (placement.includes('Center')) {\n      warning.deprecated(!placement.includes('Center'), `placement: ${placement}`, `placement: ${placement.slice(0, placement.indexOf('Center'))}`);\n    }\n  }\n  const memoTransitionName = React.useMemo(() => {\n    const rootPrefixCls = getPrefixCls();\n    if (transitionName !== undefined) {\n      return transitionName;\n    }\n    if (placement.includes('top')) {\n      return `${rootPrefixCls}-slide-down`;\n    }\n    return `${rootPrefixCls}-slide-up`;\n  }, [getPrefixCls, placement, transitionName]);\n  const memoPlacement = React.useMemo(() => {\n    if (!placement) {\n      return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';\n    }\n    if (placement.includes('Center')) {\n      return placement.slice(0, placement.indexOf('Center'));\n    }\n    return placement;\n  }, [placement, direction]);\n  const prefixCls = getPrefixCls('dropdown', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const [, token] = useToken();\n  const child = React.Children.only(isPrimitive(children) ? /*#__PURE__*/React.createElement(\"span\", null, children) : children);\n  const popupTrigger = cloneElement(child, {\n    className: classNames(`${prefixCls}-trigger`, {\n      [`${prefixCls}-rtl`]: direction === 'rtl'\n    }, child.props.className),\n    disabled: (_a = child.props.disabled) !== null && _a !== void 0 ? _a : disabled\n  });\n  const triggerActions = disabled ? [] : trigger;\n  const alignPoint = !!(triggerActions === null || triggerActions === void 0 ? void 0 : triggerActions.includes('contextMenu'));\n  // =========================== Open ============================\n  const [mergedOpen, setOpen] = useMergedState(false, {\n    value: open !== null && open !== void 0 ? open : visible\n  });\n  const onInnerOpenChange = useEvent(nextOpen => {\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen, {\n      source: 'trigger'\n    });\n    onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(nextOpen);\n    setOpen(nextOpen);\n  });\n  // =========================== Overlay ============================\n  const overlayClassNameCustomized = classNames(overlayClassName, rootClassName, hashId, cssVarCls, rootCls, dropdown === null || dropdown === void 0 ? void 0 : dropdown.className, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  });\n  const builtinPlacements = getPlacements({\n    arrowPointAtCenter: typeof arrow === 'object' && arrow.pointAtCenter,\n    autoAdjustOverflow,\n    offset: token.marginXXS,\n    arrowWidth: arrow ? token.sizePopupArrow : 0,\n    borderRadius: token.borderRadius\n  });\n  const onMenuClick = React.useCallback(() => {\n    if ((menu === null || menu === void 0 ? void 0 : menu.selectable) && (menu === null || menu === void 0 ? void 0 : menu.multiple)) {\n      return;\n    }\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(false, {\n      source: 'menu'\n    });\n    setOpen(false);\n  }, [menu === null || menu === void 0 ? void 0 : menu.selectable, menu === null || menu === void 0 ? void 0 : menu.multiple]);\n  const renderOverlay = () => {\n    // rc-dropdown already can process the function of overlay, but we have check logic here.\n    // So we need render the element to check and pass back to rc-dropdown.\n    let overlayNode;\n    if (menu === null || menu === void 0 ? void 0 : menu.items) {\n      overlayNode = /*#__PURE__*/React.createElement(Menu, Object.assign({}, menu));\n    } else if (typeof overlay === 'function') {\n      overlayNode = overlay();\n    } else {\n      overlayNode = overlay;\n    }\n    if (mergedPopupRender) {\n      overlayNode = mergedPopupRender(overlayNode);\n    }\n    overlayNode = React.Children.only(typeof overlayNode === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, overlayNode) : overlayNode);\n    return /*#__PURE__*/React.createElement(OverrideProvider, {\n      prefixCls: `${prefixCls}-menu`,\n      rootClassName: classNames(cssVarCls, rootCls),\n      expandIcon: /*#__PURE__*/React.createElement(\"span\", {\n        className: `${prefixCls}-menu-submenu-arrow`\n      }, direction === 'rtl' ? (/*#__PURE__*/React.createElement(LeftOutlined, {\n        className: `${prefixCls}-menu-submenu-arrow-icon`\n      })) : (/*#__PURE__*/React.createElement(RightOutlined, {\n        className: `${prefixCls}-menu-submenu-arrow-icon`\n      }))),\n      mode: \"vertical\",\n      selectable: false,\n      onClick: onMenuClick,\n      validator: ({\n        mode\n      }) => {\n        // Warning if use other mode\n        process.env.NODE_ENV !== \"production\" ? warning(!mode || mode === 'vertical', 'usage', `mode=\"${mode}\" is not supported for Dropdown's Menu.`) : void 0;\n      }\n    }, overlayNode);\n  };\n  // =========================== zIndex ============================\n  const [zIndex, contextZIndex] = useZIndex('Dropdown', overlayStyle === null || overlayStyle === void 0 ? void 0 : overlayStyle.zIndex);\n  // ============================ Render ============================\n  let renderNode = /*#__PURE__*/React.createElement(RcDropdown, Object.assign({\n    alignPoint: alignPoint\n  }, omit(props, ['rootClassName']), {\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay,\n    visible: mergedOpen,\n    builtinPlacements: builtinPlacements,\n    arrow: !!arrow,\n    overlayClassName: overlayClassNameCustomized,\n    prefixCls: prefixCls,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    transitionName: memoTransitionName,\n    trigger: triggerActions,\n    overlay: renderOverlay,\n    placement: memoPlacement,\n    onVisibleChange: onInnerOpenChange,\n    overlayStyle: Object.assign(Object.assign(Object.assign({}, dropdown === null || dropdown === void 0 ? void 0 : dropdown.style), overlayStyle), {\n      zIndex\n    }),\n    autoDestroy: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : destroyPopupOnHide\n  }), popupTrigger);\n  if (zIndex) {\n    renderNode = /*#__PURE__*/React.createElement(zIndexContext.Provider, {\n      value: contextZIndex\n    }, renderNode);\n  }\n  return wrapCSSVar(renderNode);\n};\n// We don't care debug panel\nconst PurePanel = genPurePanel(Dropdown, 'align', undefined, 'dropdown', prefixCls => prefixCls);\n/* istanbul ignore next */\nconst WrapPurePanel = props => (/*#__PURE__*/React.createElement(PurePanel, Object.assign({}, props), /*#__PURE__*/React.createElement(\"span\", null)));\nDropdown._InternalPanelDoNotUseOrYouWillBeFired = WrapPurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Dropdown.displayName = 'Dropdown';\n}\nexport default Dropdown;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}