{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\PrivateRoute.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../utils/auth';\nimport { message } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PrivateRoute = ({\n  children,\n  adminOnly = false\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading,\n    user\n  } = useAuth();\n  const location = useLocation();\n  useEffect(() => {\n    console.log('PrivateRoute state:', {\n      isAuthenticated,\n      loading,\n      path: location.pathname,\n      adminOnly,\n      isAdmin: user === null || user === void 0 ? void 0 : user.is_admin\n    });\n  }, [isAuthenticated, loading, location, adminOnly, user]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px'\n      },\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n  if (!isAuthenticated) {\n    console.log('PrivateRoute: Not authenticated, redirecting to login');\n\n    // 检查当前路径是否为注册页面或登录页面\n    if (location.pathname === '/register' || location.pathname === '/login') {\n      console.log('PrivateRoute: On register/login page, not redirecting');\n      return children;\n    }\n\n    // Redirect to login with the return URL in state\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 检查是否需要管理员权限\n  if (adminOnly && (!user || !user.is_admin)) {\n    console.log('PrivateRoute: User not admin, redirecting to home');\n    message.error('该页面仅限管理员访问');\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 12\n    }, this);\n  }\n  console.log('PrivateRoute: User authenticated, rendering children');\n  return children;\n};\n_s(PrivateRoute, \"bwmWIuUU7oJctd9Jb5xNggxCyF0=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = PrivateRoute;\nexport default PrivateRoute;\nvar _c;\n$RefreshReg$(_c, \"PrivateRoute\");", "map": {"version": 3, "names": ["React", "useEffect", "Navigate", "useLocation", "useAuth", "message", "jsxDEV", "_jsxDEV", "PrivateRoute", "children", "adminOnly", "_s", "isAuthenticated", "loading", "user", "location", "console", "log", "path", "pathname", "isAdmin", "is_admin", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "state", "from", "replace", "error", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/PrivateRoute.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../utils/auth';\nimport { message } from 'antd';\n\nconst PrivateRoute = ({ children, adminOnly = false }) => {\n  const { isAuthenticated, loading, user } = useAuth();\n  const location = useLocation();\n  \n  useEffect(() => {\n    console.log('PrivateRoute state:', { \n      isAuthenticated, \n      loading,\n      path: location.pathname,\n      adminOnly,\n      isAdmin: user?.is_admin\n    });\n  }, [isAuthenticated, loading, location, adminOnly, user]);\n  \n  if (loading) {\n    return (\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'center', \n        alignItems: 'center', \n        height: '100vh',\n        fontSize: '18px'\n      }}>\n        Loading...\n      </div>\n    );\n  }\n  \n  if (!isAuthenticated) {\n    console.log('PrivateRoute: Not authenticated, redirecting to login');\n    \n    // 检查当前路径是否为注册页面或登录页面\n    if (location.pathname === '/register' || location.pathname === '/login') {\n      console.log('PrivateRoute: On register/login page, not redirecting');\n      return children;\n    }\n    \n    // Redirect to login with the return URL in state\n    return <Navigate to='/login' state={{ from: location }} replace />;\n  }\n  \n  // 检查是否需要管理员权限\n  if (adminOnly && (!user || !user.is_admin)) {\n    console.log('PrivateRoute: User not admin, redirecting to home');\n    message.error('该页面仅限管理员访问');\n    return <Navigate to='/' replace />;\n  }\n  \n  console.log('PrivateRoute: User authenticated, rendering children');\n  return children;\n};\n\nexport default PrivateRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EACpD,MAAMW,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACde,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;MACjCL,eAAe;MACfC,OAAO;MACPK,IAAI,EAAEH,QAAQ,CAACI,QAAQ;MACvBT,SAAS;MACTU,OAAO,EAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,eAAe,EAAEC,OAAO,EAAEE,QAAQ,EAAEL,SAAS,EAAEI,IAAI,CAAC,CAAC;EAEzD,IAAID,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKe,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE;MACZ,CAAE;MAAAlB,QAAA,EAAC;IAEH;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,IAAI,CAACnB,eAAe,EAAE;IACpBI,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;IAEpE;IACA,IAAIF,QAAQ,CAACI,QAAQ,KAAK,WAAW,IAAIJ,QAAQ,CAACI,QAAQ,KAAK,QAAQ,EAAE;MACvEH,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE,OAAOR,QAAQ;IACjB;;IAEA;IACA,oBAAOF,OAAA,CAACL,QAAQ;MAAC8B,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEnB;MAAS,CAAE;MAACoB,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIrB,SAAS,KAAK,CAACI,IAAI,IAAI,CAACA,IAAI,CAACO,QAAQ,CAAC,EAAE;IAC1CL,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAChEZ,OAAO,CAAC+B,KAAK,CAAC,YAAY,CAAC;IAC3B,oBAAO7B,OAAA,CAACL,QAAQ;MAAC8B,EAAE,EAAC,GAAG;MAACG,OAAO;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;EAEAf,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;EACnE,OAAOR,QAAQ;AACjB,CAAC;AAACE,EAAA,CAlDIH,YAAY;EAAA,QAC2BJ,OAAO,EACjCD,WAAW;AAAA;AAAAkC,EAAA,GAFxB7B,YAAY;AAoDlB,eAAeA,YAAY;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}