from app.routers.auth import get_current_user
from app.database import get_db
from fastapi import Depends, HTTPException
from app.models.user import User
from app.models.homework import Homework
from typing import List, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import Dict, Any, List
from datetime import datetime, timedelta
import logging
import traceback
import random
import json

from ..database import get_db
from ..models.user import User, Class, ClassTeacher, ClassStudent
from ..models.homework import Homework, HomeworkAssignment, WrongQuestion, ReinforcementExercise, HomeworkCorrection
from ..models.school import School
from ..schemas import homework as homework_schema
from ..routers.auth import get_current_user

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()
@router.get("/recent", response_model=List[Dict])
async def get_recent_homework(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """获取最近作业"""
    try:
        logger.info(f"获取用户 {current_user.username} (ID: {current_user.id}) 的最近作业")
        
        # 查询最近的作业，优先显示最近批改和更新的作业
        recent_homework = (
            db.query(Homework, HomeworkAssignment, User)
            .join(
                HomeworkAssignment,
                Homework.assignment_id == HomeworkAssignment.id
            )
            .outerjoin(  # 添加学生信息
                User,
                Homework.student_id == User.id
            )
            .filter(Homework.student_id == current_user.id)
            .order_by(
                func.coalesce(Homework.graded_at, Homework.updated_at, Homework.created_at).desc()
            )
            .limit(5)
            .all()
        )
        
        logger.info(f"查询到 {len(recent_homework)} 条最新作业记录")
        
        # 转换为字典列表
        result = []
        for hw, assignment, student in recent_homework:
            # 优先使用批改时间，其次是更新时间，最后是创建时间，并转换为北京时间
            display_time = hw.graded_at or hw.updated_at or hw.created_at
            if display_time:
                # 添加8小时以转换为北京时间
                display_time = display_time + timedelta(hours=8)
            display_time_str = display_time.strftime("%Y/%m/%d %H:%M:%S") if display_time else None
            
            # 获取学生显示名称
            student_display_name = student.full_name or student.username
            
            # 记录详细日志
            logger.info(
                f"作业记录 - "
                f"作业ID: {hw.id}, "
                f"作业任务: {assignment.title}, "
                f"学生: {student_display_name}, "
                f"时间: {display_time_str}, "
                f"状态: {hw.status}"
            )
            
            # 获取作业状态的中文显示
            status_display = {
                'submitted': '已提交',
                'graded': '已批改',
                'pending': '待提交'
            }.get(hw.status, hw.status)
            
            result.append({
                "id": hw.id,
                "title": assignment.title,
                "subject": hw.subject,
                "created_at": display_time_str,  # 使用最新的时间
                "status": status_display,  # 使用中文状态显示
                "student_name": student_display_name,  # 添加学生名称
                "student_id": hw.student_id,  # 添加学生ID
                "assignment_id": hw.assignment_id,
                "assignment_title": assignment.title  # 添加作业标题
            })
        return result
    except Exception as e:
        logger.error(f"获取最近作业失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最近作业失败: {str(e)}")


@router.get("/statistics/teacher")
async def get_teacher_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取教师统计数据 - 系统级统计，显示全系统数据"""
    try:
        logger.info(f"用户 {current_user.username} (ID: {current_user.id}) 请求系统级教师统计数据")
        
        # 检查是否是管理员或教师
        if not current_user.is_admin and not current_user.is_teacher:
            logger.warning(f"用户 {current_user.username} (身份: {'普通用户'}) 尝试访问教师统计数据，权限不足")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问教师统计数据"
            )
        
        # 系统级统计：查询所有班级
        if current_user.is_admin:
            # 超级管理员查看所有班级
            teacher_classes = db.query(Class).all()
            logger.info(f"超级管理员查看所有班级: {len(teacher_classes)} 个")
        else:
            # 教师只查看自己管理的班级
            teacher_classes = db.query(Class).join(
                ClassTeacher, Class.id == ClassTeacher.class_id
            ).filter(
                ClassTeacher.teacher_id == current_user.id
            ).all()
            logger.info(f"教师查看自己管理的班级: {len(teacher_classes)} 个")
        
        if not teacher_classes and current_user.is_teacher and not current_user.is_admin:
            logger.warning(f"教师 {current_user.username} 没有管理的班级")
            return {
                "school_count": 0,
                "class_count": 0,
                "student_count": 0,
                "homework_count": 0,
                "corrected_count": 0,
                "average_score": 0,
                "average_accuracy": 0.0,
                "class_statistics": {},
                "recent_assignments": [],
                "total_homework_count": 0,
                "graded_homework_count": 0,
                "pending_homework_count": 0
            }
        
        # 获取系统级统计数据
        logger.info(f"获取{'超级管理员' if current_user.is_admin else '教师'} {current_user.username} 的系统级统计数据")
        
        # 获取学校总数 - 系统级统计显示所有学校
        try:
            school_count = db.query(func.count(School.id)).scalar()
            logger.info(f"系统级统计 - 查询到的学校总数: {school_count}")
            if school_count is None:
                school_count = 0
                logger.warning("学校总数查询返回None，设置为0")
            # 直接验证数据库中是否有学校
            schools = db.query(School).limit(5).all()
            logger.info(f"数据库中的前5所学校: {[school.name for school in schools]}")
            # 明确记录学校总数
            logger.info(f"最终返回的学校总数: {school_count}")
        except Exception as e:
            logger.error(f"获取学校总数时出错: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            school_count = 0
        
        # 班级数量 - 系统级统计显示所有班级
        if current_user.is_admin:
            # 超级管理员查看所有班级数量
            class_count = db.query(func.count(Class.id)).scalar() or 0
            logger.info(f"系统级统计 - 超级管理员查看所有班级数量: {class_count}")
        else:
            # 教师只查看自己关联的班级
            class_count = len(teacher_classes)
            logger.info(f"教师查看关联班级数量: {class_count}")
        
        # 班级ID列表
        class_ids = [cls.id for cls in teacher_classes]
        
        # 学生数量 - 系统级统计显示所有学生
        if current_user.is_admin:
            # 超级管理员查看所有学生数量
            student_count = db.query(func.count(User.id)).filter(
                User.is_teacher == False,
                User.is_admin == False
            ).scalar() or 0
            logger.info(f"系统级统计 - 超级管理员查看所有学生数量: {student_count}")
        else:
            # 教师只查看自己班级的学生
            student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                ClassStudent.class_id.in_(class_ids) if class_ids else False
            ).scalar() or 0
            logger.info(f"教师查看班级学生数量: {student_count}")
        
        # 作业数量 - 系统级统计显示所有作业
        if current_user.is_admin:
            # 超级管理员查看所有作业提交数量
            homework_count = db.query(func.count(Homework.id)).scalar() or 0
            logger.info(f"系统级统计 - 超级管理员查看所有作业提交数量: {homework_count}")
        else:
            # 教师查看作业任务数量
            homework_count = db.query(func.count(HomeworkAssignment.id)).filter(
                HomeworkAssignment.class_id.in_(class_ids) if class_ids else False
            ).scalar() or 0
            logger.info(f"教师查看作业任务数量: {homework_count}")
        
        # 已批改作业数量 - 系统级统计显示所有已批改作业
        if current_user.is_admin:
            # 超级管理员查看所有已批改作业数量
            corrected_count = db.query(func.count(Homework.id)).filter(
                Homework.status == "graded"
            ).scalar() or 0
            logger.info(f"系统级统计 - 超级管理员查看所有已批改作业数量: {corrected_count}")
        else:
            # 教师只查看自己班级的已批改作业数量
            corrected_count = db.query(func.count(Homework.id)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False,
                Homework.status == "graded"
            ).scalar() or 0
            logger.info(f"教师查看班级已批改作业数量: {corrected_count}")
        
        # 平均分数 - 系统级统计显示所有作业的平均分
        if current_user.is_admin:
            # 超级管理员查看所有作业的平均分
            average_score = db.query(func.avg(Homework.score)).filter(
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).scalar() or 0
            logger.info(f"系统级统计 - 超级管理员查看所有作业的平均分: {average_score}")
        else:
            # 教师只查看自己班级作业的平均分
            average_score = db.query(func.avg(Homework.score)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False,
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).scalar() or 0
            logger.info(f"教师查看班级作业的平均分: {average_score}")
        
        # 平均正确率 - 系统级统计显示所有作业的平均正确率
        if current_user.is_admin:
            # 超级管理员查看所有作业的平均正确率
            average_accuracy = db.query(func.avg(Homework.accuracy)).filter(
                Homework.status == "graded",
                Homework.accuracy.isnot(None)
            ).scalar() or 0.0
            logger.info(f"系统级统计 - 超级管理员查看所有作业的平均正确率: {average_accuracy}")
        else:
            # 教师只查看自己班级作业的平均正确率
            average_accuracy = db.query(func.avg(Homework.accuracy)).filter(
                Homework.class_id.in_(class_ids) if class_ids else False,
                Homework.status == "graded",
                Homework.accuracy.isnot(None)
            ).scalar() or 0.0
            logger.info(f"教师查看班级作业的平均正确率: {average_accuracy}")
        
        # 生成班级统计数据 - 系统级统计显示所有班级
        class_statistics = {}
        if current_user.is_admin:
            # 超级管理员查看所有班级的统计
            all_classes = db.query(Class).all()
            for cls in all_classes:
                # 该班级的学生数量
                cls_student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                    ClassStudent.class_id == cls.id
                ).scalar() or 0
                
                # 该班级的作业任务数量
                cls_assignment_count = db.query(func.count(HomeworkAssignment.id)).filter(
                    HomeworkAssignment.class_id == cls.id
                ).scalar() or 0
                
                # 该班级的作业提交数量
                cls_homework_count = db.query(func.count(Homework.id)).filter(
                    Homework.class_id == cls.id
                ).scalar() or 0
                
                # 该班级的平均分
                cls_average_score = db.query(func.avg(Homework.score)).filter(
                    Homework.class_id == cls.id,
                    Homework.status == "graded",
                    Homework.score.isnot(None)
                ).scalar() or 0
                
                # 该班级的作业提交率
                total_possible = cls_student_count * cls_assignment_count
                submitted_count = cls_homework_count
                submission_rate = submitted_count / total_possible if total_possible > 0 else 0
                
                # 获取学校名称
                school_name = ""
                if cls.school_id:
                    school = db.query(School).filter(School.id == cls.school_id).first()
                    if school:
                        school_name = school.name
                
                # 使用class_id作为键，创建字典
                key = f"class_{cls.id}"
                class_statistics[key] = {
                    "class_id": cls.id,
                    "class_name": cls.name,
                    "school_name": school_name,
                    "school_id": cls.school_id,
                    "student_count": cls_student_count,
                    "assignment_count": cls_assignment_count,
                    "homework_count": cls_homework_count,
                    "average_score": round(float(cls_average_score), 1),
                    "submission_rate": round(submission_rate, 2)
                }
        else:
            # 教师只查看自己管理的班级
            for cls in teacher_classes:
                # 该班级的学生数量
                cls_student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                    ClassStudent.class_id == cls.id
                ).scalar() or 0
                
                # 该班级的作业任务数量
                cls_assignment_count = db.query(func.count(HomeworkAssignment.id)).filter(
                    HomeworkAssignment.class_id == cls.id
                ).scalar() or 0
                
                # 该班级的作业提交数量
                cls_homework_count = db.query(func.count(Homework.id)).filter(
                    Homework.class_id == cls.id
                ).scalar() or 0
                
                # 该班级的平均分
                cls_average_score = db.query(func.avg(Homework.score)).filter(
                    Homework.class_id == cls.id,
                    Homework.status == "graded",
                    Homework.score.isnot(None)
                ).scalar() or 0
                
                # 该班级的作业提交率
                total_possible = cls_student_count * cls_assignment_count
                submitted_count = cls_homework_count
                submission_rate = submitted_count / total_possible if total_possible > 0 else 0
                
                # 使用class_id作为键，创建字典
                key = f"class_{cls.id}"
                class_statistics[key] = {
                    "class_id": cls.id,
                    "class_name": cls.name,
                    "student_count": cls_student_count,
                    "assignment_count": cls_assignment_count,
                    "homework_count": cls_homework_count,
                    "average_score": round(float(cls_average_score), 1),
                    "submission_rate": round(submission_rate, 2)
                }
        
        # 获取最近的作业 - 系统级统计显示所有最近作业
        recent_assignments = []
        if current_user.is_admin:
            # 超级管理员查看所有最近作业
            assignments = db.query(HomeworkAssignment).order_by(
                HomeworkAssignment.created_at.desc()
            ).limit(5).all()
            logger.info(f"系统级统计 - 超级管理员查看所有最近作业: {len(assignments)} 个")
        else:
            # 教师只查看自己班级的最近作业
            assignments = db.query(HomeworkAssignment).filter(
                HomeworkAssignment.class_id.in_(class_ids) if class_ids else False
            ).order_by(HomeworkAssignment.created_at.desc()).limit(5).all()
            logger.info(f"教师查看班级最近作业: {len(assignments)} 个")
        
        for assignment in assignments:
            # 获取该作业的提交数量
            submission_count = db.query(func.count(Homework.id)).filter(
                Homework.assignment_id == assignment.id
            ).scalar() or 0
            # 获取该作业的平均分
            assignment_avg_score = db.query(func.avg(Homework.score)).filter(
                Homework.assignment_id == assignment.id,
                Homework.status == "graded",
                Homework.score.isnot(None)
            ).scalar() or 0
            # 获取班级名称和学校名称
            class_name = ""
            school_name = ""
            if assignment.class_id:
                class_query = db.query(Class).filter(Class.id == assignment.class_id).first()
                if class_query:
                    class_name = class_query.name
                    if class_query.school_id:
                        school = db.query(School).filter(School.id == class_query.school_id).first()
                        if school:
                            school_name = school.name
            # 获取教师名称
            teacher_name = ""
            if assignment.teacher_id:
                teacher = db.query(User).filter(User.id == assignment.teacher_id).first()
                if teacher:
                    teacher_name = teacher.full_name or teacher.username
            # 获取科目名称
            subject_name = ""
            if assignment.subject_id:
                from ..models.subject import Subject
                subject = db.query(Subject).filter(Subject.id == assignment.subject_id).first()
                if subject:
                    subject_name = subject.name
            # 计算提交率
            student_count = db.query(func.count(func.distinct(ClassStudent.student_id))).filter(
                ClassStudent.class_id == assignment.class_id
            ).scalar() or 0
            submission_rate = 0
            if student_count > 0:
                submission_rate = submission_count / student_count
            recent_assignments.append({
                "id": assignment.id,
                "title": assignment.title,
                "class_id": assignment.class_id,
                "class_name": class_name,
                "school_id": assignment.school_id,
                "school_name": school_name,
                "teacher_id": assignment.teacher_id,
                "teacher_name": teacher_name,
                "subject_id": assignment.subject_id,
                "subject_name": subject_name,
                "created_at": assignment.created_at,
                "submission_count": submission_count,
                "student_count": student_count,
                "submission_rate": round(submission_rate, 2),
                "average_score": round(float(assignment_avg_score), 1)
            })
        
        return {
            "school_count": school_count,
            "class_count": class_count,
            "student_count": student_count,
            "homework_count": homework_count,
            "corrected_count": corrected_count,
            "average_score": round(float(average_score), 1),
            "average_accuracy": float(average_accuracy),
            "class_statistics": class_statistics,
            "recent_assignments": recent_assignments,
            "total_homework_count": homework_count,
            "graded_homework_count": corrected_count,
            "pending_homework_count": homework_count - corrected_count
        }
            
    except Exception as e:
        logger.error(f"生成教师统计数据时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计数据失败: {str(e)}"
        )

@router.get("/statistics/student", response_model=homework_schema.StudentStatistics)
async def get_student_statistics(
    start_date: str = None,
    end_date: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取学生统计数据"""
    try:
        logger.info(f"用户 {current_user.username} (ID: {current_user.id}) 请求学生统计数据")
        
        # 验证是否为学生或管理员
        if current_user.is_teacher and not current_user.is_admin:
            logger.warning(f"教师用户 {current_user.username} 尝试访问学生统计数据")
            raise HTTPException(status_code=403, detail="此功能仅对学生开放")
        
        # 处理日期筛选
        date_filter = True
        if start_date and end_date:
            try:
                start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
                end_datetime = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)  # 包含结束日期
                date_filter = Homework.created_at.between(start_datetime, end_datetime)
            except ValueError:
                logger.warning(f"日期格式错误: {start_date}, {end_date}")
                raise HTTPException(status_code=400, detail="日期格式错误，请使用YYYY-MM-DD格式")
        
        # 如果是管理员，获取真实数据或示例数据
        if current_user.is_admin:
            # 尝试获取一些真实数据
            latest_homeworks = db.query(Homework).order_by(Homework.created_at.desc()).limit(10).all()
            
            if latest_homeworks:
                logger.info(f"管理员用户 {current_user.username} 访问学生统计数据，使用最新 {len(latest_homeworks)} 条作业数据")
                
                # 使用系统中最新的作业数据
                student_id = latest_homeworks[0].student_id
                
                # 更新筛选条件，使用最新作业的学生ID
                total_homework_count = db.query(func.count(Homework.id)).filter(
                    Homework.student_id == student_id,
                    date_filter
                ).scalar() or 0
                
                completed_homework_count = db.query(func.count(Homework.id)).filter(
                    Homework.student_id == student_id,
                    Homework.status == "graded",
                    date_filter
                ).scalar() or 0
                
                pending_homework_count = total_homework_count - completed_homework_count
                
                # 计算平均正确率
                average_accuracy = db.query(func.avg(Homework.accuracy)).filter(
                    Homework.student_id == student_id,
                    Homework.status == "graded",
                    date_filter
                ).scalar() or 0.0
                
                # 错题统计
                wrong_question_count = db.query(func.count(WrongQuestion.id)).filter(
                    WrongQuestion.student_id == student_id
                ).scalar() or 0
                
                # 强化练习统计
                reinforcement_exercise_count = db.query(func.count(ReinforcementExercise.id)).filter(
                    ReinforcementExercise.student_id == student_id
                ).scalar() or 0
                
                completed_exercise_count = db.query(func.count(ReinforcementExercise.id)).filter(
                    ReinforcementExercise.student_id == student_id,
                    ReinforcementExercise.is_completed == True
                ).scalar() or 0
                
                # 获取该学生已批改的作业
                graded_homeworks = db.query(Homework).filter(
                    Homework.student_id == student_id,
                    Homework.status == "graded",
                    date_filter
                ).order_by(Homework.created_at.desc()).all()
                
                # 处理作业数据
                accuracy_trend = []
                correct_counts = []
                
                for hw in graded_homeworks:
                    # 获取批改数据
                    corrections = db.query(HomeworkCorrection).filter(
                        HomeworkCorrection.homework_id == hw.id
                    ).all()
                    
                    correct_count = 0
                    for correction in corrections:
                        try:
                            data = json.loads(correction.correction_data)
                            correct_count += data.get("correct_count", 0)
                        except:
                            logger.warning(f"解析批改数据失败: {correction.correction_data}")
                            continue
                    
                    correct_counts.append(correct_count)
                    
                    accuracy_trend.append({
                        "id": hw.id,
                        "title": hw.title,
                        "accuracy": hw.accuracy,
                        "correct_count": correct_count,
                        "created_at": hw.created_at.isoformat() if hw.created_at else None
                    })
                
                # 计算最高、最低和平均正确题数
                highest_correct_count = max(correct_counts) if correct_counts else 5
                lowest_correct_count = min(correct_counts) if correct_counts else 3
                average_correct_count = sum(correct_counts) / len(correct_counts) if correct_counts else 4
                
                return {
                    "pending_homework_count": pending_homework_count,
                    "average_accuracy": float(average_accuracy),
                    "total_homework_count": total_homework_count,
                    "completed_homework_count": completed_homework_count,
                    "wrong_question_count": wrong_question_count,
                    "reinforcement_exercise_count": reinforcement_exercise_count,
                    "completed_exercise_count": completed_exercise_count,
                    "highest_correct_count": highest_correct_count,
                    "average_correct_count": float(average_correct_count),
                    "lowest_correct_count": lowest_correct_count,
                    "accuracy_trend": accuracy_trend[:10]  # 限制返回最近10个作业
                }
            else:
                logger.info(f"管理员用户 {current_user.username} 访问学生统计数据，但系统中没有作业数据，返回示例数据")
                return {
                    "pending_homework_count": 2,
                    "average_accuracy": 0.78,
                    "total_homework_count": 25,
                    "completed_homework_count": 23,
                    "wrong_question_count": 15,
                    "reinforcement_exercise_count": 30,
                    "completed_exercise_count": 20,
                    "highest_correct_count": 15,
                    "average_correct_count": 10.5,
                    "lowest_correct_count": 5,
                    "accuracy_trend": [
                        {"id": 1, "title": "示例作业1", "correct_count": 10, "created_at": "2025-01-01T10:00:00"},
                        {"id": 2, "title": "示例作业2", "correct_count": 15, "created_at": "2025-01-05T10:00:00"},
                        {"id": 3, "title": "示例作业3", "correct_count": 5, "created_at": "2025-01-10T10:00:00"}
                    ]
                }
        
        # 对于学生用户，获取真实数据
        # 作业统计
        logger.info("查询作业统计")
        total_homework_count = db.query(func.count(Homework.id)).filter(
            Homework.student_id == current_user.id,
            date_filter
        ).scalar() or 0
        
        completed_homework_count = db.query(func.count(Homework.id)).filter(
            Homework.student_id == current_user.id,
            Homework.status == "graded",
            date_filter
        ).scalar() or 0
        
        pending_homework_count = total_homework_count - completed_homework_count
        
        # 计算平均正确率
        logger.info("计算平均正确率")
        average_accuracy = db.query(func.avg(Homework.accuracy)).filter(
            Homework.student_id == current_user.id,
            Homework.status == "graded",
            date_filter
        ).scalar() or 0.0
        
        # 错题统计
        logger.info("查询错题统计")
        wrong_question_count = db.query(func.count(WrongQuestion.id)).filter(
            WrongQuestion.student_id == current_user.id
        ).scalar() or 0
        
        # 强化练习统计
        logger.info("查询强化练习统计")
        reinforcement_exercise_count = db.query(func.count(ReinforcementExercise.id)).filter(
            ReinforcementExercise.student_id == current_user.id
        ).scalar() or 0
        
        completed_exercise_count = db.query(func.count(ReinforcementExercise.id)).filter(
            ReinforcementExercise.student_id == current_user.id,
            ReinforcementExercise.is_completed == True
        ).scalar() or 0
        
        # 获取已批改的作业，用于计算正确题数和正确率趋势
        logger.info("获取作业详情")
        graded_homeworks = db.query(Homework).filter(
            Homework.student_id == current_user.id,
            Homework.status == "graded",
            date_filter
        ).order_by(Homework.created_at.desc()).all()
        
        # 如果没有已批改的作业，获取最新提交的作业
        if not graded_homeworks:
            latest_homework = db.query(Homework).filter(
                Homework.student_id == current_user.id
            ).order_by(Homework.created_at.desc()).first()
            
            if latest_homework:
                graded_homeworks = [latest_homework]
                logger.info(f"未找到已批改作业，使用最新提交的作业 {latest_homework.title} (ID: {latest_homework.id}) 作为默认数据")
        
        # 计算每个作业的正确题数
        accuracy_trend = []
        correct_counts = []
        
        for hw in graded_homeworks:
            # 获取批改数据
            corrections = db.query(HomeworkCorrection).filter(
                HomeworkCorrection.homework_id == hw.id
            ).all()
            
            correct_count = 0
            for correction in corrections:
                try:
                    # 假设correction_data是JSON格式，包含correct_count字段
                    data = json.loads(correction.correction_data)
                    correct_count += data.get("correct_count", 0)
                except:
                    logger.warning(f"解析批改数据失败: {correction.correction_data}")
                    continue
            
            correct_counts.append(correct_count)
            
            accuracy_trend.append({
                "id": hw.id,
                "title": hw.title,
                "accuracy": hw.accuracy,
                "correct_count": correct_count,
                "created_at": hw.created_at.isoformat() if hw.created_at else None
            })
        
        # 如果没有任何作业数据，提供默认示例数据
        if not accuracy_trend:
            accuracy_trend = [
                {"id": 1, "title": "示例作业1", "accuracy": 0.8, "correct_count": 4, "created_at": datetime.now().isoformat()},
                {"id": 2, "title": "示例作业2", "accuracy": 0.6, "correct_count": 3, "created_at": (datetime.now() - timedelta(days=1)).isoformat()},
                {"id": 3, "title": "示例作业3", "accuracy": 0.9, "correct_count": 5, "created_at": (datetime.now() - timedelta(days=2)).isoformat()}
            ]
            correct_counts = [4, 3, 5]
        
        # 计算最高、最低和平均正确题数
        highest_correct_count = max(correct_counts) if correct_counts else 5
        lowest_correct_count = min(correct_counts) if correct_counts else 3
        average_correct_count = sum(correct_counts) / len(correct_counts) if correct_counts else 4
        
        return {
            "pending_homework_count": pending_homework_count,
            "average_accuracy": float(average_accuracy),
            "total_homework_count": total_homework_count,
            "completed_homework_count": completed_homework_count,
            "wrong_question_count": wrong_question_count,
            "reinforcement_exercise_count": reinforcement_exercise_count,
            "completed_exercise_count": completed_exercise_count,
            "highest_correct_count": highest_correct_count,
            "average_correct_count": float(average_correct_count),
            "lowest_correct_count": lowest_correct_count,
            "accuracy_trend": accuracy_trend[:10]  # 限制返回最近10个作业
        }
        
    except Exception as e:
        logger.error(f"生成学生统计数据时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")

@router.get("/statistics/wrong-questions")
async def get_wrong_question_statistics(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取错题统计数据"""
    # 根据用户角色确定查询范围
    if current_user.is_teacher:
        # 教师查看所有学生的错题类型分布
        question_type_stats = db.query(
            WrongQuestion.question_type,
            func.count(WrongQuestion.id).label("count")
        ).filter(
            WrongQuestion.homework_id.in_(
                db.query(Homework.id).join(
                    HomeworkAssignment,
                    Homework.assignment_id == HomeworkAssignment.id
                ).filter(
                    HomeworkAssignment.teacher_id == current_user.id
                )
            )
        ).group_by(
            WrongQuestion.question_type
        ).all()
    else:
        # 学生只查看自己的错题类型分布
        question_type_stats = db.query(
            WrongQuestion.question_type,
            func.count(WrongQuestion.id).label("count")
        ).filter(
            WrongQuestion.student_id == current_user.id
        ).group_by(
            WrongQuestion.question_type
        ).all()
    
    # 构建响应
    result = {}
    for item in question_type_stats:
        result[item.question_type] = item.count
    
    return result

@router.get("/statistics/export", status_code=status.HTTP_200_OK)
async def export_statistics(
    start_date: str = None,
    end_date: str = None,
    class_id: int = None,
    assignment_id: int = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """导出统计数据"""
    # 验证是否为教师
    if not current_user.is_teacher:
        raise HTTPException(status_code=403, detail="只有教师才能导出统计数据")
    
    # 将字符串日期转换为datetime对象
    start_date_obj = None
    end_date_obj = None
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(status_code=400, detail="起始日期格式无效，请使用YYYY-MM-DD格式")
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            # 结束日期设为当天结束
            end_date_obj = end_date_obj + timedelta(days=1, microseconds=-1)
        except ValueError:
            raise HTTPException(status_code=400, detail="结束日期格式无效，请使用YYYY-MM-DD格式")
    
    # 获取基本查询
    query = db.query(
        Homework.id,
        Homework.title,
        Homework.status,
        Homework.score,
        Homework.accuracy,
        Homework.created_at,
        Homework.graded_at,
        User.username.label("student_name"),
        HomeworkAssignment.title.label("assignment_title"),
        Class.name.label("class_name")
    ).join(
        User, Homework.student_id == User.id
    ).join(
        HomeworkAssignment, Homework.assignment_id == HomeworkAssignment.id, isouter=True
    ).join(
        Class, HomeworkAssignment.class_id == Class.id, isouter=True
    ).filter(
        HomeworkAssignment.teacher_id == current_user.id
    )
    
    # 应用过滤器
    if start_date_obj:
        query = query.filter(Homework.created_at >= start_date_obj)
    
    if end_date_obj:
        query = query.filter(Homework.created_at <= end_date_obj)
    
    if class_id:
        query = query.filter(HomeworkAssignment.class_id == class_id)
        
    if assignment_id:
        query = query.filter(Homework.assignment_id == assignment_id)
    
    # 执行查询
    results = query.all()
    
    # 构建响应
    export_data = []
    for row in results:
        export_data.append({
            "id": row.id,
            "title": row.title,
            "status": row.status,
            "score": row.score,
            "accuracy": row.accuracy,
            "submitted_at": row.created_at.strftime("%Y-%m-%d %H:%M:%S") if row.created_at else None,
            "graded_at": row.graded_at.strftime("%Y-%m-%d %H:%M:%S") if row.graded_at else None,
            "student_name": row.student_name,
            "assignment_title": row.assignment_title,
            "class_name": row.class_name
        })
    
    return {
        "total_count": len(export_data),
        "start_date": start_date,
        "end_date": end_date,
        "class_id": class_id,
        "assignment_id": assignment_id,
        "data": export_data
    } 