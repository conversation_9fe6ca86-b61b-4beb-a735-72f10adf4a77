{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Popup } from 'rc-tooltip';\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nexport const Overlay = ({\n  title,\n  content,\n  prefixCls\n}) => {\n  if (!title && !content) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, title && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), content && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-inner-content`\n  }, content));\n};\nexport const RawPurePanel = props => {\n  const {\n    hashId,\n    prefixCls,\n    className,\n    style,\n    placement = 'top',\n    title,\n    content,\n    children\n  } = props;\n  const titleNode = getRenderPropValue(title);\n  const contentNode = getRenderPropValue(content);\n  const cls = classNames(hashId, prefixCls, `${prefixCls}-pure`, `${prefixCls}-placement-${placement}`, className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-arrow`\n  }), /*#__PURE__*/React.createElement(Popup, Object.assign({}, props, {\n    className: hashId,\n    prefixCls: prefixCls\n  }), children || /*#__PURE__*/React.createElement(Overlay, {\n    prefixCls: prefixCls,\n    title: titleNode,\n    content: contentNode\n  })));\n};\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      className\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('popover', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RawPurePanel, Object.assign({}, restProps, {\n    prefixCls: prefixCls,\n    hashId: hashId,\n    className: classNames(className, cssVarCls)\n  })));\n};\nexport default PurePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}