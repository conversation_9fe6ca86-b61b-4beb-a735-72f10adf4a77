{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\student\\\\SmartReminder.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { notification, Button, Space, Typography, Badge } from 'antd';\nimport { BellOutlined, ClockCircleOutlined, FireOutlined, CheckCircleOutlined, ExclamationCircleOutlined, TrophyOutlined } from '@ant-design/icons';\nimport moment from 'moment';\nimport '../../styles/student.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nconst SmartReminder = ({\n  assignments = [],\n  user = {},\n  onNavigate,\n  enableNotifications = true,\n  className = '',\n  ...props\n}) => {\n  _s2();\n  var _s = $RefreshSig$();\n  const [reminders, setReminders] = useState([]);\n  const [lastCheck, setLastCheck] = useState(null);\n\n  // 检查提醒条件\n  const checkReminders = () => {\n    const now = moment();\n    const newReminders = [];\n    assignments.forEach(assignment => {\n      // 逾期提醒\n      if (assignment.due_date && assignment.submission_status === '未提交') {\n        const dueDate = moment(assignment.due_date);\n        const isOverdue = dueDate.isBefore(now);\n        const hoursUntilDue = dueDate.diff(now, 'hours');\n        const daysUntilDue = dueDate.diff(now, 'days');\n        if (isOverdue) {\n          newReminders.push({\n            id: `overdue-${assignment.id}`,\n            type: 'overdue',\n            priority: 'high',\n            title: '作业已逾期',\n            message: `《${assignment.title}》已逾期 ${Math.abs(daysUntilDue)} 天`,\n            assignment,\n            icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 19\n            }, this),\n            color: '#FF3B30',\n            action: '立即提交'\n          });\n        } else if (hoursUntilDue <= 2) {\n          newReminders.push({\n            id: `urgent-${assignment.id}`,\n            type: 'urgent',\n            priority: 'high',\n            title: '作业即将截止',\n            message: `《${assignment.title}》还有 ${hoursUntilDue} 小时截止`,\n            assignment,\n            icon: /*#__PURE__*/_jsxDEV(FireOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this),\n            color: '#FF9500',\n            action: '快速提交'\n          });\n        } else if (daysUntilDue <= 1) {\n          newReminders.push({\n            id: `warning-${assignment.id}`,\n            type: 'warning',\n            priority: 'medium',\n            title: '作业明天截止',\n            message: `《${assignment.title}》明天截止，记得及时提交`,\n            assignment,\n            icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 19\n            }, this),\n            color: '#FF9500',\n            action: '查看详情'\n          });\n        }\n      }\n\n      // 批改完成提醒\n      if (assignment.grading_status === '已批改' && assignment.score !== null) {\n        const gradedAt = moment(assignment.graded_at);\n        const hoursSinceGraded = now.diff(gradedAt, 'hours');\n        if (hoursSinceGraded <= 24) {\n          // 24小时内批改完成的\n          newReminders.push({\n            id: `graded-${assignment.id}`,\n            type: 'graded',\n            priority: 'medium',\n            title: '作业已批改',\n            message: `《${assignment.title}》已批改完成，得分 ${assignment.score} 分`,\n            assignment,\n            icon: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 19\n            }, this),\n            color: assignment.score >= 80 ? '#34C759' : '#FF9500',\n            action: '查看详情'\n          });\n        }\n      }\n    });\n\n    // 学习激励提醒\n    const completedToday = assignments.filter(a => {\n      return a.submission_status === '已提交' && moment(a.submitted_at).isSame(now, 'day');\n    }).length;\n    if (completedToday >= 3) {\n      newReminders.push({\n        id: 'achievement-daily',\n        type: 'achievement',\n        priority: 'low',\n        title: '今日学习目标达成',\n        message: `太棒了！你今天已经完成了 ${completedToday} 份作业`,\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 15\n        }, this),\n        color: '#34C759',\n        action: '查看成就'\n      });\n    }\n    setReminders(newReminders);\n    setLastCheck(now);\n  };\n\n  // 显示通知\n  const showNotification = reminder => {\n    if (!enableNotifications) return;\n    const notificationConfig = {\n      message: reminder.title,\n      description: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: reminder.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '12px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"small\",\n              onClick: () => handleReminderAction(reminder),\n              children: reminder.action\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              onClick: () => notification.destroy(reminder.id),\n              children: \"\\u5FFD\\u7565\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this),\n      icon: reminder.icon,\n      duration: reminder.priority === 'high' ? 0 : 10,\n      // 高优先级不自动关闭\n      key: reminder.id,\n      style: {\n        borderLeft: `4px solid ${reminder.color}`\n      },\n      onClick: () => handleReminderAction(reminder)\n    };\n\n    // 根据优先级选择通知类型\n    switch (reminder.priority) {\n      case 'high':\n        notification.error(notificationConfig);\n        break;\n      case 'medium':\n        notification.warning(notificationConfig);\n        break;\n      case 'low':\n        notification.success(notificationConfig);\n        break;\n      default:\n        notification.info(notificationConfig);\n    }\n  };\n\n  // 处理提醒操作\n  const handleReminderAction = reminder => {\n    notification.destroy(reminder.id);\n    if (onNavigate) {\n      switch (reminder.type) {\n        case 'overdue':\n        case 'urgent':\n        case 'warning':\n          onNavigate('/homework/upload/single', {\n            state: {\n              assignmentId: reminder.assignment.id,\n              assignmentTitle: reminder.assignment.title\n            }\n          });\n          break;\n        case 'graded':\n          onNavigate(`/homework/${reminder.assignment.homework_id}`);\n          break;\n        case 'achievement':\n          onNavigate('/profile/statistics');\n          break;\n        default:\n          onNavigate('/homework');\n      }\n    }\n  };\n\n  // 定期检查提醒\n  useEffect(() => {\n    checkReminders();\n\n    // 每5分钟检查一次\n    const interval = setInterval(checkReminders, 5 * 60 * 1000);\n    return () => clearInterval(interval);\n  }, [assignments]);\n\n  // 显示新的提醒\n  useEffect(() => {\n    if (lastCheck && reminders.length > 0) {\n      reminders.forEach(reminder => {\n        // 避免重复显示相同的提醒\n        const existingNotification = document.querySelector(`[data-testid=\"${reminder.id}\"]`);\n        if (!existingNotification) {\n          showNotification(reminder);\n        }\n      });\n    }\n  }, [reminders, lastCheck]);\n\n  // 获取提醒统计\n  const getReminderStats = () => {\n    const stats = {\n      total: reminders.length,\n      high: reminders.filter(r => r.priority === 'high').length,\n      medium: reminders.filter(r => r.priority === 'medium').length,\n      low: reminders.filter(r => r.priority === 'low').length\n    };\n    return stats;\n  };\n  const stats = getReminderStats();\n\n  // 提醒中心组件\n  const ReminderCenter = () => {\n    _s();\n    const [visible, setVisible] = useState(false);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `student-reminder-center ${className}`,\n      ...props,\n      children: [/*#__PURE__*/_jsxDEV(Badge, {\n        count: stats.high,\n        offset: [0, 0],\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 19\n          }, this),\n          onClick: () => setVisible(!visible),\n          style: {\n            color: stats.high > 0 ? '#FF3B30' : '#666666',\n            fontSize: '18px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), visible && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: '100%',\n          right: 0,\n          width: '320px',\n          background: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',\n          border: '1px solid #E8E8E8',\n          zIndex: 1000,\n          maxHeight: '400px',\n          overflow: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #F0F0F0',\n            fontWeight: 600,\n            color: '#1A1A1A'\n          },\n          children: [\"\\uD83D\\uDD14 \\u667A\\u80FD\\u63D0\\u9192 (\", stats.total, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), reminders.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '40px 20px',\n            textAlign: 'center',\n            color: '#666666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '32px',\n              marginBottom: '8px'\n            },\n            children: \"\\uD83D\\uDE0A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u6682\\u65E0\\u63D0\\u9192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: reminders.map(reminder => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              borderBottom: '1px solid #F0F0F0',\n              cursor: 'pointer',\n              borderLeft: `3px solid ${reminder.color}`,\n              transition: 'background-color 0.2s ease'\n            },\n            onClick: () => handleReminderAction(reminder),\n            onMouseEnter: e => {\n              e.target.style.backgroundColor = '#F8F9FA';\n            },\n            onMouseLeave: e => {\n              e.target.style.backgroundColor = 'transparent';\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: reminder.color,\n                  fontSize: '16px',\n                  marginTop: '2px'\n                },\n                children: reminder.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 500,\n                    color: '#1A1A1A',\n                    marginBottom: '4px'\n                  },\n                  children: reminder.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '13px',\n                    color: '#666666',\n                    lineHeight: 1.4\n                  },\n                  children: reminder.message\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 21\n            }, this)\n          }, reminder.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this);\n  };\n  _s(ReminderCenter, \"OGsIWlGlwYpVUqIrDReJ1GWx7rw=\");\n  return /*#__PURE__*/_jsxDEV(ReminderCenter, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 349,\n    columnNumber: 10\n  }, this);\n};\n\n// 浮动提醒气泡\n_s2(SmartReminder, \"ccAVfrk6O0Q/FTWk94djw/De9oE=\");\n_c = SmartReminder;\nexport const FloatingReminder = ({\n  reminder,\n  onAction,\n  onDismiss,\n  position = 'bottom-right'\n}) => {\n  if (!reminder) return null;\n  const positionStyles = {\n    'bottom-right': {\n      position: 'fixed',\n      bottom: '24px',\n      right: '24px',\n      zIndex: 1001\n    },\n    'top-right': {\n      position: 'fixed',\n      top: '24px',\n      right: '24px',\n      zIndex: 1001\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      ...positionStyles[position],\n      background: 'white',\n      borderRadius: '12px',\n      padding: '16px',\n      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n      border: `2px solid ${reminder.color}`,\n      maxWidth: '300px',\n      animation: 'slideInUp 0.3s ease-out'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '12px',\n        marginBottom: '12px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: reminder.color,\n          fontSize: '20px'\n        },\n        children: reminder.icon\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 600,\n            color: '#1A1A1A',\n            marginBottom: '4px'\n          },\n          children: reminder.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#666666',\n            lineHeight: 1.4\n          },\n          children: reminder.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: () => onAction && onAction(reminder),\n        children: reminder.action\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        onClick: () => onDismiss && onDismiss(reminder),\n        children: \"\\u5FFD\\u7565\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 377,\n    columnNumber: 5\n  }, this);\n};\n_c2 = FloatingReminder;\nexport default SmartReminder;\nvar _c, _c2;\n$RefreshReg$(_c, \"SmartReminder\");\n$RefreshReg$(_c2, \"FloatingReminder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "notification", "<PERSON><PERSON>", "Space", "Typography", "Badge", "BellOutlined", "ClockCircleOutlined", "FireOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "TrophyOutlined", "moment", "jsxDEV", "_jsxDEV", "Text", "SmartReminder", "assignments", "user", "onNavigate", "enableNotifications", "className", "props", "_s2", "_s", "$RefreshSig$", "reminders", "set<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setLastCheck", "checkReminders", "now", "newReminders", "for<PERSON>ach", "assignment", "due_date", "submission_status", "dueDate", "isOverdue", "isBefore", "hoursUntilDue", "diff", "daysUntilDue", "push", "id", "type", "priority", "title", "message", "Math", "abs", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "action", "grading_status", "score", "gradedAt", "graded_at", "hoursSinceGraded", "completedToday", "filter", "a", "submitted_at", "isSame", "length", "showNotification", "reminder", "notificationConfig", "description", "children", "style", "marginTop", "size", "onClick", "handleReminderAction", "destroy", "duration", "key", "borderLeft", "error", "warning", "success", "info", "state", "assignmentId", "assignmentTitle", "homework_id", "interval", "setInterval", "clearInterval", "existingNotification", "document", "querySelector", "getReminderStats", "stats", "total", "high", "r", "medium", "low", "ReminderCenter", "visible", "setVisible", "count", "offset", "fontSize", "position", "top", "right", "width", "background", "borderRadius", "boxShadow", "border", "zIndex", "maxHeight", "overflow", "padding", "borderBottom", "fontWeight", "textAlign", "marginBottom", "map", "cursor", "transition", "onMouseEnter", "e", "target", "backgroundColor", "onMouseLeave", "display", "alignItems", "gap", "flex", "lineHeight", "_c", "FloatingReminder", "onAction", "on<PERSON><PERSON><PERSON>", "positionStyles", "bottom", "max<PERSON><PERSON><PERSON>", "animation", "_c2", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/student/SmartReminder.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { notification, Button, Space, Typography, Badge } from 'antd';\nimport { \n  BellOutlined, \n  ClockCircleOutlined, \n  FireOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport moment from 'moment';\nimport '../../styles/student.css';\n\nconst { Text } = Typography;\n\nconst SmartReminder = ({ \n  assignments = [],\n  user = {},\n  onNavigate,\n  enableNotifications = true,\n  className = '',\n  ...props \n}) => {\n  const [reminders, setReminders] = useState([]);\n  const [lastCheck, setLastCheck] = useState(null);\n\n  // 检查提醒条件\n  const checkReminders = () => {\n    const now = moment();\n    const newReminders = [];\n\n    assignments.forEach(assignment => {\n      // 逾期提醒\n      if (assignment.due_date && assignment.submission_status === '未提交') {\n        const dueDate = moment(assignment.due_date);\n        const isOverdue = dueDate.isBefore(now);\n        const hoursUntilDue = dueDate.diff(now, 'hours');\n        const daysUntilDue = dueDate.diff(now, 'days');\n\n        if (isOverdue) {\n          newReminders.push({\n            id: `overdue-${assignment.id}`,\n            type: 'overdue',\n            priority: 'high',\n            title: '作业已逾期',\n            message: `《${assignment.title}》已逾期 ${Math.abs(daysUntilDue)} 天`,\n            assignment,\n            icon: <ExclamationCircleOutlined />,\n            color: '#FF3B30',\n            action: '立即提交'\n          });\n        } else if (hoursUntilDue <= 2) {\n          newReminders.push({\n            id: `urgent-${assignment.id}`,\n            type: 'urgent',\n            priority: 'high',\n            title: '作业即将截止',\n            message: `《${assignment.title}》还有 ${hoursUntilDue} 小时截止`,\n            assignment,\n            icon: <FireOutlined />,\n            color: '#FF9500',\n            action: '快速提交'\n          });\n        } else if (daysUntilDue <= 1) {\n          newReminders.push({\n            id: `warning-${assignment.id}`,\n            type: 'warning',\n            priority: 'medium',\n            title: '作业明天截止',\n            message: `《${assignment.title}》明天截止，记得及时提交`,\n            assignment,\n            icon: <ClockCircleOutlined />,\n            color: '#FF9500',\n            action: '查看详情'\n          });\n        }\n      }\n\n      // 批改完成提醒\n      if (assignment.grading_status === '已批改' && assignment.score !== null) {\n        const gradedAt = moment(assignment.graded_at);\n        const hoursSinceGraded = now.diff(gradedAt, 'hours');\n        \n        if (hoursSinceGraded <= 24) { // 24小时内批改完成的\n          newReminders.push({\n            id: `graded-${assignment.id}`,\n            type: 'graded',\n            priority: 'medium',\n            title: '作业已批改',\n            message: `《${assignment.title}》已批改完成，得分 ${assignment.score} 分`,\n            assignment,\n            icon: <TrophyOutlined />,\n            color: assignment.score >= 80 ? '#34C759' : '#FF9500',\n            action: '查看详情'\n          });\n        }\n      }\n    });\n\n    // 学习激励提醒\n    const completedToday = assignments.filter(a => {\n      return a.submission_status === '已提交' && \n             moment(a.submitted_at).isSame(now, 'day');\n    }).length;\n\n    if (completedToday >= 3) {\n      newReminders.push({\n        id: 'achievement-daily',\n        type: 'achievement',\n        priority: 'low',\n        title: '今日学习目标达成',\n        message: `太棒了！你今天已经完成了 ${completedToday} 份作业`,\n        icon: <CheckCircleOutlined />,\n        color: '#34C759',\n        action: '查看成就'\n      });\n    }\n\n    setReminders(newReminders);\n    setLastCheck(now);\n  };\n\n  // 显示通知\n  const showNotification = (reminder) => {\n    if (!enableNotifications) return;\n\n    const notificationConfig = {\n      message: reminder.title,\n      description: (\n        <div>\n          <Text>{reminder.message}</Text>\n          <div style={{ marginTop: '12px' }}>\n            <Space>\n              <Button \n                type=\"primary\" \n                size=\"small\"\n                onClick={() => handleReminderAction(reminder)}\n              >\n                {reminder.action}\n              </Button>\n              <Button \n                size=\"small\"\n                onClick={() => notification.destroy(reminder.id)}\n              >\n                忽略\n              </Button>\n            </Space>\n          </div>\n        </div>\n      ),\n      icon: reminder.icon,\n      duration: reminder.priority === 'high' ? 0 : 10, // 高优先级不自动关闭\n      key: reminder.id,\n      style: {\n        borderLeft: `4px solid ${reminder.color}`,\n      },\n      onClick: () => handleReminderAction(reminder)\n    };\n\n    // 根据优先级选择通知类型\n    switch (reminder.priority) {\n      case 'high':\n        notification.error(notificationConfig);\n        break;\n      case 'medium':\n        notification.warning(notificationConfig);\n        break;\n      case 'low':\n        notification.success(notificationConfig);\n        break;\n      default:\n        notification.info(notificationConfig);\n    }\n  };\n\n  // 处理提醒操作\n  const handleReminderAction = (reminder) => {\n    notification.destroy(reminder.id);\n    \n    if (onNavigate) {\n      switch (reminder.type) {\n        case 'overdue':\n        case 'urgent':\n        case 'warning':\n          onNavigate('/homework/upload/single', {\n            state: {\n              assignmentId: reminder.assignment.id,\n              assignmentTitle: reminder.assignment.title\n            }\n          });\n          break;\n        case 'graded':\n          onNavigate(`/homework/${reminder.assignment.homework_id}`);\n          break;\n        case 'achievement':\n          onNavigate('/profile/statistics');\n          break;\n        default:\n          onNavigate('/homework');\n      }\n    }\n  };\n\n  // 定期检查提醒\n  useEffect(() => {\n    checkReminders();\n    \n    // 每5分钟检查一次\n    const interval = setInterval(checkReminders, 5 * 60 * 1000);\n    \n    return () => clearInterval(interval);\n  }, [assignments]);\n\n  // 显示新的提醒\n  useEffect(() => {\n    if (lastCheck && reminders.length > 0) {\n      reminders.forEach(reminder => {\n        // 避免重复显示相同的提醒\n        const existingNotification = document.querySelector(`[data-testid=\"${reminder.id}\"]`);\n        if (!existingNotification) {\n          showNotification(reminder);\n        }\n      });\n    }\n  }, [reminders, lastCheck]);\n\n  // 获取提醒统计\n  const getReminderStats = () => {\n    const stats = {\n      total: reminders.length,\n      high: reminders.filter(r => r.priority === 'high').length,\n      medium: reminders.filter(r => r.priority === 'medium').length,\n      low: reminders.filter(r => r.priority === 'low').length\n    };\n    return stats;\n  };\n\n  const stats = getReminderStats();\n\n  // 提醒中心组件\n  const ReminderCenter = () => {\n    const [visible, setVisible] = useState(false);\n\n    return (\n      <div className={`student-reminder-center ${className}`} {...props}>\n        <Badge count={stats.high} offset={[0, 0]}>\n          <Button\n            type=\"text\"\n            icon={<BellOutlined />}\n            onClick={() => setVisible(!visible)}\n            style={{\n              color: stats.high > 0 ? '#FF3B30' : '#666666',\n              fontSize: '18px'\n            }}\n          />\n        </Badge>\n\n        {visible && (\n          <div style={{\n            position: 'absolute',\n            top: '100%',\n            right: 0,\n            width: '320px',\n            background: 'white',\n            borderRadius: '12px',\n            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)',\n            border: '1px solid #E8E8E8',\n            zIndex: 1000,\n            maxHeight: '400px',\n            overflow: 'auto'\n          }}>\n            <div style={{\n              padding: '16px',\n              borderBottom: '1px solid #F0F0F0',\n              fontWeight: 600,\n              color: '#1A1A1A'\n            }}>\n              🔔 智能提醒 ({stats.total})\n            </div>\n\n            {reminders.length === 0 ? (\n              <div style={{\n                padding: '40px 20px',\n                textAlign: 'center',\n                color: '#666666'\n              }}>\n                <div style={{ fontSize: '32px', marginBottom: '8px' }}>😊</div>\n                <div>暂无提醒</div>\n              </div>\n            ) : (\n              <div>\n                {reminders.map(reminder => (\n                  <div\n                    key={reminder.id}\n                    style={{\n                      padding: '12px 16px',\n                      borderBottom: '1px solid #F0F0F0',\n                      cursor: 'pointer',\n                      borderLeft: `3px solid ${reminder.color}`,\n                      transition: 'background-color 0.2s ease'\n                    }}\n                    onClick={() => handleReminderAction(reminder)}\n                    onMouseEnter={(e) => {\n                      e.target.style.backgroundColor = '#F8F9FA';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.backgroundColor = 'transparent';\n                    }}\n                  >\n                    <div style={{\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '8px'\n                    }}>\n                      <span style={{ \n                        color: reminder.color, \n                        fontSize: '16px',\n                        marginTop: '2px'\n                      }}>\n                        {reminder.icon}\n                      </span>\n                      <div style={{ flex: 1 }}>\n                        <div style={{\n                          fontWeight: 500,\n                          color: '#1A1A1A',\n                          marginBottom: '4px'\n                        }}>\n                          {reminder.title}\n                        </div>\n                        <div style={{\n                          fontSize: '13px',\n                          color: '#666666',\n                          lineHeight: 1.4\n                        }}>\n                          {reminder.message}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return <ReminderCenter />;\n};\n\n// 浮动提醒气泡\nexport const FloatingReminder = ({ \n  reminder, \n  onAction, \n  onDismiss,\n  position = 'bottom-right' \n}) => {\n  if (!reminder) return null;\n\n  const positionStyles = {\n    'bottom-right': {\n      position: 'fixed',\n      bottom: '24px',\n      right: '24px',\n      zIndex: 1001\n    },\n    'top-right': {\n      position: 'fixed',\n      top: '24px',\n      right: '24px',\n      zIndex: 1001\n    }\n  };\n\n  return (\n    <div\n      style={{\n        ...positionStyles[position],\n        background: 'white',\n        borderRadius: '12px',\n        padding: '16px',\n        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n        border: `2px solid ${reminder.color}`,\n        maxWidth: '300px',\n        animation: 'slideInUp 0.3s ease-out'\n      }}\n    >\n      <div style={{\n        display: 'flex',\n        alignItems: 'flex-start',\n        gap: '12px',\n        marginBottom: '12px'\n      }}>\n        <span style={{ \n          color: reminder.color, \n          fontSize: '20px' \n        }}>\n          {reminder.icon}\n        </span>\n        <div style={{ flex: 1 }}>\n          <div style={{\n            fontWeight: 600,\n            color: '#1A1A1A',\n            marginBottom: '4px'\n          }}>\n            {reminder.title}\n          </div>\n          <div style={{\n            fontSize: '14px',\n            color: '#666666',\n            lineHeight: 1.4\n          }}>\n            {reminder.message}\n          </div>\n        </div>\n      </div>\n      \n      <Space>\n        <Button \n          type=\"primary\" \n          size=\"small\"\n          onClick={() => onAction && onAction(reminder)}\n        >\n          {reminder.action}\n        </Button>\n        <Button \n          size=\"small\"\n          onClick={() => onDismiss && onDismiss(reminder)}\n        >\n          忽略\n        </Button>\n      </Space>\n    </div>\n  );\n};\n\nexport default SmartReminder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,YAAY,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,QAAQ,MAAM;AACrE,SACEC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,yBAAyB,EACzBC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAK,CAAC,GAAGX,UAAU;AAE3B,MAAMY,aAAa,GAAGA,CAAC;EACrBC,WAAW,GAAG,EAAE;EAChBC,IAAI,GAAG,CAAC,CAAC;EACTC,UAAU;EACVC,mBAAmB,GAAG,IAAI;EAC1BC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM+B,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,GAAG,GAAGnB,MAAM,CAAC,CAAC;IACpB,MAAMoB,YAAY,GAAG,EAAE;IAEvBf,WAAW,CAACgB,OAAO,CAACC,UAAU,IAAI;MAChC;MACA,IAAIA,UAAU,CAACC,QAAQ,IAAID,UAAU,CAACE,iBAAiB,KAAK,KAAK,EAAE;QACjE,MAAMC,OAAO,GAAGzB,MAAM,CAACsB,UAAU,CAACC,QAAQ,CAAC;QAC3C,MAAMG,SAAS,GAAGD,OAAO,CAACE,QAAQ,CAACR,GAAG,CAAC;QACvC,MAAMS,aAAa,GAAGH,OAAO,CAACI,IAAI,CAACV,GAAG,EAAE,OAAO,CAAC;QAChD,MAAMW,YAAY,GAAGL,OAAO,CAACI,IAAI,CAACV,GAAG,EAAE,MAAM,CAAC;QAE9C,IAAIO,SAAS,EAAE;UACbN,YAAY,CAACW,IAAI,CAAC;YAChBC,EAAE,EAAE,WAAWV,UAAU,CAACU,EAAE,EAAE;YAC9BC,IAAI,EAAE,SAAS;YACfC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,IAAId,UAAU,CAACa,KAAK,QAAQE,IAAI,CAACC,GAAG,CAACR,YAAY,CAAC,IAAI;YAC/DR,UAAU;YACViB,IAAI,eAAErC,OAAA,CAACJ,yBAAyB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YACnCC,KAAK,EAAE,SAAS;YAChBC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIjB,aAAa,IAAI,CAAC,EAAE;UAC7BR,YAAY,CAACW,IAAI,CAAC;YAChBC,EAAE,EAAE,UAAUV,UAAU,CAACU,EAAE,EAAE;YAC7BC,IAAI,EAAE,QAAQ;YACdC,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,QAAQ;YACfC,OAAO,EAAE,IAAId,UAAU,CAACa,KAAK,OAAOP,aAAa,OAAO;YACxDN,UAAU;YACViB,IAAI,eAAErC,OAAA,CAACN,YAAY;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YACtBC,KAAK,EAAE,SAAS;YAChBC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIf,YAAY,IAAI,CAAC,EAAE;UAC5BV,YAAY,CAACW,IAAI,CAAC;YAChBC,EAAE,EAAE,WAAWV,UAAU,CAACU,EAAE,EAAE;YAC9BC,IAAI,EAAE,SAAS;YACfC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,QAAQ;YACfC,OAAO,EAAE,IAAId,UAAU,CAACa,KAAK,cAAc;YAC3Cb,UAAU;YACViB,IAAI,eAAErC,OAAA,CAACP,mBAAmB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YAC7BC,KAAK,EAAE,SAAS;YAChBC,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIvB,UAAU,CAACwB,cAAc,KAAK,KAAK,IAAIxB,UAAU,CAACyB,KAAK,KAAK,IAAI,EAAE;QACpE,MAAMC,QAAQ,GAAGhD,MAAM,CAACsB,UAAU,CAAC2B,SAAS,CAAC;QAC7C,MAAMC,gBAAgB,GAAG/B,GAAG,CAACU,IAAI,CAACmB,QAAQ,EAAE,OAAO,CAAC;QAEpD,IAAIE,gBAAgB,IAAI,EAAE,EAAE;UAAE;UAC5B9B,YAAY,CAACW,IAAI,CAAC;YAChBC,EAAE,EAAE,UAAUV,UAAU,CAACU,EAAE,EAAE;YAC7BC,IAAI,EAAE,QAAQ;YACdC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE,OAAO;YACdC,OAAO,EAAE,IAAId,UAAU,CAACa,KAAK,aAAab,UAAU,CAACyB,KAAK,IAAI;YAC9DzB,UAAU;YACViB,IAAI,eAAErC,OAAA,CAACH,cAAc;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;YACxBC,KAAK,EAAEtB,UAAU,CAACyB,KAAK,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;YACrDF,MAAM,EAAE;UACV,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;;IAEF;IACA,MAAMM,cAAc,GAAG9C,WAAW,CAAC+C,MAAM,CAACC,CAAC,IAAI;MAC7C,OAAOA,CAAC,CAAC7B,iBAAiB,KAAK,KAAK,IAC7BxB,MAAM,CAACqD,CAAC,CAACC,YAAY,CAAC,CAACC,MAAM,CAACpC,GAAG,EAAE,KAAK,CAAC;IAClD,CAAC,CAAC,CAACqC,MAAM;IAET,IAAIL,cAAc,IAAI,CAAC,EAAE;MACvB/B,YAAY,CAACW,IAAI,CAAC;QAChBC,EAAE,EAAE,mBAAmB;QACvBC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,UAAU;QACjBC,OAAO,EAAE,gBAAgBe,cAAc,MAAM;QAC7CZ,IAAI,eAAErC,OAAA,CAACL,mBAAmB;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC7BC,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IAEA9B,YAAY,CAACK,YAAY,CAAC;IAC1BH,YAAY,CAACE,GAAG,CAAC;EACnB,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,IAAI,CAAClD,mBAAmB,EAAE;IAE1B,MAAMmD,kBAAkB,GAAG;MACzBvB,OAAO,EAAEsB,QAAQ,CAACvB,KAAK;MACvByB,WAAW,eACT1D,OAAA;QAAA2D,QAAA,gBACE3D,OAAA,CAACC,IAAI;UAAA0D,QAAA,EAAEH,QAAQ,CAACtB;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/BzC,OAAA;UAAK4D,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAF,QAAA,eAChC3D,OAAA,CAACX,KAAK;YAAAsE,QAAA,gBACJ3D,OAAA,CAACZ,MAAM;cACL2C,IAAI,EAAC,SAAS;cACd+B,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAACR,QAAQ,CAAE;cAAAG,QAAA,EAE7CH,QAAQ,CAACb;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACTzC,OAAA,CAACZ,MAAM;cACL0E,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAM5E,YAAY,CAAC8E,OAAO,CAACT,QAAQ,CAAC1B,EAAE,CAAE;cAAA6B,QAAA,EAClD;YAED;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MACDJ,IAAI,EAAEmB,QAAQ,CAACnB,IAAI;MACnB6B,QAAQ,EAAEV,QAAQ,CAACxB,QAAQ,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE;MAAE;MACjDmC,GAAG,EAAEX,QAAQ,CAAC1B,EAAE;MAChB8B,KAAK,EAAE;QACLQ,UAAU,EAAE,aAAaZ,QAAQ,CAACd,KAAK;MACzC,CAAC;MACDqB,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAACR,QAAQ;IAC9C,CAAC;;IAED;IACA,QAAQA,QAAQ,CAACxB,QAAQ;MACvB,KAAK,MAAM;QACT7C,YAAY,CAACkF,KAAK,CAACZ,kBAAkB,CAAC;QACtC;MACF,KAAK,QAAQ;QACXtE,YAAY,CAACmF,OAAO,CAACb,kBAAkB,CAAC;QACxC;MACF,KAAK,KAAK;QACRtE,YAAY,CAACoF,OAAO,CAACd,kBAAkB,CAAC;QACxC;MACF;QACEtE,YAAY,CAACqF,IAAI,CAACf,kBAAkB,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMO,oBAAoB,GAAIR,QAAQ,IAAK;IACzCrE,YAAY,CAAC8E,OAAO,CAACT,QAAQ,CAAC1B,EAAE,CAAC;IAEjC,IAAIzB,UAAU,EAAE;MACd,QAAQmD,QAAQ,CAACzB,IAAI;QACnB,KAAK,SAAS;QACd,KAAK,QAAQ;QACb,KAAK,SAAS;UACZ1B,UAAU,CAAC,yBAAyB,EAAE;YACpCoE,KAAK,EAAE;cACLC,YAAY,EAAElB,QAAQ,CAACpC,UAAU,CAACU,EAAE;cACpC6C,eAAe,EAAEnB,QAAQ,CAACpC,UAAU,CAACa;YACvC;UACF,CAAC,CAAC;UACF;QACF,KAAK,QAAQ;UACX5B,UAAU,CAAC,aAAamD,QAAQ,CAACpC,UAAU,CAACwD,WAAW,EAAE,CAAC;UAC1D;QACF,KAAK,aAAa;UAChBvE,UAAU,CAAC,qBAAqB,CAAC;UACjC;QACF;UACEA,UAAU,CAAC,WAAW,CAAC;MAC3B;IACF;EACF,CAAC;;EAED;EACAnB,SAAS,CAAC,MAAM;IACd8B,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAM6D,QAAQ,GAAGC,WAAW,CAAC9D,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAE3D,OAAO,MAAM+D,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC1E,WAAW,CAAC,CAAC;;EAEjB;EACAjB,SAAS,CAAC,MAAM;IACd,IAAI4B,SAAS,IAAIF,SAAS,CAAC0C,MAAM,GAAG,CAAC,EAAE;MACrC1C,SAAS,CAACO,OAAO,CAACqC,QAAQ,IAAI;QAC5B;QACA,MAAMwB,oBAAoB,GAAGC,QAAQ,CAACC,aAAa,CAAC,iBAAiB1B,QAAQ,CAAC1B,EAAE,IAAI,CAAC;QACrF,IAAI,CAACkD,oBAAoB,EAAE;UACzBzB,gBAAgB,CAACC,QAAQ,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5C,SAAS,EAAEE,SAAS,CAAC,CAAC;;EAE1B;EACA,MAAMqE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,KAAK,GAAG;MACZC,KAAK,EAAEzE,SAAS,CAAC0C,MAAM;MACvBgC,IAAI,EAAE1E,SAAS,CAACsC,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAACvD,QAAQ,KAAK,MAAM,CAAC,CAACsB,MAAM;MACzDkC,MAAM,EAAE5E,SAAS,CAACsC,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAACvD,QAAQ,KAAK,QAAQ,CAAC,CAACsB,MAAM;MAC7DmC,GAAG,EAAE7E,SAAS,CAACsC,MAAM,CAACqC,CAAC,IAAIA,CAAC,CAACvD,QAAQ,KAAK,KAAK,CAAC,CAACsB;IACnD,CAAC;IACD,OAAO8B,KAAK;EACd,CAAC;EAED,MAAMA,KAAK,GAAGD,gBAAgB,CAAC,CAAC;;EAEhC;EACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAAAhF,EAAA;IAC3B,MAAM,CAACiF,OAAO,EAAEC,UAAU,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;IAE7C,oBACEe,OAAA;MAAKO,SAAS,EAAE,2BAA2BA,SAAS,EAAG;MAAA,GAAKC,KAAK;MAAAmD,QAAA,gBAC/D3D,OAAA,CAACT,KAAK;QAACsG,KAAK,EAAET,KAAK,CAACE,IAAK;QAACQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;QAAAnC,QAAA,eACvC3D,OAAA,CAACZ,MAAM;UACL2C,IAAI,EAAC,MAAM;UACXM,IAAI,eAAErC,OAAA,CAACR,YAAY;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBsB,OAAO,EAAEA,CAAA,KAAM6B,UAAU,CAAC,CAACD,OAAO,CAAE;UACpC/B,KAAK,EAAE;YACLlB,KAAK,EAAE0C,KAAK,CAACE,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;YAC7CS,QAAQ,EAAE;UACZ;QAAE;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,EAEPkD,OAAO,iBACN3F,OAAA;QAAK4D,KAAK,EAAE;UACVoC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,MAAM;UACXC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,OAAO;UACnBC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,gCAAgC;UAC3CC,MAAM,EAAE,mBAAmB;UAC3BC,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE,OAAO;UAClBC,QAAQ,EAAE;QACZ,CAAE;QAAA/C,QAAA,gBACA3D,OAAA;UAAK4D,KAAK,EAAE;YACV+C,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE,mBAAmB;YACjCC,UAAU,EAAE,GAAG;YACfnE,KAAK,EAAE;UACT,CAAE;UAAAiB,QAAA,GAAC,yCACQ,EAACyB,KAAK,CAACC,KAAK,EAAC,GACxB;QAAA;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAEL7B,SAAS,CAAC0C,MAAM,KAAK,CAAC,gBACrBtD,OAAA;UAAK4D,KAAK,EAAE;YACV+C,OAAO,EAAE,WAAW;YACpBG,SAAS,EAAE,QAAQ;YACnBpE,KAAK,EAAE;UACT,CAAE;UAAAiB,QAAA,gBACA3D,OAAA;YAAK4D,KAAK,EAAE;cAAEmC,QAAQ,EAAE,MAAM;cAAEgB,YAAY,EAAE;YAAM,CAAE;YAAApD,QAAA,EAAC;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/DzC,OAAA;YAAA2D,QAAA,EAAK;UAAI;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,gBAENzC,OAAA;UAAA2D,QAAA,EACG/C,SAAS,CAACoG,GAAG,CAACxD,QAAQ,iBACrBxD,OAAA;YAEE4D,KAAK,EAAE;cACL+C,OAAO,EAAE,WAAW;cACpBC,YAAY,EAAE,mBAAmB;cACjCK,MAAM,EAAE,SAAS;cACjB7C,UAAU,EAAE,aAAaZ,QAAQ,CAACd,KAAK,EAAE;cACzCwE,UAAU,EAAE;YACd,CAAE;YACFnD,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAACR,QAAQ,CAAE;YAC9C2D,YAAY,EAAGC,CAAC,IAAK;cACnBA,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAC0D,eAAe,GAAG,SAAS;YAC5C,CAAE;YACFC,YAAY,EAAGH,CAAC,IAAK;cACnBA,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAC0D,eAAe,GAAG,aAAa;YAChD,CAAE;YAAA3D,QAAA,eAEF3D,OAAA;cAAK4D,KAAK,EAAE;gBACV4D,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,YAAY;gBACxBC,GAAG,EAAE;cACP,CAAE;cAAA/D,QAAA,gBACA3D,OAAA;gBAAM4D,KAAK,EAAE;kBACXlB,KAAK,EAAEc,QAAQ,CAACd,KAAK;kBACrBqD,QAAQ,EAAE,MAAM;kBAChBlC,SAAS,EAAE;gBACb,CAAE;gBAAAF,QAAA,EACCH,QAAQ,CAACnB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACPzC,OAAA;gBAAK4D,KAAK,EAAE;kBAAE+D,IAAI,EAAE;gBAAE,CAAE;gBAAAhE,QAAA,gBACtB3D,OAAA;kBAAK4D,KAAK,EAAE;oBACViD,UAAU,EAAE,GAAG;oBACfnE,KAAK,EAAE,SAAS;oBAChBqE,YAAY,EAAE;kBAChB,CAAE;kBAAApD,QAAA,EACCH,QAAQ,CAACvB;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACNzC,OAAA;kBAAK4D,KAAK,EAAE;oBACVmC,QAAQ,EAAE,MAAM;oBAChBrD,KAAK,EAAE,SAAS;oBAChBkF,UAAU,EAAE;kBACd,CAAE;kBAAAjE,QAAA,EACCH,QAAQ,CAACtB;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA5CDe,QAAQ,CAAC1B,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Cb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAAC/B,EAAA,CA1GIgF,cAAc;EA4GpB,oBAAO1F,OAAA,CAAC0F,cAAc;IAAApD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3B,CAAC;;AAED;AAAAhC,GAAA,CAhVMP,aAAa;AAAA2H,EAAA,GAAb3H,aAAa;AAiVnB,OAAO,MAAM4H,gBAAgB,GAAGA,CAAC;EAC/BtE,QAAQ;EACRuE,QAAQ;EACRC,SAAS;EACThC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,IAAI,CAACxC,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMyE,cAAc,GAAG;IACrB,cAAc,EAAE;MACdjC,QAAQ,EAAE,OAAO;MACjBkC,MAAM,EAAE,MAAM;MACdhC,KAAK,EAAE,MAAM;MACbM,MAAM,EAAE;IACV,CAAC;IACD,WAAW,EAAE;MACXR,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,MAAM;MACXC,KAAK,EAAE,MAAM;MACbM,MAAM,EAAE;IACV;EACF,CAAC;EAED,oBACExG,OAAA;IACE4D,KAAK,EAAE;MACL,GAAGqE,cAAc,CAACjC,QAAQ,CAAC;MAC3BI,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBM,OAAO,EAAE,MAAM;MACfL,SAAS,EAAE,gCAAgC;MAC3CC,MAAM,EAAE,aAAa/C,QAAQ,CAACd,KAAK,EAAE;MACrCyF,QAAQ,EAAE,OAAO;MACjBC,SAAS,EAAE;IACb,CAAE;IAAAzE,QAAA,gBAEF3D,OAAA;MAAK4D,KAAK,EAAE;QACV4D,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,YAAY;QACxBC,GAAG,EAAE,MAAM;QACXX,YAAY,EAAE;MAChB,CAAE;MAAApD,QAAA,gBACA3D,OAAA;QAAM4D,KAAK,EAAE;UACXlB,KAAK,EAAEc,QAAQ,CAACd,KAAK;UACrBqD,QAAQ,EAAE;QACZ,CAAE;QAAApC,QAAA,EACCH,QAAQ,CAACnB;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACPzC,OAAA;QAAK4D,KAAK,EAAE;UAAE+D,IAAI,EAAE;QAAE,CAAE;QAAAhE,QAAA,gBACtB3D,OAAA;UAAK4D,KAAK,EAAE;YACViD,UAAU,EAAE,GAAG;YACfnE,KAAK,EAAE,SAAS;YAChBqE,YAAY,EAAE;UAChB,CAAE;UAAApD,QAAA,EACCH,QAAQ,CAACvB;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACNzC,OAAA;UAAK4D,KAAK,EAAE;YACVmC,QAAQ,EAAE,MAAM;YAChBrD,KAAK,EAAE,SAAS;YAChBkF,UAAU,EAAE;UACd,CAAE;UAAAjE,QAAA,EACCH,QAAQ,CAACtB;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzC,OAAA,CAACX,KAAK;MAAAsE,QAAA,gBACJ3D,OAAA,CAACZ,MAAM;QACL2C,IAAI,EAAC,SAAS;QACd+B,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEA,CAAA,KAAMgE,QAAQ,IAAIA,QAAQ,CAACvE,QAAQ,CAAE;QAAAG,QAAA,EAE7CH,QAAQ,CAACb;MAAM;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACTzC,OAAA,CAACZ,MAAM;QACL0E,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEA,CAAA,KAAMiE,SAAS,IAAIA,SAAS,CAACxE,QAAQ,CAAE;QAAAG,QAAA,EACjD;MAED;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC4F,GAAA,GAnFWP,gBAAgB;AAqF7B,eAAe5H,aAAa;AAAC,IAAA2H,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}