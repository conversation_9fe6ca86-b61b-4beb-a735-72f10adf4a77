{"ast": null, "code": "import toArray from \"rc-util/es/Children/toArray\";\nfunction useItems(items, children) {\n  if (items && Array.isArray(items)) {\n    return items;\n  }\n  return toArray(children).map(ele => {\n    var _a, _b;\n    return Object.assign({\n      children: (_b = (_a = ele === null || ele === void 0 ? void 0 : ele.props) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : ''\n    }, ele.props);\n  });\n}\nexport default useItems;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}