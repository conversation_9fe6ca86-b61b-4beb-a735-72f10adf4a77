{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport cls from 'classnames';\nimport RcTreeSelect, { SHOW_ALL, SHOW_CHILD, SHOW_PARENT, TreeNode } from 'rc-tree-select';\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport genPurePanel from '../_util/PurePanel';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport { FormItemInputContext } from '../form/context';\nimport useVariant from '../form/hooks/useVariants';\nimport mergedBuiltinPlacements from '../select/mergedBuiltinPlacements';\nimport useSelectStyle from '../select/style';\nimport useIcons from '../select/useIcons';\nimport useShowArrow from '../select/useShowArrow';\nimport { useCompactItemContext } from '../space/Compact';\nimport { useToken } from '../theme/internal';\nimport SwitcherIconCom from '../tree/utils/iconUtil';\nimport useStyle from './style';\nimport { useComponentConfig } from '../config-provider/context';\nconst InternalTreeSelect = (props, ref) => {\n  var _a, _b, _c, _d, _e;\n  const {\n      prefixCls: customizePrefixCls,\n      size: customizeSize,\n      disabled: customDisabled,\n      bordered = true,\n      style,\n      className,\n      rootClassName,\n      treeCheckable,\n      multiple,\n      listHeight = 256,\n      listItemHeight: customListItemHeight,\n      placement,\n      notFoundContent,\n      switcherIcon,\n      treeLine,\n      getPopupContainer,\n      popupClassName,\n      dropdownClassName,\n      treeIcon = false,\n      transitionName,\n      choiceTransitionName = '',\n      status: customStatus,\n      treeExpandAction,\n      builtinPlacements,\n      dropdownMatchSelectWidth,\n      popupMatchSelectWidth,\n      allowClear,\n      variant: customVariant,\n      dropdownStyle,\n      dropdownRender,\n      popupRender,\n      onDropdownVisibleChange,\n      onOpenChange,\n      tagRender,\n      maxCount,\n      showCheckedStrategy,\n      treeCheckStrictly,\n      styles,\n      classNames\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"size\", \"disabled\", \"bordered\", \"style\", \"className\", \"rootClassName\", \"treeCheckable\", \"multiple\", \"listHeight\", \"listItemHeight\", \"placement\", \"notFoundContent\", \"switcherIcon\", \"treeLine\", \"getPopupContainer\", \"popupClassName\", \"dropdownClassName\", \"treeIcon\", \"transitionName\", \"choiceTransitionName\", \"status\", \"treeExpandAction\", \"builtinPlacements\", \"dropdownMatchSelectWidth\", \"popupMatchSelectWidth\", \"allowClear\", \"variant\", \"dropdownStyle\", \"dropdownRender\", \"popupRender\", \"onDropdownVisibleChange\", \"onOpenChange\", \"tagRender\", \"maxCount\", \"showCheckedStrategy\", \"treeCheckStrictly\", \"styles\", \"classNames\"]);\n  const {\n    getPopupContainer: getContextPopupContainer,\n    getPrefixCls,\n    renderEmpty,\n    direction,\n    virtual,\n    popupMatchSelectWidth: contextPopupMatchSelectWidth,\n    popupOverflow\n  } = React.useContext(ConfigContext);\n  const {\n    styles: contextStyles,\n    classNames: contextClassNames\n  } = useComponentConfig('treeSelect');\n  const [, token] = useToken();\n  const listItemHeight = customListItemHeight !== null && customListItemHeight !== void 0 ? customListItemHeight : (token === null || token === void 0 ? void 0 : token.controlHeightSM) + (token === null || token === void 0 ? void 0 : token.paddingXXS);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('TreeSelect');\n    const deprecatedProps = {\n      dropdownMatchSelectWidth: 'popupMatchSelectWidth',\n      dropdownStyle: 'styles.popup.root',\n      dropdownClassName: 'classNames.popup.root',\n      popupClassName: 'classNames.popup.root',\n      dropdownRender: 'popupRender',\n      onDropdownVisibleChange: 'onOpenChange',\n      bordered: 'variant'\n    };\n    Object.entries(deprecatedProps).forEach(([oldProp, newProp]) => {\n      warning.deprecated(!(oldProp in props), oldProp, newProp);\n    });\n    process.env.NODE_ENV !== \"production\" ? warning(multiple !== false || !treeCheckable, 'usage', '`multiple` will always be `true` when `treeCheckable` is true') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!('showArrow' in props), 'deprecated', '`showArrow` is deprecated which will be removed in next major version. It will be a default behavior, you can hide it by setting `suffixIcon` to null.') : void 0;\n  }\n  const rootPrefixCls = getPrefixCls();\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  const treePrefixCls = getPrefixCls('select-tree', customizePrefixCls);\n  const treeSelectPrefixCls = getPrefixCls('tree-select', customizePrefixCls);\n  const {\n    compactSize,\n    compactItemClassnames\n  } = useCompactItemContext(prefixCls, direction);\n  const rootCls = useCSSVarCls(prefixCls);\n  const treeSelectRootCls = useCSSVarCls(treeSelectPrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useSelectStyle(prefixCls, rootCls);\n  const [treeSelectWrapCSSVar] = useStyle(treeSelectPrefixCls, treePrefixCls, treeSelectRootCls);\n  const [variant, enableVariantCls] = useVariant('treeSelect', customVariant, bordered);\n  const mergedPopupClassName = cls(((_a = classNames === null || classNames === void 0 ? void 0 : classNames.popup) === null || _a === void 0 ? void 0 : _a.root) || ((_b = contextClassNames === null || contextClassNames === void 0 ? void 0 : contextClassNames.popup) === null || _b === void 0 ? void 0 : _b.root) || popupClassName || dropdownClassName, `${treeSelectPrefixCls}-dropdown`, {\n    [`${treeSelectPrefixCls}-dropdown-rtl`]: direction === 'rtl'\n  }, rootClassName, contextClassNames.root, classNames === null || classNames === void 0 ? void 0 : classNames.root, cssVarCls, rootCls, treeSelectRootCls, hashId);\n  const mergedPopupStyle = ((_c = styles === null || styles === void 0 ? void 0 : styles.popup) === null || _c === void 0 ? void 0 : _c.root) || ((_d = contextStyles === null || contextStyles === void 0 ? void 0 : contextStyles.popup) === null || _d === void 0 ? void 0 : _d.root) || dropdownStyle;\n  const mergedPopupRender = popupRender || dropdownRender;\n  const mergedOnOpenChange = onOpenChange || onDropdownVisibleChange;\n  const isMultiple = !!(treeCheckable || multiple);\n  const mergedMaxCount = React.useMemo(() => {\n    if (maxCount && (showCheckedStrategy === 'SHOW_ALL' && !treeCheckStrictly || showCheckedStrategy === 'SHOW_PARENT')) {\n      return undefined;\n    }\n    return maxCount;\n  }, [maxCount, showCheckedStrategy, treeCheckStrictly]);\n  const showSuffixIcon = useShowArrow(props.suffixIcon, props.showArrow);\n  const mergedPopupMatchSelectWidth = (_e = popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth) !== null && _e !== void 0 ? _e : contextPopupMatchSelectWidth;\n  // ===================== Form =====================\n  const {\n    status: contextStatus,\n    hasFeedback,\n    isFormItemInput,\n    feedbackIcon\n  } = React.useContext(FormItemInputContext);\n  const mergedStatus = getMergedStatus(contextStatus, customStatus);\n  // ===================== Icons =====================\n  const {\n    suffixIcon,\n    removeIcon,\n    clearIcon\n  } = useIcons(Object.assign(Object.assign({}, restProps), {\n    multiple: isMultiple,\n    showSuffixIcon,\n    hasFeedback,\n    feedbackIcon,\n    prefixCls,\n    componentName: 'TreeSelect'\n  }));\n  const mergedAllowClear = allowClear === true ? {\n    clearIcon\n  } : allowClear;\n  // ===================== Empty =====================\n  let mergedNotFound;\n  if (notFoundContent !== undefined) {\n    mergedNotFound = notFoundContent;\n  } else {\n    mergedNotFound = (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Select')) || /*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n      componentName: \"Select\"\n    });\n  }\n  // ==================== Render =====================\n  const selectProps = omit(restProps, ['suffixIcon', 'removeIcon', 'clearIcon', 'itemIcon', 'switcherIcon', 'style']);\n  // ===================== Placement =====================\n  const memoizedPlacement = React.useMemo(() => {\n    if (placement !== undefined) {\n      return placement;\n    }\n    return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';\n  }, [placement, direction]);\n  const mergedSize = useSize(ctx => {\n    var _a;\n    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : compactSize) !== null && _a !== void 0 ? _a : ctx;\n  });\n  // ===================== Disabled =====================\n  const disabled = React.useContext(DisabledContext);\n  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  const mergedClassName = cls(!customizePrefixCls && treeSelectPrefixCls, {\n    [`${prefixCls}-lg`]: mergedSize === 'large',\n    [`${prefixCls}-sm`]: mergedSize === 'small',\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-${variant}`]: enableVariantCls,\n    [`${prefixCls}-in-form-item`]: isFormItemInput\n  }, getStatusClassNames(prefixCls, mergedStatus, hasFeedback), compactItemClassnames, className, rootClassName, contextClassNames.root, classNames === null || classNames === void 0 ? void 0 : classNames.root, cssVarCls, rootCls, treeSelectRootCls, hashId);\n  const renderSwitcherIcon = nodeProps => (/*#__PURE__*/React.createElement(SwitcherIconCom, {\n    prefixCls: treePrefixCls,\n    switcherIcon: switcherIcon,\n    treeNodeProps: nodeProps,\n    showLine: treeLine\n  }));\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('SelectLike', mergedPopupStyle === null || mergedPopupStyle === void 0 ? void 0 : mergedPopupStyle.zIndex);\n  const returnNode = /*#__PURE__*/React.createElement(RcTreeSelect, Object.assign({\n    virtual: virtual,\n    disabled: mergedDisabled\n  }, selectProps, {\n    dropdownMatchSelectWidth: mergedPopupMatchSelectWidth,\n    builtinPlacements: mergedBuiltinPlacements(builtinPlacements, popupOverflow),\n    ref: ref,\n    prefixCls: prefixCls,\n    className: mergedClassName,\n    style: Object.assign(Object.assign({}, styles === null || styles === void 0 ? void 0 : styles.root), style),\n    listHeight: listHeight,\n    listItemHeight: listItemHeight,\n    treeCheckable: treeCheckable ? /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-tree-checkbox-inner`\n    }) : treeCheckable,\n    treeLine: !!treeLine,\n    suffixIcon: suffixIcon,\n    multiple: isMultiple,\n    placement: memoizedPlacement,\n    removeIcon: removeIcon,\n    allowClear: mergedAllowClear,\n    switcherIcon: renderSwitcherIcon,\n    showTreeIcon: treeIcon,\n    notFoundContent: mergedNotFound,\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    treeMotion: null,\n    dropdownClassName: mergedPopupClassName,\n    dropdownStyle: Object.assign(Object.assign({}, mergedPopupStyle), {\n      zIndex\n    }),\n    dropdownRender: mergedPopupRender,\n    onDropdownVisibleChange: mergedOnOpenChange,\n    choiceTransitionName: getTransitionName(rootPrefixCls, '', choiceTransitionName),\n    transitionName: getTransitionName(rootPrefixCls, 'slide-up', transitionName),\n    treeExpandAction: treeExpandAction,\n    tagRender: isMultiple ? tagRender : undefined,\n    maxCount: mergedMaxCount,\n    showCheckedStrategy: showCheckedStrategy,\n    treeCheckStrictly: treeCheckStrictly\n  }));\n  return wrapCSSVar(treeSelectWrapCSSVar(returnNode));\n};\nconst TreeSelectRef = /*#__PURE__*/React.forwardRef(InternalTreeSelect);\nconst TreeSelect = TreeSelectRef;\n// We don't care debug panel\n/* istanbul ignore next */\nconst PurePanel = genPurePanel(TreeSelect, 'dropdownAlign', props => omit(props, ['visible']));\nTreeSelect.TreeNode = TreeNode;\nTreeSelect.SHOW_ALL = SHOW_ALL;\nTreeSelect.SHOW_PARENT = SHOW_PARENT;\nTreeSelect.SHOW_CHILD = SHOW_CHILD;\nTreeSelect._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  TreeSelect.displayName = 'TreeSelect';\n}\nexport { TreeNode };\nexport default TreeSelect;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}