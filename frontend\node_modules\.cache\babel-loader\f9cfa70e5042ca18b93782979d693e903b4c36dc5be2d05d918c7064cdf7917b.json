{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { isPresetColor } from '../_util/colors';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style/ribbon';\nconst Ribbon = props => {\n  const {\n    className,\n    prefixCls: customizePrefixCls,\n    style,\n    color,\n    children,\n    text,\n    placement = 'end',\n    rootClassName\n  } = props;\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('ribbon', customizePrefixCls);\n  const wrapperCls = `${prefixCls}-wrapper`;\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, wrapperCls);\n  const colorInPreset = isPresetColor(color, false);\n  const ribbonCls = classNames(prefixCls, `${prefixCls}-placement-${placement}`, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-color-${color}`]: colorInPreset\n  }, className);\n  const colorStyle = {};\n  const cornerColorStyle = {};\n  if (color && !colorInPreset) {\n    colorStyle.background = color;\n    cornerColorStyle.color = color;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapperCls, rootClassName, hashId, cssVarCls)\n  }, children, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(ribbonCls, hashId),\n    style: Object.assign(Object.assign({}, colorStyle), style)\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-text`\n  }, text), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-corner`,\n    style: cornerColorStyle\n  }))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ribbon.displayName = 'Ribbon';\n}\nexport default Ribbon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}