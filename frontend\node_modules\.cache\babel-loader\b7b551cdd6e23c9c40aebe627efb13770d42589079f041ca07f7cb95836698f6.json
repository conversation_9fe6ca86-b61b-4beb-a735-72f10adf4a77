{"ast": null, "code": "// 处理图片URL的工具函数\nexport const getImageUrl = imagePath => {\n  if (!imagePath || imagePath.startsWith('http')) {\n    return imagePath;\n  }\n\n  // 根据当前域名构建完整的图片URL\n  const currentHost = window.location.hostname;\n  const apiUrl = process.env.REACT_APP_API_URL;\n  let staticBaseUrl;\n  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\n    staticBaseUrl = 'http://localhost:8083';\n  } else if (currentHost === '17learn.cn') {\n    staticBaseUrl = 'http://danphy.xicp.net:23277';\n  } else if (apiUrl) {\n    staticBaseUrl = apiUrl.replace('/api', '');\n  }\n  return imagePath.startsWith('/') ? `${staticBaseUrl}${imagePath}` : `${staticBaseUrl}/${imagePath}`;\n};", "map": {"version": 3, "names": ["getImageUrl", "imagePath", "startsWith", "currentHost", "window", "location", "hostname", "apiUrl", "process", "env", "REACT_APP_API_URL", "staticBaseUrl", "replace"], "sources": ["D:/pythonproject/checkingsys/frontend/src/utils/imageUrl.js"], "sourcesContent": ["// 处理图片URL的工具函数\r\nexport const getImageUrl = (imagePath) => {\r\n  if (!imagePath || imagePath.startsWith('http')) {\r\n    return imagePath;\r\n  }\r\n\r\n  // 根据当前域名构建完整的图片URL\r\n  const currentHost = window.location.hostname;\r\n  const apiUrl = process.env.REACT_APP_API_URL;\r\n  \r\n  let staticBaseUrl;\r\n  if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\r\n    staticBaseUrl = 'http://localhost:8083';\r\n  } else if (currentHost === '17learn.cn') {\r\n    staticBaseUrl = 'http://danphy.xicp.net:23277';\r\n  } else if (apiUrl) {\r\n    staticBaseUrl = apiUrl.replace('/api', '');\r\n  }\r\n\r\n  return imagePath.startsWith('/') ? `${staticBaseUrl}${imagePath}` : `${staticBaseUrl}/${imagePath}`;\r\n};\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,WAAW,GAAIC,SAAS,IAAK;EACxC,IAAI,CAACA,SAAS,IAAIA,SAAS,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;IAC9C,OAAOD,SAAS;EAClB;;EAEA;EACA,MAAME,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAC5C,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;EAE5C,IAAIC,aAAa;EACjB,IAAIR,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,WAAW,EAAE;IAC9DQ,aAAa,GAAG,uBAAuB;EACzC,CAAC,MAAM,IAAIR,WAAW,KAAK,YAAY,EAAE;IACvCQ,aAAa,GAAG,8BAA8B;EAChD,CAAC,MAAM,IAAIJ,MAAM,EAAE;IACjBI,aAAa,GAAGJ,MAAM,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EAC5C;EAEA,OAAOX,SAAS,CAACC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAGS,aAAa,GAAGV,SAAS,EAAE,GAAG,GAAGU,aAAa,IAAIV,SAAS,EAAE;AACrG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}