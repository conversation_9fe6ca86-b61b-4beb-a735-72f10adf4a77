{"ast": null, "code": "import { useStyleRegister } from '@ant-design/cssinjs';\nimport { genCalc as calc, mergeToken, statisticToken, statistic } from '@ant-design/cssinjs-utils';\nimport { PresetColors } from './interface';\nimport { getLineHeight } from './themes/shared/genFontSizes';\nimport useToken from './useToken';\nimport { genComponentStyleHook, genStyleHooks, genSubStyleComponent } from './util/genStyleUtils';\nimport genPresetColor from './util/genPresetColor';\nimport useResetIconStyle from './util/useResetIconStyle';\nexport { DesignTokenContext, defaultConfig } from './context';\nexport {\n// generators\ngenComponentStyleHook, genSubStyleComponent, genPresetColor, genStyleHooks,\n// utils\nmergeToken, statisticToken, calc, getLineHeight,\n// hooks\nuseResetIconStyle, useStyleRegister, useToken,\n// constant\nPresetColors, statistic };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}