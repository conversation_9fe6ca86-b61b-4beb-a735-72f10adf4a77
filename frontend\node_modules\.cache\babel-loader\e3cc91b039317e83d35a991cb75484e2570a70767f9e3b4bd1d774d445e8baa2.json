{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport extendsObject from '../_util/extendsObject';\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport useSize from '../config-provider/hooks/useSize';\nimport { Row } from '../grid';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport Pagination from '../pagination';\nimport Spin from '../spin';\nimport { ListContext } from './context';\nimport Item from './Item';\nimport useStyle from './style';\nfunction InternalList(props, ref) {\n  const {\n      pagination = false,\n      prefixCls: customizePrefixCls,\n      bordered = false,\n      split = true,\n      className,\n      rootClassName,\n      style,\n      children,\n      itemLayout,\n      loadMore,\n      grid,\n      dataSource = [],\n      size: customizeSize,\n      header,\n      footer,\n      loading = false,\n      rowKey,\n      renderItem,\n      locale\n    } = props,\n    rest = __rest(props, [\"pagination\", \"prefixCls\", \"bordered\", \"split\", \"className\", \"rootClassName\", \"style\", \"children\", \"itemLayout\", \"loadMore\", \"grid\", \"dataSource\", \"size\", \"header\", \"footer\", \"loading\", \"rowKey\", \"renderItem\", \"locale\"]);\n  const paginationObj = pagination && typeof pagination === 'object' ? pagination : {};\n  const [paginationCurrent, setPaginationCurrent] = React.useState(paginationObj.defaultCurrent || 1);\n  const [paginationSize, setPaginationSize] = React.useState(paginationObj.defaultPageSize || 10);\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('list');\n  const {\n    renderEmpty\n  } = React.useContext(ConfigContext);\n  const defaultPaginationProps = {\n    current: 1,\n    total: 0,\n    position: 'bottom'\n  };\n  const triggerPaginationEvent = eventName => (page, pageSize) => {\n    var _a;\n    setPaginationCurrent(page);\n    setPaginationSize(pageSize);\n    if (pagination) {\n      (_a = pagination === null || pagination === void 0 ? void 0 : pagination[eventName]) === null || _a === void 0 ? void 0 : _a.call(pagination, page, pageSize);\n    }\n  };\n  const onPaginationChange = triggerPaginationEvent('onChange');\n  const onPaginationShowSizeChange = triggerPaginationEvent('onShowSizeChange');\n  const renderInternalItem = (item, index) => {\n    if (!renderItem) {\n      return null;\n    }\n    let key;\n    if (typeof rowKey === 'function') {\n      key = rowKey(item);\n    } else if (rowKey) {\n      key = item[rowKey];\n    } else {\n      key = item.key;\n    }\n    if (!key) {\n      key = `list-item-${index}`;\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: key\n    }, renderItem(item, index));\n  };\n  const isSomethingAfterLastItem = !!(loadMore || pagination || footer);\n  const prefixCls = getPrefixCls('list', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  let loadingProp = loading;\n  if (typeof loadingProp === 'boolean') {\n    loadingProp = {\n      spinning: loadingProp\n    };\n  }\n  const isLoading = !!(loadingProp === null || loadingProp === void 0 ? void 0 : loadingProp.spinning);\n  const mergedSize = useSize(customizeSize);\n  // large => lg\n  // small => sm\n  let sizeCls = '';\n  switch (mergedSize) {\n    case 'large':\n      sizeCls = 'lg';\n      break;\n    case 'small':\n      sizeCls = 'sm';\n      break;\n    default:\n      break;\n  }\n  const classString = classNames(prefixCls, {\n    [`${prefixCls}-vertical`]: itemLayout === 'vertical',\n    [`${prefixCls}-${sizeCls}`]: sizeCls,\n    [`${prefixCls}-split`]: split,\n    [`${prefixCls}-bordered`]: bordered,\n    [`${prefixCls}-loading`]: isLoading,\n    [`${prefixCls}-grid`]: !!grid,\n    [`${prefixCls}-something-after-last-item`]: isSomethingAfterLastItem,\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, contextClassName, className, rootClassName, hashId, cssVarCls);\n  const paginationProps = extendsObject(defaultPaginationProps, {\n    total: dataSource.length,\n    current: paginationCurrent,\n    pageSize: paginationSize\n  }, pagination || {});\n  const largestPage = Math.ceil(paginationProps.total / paginationProps.pageSize);\n  paginationProps.current = Math.min(paginationProps.current, largestPage);\n  const paginationContent = pagination && (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-pagination`)\n  }, /*#__PURE__*/React.createElement(Pagination, Object.assign({\n    align: \"end\"\n  }, paginationProps, {\n    onChange: onPaginationChange,\n    onShowSizeChange: onPaginationShowSizeChange\n  }))));\n  let splitDataSource = _toConsumableArray(dataSource);\n  if (pagination) {\n    if (dataSource.length > (paginationProps.current - 1) * paginationProps.pageSize) {\n      splitDataSource = _toConsumableArray(dataSource).splice((paginationProps.current - 1) * paginationProps.pageSize, paginationProps.pageSize);\n    }\n  }\n  const needResponsive = Object.keys(grid || {}).some(key => ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key));\n  const screens = useBreakpoint(needResponsive);\n  const currentBreakpoint = React.useMemo(() => {\n    for (let i = 0; i < responsiveArray.length; i += 1) {\n      const breakpoint = responsiveArray[i];\n      if (screens[breakpoint]) {\n        return breakpoint;\n      }\n    }\n    return undefined;\n  }, [screens]);\n  const colStyle = React.useMemo(() => {\n    if (!grid) {\n      return undefined;\n    }\n    const columnCount = currentBreakpoint && grid[currentBreakpoint] ? grid[currentBreakpoint] : grid.column;\n    if (columnCount) {\n      return {\n        width: `${100 / columnCount}%`,\n        maxWidth: `${100 / columnCount}%`\n      };\n    }\n  }, [JSON.stringify(grid), currentBreakpoint]);\n  let childrenContent = isLoading && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      minHeight: 53\n    }\n  });\n  if (splitDataSource.length > 0) {\n    const items = splitDataSource.map(renderInternalItem);\n    childrenContent = grid ? (/*#__PURE__*/React.createElement(Row, {\n      gutter: grid.gutter\n    }, React.Children.map(items, child => (/*#__PURE__*/React.createElement(\"div\", {\n      key: child === null || child === void 0 ? void 0 : child.key,\n      style: colStyle\n    }, child))))) : (/*#__PURE__*/React.createElement(\"ul\", {\n      className: `${prefixCls}-items`\n    }, items));\n  } else if (!children && !isLoading) {\n    childrenContent = /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-empty-text`\n    }, (locale === null || locale === void 0 ? void 0 : locale.emptyText) || (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('List')) || /*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n      componentName: \"List\"\n    }));\n  }\n  const paginationPosition = paginationProps.position;\n  const contextValue = React.useMemo(() => ({\n    grid,\n    itemLayout\n  }), [JSON.stringify(grid), itemLayout]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ListContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    className: classString\n  }, rest), (paginationPosition === 'top' || paginationPosition === 'both') && paginationContent, header && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-header`\n  }, header), /*#__PURE__*/React.createElement(Spin, Object.assign({}, loadingProp), childrenContent, children), footer && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-footer`\n  }, footer), loadMore || (paginationPosition === 'bottom' || paginationPosition === 'both') && paginationContent)));\n}\nconst ListWithForwardRef = /*#__PURE__*/React.forwardRef(InternalList);\nif (process.env.NODE_ENV !== 'production') {\n  ListWithForwardRef.displayName = 'List';\n}\nconst List = ListWithForwardRef;\nList.Item = Item;\nexport default List;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}