{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { FormItemInputContext } from '../form/context';\nimport { Button, Group } from '../radio';\nimport Select from '../select';\nconst YEAR_SELECT_OFFSET = 10;\nconst YEAR_SELECT_TOTAL = 20;\nfunction YearSelect(props) {\n  const {\n    fullscreen,\n    validRange,\n    generateConfig,\n    locale,\n    prefixCls,\n    value,\n    onChange,\n    divRef\n  } = props;\n  const year = generateConfig.getYear(value || generateConfig.getNow());\n  let start = year - YEAR_SELECT_OFFSET;\n  let end = start + YEAR_SELECT_TOTAL;\n  if (validRange) {\n    start = generateConfig.getYear(validRange[0]);\n    end = generateConfig.getYear(validRange[1]) + 1;\n  }\n  const suffix = locale && locale.year === '年' ? '年' : '';\n  const options = [];\n  for (let index = start; index < end; index++) {\n    options.push({\n      label: `${index}${suffix}`,\n      value: index\n    });\n  }\n  return /*#__PURE__*/React.createElement(Select, {\n    size: fullscreen ? undefined : 'small',\n    options: options,\n    value: year,\n    className: `${prefixCls}-year-select`,\n    onChange: numYear => {\n      let newDate = generateConfig.setYear(value, numYear);\n      if (validRange) {\n        const [startDate, endDate] = validRange;\n        const newYear = generateConfig.getYear(newDate);\n        const newMonth = generateConfig.getMonth(newDate);\n        if (newYear === generateConfig.getYear(endDate) && newMonth > generateConfig.getMonth(endDate)) {\n          newDate = generateConfig.setMonth(newDate, generateConfig.getMonth(endDate));\n        }\n        if (newYear === generateConfig.getYear(startDate) && newMonth < generateConfig.getMonth(startDate)) {\n          newDate = generateConfig.setMonth(newDate, generateConfig.getMonth(startDate));\n        }\n      }\n      onChange(newDate);\n    },\n    getPopupContainer: () => divRef.current\n  });\n}\nfunction MonthSelect(props) {\n  const {\n    prefixCls,\n    fullscreen,\n    validRange,\n    value,\n    generateConfig,\n    locale,\n    onChange,\n    divRef\n  } = props;\n  const month = generateConfig.getMonth(value || generateConfig.getNow());\n  let start = 0;\n  let end = 11;\n  if (validRange) {\n    const [rangeStart, rangeEnd] = validRange;\n    const currentYear = generateConfig.getYear(value);\n    if (generateConfig.getYear(rangeEnd) === currentYear) {\n      end = generateConfig.getMonth(rangeEnd);\n    }\n    if (generateConfig.getYear(rangeStart) === currentYear) {\n      start = generateConfig.getMonth(rangeStart);\n    }\n  }\n  const months = locale.shortMonths || generateConfig.locale.getShortMonths(locale.locale);\n  const options = [];\n  for (let index = start; index <= end; index += 1) {\n    options.push({\n      label: months[index],\n      value: index\n    });\n  }\n  return /*#__PURE__*/React.createElement(Select, {\n    size: fullscreen ? undefined : 'small',\n    className: `${prefixCls}-month-select`,\n    value: month,\n    options: options,\n    onChange: newMonth => {\n      onChange(generateConfig.setMonth(value, newMonth));\n    },\n    getPopupContainer: () => divRef.current\n  });\n}\nfunction ModeSwitch(props) {\n  const {\n    prefixCls,\n    locale,\n    mode,\n    fullscreen,\n    onModeChange\n  } = props;\n  return /*#__PURE__*/React.createElement(Group, {\n    onChange: ({\n      target: {\n        value\n      }\n    }) => {\n      onModeChange(value);\n    },\n    value: mode,\n    size: fullscreen ? undefined : 'small',\n    className: `${prefixCls}-mode-switch`\n  }, /*#__PURE__*/React.createElement(Button, {\n    value: \"month\"\n  }, locale.month), /*#__PURE__*/React.createElement(Button, {\n    value: \"year\"\n  }, locale.year));\n}\nfunction CalendarHeader(props) {\n  const {\n    prefixCls,\n    fullscreen,\n    mode,\n    onChange,\n    onModeChange\n  } = props;\n  const divRef = React.useRef(null);\n  const formItemInputContext = useContext(FormItemInputContext);\n  const mergedFormItemInputContext = useMemo(() => Object.assign(Object.assign({}, formItemInputContext), {\n    isFormItemInput: false\n  }), [formItemInputContext]);\n  const sharedProps = Object.assign(Object.assign({}, props), {\n    fullscreen,\n    divRef\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-header`,\n    ref: divRef\n  }, /*#__PURE__*/React.createElement(FormItemInputContext.Provider, {\n    value: mergedFormItemInputContext\n  }, /*#__PURE__*/React.createElement(YearSelect, Object.assign({}, sharedProps, {\n    onChange: v => {\n      onChange(v, 'year');\n    }\n  })), mode === 'month' && (/*#__PURE__*/React.createElement(MonthSelect, Object.assign({}, sharedProps, {\n    onChange: v => {\n      onChange(v, 'month');\n    }\n  })))), /*#__PURE__*/React.createElement(ModeSwitch, Object.assign({}, sharedProps, {\n    onModeChange: onModeChange\n  })));\n}\nexport default CalendarHeader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}