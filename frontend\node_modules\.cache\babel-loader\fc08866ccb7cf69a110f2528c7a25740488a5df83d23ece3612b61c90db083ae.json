{"ast": null, "code": "\"use client\";\n\n/* eslint-disable react/no-array-index-key */\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useItems from './hooks/useItems';\nimport useResizable from './hooks/useResizable';\nimport useResize from './hooks/useResize';\nimport useSizes from './hooks/useSizes';\nimport { InternalPanel } from './Panel';\nimport SplitBar from './SplitBar';\nimport useStyle from './style';\nconst Splitter = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    style,\n    layout = 'horizontal',\n    children,\n    rootClassName,\n    onResizeStart,\n    onResize,\n    onResizeEnd,\n    lazy\n  } = props;\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('splitter');\n  const prefixCls = getPrefixCls('splitter', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  // ======================== Direct ========================\n  const isVertical = layout === 'vertical';\n  const isRTL = direction === 'rtl';\n  const reverse = !isVertical && isRTL;\n  // ====================== Items Data ======================\n  const items = useItems(children);\n  // >>> Warning for uncontrolled\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Splitter');\n    let existSize = false;\n    let existUndefinedSize = false;\n    items.forEach(item => {\n      if (item.size !== undefined) {\n        existSize = true;\n      } else {\n        existUndefinedSize = true;\n      }\n    });\n    if (existSize && existUndefinedSize && !onResize) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'usage', 'When part of `Splitter.Panel` has `size`, `onResize` is required or change `size` to `defaultSize`.') : void 0;\n    }\n  }\n  // ====================== Container =======================\n  const [containerSize, setContainerSize] = useState();\n  const onContainerResize = size => {\n    const {\n      offsetWidth,\n      offsetHeight\n    } = size;\n    const containerSize = isVertical ? offsetHeight : offsetWidth;\n    // Skip when container has no size, Such as nested in a hidden tab panel\n    // to fix: https://github.com/ant-design/ant-design/issues/51106\n    if (containerSize === 0) {\n      return;\n    }\n    setContainerSize(containerSize);\n  };\n  // ========================= Size =========================\n  const [panelSizes, itemPxSizes, itemPtgSizes, itemPtgMinSizes, itemPtgMaxSizes, updateSizes] = useSizes(items, containerSize);\n  // ====================== Resizable =======================\n  const resizableInfos = useResizable(items, itemPxSizes, isRTL);\n  const [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex] = useResize(items, resizableInfos, itemPtgSizes, containerSize, updateSizes, isRTL);\n  // ======================== Events ========================\n  const onInternalResizeStart = useEvent(index => {\n    onOffsetStart(index);\n    onResizeStart === null || onResizeStart === void 0 ? void 0 : onResizeStart(itemPxSizes);\n  });\n  const onInternalResizeUpdate = useEvent((index, offset, lazyEnd) => {\n    const nextSizes = onOffsetUpdate(index, offset);\n    if (lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n    } else {\n      onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    }\n  });\n  const onInternalResizeEnd = useEvent(lazyEnd => {\n    onOffsetEnd();\n    if (!lazyEnd) {\n      onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(itemPxSizes);\n    }\n  });\n  const onInternalCollapse = useEvent((index, type) => {\n    const nextSizes = onCollapse(index, type);\n    onResize === null || onResize === void 0 ? void 0 : onResize(nextSizes);\n    onResizeEnd === null || onResizeEnd === void 0 ? void 0 : onResizeEnd(nextSizes);\n  });\n  // ======================== Styles ========================\n  const containerClassName = classNames(prefixCls, className, `${prefixCls}-${layout}`, {\n    [`${prefixCls}-rtl`]: isRTL\n  }, rootClassName, contextClassName, cssVarCls, rootCls, hashId);\n  // ======================== Render ========================\n  const maskCls = `${prefixCls}-mask`;\n  const stackSizes = React.useMemo(() => {\n    const mergedSizes = [];\n    let stack = 0;\n    for (let i = 0; i < items.length; i += 1) {\n      stack += itemPtgSizes[i];\n      mergedSizes.push(stack);\n    }\n    return mergedSizes;\n  }, [itemPtgSizes]);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onContainerResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: mergedStyle,\n    className: containerClassName\n  }, items.map((item, idx) => {\n    // Panel\n    const panel = /*#__PURE__*/React.createElement(InternalPanel, Object.assign({}, item, {\n      prefixCls: prefixCls,\n      size: panelSizes[idx]\n    }));\n    // Split Bar\n    let splitBar = null;\n    const resizableInfo = resizableInfos[idx];\n    if (resizableInfo) {\n      const ariaMinStart = (stackSizes[idx - 1] || 0) + itemPtgMinSizes[idx];\n      const ariaMinEnd = (stackSizes[idx + 1] || 100) - itemPtgMaxSizes[idx + 1];\n      const ariaMaxStart = (stackSizes[idx - 1] || 0) + itemPtgMaxSizes[idx];\n      const ariaMaxEnd = (stackSizes[idx + 1] || 100) - itemPtgMinSizes[idx + 1];\n      splitBar = /*#__PURE__*/React.createElement(SplitBar, {\n        lazy: lazy,\n        index: idx,\n        active: movingIndex === idx,\n        prefixCls: prefixCls,\n        vertical: isVertical,\n        resizable: resizableInfo.resizable,\n        ariaNow: stackSizes[idx] * 100,\n        ariaMin: Math.max(ariaMinStart, ariaMinEnd) * 100,\n        ariaMax: Math.min(ariaMaxStart, ariaMaxEnd) * 100,\n        startCollapsible: resizableInfo.startCollapsible,\n        endCollapsible: resizableInfo.endCollapsible,\n        onOffsetStart: onInternalResizeStart,\n        onOffsetUpdate: (index, offsetX, offsetY, lazyEnd) => {\n          let offset = isVertical ? offsetY : offsetX;\n          if (reverse) {\n            offset = -offset;\n          }\n          onInternalResizeUpdate(index, offset, lazyEnd);\n        },\n        onOffsetEnd: onInternalResizeEnd,\n        onCollapse: onInternalCollapse,\n        containerSize: containerSize || 0\n      });\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, {\n      key: `split-panel-${idx}`\n    }, panel, splitBar);\n  }), typeof movingIndex === 'number' && (/*#__PURE__*/React.createElement(\"div\", {\n    \"aria-hidden\": true,\n    className: classNames(maskCls, `${maskCls}-${layout}`)\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Splitter.displayName = 'Splitter';\n}\nexport default Splitter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}