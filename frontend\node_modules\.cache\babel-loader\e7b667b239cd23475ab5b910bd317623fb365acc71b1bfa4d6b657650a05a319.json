{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport CaretDownFilled from \"@ant-design/icons/es/icons/CaretDownFilled\";\nimport FileOutlined from \"@ant-design/icons/es/icons/FileOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport MinusSquareOutlined from \"@ant-design/icons/es/icons/MinusSquareOutlined\";\nimport PlusSquareOutlined from \"@ant-design/icons/es/icons/PlusSquareOutlined\";\nimport classNames from 'classnames';\nimport { cloneElement } from '../../_util/reactNode';\nconst SwitcherIconCom = props => {\n  const {\n    prefixCls,\n    switcherIcon,\n    treeNodeProps,\n    showLine,\n    switcherLoadingIcon\n  } = props;\n  const {\n    isLeaf,\n    expanded,\n    loading\n  } = treeNodeProps;\n  if (loading) {\n    if (/*#__PURE__*/React.isValidElement(switcherLoadingIcon)) {\n      return switcherLoadingIcon;\n    }\n    return /*#__PURE__*/React.createElement(LoadingOutlined, {\n      className: `${prefixCls}-switcher-loading-icon`\n    });\n  }\n  let showLeafIcon;\n  if (showLine && typeof showLine === 'object') {\n    showLeafIcon = showLine.showLeafIcon;\n  }\n  if (isLeaf) {\n    if (!showLine) {\n      return null;\n    }\n    if (typeof showLeafIcon !== 'boolean' && !!showLeafIcon) {\n      const leafIcon = typeof showLeafIcon === 'function' ? showLeafIcon(treeNodeProps) : showLeafIcon;\n      const leafCls = `${prefixCls}-switcher-line-custom-icon`;\n      if (/*#__PURE__*/React.isValidElement(leafIcon)) {\n        return cloneElement(leafIcon, {\n          className: classNames(leafIcon.props.className || '', leafCls)\n        });\n      }\n      return leafIcon;\n    }\n    return showLeafIcon ? (/*#__PURE__*/React.createElement(FileOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    })) : (/*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-switcher-leaf-line`\n    }));\n  }\n  const switcherCls = `${prefixCls}-switcher-icon`;\n  const switcher = typeof switcherIcon === 'function' ? switcherIcon(treeNodeProps) : switcherIcon;\n  if (/*#__PURE__*/React.isValidElement(switcher)) {\n    return cloneElement(switcher, {\n      className: classNames(switcher.props.className || '', switcherCls)\n    });\n  }\n  if (switcher !== undefined) {\n    return switcher;\n  }\n  if (showLine) {\n    return expanded ? (/*#__PURE__*/React.createElement(MinusSquareOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    })) : (/*#__PURE__*/React.createElement(PlusSquareOutlined, {\n      className: `${prefixCls}-switcher-line-icon`\n    }));\n  }\n  return /*#__PURE__*/React.createElement(CaretDownFilled, {\n    className: switcherCls\n  });\n};\nexport default SwitcherIconCom;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}