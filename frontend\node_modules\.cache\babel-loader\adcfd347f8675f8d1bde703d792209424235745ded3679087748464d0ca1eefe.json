{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { isPresetSize, isValidGapNumber } from '../_util/gapSize';\nimport { useComponentConfig } from '../config-provider/context';\nimport Compact from './Compact';\nimport { SpaceContextProvider } from './context';\nimport Item from './Item';\nimport useStyle from './style';\nexport { SpaceContext } from './context';\nconst InternalSpace = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n    getPrefixCls,\n    direction: directionConfig,\n    size: contextSize,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('space');\n  const {\n      size = contextSize !== null && contextSize !== void 0 ? contextSize : 'small',\n      align,\n      className,\n      rootClassName,\n      children,\n      direction = 'horizontal',\n      prefixCls: customizePrefixCls,\n      split,\n      style,\n      wrap = false,\n      classNames: customClassNames,\n      styles\n    } = props,\n    otherProps = __rest(props, [\"size\", \"align\", \"className\", \"rootClassName\", \"children\", \"direction\", \"prefixCls\", \"split\", \"style\", \"wrap\", \"classNames\", \"styles\"]);\n  const [horizontalSize, verticalSize] = Array.isArray(size) ? size : [size, size];\n  const isPresetVerticalSize = isPresetSize(verticalSize);\n  const isPresetHorizontalSize = isPresetSize(horizontalSize);\n  const isValidVerticalSize = isValidGapNumber(verticalSize);\n  const isValidHorizontalSize = isValidGapNumber(horizontalSize);\n  const childNodes = toArray(children, {\n    keepEmpty: true\n  });\n  const mergedAlign = align === undefined && direction === 'horizontal' ? 'center' : align;\n  const prefixCls = getPrefixCls('space', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, contextClassName, hashId, `${prefixCls}-${direction}`, {\n    [`${prefixCls}-rtl`]: directionConfig === 'rtl',\n    [`${prefixCls}-align-${mergedAlign}`]: mergedAlign,\n    [`${prefixCls}-gap-row-${verticalSize}`]: isPresetVerticalSize,\n    [`${prefixCls}-gap-col-${horizontalSize}`]: isPresetHorizontalSize\n  }, className, rootClassName, cssVarCls);\n  const itemClassName = classNames(`${prefixCls}-item`, (_a = customClassNames === null || customClassNames === void 0 ? void 0 : customClassNames.item) !== null && _a !== void 0 ? _a : contextClassNames.item);\n  // Calculate latest one\n  let latestIndex = 0;\n  const nodes = childNodes.map((child, i) => {\n    var _a;\n    if (child !== null && child !== undefined) {\n      latestIndex = i;\n    }\n    const key = (child === null || child === void 0 ? void 0 : child.key) || `${itemClassName}-${i}`;\n    return /*#__PURE__*/React.createElement(Item, {\n      className: itemClassName,\n      key: key,\n      index: i,\n      split: split,\n      style: (_a = styles === null || styles === void 0 ? void 0 : styles.item) !== null && _a !== void 0 ? _a : contextStyles.item\n    }, child);\n  });\n  const spaceContext = React.useMemo(() => ({\n    latestIndex\n  }), [latestIndex]);\n  // =========================== Render ===========================\n  if (childNodes.length === 0) {\n    return null;\n  }\n  const gapStyle = {};\n  if (wrap) {\n    gapStyle.flexWrap = 'wrap';\n  }\n  if (!isPresetHorizontalSize && isValidHorizontalSize) {\n    gapStyle.columnGap = horizontalSize;\n  }\n  if (!isPresetVerticalSize && isValidVerticalSize) {\n    gapStyle.rowGap = verticalSize;\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: cls,\n    style: Object.assign(Object.assign(Object.assign({}, gapStyle), contextStyle), style)\n  }, otherProps), /*#__PURE__*/React.createElement(SpaceContextProvider, {\n    value: spaceContext\n  }, nodes)));\n});\nconst Space = InternalSpace;\nSpace.Compact = Compact;\nif (process.env.NODE_ENV !== 'production') {\n  Space.displayName = 'Space';\n}\nexport default Space;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}