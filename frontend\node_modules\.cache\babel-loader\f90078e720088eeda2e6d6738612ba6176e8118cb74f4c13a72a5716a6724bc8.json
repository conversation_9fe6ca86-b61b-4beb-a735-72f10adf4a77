{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst StatisticNumber = props => {\n  const {\n    value,\n    formatter,\n    precision,\n    decimalSeparator,\n    groupSeparator = '',\n    prefixCls\n  } = props;\n  let valueNode;\n  if (typeof formatter === 'function') {\n    // Customize formatter\n    valueNode = formatter(value);\n  } else {\n    // Internal formatter\n    const val = String(value);\n    const cells = val.match(/^(-?)(\\d*)(\\.(\\d+))?$/);\n    // Process if illegal number\n    if (!cells || val === '-') {\n      valueNode = val;\n    } else {\n      const negative = cells[1];\n      let int = cells[2] || '0';\n      let decimal = cells[4] || '';\n      int = int.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator);\n      if (typeof precision === 'number') {\n        decimal = decimal.padEnd(precision, '0').slice(0, precision > 0 ? precision : 0);\n      }\n      if (decimal) {\n        decimal = `${decimalSeparator}${decimal}`;\n      }\n      valueNode = [/*#__PURE__*/React.createElement(\"span\", {\n        key: \"int\",\n        className: `${prefixCls}-content-value-int`\n      }, negative, int), decimal && (/*#__PURE__*/React.createElement(\"span\", {\n        key: \"decimal\",\n        className: `${prefixCls}-content-value-decimal`\n      }, decimal))];\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-value`\n  }, valueNode);\n};\nexport default StatisticNumber;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}