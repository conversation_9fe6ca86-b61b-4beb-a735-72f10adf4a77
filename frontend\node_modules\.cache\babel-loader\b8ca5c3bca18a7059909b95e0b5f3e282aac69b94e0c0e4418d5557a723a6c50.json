{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar Keyframe = /*#__PURE__*/function () {\n  function Keyframe(name, style) {\n    _classCallCheck(this, Keyframe);\n    _defineProperty(this, \"name\", void 0);\n    _defineProperty(this, \"style\", void 0);\n    _defineProperty(this, \"_keyframe\", true);\n    this.name = name;\n    this.style = style;\n  }\n  _createClass(Keyframe, [{\n    key: \"getName\",\n    value: function getName() {\n      var hashId = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n      return hashId ? \"\".concat(hashId, \"-\").concat(this.name) : this.name;\n    }\n  }]);\n  return Keyframe;\n}();\nexport default Keyframe;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}