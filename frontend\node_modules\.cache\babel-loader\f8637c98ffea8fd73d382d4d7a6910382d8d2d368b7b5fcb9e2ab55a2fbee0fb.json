{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\SystemHomeworkAnalysis\\\\SystemAssignmentSelector.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Card, Row, Col, Select, Button, Table, Typography, Input, DatePicker, Space, Tag, message, Spin } from 'antd';\nimport { BarChartOutlined, ClearOutlined, SearchOutlined, FilterOutlined } from '@ant-design/icons';\nimport api from '../../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst SystemAssignmentSelector = ({\n  user\n}) => {\n  _s();\n  var _provinces$find2, _cities$find2, _districts$find2, _schools$find, _classes$find, _subjects$find;\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n\n  // 基础数据\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [schools, setSchools] = useState([]);\n  const [classes, setClasses] = useState([]);\n  const [subjects, setSubjects] = useState([]);\n  const [, setAssignments] = useState([]);\n  const [filteredAssignments, setFilteredAssignments] = useState([]);\n\n  // 筛选条件\n  const [filters, setFilters] = useState({\n    selectedProvince: null,\n    selectedCity: null,\n    selectedDistrict: null,\n    selectedSchool: null,\n    selectedGrade: null,\n    selectedClass: null,\n    selectedSubject: null,\n    selectedAssignment: null,\n    searchText: '',\n    dateRange: null\n  });\n\n  // 年级选项\n  const gradeOptions = [{\n    value: '七年级',\n    label: '七年级'\n  }, {\n    value: '八年级',\n    label: '八年级'\n  }, {\n    value: '九年级',\n    label: '九年级'\n  }, {\n    value: '高一',\n    label: '高一'\n  }, {\n    value: '高二',\n    label: '高二'\n  }, {\n    value: '高三',\n    label: '高三'\n  }];\n\n  // 获取基础数据\n  const fetchInitialData = async () => {\n    try {\n      var _subjectsResponse$ite;\n      setLoading(true);\n      console.log('🔄 开始获取系统级筛选数据...');\n\n      // 并行获取基础数据\n      const [provincesResponse, schoolsResponse, subjectsResponse] = await Promise.all([api.get('/schools/region/provinces'), api.get('/admin/schools'), api.get('/admin/subjects')]);\n      console.log('📍 省份数据:', provincesResponse === null || provincesResponse === void 0 ? void 0 : provincesResponse.length);\n      console.log('🏫 学校数据:', schoolsResponse === null || schoolsResponse === void 0 ? void 0 : schoolsResponse.length);\n      console.log('📚 科目数据:', (subjectsResponse === null || subjectsResponse === void 0 ? void 0 : (_subjectsResponse$ite = subjectsResponse.items) === null || _subjectsResponse$ite === void 0 ? void 0 : _subjectsResponse$ite.length) || (subjectsResponse === null || subjectsResponse === void 0 ? void 0 : subjectsResponse.length));\n\n      // 处理省份数据\n      setProvinces(provincesResponse || []);\n      setSchools(schoolsResponse || []);\n\n      // 处理科目数据 - 初始时显示所有科目\n      const subjectData = (subjectsResponse === null || subjectsResponse === void 0 ? void 0 : subjectsResponse.items) || subjectsResponse || [];\n      setSubjects(subjectData);\n    } catch (error) {\n      console.error('❌ 获取基础数据失败:', error);\n      message.error('获取筛选数据失败，请刷新页面重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 根据学校获取对应的科目\n  const fetchSubjectsBySchool = async schoolId => {\n    try {\n      console.log('📚 根据学校获取科目:', schoolId);\n      if (schoolId) {\n        // 使用公开科目API，传入学校ID参数\n        const response = await api.get(`/public/subjects?school_id=${schoolId}`);\n        const subjectData = response || [];\n        console.log('📚 学校科目数据:', subjectData.length);\n        console.log('📚 学校科目列表:', subjectData.map(s => s.name).join(', '));\n        setSubjects(subjectData);\n      } else {\n        // 如果没有选择学校，显示所有科目\n        const subjectsResponse = await api.get('/admin/subjects');\n        const subjectData = (subjectsResponse === null || subjectsResponse === void 0 ? void 0 : subjectsResponse.items) || subjectsResponse || [];\n        console.log('📚 所有科目数据:', subjectData.length);\n        setSubjects(subjectData);\n      }\n    } catch (error) {\n      console.error('❌ 获取科目失败:', error);\n      // 如果获取科目失败，回退到所有科目\n      try {\n        const subjectsResponse = await api.get('/admin/subjects');\n        const subjectData = (subjectsResponse === null || subjectsResponse === void 0 ? void 0 : subjectsResponse.items) || subjectsResponse || [];\n        setSubjects(subjectData);\n      } catch (fallbackError) {\n        console.error('❌ 获取科目回退方案也失败:', fallbackError);\n      }\n    }\n  };\n\n  // 根据省份获取城市\n  const fetchCities = async provinceId => {\n    if (!provinceId) {\n      setCities([]);\n      return;\n    }\n    try {\n      console.log('🏙️ 根据省份获取城市:', provinceId);\n      const response = await api.get(`/schools/region/cities?province_id=${provinceId}`);\n      const cityData = response || [];\n      console.log('🏙️ 城市数据:', cityData.length);\n      setCities(cityData);\n    } catch (error) {\n      console.error('❌ 获取城市数据失败:', error);\n      setCities([]);\n    }\n  };\n\n  // 根据城市获取区县\n  const fetchDistricts = async cityId => {\n    if (!cityId) {\n      setDistricts([]);\n      return;\n    }\n    try {\n      console.log('🏘️ 根据城市获取区县:', cityId);\n      const response = await api.get(`/schools/region/districts?city_id=${cityId}`);\n      const districtData = response || [];\n      console.log('🏘️ 区县数据:', districtData.length);\n      setDistricts(districtData);\n    } catch (error) {\n      console.error('❌ 获取区县数据失败:', error);\n      setDistricts([]);\n    }\n  };\n\n  // 根据筛选条件获取班级\n  const fetchClasses = async (schoolId, grade) => {\n    if (!schoolId) {\n      setClasses([]);\n      return;\n    }\n    try {\n      const params = new URLSearchParams();\n      params.append('school_id', schoolId);\n      if (grade) {\n        params.append('grade', grade);\n      }\n      const response = await api.get(`/admin/classes?${params.toString()}`);\n      console.log('🏛️ 班级数据:', response === null || response === void 0 ? void 0 : response.length);\n      setClasses(response || []);\n    } catch (error) {\n      console.error('❌ 获取班级数据失败:', error);\n      setClasses([]);\n    }\n  };\n\n  // 根据筛选条件获取作业分配\n  const fetchAssignments = async (currentFilters = filters) => {\n    try {\n      setLoading(true);\n      console.log('📝 开始获取作业分配数据...', currentFilters);\n      const params = new URLSearchParams();\n\n      // 添加筛选参数\n      if (currentFilters.selectedSchool) {\n        params.append('school_id', currentFilters.selectedSchool);\n      }\n      if (currentFilters.selectedClass) {\n        params.append('class_id', currentFilters.selectedClass);\n      }\n      if (currentFilters.selectedSubject) {\n        params.append('subject_id', currentFilters.selectedSubject);\n      }\n      if (currentFilters.selectedGrade) {\n        params.append('grade', currentFilters.selectedGrade);\n      }\n\n      // 添加地区筛选参数\n      if (currentFilters.selectedProvince) {\n        params.append('province_id', currentFilters.selectedProvince);\n      }\n      if (currentFilters.selectedCity) {\n        params.append('city_id', currentFilters.selectedCity);\n      }\n      if (currentFilters.selectedDistrict) {\n        params.append('district_id', currentFilters.selectedDistrict);\n      }\n      if (currentFilters.searchText) {\n        params.append('search', currentFilters.searchText);\n      }\n      if (currentFilters.dateRange && currentFilters.dateRange.length === 2) {\n        params.append('start_date', currentFilters.dateRange[0].format('YYYY-MM-DD'));\n        params.append('end_date', currentFilters.dateRange[1].format('YYYY-MM-DD'));\n      }\n\n      // 使用系统级作业分配API\n      const response = await api.get(`/system/homework-assignments?${params.toString()}`);\n      console.log('📝 作业分配数据:', response === null || response === void 0 ? void 0 : response.length);\n      const assignmentsData = response || [];\n      setFilteredAssignments(assignmentsData);\n    } catch (error) {\n      console.error('❌ 获取作业分配数据失败:', error);\n      message.error('获取作业数据失败');\n      setFilteredAssignments([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理筛选条件变化\n  const handleFilterChange = async (key, value) => {\n    const newFilters = {\n      ...filters,\n      [key]: value\n    };\n\n    // 级联清除下级筛选\n    if (key === 'selectedProvince') {\n      newFilters.selectedCity = null;\n      newFilters.selectedDistrict = null;\n      newFilters.selectedSchool = null;\n      newFilters.selectedClass = null;\n      setCities([]);\n      setDistricts([]);\n      setClasses([]);\n\n      // 获取该省份的城市\n      if (value) {\n        await fetchCities(value);\n      }\n    }\n    if (key === 'selectedCity') {\n      newFilters.selectedDistrict = null;\n      newFilters.selectedSchool = null;\n      newFilters.selectedClass = null;\n      setDistricts([]);\n      setClasses([]);\n\n      // 获取该城市的区县\n      if (value) {\n        await fetchDistricts(value);\n      }\n    }\n    if (key === 'selectedDistrict') {\n      newFilters.selectedSchool = null;\n      newFilters.selectedClass = null;\n      setClasses([]);\n    }\n    if (key === 'selectedSchool') {\n      newFilters.selectedClass = null;\n      newFilters.selectedSubject = null; // 清除科目选择\n      if (value) {\n        fetchClasses(value, newFilters.selectedGrade);\n        // 根据选择的学校获取对应的科目\n        await fetchSubjectsBySchool(value);\n      } else {\n        setClasses([]);\n        // 如果没有选择学校，显示所有科目\n        await fetchSubjectsBySchool(null);\n      }\n    }\n    if (key === 'selectedGrade') {\n      newFilters.selectedClass = null;\n      if (newFilters.selectedSchool) {\n        fetchClasses(newFilters.selectedSchool, value);\n      }\n    }\n    setFilters(newFilters);\n    console.log('🔧 筛选条件更新:', newFilters);\n\n    // 实时触发筛选（除了搜索框和日期范围，这些需要手动触发）\n    if (key !== 'searchText' && key !== 'dateRange') {\n      await fetchAssignments(newFilters);\n    }\n  };\n\n  // 应用筛选（主要用于搜索框和日期范围）\n  const applyFilters = () => {\n    fetchAssignments(filters);\n  };\n\n  // 清除所有筛选条件\n  const clearAllFilters = async () => {\n    const emptyFilters = {\n      selectedProvince: null,\n      selectedCity: null,\n      selectedDistrict: null,\n      selectedSchool: null,\n      selectedGrade: null,\n      selectedClass: null,\n      selectedSubject: null,\n      selectedAssignment: null,\n      searchText: '',\n      dateRange: null\n    };\n    setFilters(emptyFilters);\n    setCities([]);\n    setDistricts([]);\n    setClasses([]);\n    console.log('🗑️ 已清除所有筛选条件');\n\n    // 重新加载所有数据\n    await fetchAssignments(emptyFilters);\n  };\n\n  // 跳转到系统作业分析页面\n  const handleAnalyzeAssignment = assignmentId => {\n    navigate(`/system-homework-analysis/overview?assignmentId=${assignmentId}`);\n  };\n\n  // 获取状态标签\n  const getStatusTag = status => {\n    switch (status) {\n      case 'published':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          children: \"\\u5DF2\\u53D1\\u5E03\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: \"\\u5DF2\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 16\n        }, this);\n      case 'graded':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"purple\",\n          children: \"\\u5DF2\\u6279\\u6539\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 16\n        }, this);\n      case 'active':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"cyan\",\n          children: \"\\u8FDB\\u884C\\u4E2D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: status || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '作业标题',\n    dataIndex: 'title',\n    key: 'title',\n    width: 200,\n    ellipsis: true\n  }, {\n    title: '学校',\n    dataIndex: 'school_name',\n    key: 'school_name',\n    width: 120,\n    ellipsis: true\n  }, {\n    title: '班级',\n    dataIndex: 'class_name',\n    key: 'class_name',\n    width: 100\n  }, {\n    title: '科目',\n    dataIndex: 'subject_name',\n    key: 'subject_name',\n    width: 80\n  }, {\n    title: '教师',\n    dataIndex: 'teacher_name',\n    key: 'teacher_name',\n    width: 100\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    width: 120,\n    render: text => text ? new Date(text).toLocaleDateString() : '-'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => getStatusTag(status)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 17\n      }, this),\n      onClick: () => handleAnalyzeAssignment(record.assignment_id),\n      children: \"\\u5206\\u6790\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 组件初始化\n  useEffect(() => {\n    const initializeData = async () => {\n      await fetchInitialData();\n      // 初始化时加载所有作业数据\n      await fetchAssignments();\n    };\n    initializeData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(FilterOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this), \" \\u7CFB\\u7EDF\\u4F5C\\u4E1A\\u5206\\u6790 - \\u7B5B\\u9009\\u6761\\u4EF6\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u7B5B\\u9009\\u6761\\u4EF6\",\n      style: {\n        marginBottom: '24px'\n      },\n      bodyStyle: {\n        padding: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [12, 12],\n        align: \"bottom\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u7701\\u4EFD\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u7701\\u4EFD\",\n            value: filters.selectedProvince,\n            onChange: value => handleFilterChange('selectedProvince', value),\n            allowClear: true,\n            size: \"small\",\n            children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n              value: province.id,\n              children: province.name\n            }, province.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u57CE\\u5E02\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u57CE\\u5E02\",\n            value: filters.selectedCity,\n            onChange: value => handleFilterChange('selectedCity', value),\n            disabled: !filters.selectedProvince,\n            allowClear: true,\n            size: \"small\",\n            children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n              value: city.id,\n              children: city.name\n            }, city.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u533A\\u53BF\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u533A\\u53BF\",\n            value: filters.selectedDistrict,\n            onChange: value => handleFilterChange('selectedDistrict', value),\n            disabled: !filters.selectedCity,\n            allowClear: true,\n            size: \"small\",\n            children: districts.map(district => /*#__PURE__*/_jsxDEV(Option, {\n              value: district.id,\n              children: district.name\n            }, district.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 8,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u5B66\\u6821\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u5B66\\u6821\",\n            value: filters.selectedSchool,\n            onChange: value => handleFilterChange('selectedSchool', value),\n            allowClear: true,\n            showSearch: true,\n            size: \"small\",\n            filterOption: (input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0,\n            children: schools.filter(school => {\n              // 根据选中的省份、城市、区县筛选学校\n              if (filters.selectedProvince) {\n                var _provinces$find;\n                const selectedProvince = (_provinces$find = provinces.find(p => p.id === filters.selectedProvince)) === null || _provinces$find === void 0 ? void 0 : _provinces$find.name;\n                if (school.province !== selectedProvince) return false;\n              }\n              if (filters.selectedCity) {\n                var _cities$find;\n                const selectedCity = (_cities$find = cities.find(c => c.id === filters.selectedCity)) === null || _cities$find === void 0 ? void 0 : _cities$find.name;\n                if (school.city !== selectedCity) return false;\n              }\n              if (filters.selectedDistrict) {\n                var _districts$find;\n                const selectedDistrict = (_districts$find = districts.find(d => d.id === filters.selectedDistrict)) === null || _districts$find === void 0 ? void 0 : _districts$find.name;\n                if (school.district !== selectedDistrict) return false;\n              }\n              return true;\n            }).map(school => /*#__PURE__*/_jsxDEV(Option, {\n              value: school.id,\n              children: school.name\n            }, school.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u5E74\\u7EA7\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u5E74\\u7EA7\",\n            value: filters.selectedGrade,\n            onChange: value => handleFilterChange('selectedGrade', value),\n            allowClear: true,\n            size: \"small\",\n            children: gradeOptions.map(grade => /*#__PURE__*/_jsxDEV(Option, {\n              value: grade.value,\n              children: grade.label\n            }, grade.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u73ED\\u7EA7\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u73ED\\u7EA7\",\n            value: filters.selectedClass,\n            onChange: value => handleFilterChange('selectedClass', value),\n            disabled: !filters.selectedSchool,\n            allowClear: true,\n            size: \"small\",\n            children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n              value: cls.id,\n              children: cls.name\n            }, cls.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u79D1\\u76EE\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u9009\\u62E9\\u79D1\\u76EE\",\n            value: filters.selectedSubject,\n            onChange: value => handleFilterChange('selectedSubject', value),\n            allowClear: true,\n            size: \"small\",\n            children: subjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n              value: subject.id,\n              children: subject.name\n            }, subject.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 8,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u65F6\\u95F4\\u8303\\u56F4\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n            style: {\n              width: '100%'\n            },\n            value: filters.dateRange,\n            onChange: dates => handleFilterChange('dateRange', dates),\n            placeholder: ['开始日期', '结束日期'],\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u4F5C\\u4E1A\\u6807\\u9898\\u641C\\u7D22\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8F93\\u5165\\u5173\\u952E\\u8BCD\\u641C\\u7D22\",\n            value: filters.searchText,\n            onChange: e => handleFilterChange('searchText', e.target.value),\n            onPressEnter: applyFilters,\n            prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 23\n            }, this),\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                fontSize: '12px',\n                color: '#666'\n              },\n              children: \"\\u64CD\\u4F5C\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 23\n              }, this),\n              onClick: applyFilters,\n              loading: loading,\n              size: \"small\",\n              children: \"\\u641C\\u7D22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 23\n              }, this),\n              onClick: clearAllFilters,\n              size: \"small\",\n              children: \"\\u6E05\\u9664\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u7B5B\\u9009\\u7ED3\\u679C\",\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: [\"\\u7701\\u4EFD: \", filters.selectedProvince ? (_provinces$find2 = provinces.find(p => p.id === filters.selectedProvince)) === null || _provinces$find2 === void 0 ? void 0 : _provinces$find2.name : '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"geekblue\",\n          children: [\"\\u57CE\\u5E02: \", filters.selectedCity ? (_cities$find2 = cities.find(c => c.id === filters.selectedCity)) === null || _cities$find2 === void 0 ? void 0 : _cities$find2.name : '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"purple\",\n          children: [\"\\u533A\\u53BF: \", filters.selectedDistrict ? (_districts$find2 = districts.find(d => d.id === filters.selectedDistrict)) === null || _districts$find2 === void 0 ? void 0 : _districts$find2.name : '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"green\",\n          children: [\"\\u5B66\\u6821: \", filters.selectedSchool ? (_schools$find = schools.find(s => s.id === filters.selectedSchool)) === null || _schools$find === void 0 ? void 0 : _schools$find.name : '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"orange\",\n          children: [\"\\u5E74\\u7EA7: \", filters.selectedGrade || '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"magenta\",\n          children: [\"\\u73ED\\u7EA7: \", filters.selectedClass ? (_classes$find = classes.find(c => c.id === filters.selectedClass)) === null || _classes$find === void 0 ? void 0 : _classes$find.name : '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"cyan\",\n          children: [\"\\u79D1\\u76EE: \", filters.selectedSubject ? (_subjects$find = subjects.find(s => s.id === filters.selectedSubject)) === null || _subjects$find === void 0 ? void 0 : _subjects$find.name : '全部']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: [\"\\u4F5C\\u4E1A\\u5217\\u8868 (\", filteredAssignments.length, \" \\u4E2A\\u53EF\\u5206\\u6790\\u7684\\u4F5C\\u4E1A)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F5C\\u4E1A\\u5217\\u8868\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading,\n        children: filteredAssignments.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n          columns: columns,\n          dataSource: filteredAssignments,\n          rowKey: \"id\",\n          pagination: {\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\n            pageSizeOptions: ['10', '20', '50', '100']\n          },\n          scroll: {\n            x: 800\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '40px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u6682\\u65E0\\u53EF\\u5206\\u6790\\u7684\\u4F5C\\u4E1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#999'\n            },\n            children: \"\\u8BF7\\u8C03\\u6574\\u7B5B\\u9009\\u6761\\u4EF6\\u6216\\u786E\\u4FDD\\u9009\\u5B9A\\u8303\\u56F4\\u5185\\u6709\\u5DF2\\u53D1\\u5E03\\u7684\\u4F5C\\u4E1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 712,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 446,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemAssignmentSelector, \"9JYx3hvnT4/e+p+++Am0DsSG9LA=\", false, function () {\n  return [useNavigate];\n});\n_c = SystemAssignmentSelector;\nexport default SystemAssignmentSelector;\nvar _c;\n$RefreshReg$(_c, \"SystemAssignmentSelector\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Card", "Row", "Col", "Select", "<PERSON><PERSON>", "Table", "Typography", "Input", "DatePicker", "Space", "Tag", "message", "Spin", "BarChartOutlined", "ClearOutlined", "SearchOutlined", "FilterOutlined", "api", "jsxDEV", "_jsxDEV", "Title", "Option", "RangePicker", "SystemAssignmentSelector", "user", "_s", "_provinces$find2", "_cities$find2", "_districts$find2", "_schools$find", "_classes$find", "_subjects$find", "navigate", "loading", "setLoading", "provinces", "setProvinces", "cities", "setCities", "districts", "setDistricts", "schools", "setSchools", "classes", "setClasses", "subjects", "setSubjects", "setAssignments", "filteredAssignments", "setFilteredAssignments", "filters", "setFilters", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCity", "selectedDistrict", "selectedSchool", "selected<PERSON><PERSON>", "selectedClass", "selectedSubject", "selectedAssignment", "searchText", "date<PERSON><PERSON><PERSON>", "gradeOptions", "value", "label", "fetchInitialData", "_subjectsResponse$ite", "console", "log", "provincesResponse", "schoolsResponse", "subjectsResponse", "Promise", "all", "get", "length", "items", "subjectData", "error", "fetchSubjectsBySchool", "schoolId", "response", "map", "s", "name", "join", "fallback<PERSON><PERSON>r", "fetchCities", "provinceId", "cityData", "fetchDistricts", "cityId", "districtData", "fetchClasses", "grade", "params", "URLSearchParams", "append", "toString", "fetchAssignments", "currentFilters", "format", "assignmentsData", "handleFilterChange", "key", "newFilters", "applyFilters", "clearAllFilters", "emptyFilters", "handleAnalyzeAssignment", "assignmentId", "getStatusTag", "status", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "columns", "title", "dataIndex", "width", "ellipsis", "render", "text", "Date", "toLocaleDateString", "_", "record", "type", "icon", "onClick", "assignment_id", "initializeData", "style", "padding", "level", "marginBottom", "bodyStyle", "gutter", "align", "xs", "sm", "md", "fontSize", "placeholder", "onChange", "allowClear", "size", "province", "id", "disabled", "city", "district", "showSearch", "filterOption", "input", "option", "toLowerCase", "indexOf", "filter", "school", "_provinces$find", "find", "p", "_cities$find", "c", "_districts$find", "d", "cls", "subject", "dates", "e", "target", "onPressEnter", "prefix", "spinning", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "pageSizeOptions", "scroll", "x", "textAlign", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/SystemHomeworkAnalysis/SystemAssignmentSelector.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { \n  Card, \n  Row, \n  Col, \n  Select, \n  Button, \n  Table, \n  Typography, \n  Input, \n  DatePicker, \n  Space,\n  Tag,\n  message,\n  Spin\n} from 'antd';\nimport { \n  BarChartOutlined, \n  ClearOutlined, \n  SearchOutlined,\n  FilterOutlined\n} from '@ant-design/icons';\nimport api from '../../utils/api';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\nconst SystemAssignmentSelector = ({ user }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  \n  // 基础数据\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [schools, setSchools] = useState([]);\n  const [classes, setClasses] = useState([]);\n  const [subjects, setSubjects] = useState([]);\n  const [, setAssignments] = useState([]);\n  const [filteredAssignments, setFilteredAssignments] = useState([]);\n\n  // 筛选条件\n  const [filters, setFilters] = useState({\n    selectedProvince: null,\n    selectedCity: null,\n    selectedDistrict: null,\n    selectedSchool: null,\n    selectedGrade: null,\n    selectedClass: null,\n    selectedSubject: null,\n    selectedAssignment: null,\n    searchText: '',\n    dateRange: null\n  });\n\n  // 年级选项\n  const gradeOptions = [\n    { value: '七年级', label: '七年级' },\n    { value: '八年级', label: '八年级' },\n    { value: '九年级', label: '九年级' },\n    { value: '高一', label: '高一' },\n    { value: '高二', label: '高二' },\n    { value: '高三', label: '高三' }\n  ];\n\n  // 获取基础数据\n  const fetchInitialData = async () => {\n    try {\n      setLoading(true);\n      console.log('🔄 开始获取系统级筛选数据...');\n\n      // 并行获取基础数据\n      const [provincesResponse, schoolsResponse, subjectsResponse] = await Promise.all([\n        api.get('/schools/region/provinces'),\n        api.get('/admin/schools'),\n        api.get('/admin/subjects')\n      ]);\n\n      console.log('📍 省份数据:', provincesResponse?.length);\n      console.log('🏫 学校数据:', schoolsResponse?.length);\n      console.log('📚 科目数据:', subjectsResponse?.items?.length || subjectsResponse?.length);\n\n      // 处理省份数据\n      setProvinces(provincesResponse || []);\n      setSchools(schoolsResponse || []);\n\n      // 处理科目数据 - 初始时显示所有科目\n      const subjectData = subjectsResponse?.items || subjectsResponse || [];\n      setSubjects(subjectData);\n\n    } catch (error) {\n      console.error('❌ 获取基础数据失败:', error);\n      message.error('获取筛选数据失败，请刷新页面重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 根据学校获取对应的科目\n  const fetchSubjectsBySchool = async (schoolId) => {\n    try {\n      console.log('📚 根据学校获取科目:', schoolId);\n\n      if (schoolId) {\n        // 使用公开科目API，传入学校ID参数\n        const response = await api.get(`/public/subjects?school_id=${schoolId}`);\n        const subjectData = response || [];\n        console.log('📚 学校科目数据:', subjectData.length);\n        console.log('📚 学校科目列表:', subjectData.map(s => s.name).join(', '));\n        setSubjects(subjectData);\n      } else {\n        // 如果没有选择学校，显示所有科目\n        const subjectsResponse = await api.get('/admin/subjects');\n        const subjectData = subjectsResponse?.items || subjectsResponse || [];\n        console.log('📚 所有科目数据:', subjectData.length);\n        setSubjects(subjectData);\n      }\n    } catch (error) {\n      console.error('❌ 获取科目失败:', error);\n      // 如果获取科目失败，回退到所有科目\n      try {\n        const subjectsResponse = await api.get('/admin/subjects');\n        const subjectData = subjectsResponse?.items || subjectsResponse || [];\n        setSubjects(subjectData);\n      } catch (fallbackError) {\n        console.error('❌ 获取科目回退方案也失败:', fallbackError);\n      }\n    }\n  };\n\n  // 根据省份获取城市\n  const fetchCities = async (provinceId) => {\n    if (!provinceId) {\n      setCities([]);\n      return;\n    }\n\n    try {\n      console.log('🏙️ 根据省份获取城市:', provinceId);\n      const response = await api.get(`/schools/region/cities?province_id=${provinceId}`);\n      const cityData = response || [];\n      console.log('🏙️ 城市数据:', cityData.length);\n      setCities(cityData);\n    } catch (error) {\n      console.error('❌ 获取城市数据失败:', error);\n      setCities([]);\n    }\n  };\n\n  // 根据城市获取区县\n  const fetchDistricts = async (cityId) => {\n    if (!cityId) {\n      setDistricts([]);\n      return;\n    }\n\n    try {\n      console.log('🏘️ 根据城市获取区县:', cityId);\n      const response = await api.get(`/schools/region/districts?city_id=${cityId}`);\n      const districtData = response || [];\n      console.log('🏘️ 区县数据:', districtData.length);\n      setDistricts(districtData);\n    } catch (error) {\n      console.error('❌ 获取区县数据失败:', error);\n      setDistricts([]);\n    }\n  };\n\n  // 根据筛选条件获取班级\n  const fetchClasses = async (schoolId, grade) => {\n    if (!schoolId) {\n      setClasses([]);\n      return;\n    }\n\n    try {\n      const params = new URLSearchParams();\n      params.append('school_id', schoolId);\n      if (grade) {\n        params.append('grade', grade);\n      }\n\n      const response = await api.get(`/admin/classes?${params.toString()}`);\n      console.log('🏛️ 班级数据:', response?.length);\n      setClasses(response || []);\n    } catch (error) {\n      console.error('❌ 获取班级数据失败:', error);\n      setClasses([]);\n    }\n  };\n\n  // 根据筛选条件获取作业分配\n  const fetchAssignments = async (currentFilters = filters) => {\n    try {\n      setLoading(true);\n      console.log('📝 开始获取作业分配数据...', currentFilters);\n\n      const params = new URLSearchParams();\n\n      // 添加筛选参数\n      if (currentFilters.selectedSchool) {\n        params.append('school_id', currentFilters.selectedSchool);\n      }\n      if (currentFilters.selectedClass) {\n        params.append('class_id', currentFilters.selectedClass);\n      }\n      if (currentFilters.selectedSubject) {\n        params.append('subject_id', currentFilters.selectedSubject);\n      }\n      if (currentFilters.selectedGrade) {\n        params.append('grade', currentFilters.selectedGrade);\n      }\n\n      // 添加地区筛选参数\n      if (currentFilters.selectedProvince) {\n        params.append('province_id', currentFilters.selectedProvince);\n      }\n      if (currentFilters.selectedCity) {\n        params.append('city_id', currentFilters.selectedCity);\n      }\n      if (currentFilters.selectedDistrict) {\n        params.append('district_id', currentFilters.selectedDistrict);\n      }\n      if (currentFilters.searchText) {\n        params.append('search', currentFilters.searchText);\n      }\n      if (currentFilters.dateRange && currentFilters.dateRange.length === 2) {\n        params.append('start_date', currentFilters.dateRange[0].format('YYYY-MM-DD'));\n        params.append('end_date', currentFilters.dateRange[1].format('YYYY-MM-DD'));\n      }\n\n      // 使用系统级作业分配API\n      const response = await api.get(`/system/homework-assignments?${params.toString()}`);\n      console.log('📝 作业分配数据:', response?.length);\n\n      const assignmentsData = response || [];\n      setFilteredAssignments(assignmentsData);\n\n    } catch (error) {\n      console.error('❌ 获取作业分配数据失败:', error);\n      message.error('获取作业数据失败');\n      setFilteredAssignments([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理筛选条件变化\n  const handleFilterChange = async (key, value) => {\n    const newFilters = { ...filters, [key]: value };\n\n    // 级联清除下级筛选\n    if (key === 'selectedProvince') {\n      newFilters.selectedCity = null;\n      newFilters.selectedDistrict = null;\n      newFilters.selectedSchool = null;\n      newFilters.selectedClass = null;\n      setCities([]);\n      setDistricts([]);\n      setClasses([]);\n\n      // 获取该省份的城市\n      if (value) {\n        await fetchCities(value);\n      }\n    }\n\n    if (key === 'selectedCity') {\n      newFilters.selectedDistrict = null;\n      newFilters.selectedSchool = null;\n      newFilters.selectedClass = null;\n      setDistricts([]);\n      setClasses([]);\n\n      // 获取该城市的区县\n      if (value) {\n        await fetchDistricts(value);\n      }\n    }\n\n    if (key === 'selectedDistrict') {\n      newFilters.selectedSchool = null;\n      newFilters.selectedClass = null;\n      setClasses([]);\n    }\n\n    if (key === 'selectedSchool') {\n      newFilters.selectedClass = null;\n      newFilters.selectedSubject = null; // 清除科目选择\n      if (value) {\n        fetchClasses(value, newFilters.selectedGrade);\n        // 根据选择的学校获取对应的科目\n        await fetchSubjectsBySchool(value);\n      } else {\n        setClasses([]);\n        // 如果没有选择学校，显示所有科目\n        await fetchSubjectsBySchool(null);\n      }\n    }\n\n    if (key === 'selectedGrade') {\n      newFilters.selectedClass = null;\n      if (newFilters.selectedSchool) {\n        fetchClasses(newFilters.selectedSchool, value);\n      }\n    }\n\n    setFilters(newFilters);\n    console.log('🔧 筛选条件更新:', newFilters);\n\n    // 实时触发筛选（除了搜索框和日期范围，这些需要手动触发）\n    if (key !== 'searchText' && key !== 'dateRange') {\n      await fetchAssignments(newFilters);\n    }\n  };\n\n  // 应用筛选（主要用于搜索框和日期范围）\n  const applyFilters = () => {\n    fetchAssignments(filters);\n  };\n\n  // 清除所有筛选条件\n  const clearAllFilters = async () => {\n    const emptyFilters = {\n      selectedProvince: null,\n      selectedCity: null,\n      selectedDistrict: null,\n      selectedSchool: null,\n      selectedGrade: null,\n      selectedClass: null,\n      selectedSubject: null,\n      selectedAssignment: null,\n      searchText: '',\n      dateRange: null\n    };\n\n    setFilters(emptyFilters);\n    setCities([]);\n    setDistricts([]);\n    setClasses([]);\n    console.log('🗑️ 已清除所有筛选条件');\n\n    // 重新加载所有数据\n    await fetchAssignments(emptyFilters);\n  };\n\n  // 跳转到系统作业分析页面\n  const handleAnalyzeAssignment = (assignmentId) => {\n    navigate(`/system-homework-analysis/overview?assignmentId=${assignmentId}`);\n  };\n\n  // 获取状态标签\n  const getStatusTag = (status) => {\n    switch (status) {\n      case 'published':\n        return <Tag color=\"green\">已发布</Tag>;\n      case 'completed':\n        return <Tag color=\"blue\">已完成</Tag>;\n      case 'graded':\n        return <Tag color=\"purple\">已批改</Tag>;\n      case 'active':\n        return <Tag color=\"cyan\">进行中</Tag>;\n      default:\n        return <Tag color=\"default\">{status || '未知'}</Tag>;\n    }\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '作业标题',\n      dataIndex: 'title',\n      key: 'title',\n      width: 200,\n      ellipsis: true\n    },\n    {\n      title: '学校',\n      dataIndex: 'school_name',\n      key: 'school_name',\n      width: 120,\n      ellipsis: true\n    },\n    {\n      title: '班级',\n      dataIndex: 'class_name',\n      key: 'class_name',\n      width: 100\n    },\n    {\n      title: '科目',\n      dataIndex: 'subject_name',\n      key: 'subject_name',\n      width: 80\n    },\n    {\n      title: '教师',\n      dataIndex: 'teacher_name',\n      key: 'teacher_name',\n      width: 100\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      width: 120,\n      render: (text) => text ? new Date(text).toLocaleDateString() : '-'\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status) => getStatusTag(status)\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => (\n        <Button\n          type=\"primary\"\n          icon={<BarChartOutlined />}\n          onClick={() => handleAnalyzeAssignment(record.assignment_id)}\n        >\n          分析\n        </Button>\n      )\n    }\n  ];\n\n  // 组件初始化\n  useEffect(() => {\n    const initializeData = async () => {\n      await fetchInitialData();\n      // 初始化时加载所有作业数据\n      await fetchAssignments();\n    };\n\n    initializeData();\n  }, []);\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>\n        <FilterOutlined /> 系统作业分析 - 筛选条件\n      </Title>\n      \n      <Card\n        title=\"筛选条件\"\n        style={{ marginBottom: '24px' }}\n        bodyStyle={{ padding: '16px' }}\n      >\n        <Row gutter={[12, 12]} align=\"bottom\">\n          {/* 第一行：地区筛选 */}\n          <Col xs={12} sm={6} md={4}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>省份：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择省份\"\n              value={filters.selectedProvince}\n              onChange={(value) => handleFilterChange('selectedProvince', value)}\n              allowClear\n              size=\"small\"\n            >\n              {provinces.map(province => (\n                <Option key={province.id} value={province.id}>\n                  {province.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n\n          <Col xs={12} sm={6} md={4}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>城市：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择城市\"\n              value={filters.selectedCity}\n              onChange={(value) => handleFilterChange('selectedCity', value)}\n              disabled={!filters.selectedProvince}\n              allowClear\n              size=\"small\"\n            >\n              {cities.map(city => (\n                <Option key={city.id} value={city.id}>\n                  {city.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n\n          <Col xs={12} sm={6} md={4}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>区县：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择区县\"\n              value={filters.selectedDistrict}\n              onChange={(value) => handleFilterChange('selectedDistrict', value)}\n              disabled={!filters.selectedCity}\n              allowClear\n              size=\"small\"\n            >\n              {districts.map(district => (\n                <Option key={district.id} value={district.id}>\n                  {district.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n\n          {/* 第二行：学校、年级、班级 */}\n          <Col xs={12} sm={8} md={6}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>学校：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择学校\"\n              value={filters.selectedSchool}\n              onChange={(value) => handleFilterChange('selectedSchool', value)}\n              allowClear\n              showSearch\n              size=\"small\"\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {schools\n                .filter(school => {\n                  // 根据选中的省份、城市、区县筛选学校\n                  if (filters.selectedProvince) {\n                    const selectedProvince = provinces.find(p => p.id === filters.selectedProvince)?.name;\n                    if (school.province !== selectedProvince) return false;\n                  }\n                  if (filters.selectedCity) {\n                    const selectedCity = cities.find(c => c.id === filters.selectedCity)?.name;\n                    if (school.city !== selectedCity) return false;\n                  }\n                  if (filters.selectedDistrict) {\n                    const selectedDistrict = districts.find(d => d.id === filters.selectedDistrict)?.name;\n                    if (school.district !== selectedDistrict) return false;\n                  }\n                  return true;\n                })\n                .map(school => (\n                  <Option key={school.id} value={school.id}>\n                    {school.name}\n                  </Option>\n                ))}\n            </Select>\n          </Col>\n\n          <Col xs={12} sm={6} md={4}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>年级：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择年级\"\n              value={filters.selectedGrade}\n              onChange={(value) => handleFilterChange('selectedGrade', value)}\n              allowClear\n              size=\"small\"\n            >\n              {gradeOptions.map(grade => (\n                <Option key={grade.value} value={grade.value}>\n                  {grade.label}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n\n          <Col xs={12} sm={6} md={4}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>班级：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择班级\"\n              value={filters.selectedClass}\n              onChange={(value) => handleFilterChange('selectedClass', value)}\n              disabled={!filters.selectedSchool}\n              allowClear\n              size=\"small\"\n            >\n              {classes.map(cls => (\n                <Option key={cls.id} value={cls.id}>\n                  {cls.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n\n          {/* 第三行：科目、时间范围、搜索 */}\n          <Col xs={12} sm={6} md={4}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>科目：</label>\n            </div>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择科目\"\n              value={filters.selectedSubject}\n              onChange={(value) => handleFilterChange('selectedSubject', value)}\n              allowClear\n              size=\"small\"\n            >\n              {subjects.map(subject => (\n                <Option key={subject.id} value={subject.id}>\n                  {subject.name}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n\n          <Col xs={12} sm={8} md={6}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>时间范围：</label>\n            </div>\n            <RangePicker\n              style={{ width: '100%' }}\n              value={filters.dateRange}\n              onChange={(dates) => handleFilterChange('dateRange', dates)}\n              placeholder={['开始日期', '结束日期']}\n              size=\"small\"\n            />\n          </Col>\n\n          <Col xs={24} sm={8} md={6}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>作业标题搜索：</label>\n            </div>\n            <Input\n              placeholder=\"输入关键词搜索\"\n              value={filters.searchText}\n              onChange={(e) => handleFilterChange('searchText', e.target.value)}\n              onPressEnter={applyFilters}\n              prefix={<SearchOutlined />}\n              size=\"small\"\n            />\n          </Col>\n\n          {/* 操作按钮 */}\n          <Col xs={24} md={6}>\n            <div style={{ marginBottom: '4px' }}>\n              <label style={{ fontSize: '12px', color: '#666' }}>操作：</label>\n            </div>\n            <Space size=\"small\">\n              <Button\n                type=\"primary\"\n                icon={<SearchOutlined />}\n                onClick={applyFilters}\n                loading={loading}\n                size=\"small\"\n              >\n                搜索\n              </Button>\n              <Button\n                icon={<ClearOutlined />}\n                onClick={clearAllFilters}\n                size=\"small\"\n              >\n                清除\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 筛选结果统计 */}\n      <Card title=\"筛选结果\" style={{ marginBottom: '24px' }}>\n        <div style={{ marginBottom: '16px' }}>\n          <Tag color=\"blue\">省份: {filters.selectedProvince ? provinces.find(p => p.id === filters.selectedProvince)?.name : '全部'}</Tag>\n          <Tag color=\"geekblue\">城市: {filters.selectedCity ? cities.find(c => c.id === filters.selectedCity)?.name : '全部'}</Tag>\n          <Tag color=\"purple\">区县: {filters.selectedDistrict ? districts.find(d => d.id === filters.selectedDistrict)?.name : '全部'}</Tag>\n          <Tag color=\"green\">学校: {filters.selectedSchool ? schools.find(s => s.id === filters.selectedSchool)?.name : '全部'}</Tag>\n          <Tag color=\"orange\">年级: {filters.selectedGrade || '全部'}</Tag>\n          <Tag color=\"magenta\">班级: {filters.selectedClass ? classes.find(c => c.id === filters.selectedClass)?.name : '全部'}</Tag>\n          <Tag color=\"cyan\">科目: {filters.selectedSubject ? subjects.find(s => s.id === filters.selectedSubject)?.name : '全部'}</Tag>\n        </div>\n        <div>\n          <strong>作业列表 ({filteredAssignments.length} 个可分析的作业)</strong>\n        </div>\n      </Card>\n\n      {/* 作业列表 */}\n      <Card title=\"作业列表\">\n        <Spin spinning={loading}>\n          {filteredAssignments.length > 0 ? (\n            <Table\n              columns={columns}\n              dataSource={filteredAssignments}\n              rowKey=\"id\"\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,\n                pageSizeOptions: ['10', '20', '50', '100']\n              }}\n              scroll={{ x: 800 }}\n            />\n          ) : (\n            <div style={{ textAlign: 'center', padding: '40px' }}>\n              <p>暂无可分析的作业</p>\n              <p style={{ color: '#999' }}>\n                请调整筛选条件或确保选定范围内有已发布的作业\n              </p>\n            </div>\n          )}\n        </Spin>\n      </Card>\n    </div>\n  );\n};\n\nexport default SystemAssignmentSelector;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,IAAI,QACC,MAAM;AACb,SACEC,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAM;EAAEC;AAAM,CAAC,GAAGd,UAAU;AAC5B,MAAM;EAAEe;AAAO,CAAC,GAAGlB,MAAM;AACzB,MAAM;EAAEmB;AAAY,CAAC,GAAGd,UAAU;AAElC,MAAMe,wBAAwB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,aAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,cAAA;EAC7C,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,GAAGkD,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACvC,MAAM,CAACmD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC;IACrCuD,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE,IAAI;IAClBC,gBAAgB,EAAE,IAAI;IACtBC,cAAc,EAAE,IAAI;IACpBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE,IAAI;IACxBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAED,KAAK,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC,CAC7B;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,qBAAA;MACFhC,UAAU,CAAC,IAAI,CAAC;MAChBiC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;;MAEhC;MACA,MAAM,CAACC,iBAAiB,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC/ExD,GAAG,CAACyD,GAAG,CAAC,2BAA2B,CAAC,EACpCzD,GAAG,CAACyD,GAAG,CAAC,gBAAgB,CAAC,EACzBzD,GAAG,CAACyD,GAAG,CAAC,iBAAiB,CAAC,CAC3B,CAAC;MAEFP,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEM,MAAM,CAAC;MAClDR,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEK,MAAM,CAAC;MAChDR,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,CAAAG,gBAAgB,aAAhBA,gBAAgB,wBAAAL,qBAAA,GAAhBK,gBAAgB,CAAEK,KAAK,cAAAV,qBAAA,uBAAvBA,qBAAA,CAAyBS,MAAM,MAAIJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEI,MAAM,EAAC;;MAEpF;MACAvC,YAAY,CAACiC,iBAAiB,IAAI,EAAE,CAAC;MACrC3B,UAAU,CAAC4B,eAAe,IAAI,EAAE,CAAC;;MAEjC;MACA,MAAMO,WAAW,GAAG,CAAAN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,KAAK,KAAIL,gBAAgB,IAAI,EAAE;MACrEzB,WAAW,CAAC+B,WAAW,CAAC;IAE1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCnE,OAAO,CAACmE,KAAK,CAAC,kBAAkB,CAAC;IACnC,CAAC,SAAS;MACR5C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6C,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEY,QAAQ,CAAC;MAErC,IAAIA,QAAQ,EAAE;QACZ;QACA,MAAMC,QAAQ,GAAG,MAAMhE,GAAG,CAACyD,GAAG,CAAC,8BAA8BM,QAAQ,EAAE,CAAC;QACxE,MAAMH,WAAW,GAAGI,QAAQ,IAAI,EAAE;QAClCd,OAAO,CAACC,GAAG,CAAC,YAAY,EAAES,WAAW,CAACF,MAAM,CAAC;QAC7CR,OAAO,CAACC,GAAG,CAAC,YAAY,EAAES,WAAW,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClEvC,WAAW,CAAC+B,WAAW,CAAC;MAC1B,CAAC,MAAM;QACL;QACA,MAAMN,gBAAgB,GAAG,MAAMtD,GAAG,CAACyD,GAAG,CAAC,iBAAiB,CAAC;QACzD,MAAMG,WAAW,GAAG,CAAAN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,KAAK,KAAIL,gBAAgB,IAAI,EAAE;QACrEJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAES,WAAW,CAACF,MAAM,CAAC;QAC7C7B,WAAW,CAAC+B,WAAW,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACA,IAAI;QACF,MAAMP,gBAAgB,GAAG,MAAMtD,GAAG,CAACyD,GAAG,CAAC,iBAAiB,CAAC;QACzD,MAAMG,WAAW,GAAG,CAAAN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,KAAK,KAAIL,gBAAgB,IAAI,EAAE;QACrEzB,WAAW,CAAC+B,WAAW,CAAC;MAC1B,CAAC,CAAC,OAAOS,aAAa,EAAE;QACtBnB,OAAO,CAACW,KAAK,CAAC,gBAAgB,EAAEQ,aAAa,CAAC;MAChD;IACF;EACF,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG,MAAOC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,EAAE;MACflD,SAAS,CAAC,EAAE,CAAC;MACb;IACF;IAEA,IAAI;MACF6B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoB,UAAU,CAAC;MACxC,MAAMP,QAAQ,GAAG,MAAMhE,GAAG,CAACyD,GAAG,CAAC,sCAAsCc,UAAU,EAAE,CAAC;MAClF,MAAMC,QAAQ,GAAGR,QAAQ,IAAI,EAAE;MAC/Bd,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqB,QAAQ,CAACd,MAAM,CAAC;MACzCrC,SAAS,CAACmD,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCxC,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;;EAED;EACA,MAAMoD,cAAc,GAAG,MAAOC,MAAM,IAAK;IACvC,IAAI,CAACA,MAAM,EAAE;MACXnD,YAAY,CAAC,EAAE,CAAC;MAChB;IACF;IAEA,IAAI;MACF2B,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuB,MAAM,CAAC;MACpC,MAAMV,QAAQ,GAAG,MAAMhE,GAAG,CAACyD,GAAG,CAAC,qCAAqCiB,MAAM,EAAE,CAAC;MAC7E,MAAMC,YAAY,GAAGX,QAAQ,IAAI,EAAE;MACnCd,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwB,YAAY,CAACjB,MAAM,CAAC;MAC7CnC,YAAY,CAACoD,YAAY,CAAC;IAC5B,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtC,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAG,MAAAA,CAAOb,QAAQ,EAAEc,KAAK,KAAK;IAC9C,IAAI,CAACd,QAAQ,EAAE;MACbpC,UAAU,CAAC,EAAE,CAAC;MACd;IACF;IAEA,IAAI;MACF,MAAMmD,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;MACpCD,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEjB,QAAQ,CAAC;MACpC,IAAIc,KAAK,EAAE;QACTC,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAAC;MAC/B;MAEA,MAAMb,QAAQ,GAAG,MAAMhE,GAAG,CAACyD,GAAG,CAAC,kBAAkBqB,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MACrE/B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEa,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEN,MAAM,CAAC;MAC1C/B,UAAU,CAACqC,QAAQ,IAAI,EAAE,CAAC;IAC5B,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnClC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMuD,gBAAgB,GAAG,MAAAA,CAAOC,cAAc,GAAGlD,OAAO,KAAK;IAC3D,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBiC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgC,cAAc,CAAC;MAE/C,MAAML,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;;MAEpC;MACA,IAAII,cAAc,CAAC7C,cAAc,EAAE;QACjCwC,MAAM,CAACE,MAAM,CAAC,WAAW,EAAEG,cAAc,CAAC7C,cAAc,CAAC;MAC3D;MACA,IAAI6C,cAAc,CAAC3C,aAAa,EAAE;QAChCsC,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEG,cAAc,CAAC3C,aAAa,CAAC;MACzD;MACA,IAAI2C,cAAc,CAAC1C,eAAe,EAAE;QAClCqC,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEG,cAAc,CAAC1C,eAAe,CAAC;MAC7D;MACA,IAAI0C,cAAc,CAAC5C,aAAa,EAAE;QAChCuC,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEG,cAAc,CAAC5C,aAAa,CAAC;MACtD;;MAEA;MACA,IAAI4C,cAAc,CAAChD,gBAAgB,EAAE;QACnC2C,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEG,cAAc,CAAChD,gBAAgB,CAAC;MAC/D;MACA,IAAIgD,cAAc,CAAC/C,YAAY,EAAE;QAC/B0C,MAAM,CAACE,MAAM,CAAC,SAAS,EAAEG,cAAc,CAAC/C,YAAY,CAAC;MACvD;MACA,IAAI+C,cAAc,CAAC9C,gBAAgB,EAAE;QACnCyC,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEG,cAAc,CAAC9C,gBAAgB,CAAC;MAC/D;MACA,IAAI8C,cAAc,CAACxC,UAAU,EAAE;QAC7BmC,MAAM,CAACE,MAAM,CAAC,QAAQ,EAAEG,cAAc,CAACxC,UAAU,CAAC;MACpD;MACA,IAAIwC,cAAc,CAACvC,SAAS,IAAIuC,cAAc,CAACvC,SAAS,CAACc,MAAM,KAAK,CAAC,EAAE;QACrEoB,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEG,cAAc,CAACvC,SAAS,CAAC,CAAC,CAAC,CAACwC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC7EN,MAAM,CAACE,MAAM,CAAC,UAAU,EAAEG,cAAc,CAACvC,SAAS,CAAC,CAAC,CAAC,CAACwC,MAAM,CAAC,YAAY,CAAC,CAAC;MAC7E;;MAEA;MACA,MAAMpB,QAAQ,GAAG,MAAMhE,GAAG,CAACyD,GAAG,CAAC,gCAAgCqB,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC;MACnF/B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEa,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEN,MAAM,CAAC;MAE3C,MAAM2B,eAAe,GAAGrB,QAAQ,IAAI,EAAE;MACtChC,sBAAsB,CAACqD,eAAe,CAAC;IAEzC,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCnE,OAAO,CAACmE,KAAK,CAAC,UAAU,CAAC;MACzB7B,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqE,kBAAkB,GAAG,MAAAA,CAAOC,GAAG,EAAEzC,KAAK,KAAK;IAC/C,MAAM0C,UAAU,GAAG;MAAE,GAAGvD,OAAO;MAAE,CAACsD,GAAG,GAAGzC;IAAM,CAAC;;IAE/C;IACA,IAAIyC,GAAG,KAAK,kBAAkB,EAAE;MAC9BC,UAAU,CAACpD,YAAY,GAAG,IAAI;MAC9BoD,UAAU,CAACnD,gBAAgB,GAAG,IAAI;MAClCmD,UAAU,CAAClD,cAAc,GAAG,IAAI;MAChCkD,UAAU,CAAChD,aAAa,GAAG,IAAI;MAC/BnB,SAAS,CAAC,EAAE,CAAC;MACbE,YAAY,CAAC,EAAE,CAAC;MAChBI,UAAU,CAAC,EAAE,CAAC;;MAEd;MACA,IAAImB,KAAK,EAAE;QACT,MAAMwB,WAAW,CAACxB,KAAK,CAAC;MAC1B;IACF;IAEA,IAAIyC,GAAG,KAAK,cAAc,EAAE;MAC1BC,UAAU,CAACnD,gBAAgB,GAAG,IAAI;MAClCmD,UAAU,CAAClD,cAAc,GAAG,IAAI;MAChCkD,UAAU,CAAChD,aAAa,GAAG,IAAI;MAC/BjB,YAAY,CAAC,EAAE,CAAC;MAChBI,UAAU,CAAC,EAAE,CAAC;;MAEd;MACA,IAAImB,KAAK,EAAE;QACT,MAAM2B,cAAc,CAAC3B,KAAK,CAAC;MAC7B;IACF;IAEA,IAAIyC,GAAG,KAAK,kBAAkB,EAAE;MAC9BC,UAAU,CAAClD,cAAc,GAAG,IAAI;MAChCkD,UAAU,CAAChD,aAAa,GAAG,IAAI;MAC/Bb,UAAU,CAAC,EAAE,CAAC;IAChB;IAEA,IAAI4D,GAAG,KAAK,gBAAgB,EAAE;MAC5BC,UAAU,CAAChD,aAAa,GAAG,IAAI;MAC/BgD,UAAU,CAAC/C,eAAe,GAAG,IAAI,CAAC,CAAC;MACnC,IAAIK,KAAK,EAAE;QACT8B,YAAY,CAAC9B,KAAK,EAAE0C,UAAU,CAACjD,aAAa,CAAC;QAC7C;QACA,MAAMuB,qBAAqB,CAAChB,KAAK,CAAC;MACpC,CAAC,MAAM;QACLnB,UAAU,CAAC,EAAE,CAAC;QACd;QACA,MAAMmC,qBAAqB,CAAC,IAAI,CAAC;MACnC;IACF;IAEA,IAAIyB,GAAG,KAAK,eAAe,EAAE;MAC3BC,UAAU,CAAChD,aAAa,GAAG,IAAI;MAC/B,IAAIgD,UAAU,CAAClD,cAAc,EAAE;QAC7BsC,YAAY,CAACY,UAAU,CAAClD,cAAc,EAAEQ,KAAK,CAAC;MAChD;IACF;IAEAZ,UAAU,CAACsD,UAAU,CAAC;IACtBtC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqC,UAAU,CAAC;;IAErC;IACA,IAAID,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW,EAAE;MAC/C,MAAML,gBAAgB,CAACM,UAAU,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBP,gBAAgB,CAACjD,OAAO,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,MAAMC,YAAY,GAAG;MACnBxD,gBAAgB,EAAE,IAAI;MACtBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,IAAI;MACtBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,IAAI;MACnBC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE;IACb,CAAC;IAEDV,UAAU,CAACyD,YAAY,CAAC;IACxBtE,SAAS,CAAC,EAAE,CAAC;IACbE,YAAY,CAAC,EAAE,CAAC;IAChBI,UAAU,CAAC,EAAE,CAAC;IACduB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;IAE5B;IACA,MAAM+B,gBAAgB,CAACS,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIC,YAAY,IAAK;IAChD9E,QAAQ,CAAC,mDAAmD8E,YAAY,EAAE,CAAC;EAC7E,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,MAAM,IAAK;IAC/B,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,oBAAO7F,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACrC,KAAK,WAAW;QACd,oBAAOnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACpC,KAAK,QAAQ;QACX,oBAAOnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,QAAQ;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtC,KAAK,QAAQ;QACX,oBAAOnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACpC;QACE,oBAAOnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,SAAS;UAAAC,QAAA,EAAEF,MAAM,IAAI;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;IACtD;EACF,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBjB,GAAG,EAAE,OAAO;IACZkB,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBjB,GAAG,EAAE,aAAa;IAClBkB,KAAK,EAAE,GAAG;IACVC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBjB,GAAG,EAAE,YAAY;IACjBkB,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBjB,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBjB,GAAG,EAAE,cAAc;IACnBkB,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBjB,GAAG,EAAE,YAAY;IACjBkB,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGC,IAAI,IAAKA,IAAI,GAAG,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;EACjE,CAAC,EACD;IACEP,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBjB,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGZ,MAAM,IAAKD,YAAY,CAACC,MAAM;EACzC,CAAC,EACD;IACEQ,KAAK,EAAE,IAAI;IACXhB,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE,GAAG;IACVE,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChB9G,OAAA,CAACf,MAAM;MACL8H,IAAI,EAAC,SAAS;MACdC,IAAI,eAAEhH,OAAA,CAACN,gBAAgB;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC3Bc,OAAO,EAAEA,CAAA,KAAMvB,uBAAuB,CAACoB,MAAM,CAACI,aAAa,CAAE;MAAAnB,QAAA,EAC9D;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAEZ,CAAC,CACF;;EAED;EACAxH,SAAS,CAAC,MAAM;IACd,MAAMwI,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMrE,gBAAgB,CAAC,CAAC;MACxB;MACA,MAAMkC,gBAAgB,CAAC,CAAC;IAC1B,CAAC;IAEDmC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEnH,OAAA;IAAKoH,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAtB,QAAA,gBAC9B/F,OAAA,CAACC,KAAK;MAACqH,KAAK,EAAE,CAAE;MAAAvB,QAAA,gBACd/F,OAAA,CAACH,cAAc;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,oEACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERnG,OAAA,CAACnB,IAAI;MACHwH,KAAK,EAAC,0BAAM;MACZe,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAChCC,SAAS,EAAE;QAAEH,OAAO,EAAE;MAAO,CAAE;MAAAtB,QAAA,eAE/B/F,OAAA,CAAClB,GAAG;QAAC2I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAA3B,QAAA,gBAEnC/F,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACE,gBAAiB;YAChC+F,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,kBAAkB,EAAExC,KAAK,CAAE;YACnEqF,UAAU;YACVC,IAAI,EAAC,OAAO;YAAAnC,QAAA,EAEX/E,SAAS,CAAC+C,GAAG,CAACoE,QAAQ,iBACrBnI,OAAA,CAACE,MAAM;cAAmB0C,KAAK,EAAEuF,QAAQ,CAACC,EAAG;cAAArC,QAAA,EAC1CoC,QAAQ,CAAClE;YAAI,GADHkE,QAAQ,CAACC,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACG,YAAa;YAC5B8F,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,cAAc,EAAExC,KAAK,CAAE;YAC/DyF,QAAQ,EAAE,CAACtG,OAAO,CAACE,gBAAiB;YACpCgG,UAAU;YACVC,IAAI,EAAC,OAAO;YAAAnC,QAAA,EAEX7E,MAAM,CAAC6C,GAAG,CAACuE,IAAI,iBACdtI,OAAA,CAACE,MAAM;cAAe0C,KAAK,EAAE0F,IAAI,CAACF,EAAG;cAAArC,QAAA,EAClCuC,IAAI,CAACrE;YAAI,GADCqE,IAAI,CAACF,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACI,gBAAiB;YAChC6F,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,kBAAkB,EAAExC,KAAK,CAAE;YACnEyF,QAAQ,EAAE,CAACtG,OAAO,CAACG,YAAa;YAChC+F,UAAU;YACVC,IAAI,EAAC,OAAO;YAAAnC,QAAA,EAEX3E,SAAS,CAAC2C,GAAG,CAACwE,QAAQ,iBACrBvI,OAAA,CAACE,MAAM;cAAmB0C,KAAK,EAAE2F,QAAQ,CAACH,EAAG;cAAArC,QAAA,EAC1CwC,QAAQ,CAACtE;YAAI,GADHsE,QAAQ,CAACH,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACK,cAAe;YAC9B4F,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,gBAAgB,EAAExC,KAAK,CAAE;YACjEqF,UAAU;YACVO,UAAU;YACVN,IAAI,EAAC,OAAO;YACZO,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAAC5C,QAAQ,CAAC6C,WAAW,CAAC,CAAC,CAACC,OAAO,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,CAC/D;YAAA7C,QAAA,EAEAzE,OAAO,CACLwH,MAAM,CAACC,MAAM,IAAI;cAChB;cACA,IAAIhH,OAAO,CAACE,gBAAgB,EAAE;gBAAA,IAAA+G,eAAA;gBAC5B,MAAM/G,gBAAgB,IAAA+G,eAAA,GAAGhI,SAAS,CAACiI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKrG,OAAO,CAACE,gBAAgB,CAAC,cAAA+G,eAAA,uBAAtDA,eAAA,CAAwD/E,IAAI;gBACrF,IAAI8E,MAAM,CAACZ,QAAQ,KAAKlG,gBAAgB,EAAE,OAAO,KAAK;cACxD;cACA,IAAIF,OAAO,CAACG,YAAY,EAAE;gBAAA,IAAAiH,YAAA;gBACxB,MAAMjH,YAAY,IAAAiH,YAAA,GAAGjI,MAAM,CAAC+H,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKrG,OAAO,CAACG,YAAY,CAAC,cAAAiH,YAAA,uBAA/CA,YAAA,CAAiDlF,IAAI;gBAC1E,IAAI8E,MAAM,CAACT,IAAI,KAAKpG,YAAY,EAAE,OAAO,KAAK;cAChD;cACA,IAAIH,OAAO,CAACI,gBAAgB,EAAE;gBAAA,IAAAkH,eAAA;gBAC5B,MAAMlH,gBAAgB,IAAAkH,eAAA,GAAGjI,SAAS,CAAC6H,IAAI,CAACK,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKrG,OAAO,CAACI,gBAAgB,CAAC,cAAAkH,eAAA,uBAAtDA,eAAA,CAAwDpF,IAAI;gBACrF,IAAI8E,MAAM,CAACR,QAAQ,KAAKpG,gBAAgB,EAAE,OAAO,KAAK;cACxD;cACA,OAAO,IAAI;YACb,CAAC,CAAC,CACD4B,GAAG,CAACgF,MAAM,iBACT/I,OAAA,CAACE,MAAM;cAAiB0C,KAAK,EAAEmG,MAAM,CAACX,EAAG;cAAArC,QAAA,EACtCgD,MAAM,CAAC9E;YAAI,GADD8E,MAAM,CAACX,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACM,aAAc;YAC7B2F,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,eAAe,EAAExC,KAAK,CAAE;YAChEqF,UAAU;YACVC,IAAI,EAAC,OAAO;YAAAnC,QAAA,EAEXpD,YAAY,CAACoB,GAAG,CAACY,KAAK,iBACrB3E,OAAA,CAACE,MAAM;cAAmB0C,KAAK,EAAE+B,KAAK,CAAC/B,KAAM;cAAAmD,QAAA,EAC1CpB,KAAK,CAAC9B;YAAK,GADD8B,KAAK,CAAC/B,KAAK;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACO,aAAc;YAC7B0F,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,eAAe,EAAExC,KAAK,CAAE;YAChEyF,QAAQ,EAAE,CAACtG,OAAO,CAACK,cAAe;YAClC6F,UAAU;YACVC,IAAI,EAAC,OAAO;YAAAnC,QAAA,EAEXvE,OAAO,CAACuC,GAAG,CAACwF,GAAG,iBACdvJ,OAAA,CAACE,MAAM;cAAc0C,KAAK,EAAE2G,GAAG,CAACnB,EAAG;cAAArC,QAAA,EAChCwD,GAAG,CAACtF;YAAI,GADEsF,GAAG,CAACnB,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAAChB,MAAM;YACLoI,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzBwB,WAAW,EAAC,0BAAM;YAClBnF,KAAK,EAAEb,OAAO,CAACQ,eAAgB;YAC/ByF,QAAQ,EAAGpF,KAAK,IAAKwC,kBAAkB,CAAC,iBAAiB,EAAExC,KAAK,CAAE;YAClEqF,UAAU;YACVC,IAAI,EAAC,OAAO;YAAAnC,QAAA,EAEXrE,QAAQ,CAACqC,GAAG,CAACyF,OAAO,iBACnBxJ,OAAA,CAACE,MAAM;cAAkB0C,KAAK,EAAE4G,OAAO,CAACpB,EAAG;cAAArC,QAAA,EACxCyD,OAAO,CAACvF;YAAI,GADFuF,OAAO,CAACpB,EAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNnG,OAAA,CAACG,WAAW;YACViH,KAAK,EAAE;cAAEb,KAAK,EAAE;YAAO,CAAE;YACzB3D,KAAK,EAAEb,OAAO,CAACW,SAAU;YACzBsF,QAAQ,EAAGyB,KAAK,IAAKrE,kBAAkB,CAAC,WAAW,EAAEqE,KAAK,CAAE;YAC5D1B,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAE;YAC9BG,IAAI,EAAC;UAAO;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACxB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNnG,OAAA,CAACZ,KAAK;YACJ2I,WAAW,EAAC,4CAAS;YACrBnF,KAAK,EAAEb,OAAO,CAACU,UAAW;YAC1BuF,QAAQ,EAAG0B,CAAC,IAAKtE,kBAAkB,CAAC,YAAY,EAAEsE,CAAC,CAACC,MAAM,CAAC/G,KAAK,CAAE;YAClEgH,YAAY,EAAErE,YAAa;YAC3BsE,MAAM,eAAE7J,OAAA,CAACJ,cAAc;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3B+B,IAAI,EAAC;UAAO;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnG,OAAA,CAACjB,GAAG;UAAC4I,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAA9B,QAAA,gBACjB/F,OAAA;YAAKoH,KAAK,EAAE;cAAEG,YAAY,EAAE;YAAM,CAAE;YAAAxB,QAAA,eAClC/F,OAAA;cAAOoH,KAAK,EAAE;gBAAEU,QAAQ,EAAE,MAAM;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNnG,OAAA,CAACV,KAAK;YAAC4I,IAAI,EAAC,OAAO;YAAAnC,QAAA,gBACjB/F,OAAA,CAACf,MAAM;cACL8H,IAAI,EAAC,SAAS;cACdC,IAAI,eAAEhH,OAAA,CAACJ,cAAc;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBc,OAAO,EAAE1B,YAAa;cACtBzE,OAAO,EAAEA,OAAQ;cACjBoH,IAAI,EAAC,OAAO;cAAAnC,QAAA,EACb;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAACf,MAAM;cACL+H,IAAI,eAAEhH,OAAA,CAACL,aAAa;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBc,OAAO,EAAEzB,eAAgB;cACzB0C,IAAI,EAAC,OAAO;cAAAnC,QAAA,EACb;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnG,OAAA,CAACnB,IAAI;MAACwH,KAAK,EAAC,0BAAM;MAACe,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAxB,QAAA,gBACjD/F,OAAA;QAAKoH,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAO,CAAE;QAAAxB,QAAA,gBACnC/F,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,MAAM;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACE,gBAAgB,IAAA1B,gBAAA,GAAGS,SAAS,CAACiI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKrG,OAAO,CAACE,gBAAgB,CAAC,cAAA1B,gBAAA,uBAAtDA,gBAAA,CAAwD0D,IAAI,GAAG,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5HnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,UAAU;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACG,YAAY,IAAA1B,aAAA,GAAGU,MAAM,CAAC+H,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKrG,OAAO,CAACG,YAAY,CAAC,cAAA1B,aAAA,uBAA/CA,aAAA,CAAiDyD,IAAI,GAAG,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrHnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,QAAQ;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACI,gBAAgB,IAAA1B,gBAAA,GAAGW,SAAS,CAAC6H,IAAI,CAACK,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKrG,OAAO,CAACI,gBAAgB,CAAC,cAAA1B,gBAAA,uBAAtDA,gBAAA,CAAwDwD,IAAI,GAAG,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9HnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,OAAO;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACK,cAAc,IAAA1B,aAAA,GAAGY,OAAO,CAAC2H,IAAI,CAACjF,CAAC,IAAIA,CAAC,CAACoE,EAAE,KAAKrG,OAAO,CAACK,cAAc,CAAC,cAAA1B,aAAA,uBAAlDA,aAAA,CAAoDuD,IAAI,GAAG,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvHnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,QAAQ;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACM,aAAa,IAAI,IAAI;QAAA;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7DnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,SAAS;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACO,aAAa,IAAA3B,aAAA,GAAGa,OAAO,CAACyH,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKrG,OAAO,CAACO,aAAa,CAAC,cAAA3B,aAAA,uBAAjDA,aAAA,CAAmDsD,IAAI,GAAG,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvHnG,OAAA,CAACT,GAAG;UAACuG,KAAK,EAAC,MAAM;UAAAC,QAAA,GAAC,gBAAI,EAAChE,OAAO,CAACQ,eAAe,IAAA3B,cAAA,GAAGc,QAAQ,CAACuH,IAAI,CAACjF,CAAC,IAAIA,CAAC,CAACoE,EAAE,KAAKrG,OAAO,CAACQ,eAAe,CAAC,cAAA3B,cAAA,uBAApDA,cAAA,CAAsDqD,IAAI,GAAG,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtH,CAAC,eACNnG,OAAA;QAAA+F,QAAA,eACE/F,OAAA;UAAA+F,QAAA,GAAQ,4BAAM,EAAClE,mBAAmB,CAAC2B,MAAM,EAAC,8CAAS;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnG,OAAA,CAACnB,IAAI;MAACwH,KAAK,EAAC,0BAAM;MAAAN,QAAA,eAChB/F,OAAA,CAACP,IAAI;QAACqK,QAAQ,EAAEhJ,OAAQ;QAAAiF,QAAA,EACrBlE,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,gBAC7BxD,OAAA,CAACd,KAAK;UACJkH,OAAO,EAAEA,OAAQ;UACjB2D,UAAU,EAAElI,mBAAoB;UAChCmI,MAAM,EAAC,IAAI;UACXC,UAAU,EAAE;YACVC,QAAQ,EAAE,EAAE;YACZC,eAAe,EAAE,IAAI;YACrBC,eAAe,EAAE,IAAI;YACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KAAK,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK,MAAM;YACzEE,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;UAC3C,CAAE;UACFC,MAAM,EAAE;YAAEC,CAAC,EAAE;UAAI;QAAE;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,gBAEFnG,OAAA;UAAKoH,KAAK,EAAE;YAAEuD,SAAS,EAAE,QAAQ;YAAEtD,OAAO,EAAE;UAAO,CAAE;UAAAtB,QAAA,gBACnD/F,OAAA;YAAA+F,QAAA,EAAG;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACfnG,OAAA;YAAGoH,KAAK,EAAE;cAAEtB,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAE7B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7F,EAAA,CArrBIF,wBAAwB;EAAA,QACXxB,WAAW;AAAA;AAAAgM,EAAA,GADxBxK,wBAAwB;AAurB9B,eAAeA,wBAAwB;AAAC,IAAAwK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}