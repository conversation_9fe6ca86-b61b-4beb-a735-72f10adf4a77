{"ast": null, "code": "const t = t => \"object\" == typeof t && null != t && 1 === t.nodeType,\n  e = (t, e) => (!e || \"hidden\" !== t) && \"visible\" !== t && \"clip\" !== t,\n  n = (t, n) => {\n    if (t.clientHeight < t.scrollHeight || t.clientWidth < t.scrollWidth) {\n      const o = getComputedStyle(t, null);\n      return e(o.overflowY, n) || e(o.overflowX, n) || (t => {\n        const e = (t => {\n          if (!t.ownerDocument || !t.ownerDocument.defaultView) return null;\n          try {\n            return t.ownerDocument.defaultView.frameElement;\n          } catch (t) {\n            return null;\n          }\n        })(t);\n        return !!e && (e.clientHeight < t.scrollHeight || e.clientWidth < t.scrollWidth);\n      })(t);\n    }\n    return !1;\n  },\n  o = (t, e, n, o, l, r, i, s) => r < t && i > e || r > t && i < e ? 0 : r <= t && s <= n || i >= e && s >= n ? r - t - o : i > e && s < n || r < t && s > n ? i - e + l : 0,\n  l = t => {\n    const e = t.parentElement;\n    return null == e ? t.getRootNode().host || null : e;\n  },\n  r = (e, r) => {\n    var i, s, d, h;\n    if (\"undefined\" == typeof document) return [];\n    const {\n        scrollMode: c,\n        block: f,\n        inline: u,\n        boundary: a,\n        skipOverflowHiddenElements: g\n      } = r,\n      p = \"function\" == typeof a ? a : t => t !== a;\n    if (!t(e)) throw new TypeError(\"Invalid target\");\n    const m = document.scrollingElement || document.documentElement,\n      w = [];\n    let W = e;\n    for (; t(W) && p(W);) {\n      if (W = l(W), W === m) {\n        w.push(W);\n        break;\n      }\n      null != W && W === document.body && n(W) && !n(document.documentElement) || null != W && n(W, g) && w.push(W);\n    }\n    const b = null != (s = null == (i = window.visualViewport) ? void 0 : i.width) ? s : innerWidth,\n      H = null != (h = null == (d = window.visualViewport) ? void 0 : d.height) ? h : innerHeight,\n      {\n        scrollX: y,\n        scrollY: M\n      } = window,\n      {\n        height: v,\n        width: E,\n        top: x,\n        right: C,\n        bottom: I,\n        left: R\n      } = e.getBoundingClientRect(),\n      {\n        top: T,\n        right: B,\n        bottom: F,\n        left: V\n      } = (t => {\n        const e = window.getComputedStyle(t);\n        return {\n          top: parseFloat(e.scrollMarginTop) || 0,\n          right: parseFloat(e.scrollMarginRight) || 0,\n          bottom: parseFloat(e.scrollMarginBottom) || 0,\n          left: parseFloat(e.scrollMarginLeft) || 0\n        };\n      })(e);\n    let k = \"start\" === f || \"nearest\" === f ? x - T : \"end\" === f ? I + F : x + v / 2 - T + F,\n      D = \"center\" === u ? R + E / 2 - V + B : \"end\" === u ? C + B : R - V;\n    const L = [];\n    for (let t = 0; t < w.length; t++) {\n      const e = w[t],\n        {\n          height: l,\n          width: r,\n          top: i,\n          right: s,\n          bottom: d,\n          left: h\n        } = e.getBoundingClientRect();\n      if (\"if-needed\" === c && x >= 0 && R >= 0 && I <= H && C <= b && (e === m && !n(e) || x >= i && I <= d && R >= h && C <= s)) return L;\n      const a = getComputedStyle(e),\n        g = parseInt(a.borderLeftWidth, 10),\n        p = parseInt(a.borderTopWidth, 10),\n        W = parseInt(a.borderRightWidth, 10),\n        T = parseInt(a.borderBottomWidth, 10);\n      let B = 0,\n        F = 0;\n      const V = \"offsetWidth\" in e ? e.offsetWidth - e.clientWidth - g - W : 0,\n        S = \"offsetHeight\" in e ? e.offsetHeight - e.clientHeight - p - T : 0,\n        X = \"offsetWidth\" in e ? 0 === e.offsetWidth ? 0 : r / e.offsetWidth : 0,\n        Y = \"offsetHeight\" in e ? 0 === e.offsetHeight ? 0 : l / e.offsetHeight : 0;\n      if (m === e) B = \"start\" === f ? k : \"end\" === f ? k - H : \"nearest\" === f ? o(M, M + H, H, p, T, M + k, M + k + v, v) : k - H / 2, F = \"start\" === u ? D : \"center\" === u ? D - b / 2 : \"end\" === u ? D - b : o(y, y + b, b, g, W, y + D, y + D + E, E), B = Math.max(0, B + M), F = Math.max(0, F + y);else {\n        B = \"start\" === f ? k - i - p : \"end\" === f ? k - d + T + S : \"nearest\" === f ? o(i, d, l, p, T + S, k, k + v, v) : k - (i + l / 2) + S / 2, F = \"start\" === u ? D - h - g : \"center\" === u ? D - (h + r / 2) + V / 2 : \"end\" === u ? D - s + W + V : o(h, s, r, g, W + V, D, D + E, E);\n        const {\n          scrollLeft: t,\n          scrollTop: n\n        } = e;\n        B = 0 === Y ? 0 : Math.max(0, Math.min(n + B / Y, e.scrollHeight - l / Y + S)), F = 0 === X ? 0 : Math.max(0, Math.min(t + F / X, e.scrollWidth - r / X + V)), k += n - B, D += t - F;\n      }\n      L.push({\n        el: e,\n        top: B,\n        left: F\n      });\n    }\n    return L;\n  };\nexport { r as compute }; //# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}