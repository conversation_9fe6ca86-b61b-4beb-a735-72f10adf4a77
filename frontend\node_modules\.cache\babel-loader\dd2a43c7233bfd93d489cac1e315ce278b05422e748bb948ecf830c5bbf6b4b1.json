{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genTimelineStyle = token => {\n  const {\n    componentCls,\n    calc\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      margin: 0,\n      padding: 0,\n      listStyle: 'none',\n      [`${componentCls}-item`]: {\n        position: 'relative',\n        margin: 0,\n        paddingBottom: token.itemPaddingBottom,\n        fontSize: token.fontSize,\n        listStyle: 'none',\n        '&-tail': {\n          position: 'absolute',\n          insetBlockStart: token.itemHeadSize,\n          insetInlineStart: calc(calc(token.itemHeadSize).sub(token.tailWidth)).div(2).equal(),\n          height: `calc(100% - ${unit(token.itemHeadSize)})`,\n          borderInlineStart: `${unit(token.tailWidth)} ${token.lineType} ${token.tailColor}`\n        },\n        '&-pending': {\n          [`${componentCls}-item-head`]: {\n            fontSize: token.fontSizeSM,\n            backgroundColor: 'transparent'\n          },\n          [`${componentCls}-item-tail`]: {\n            display: 'none'\n          }\n        },\n        '&-head': {\n          position: 'absolute',\n          width: token.itemHeadSize,\n          height: token.itemHeadSize,\n          backgroundColor: token.dotBg,\n          border: `${unit(token.dotBorderWidth)} ${token.lineType} transparent`,\n          borderRadius: '50%',\n          '&-blue': {\n            color: token.colorPrimary,\n            borderColor: token.colorPrimary\n          },\n          '&-red': {\n            color: token.colorError,\n            borderColor: token.colorError\n          },\n          '&-green': {\n            color: token.colorSuccess,\n            borderColor: token.colorSuccess\n          },\n          '&-gray': {\n            color: token.colorTextDisabled,\n            borderColor: token.colorTextDisabled\n          }\n        },\n        '&-head-custom': {\n          position: 'absolute',\n          insetBlockStart: calc(token.itemHeadSize).div(2).equal(),\n          insetInlineStart: calc(token.itemHeadSize).div(2).equal(),\n          width: 'auto',\n          height: 'auto',\n          marginBlockStart: 0,\n          paddingBlock: token.customHeadPaddingVertical,\n          lineHeight: 1,\n          textAlign: 'center',\n          border: 0,\n          borderRadius: 0,\n          transform: 'translate(-50%, -50%)'\n        },\n        '&-content': {\n          position: 'relative',\n          insetBlockStart: calc(calc(token.fontSize).mul(token.lineHeight).sub(token.fontSize)).mul(-1).add(token.lineWidth).equal(),\n          marginInlineStart: calc(token.margin).add(token.itemHeadSize).equal(),\n          marginInlineEnd: 0,\n          marginBlockStart: 0,\n          marginBlockEnd: 0,\n          wordBreak: 'break-word'\n        },\n        '&-last': {\n          [`> ${componentCls}-item-tail`]: {\n            display: 'none'\n          },\n          [`> ${componentCls}-item-content`]: {\n            minHeight: calc(token.controlHeightLG).mul(1.2).equal()\n          }\n        }\n      },\n      [`&${componentCls}-alternate,\n        &${componentCls}-right,\n        &${componentCls}-label`]: {\n        [`${componentCls}-item`]: {\n          '&-tail, &-head, &-head-custom': {\n            insetInlineStart: '50%'\n          },\n          '&-head': {\n            marginInlineStart: calc(token.marginXXS).mul(-1).equal(),\n            '&-custom': {\n              marginInlineStart: calc(token.tailWidth).div(2).equal()\n            }\n          },\n          '&-left': {\n            [`${componentCls}-item-content`]: {\n              insetInlineStart: `calc(50% - ${unit(token.marginXXS)})`,\n              width: `calc(50% - ${unit(token.marginSM)})`,\n              textAlign: 'start'\n            }\n          },\n          '&-right': {\n            [`${componentCls}-item-content`]: {\n              width: `calc(50% - ${unit(token.marginSM)})`,\n              margin: 0,\n              textAlign: 'end'\n            }\n          }\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`${componentCls}-item-right`]: {\n          [`${componentCls}-item-tail,\n            ${componentCls}-item-head,\n            ${componentCls}-item-head-custom`]: {\n            insetInlineStart: `calc(100% - ${unit(calc(calc(token.itemHeadSize).add(token.tailWidth)).div(2).equal())})`\n          },\n          [`${componentCls}-item-content`]: {\n            width: `calc(100% - ${unit(calc(token.itemHeadSize).add(token.marginXS).equal())})`\n          }\n        }\n      },\n      [`&${componentCls}-pending\n        ${componentCls}-item-last\n        ${componentCls}-item-tail`]: {\n        display: 'block',\n        height: `calc(100% - ${unit(token.margin)})`,\n        borderInlineStart: `${unit(token.tailWidth)} dotted ${token.tailColor}`\n      },\n      [`&${componentCls}-reverse\n        ${componentCls}-item-last\n        ${componentCls}-item-tail`]: {\n        display: 'none'\n      },\n      [`&${componentCls}-reverse ${componentCls}-item-pending`]: {\n        [`${componentCls}-item-tail`]: {\n          insetBlockStart: token.margin,\n          display: 'block',\n          height: `calc(100% - ${unit(token.margin)})`,\n          borderInlineStart: `${unit(token.tailWidth)} dotted ${token.tailColor}`\n        },\n        [`${componentCls}-item-content`]: {\n          minHeight: calc(token.controlHeightLG).mul(1.2).equal()\n        }\n      },\n      [`&${componentCls}-label`]: {\n        [`${componentCls}-item-label`]: {\n          position: 'absolute',\n          insetBlockStart: calc(calc(token.fontSize).mul(token.lineHeight).sub(token.fontSize)).mul(-1).add(token.tailWidth).equal(),\n          width: `calc(50% - ${unit(token.marginSM)})`,\n          textAlign: 'end'\n        },\n        [`${componentCls}-item-right`]: {\n          [`${componentCls}-item-label`]: {\n            insetInlineStart: `calc(50% + ${unit(token.marginSM)})`,\n            width: `calc(50% - ${unit(token.marginSM)})`,\n            textAlign: 'start'\n          }\n        }\n      },\n      // ====================== RTL =======================\n      '&-rtl': {\n        direction: 'rtl',\n        [`${componentCls}-item-head-custom`]: {\n          transform: `translate(50%, -50%)`\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  tailColor: token.colorSplit,\n  tailWidth: token.lineWidthBold,\n  dotBorderWidth: token.wireframe ? token.lineWidthBold : token.lineWidth * 3,\n  dotBg: token.colorBgContainer,\n  itemPaddingBottom: token.padding * 1.25\n});\nexport default genStyleHooks('Timeline', token => {\n  const timeLineToken = mergeToken(token, {\n    itemHeadSize: 10,\n    customHeadPaddingVertical: token.paddingXXS,\n    paddingInlineEnd: 2\n  });\n  return [genTimelineStyle(timeLineToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}