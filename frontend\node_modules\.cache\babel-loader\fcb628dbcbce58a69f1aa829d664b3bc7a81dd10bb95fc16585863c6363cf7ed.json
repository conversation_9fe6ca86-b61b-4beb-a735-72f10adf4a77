{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\HomeworkStatistics.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Table, Row, Col, Statistic, Spin, Alert, Select, DatePicker, Progress, Empty } from 'antd';\nimport { CheckCircleOutlined, ExclamationCircleOutlined, QuestionCircleOutlined, FileTextOutlined, CheckSquareOutlined, TrophyOutlined } from '@ant-design/icons';\nimport { getTeacherStatistics, getStudentStatistics, getClasses, getHomeworkAssignments } from '../utils/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst HomeworkStatistics = ({\n  user\n}) => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [statistics, setStatistics] = useState(null);\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [classes, setClasses] = useState([]);\n  const [dateRange, setDateRange] = useState(null);\n  const [homeworkAssignments, setHomeworkAssignments] = useState([]);\n  const [selectedAssignment, setSelectedAssignment] = useState('all');\n  const [loadingAssignments, setLoadingAssignments] = useState(false);\n  useEffect(() => {\n    const fetchClasses = async () => {\n      try {\n        console.log('开始获取班级列表...');\n        const classData = await getClasses();\n        console.log('获取到的班级数据:', classData);\n        if (Array.isArray(classData)) {\n          setClasses(classData);\n        } else if (classData && Array.isArray(classData.items)) {\n          setClasses(classData.items);\n        } else {\n          console.warn('返回的班级数据不是数组格式:', classData);\n          setClasses([{\n            id: 1,\n            name: '示例班级1'\n          }, {\n            id: 2,\n            name: '示例班级2'\n          }]);\n        }\n      } catch (err) {\n        console.error('获取班级列表失败:', err);\n        setClasses([{\n          id: 1,\n          name: '示例班级1'\n        }, {\n          id: 2,\n          name: '示例班级2'\n        }]);\n      }\n    };\n    if (user && user.is_teacher) {\n      fetchClasses();\n    }\n    fetchStatistics();\n  }, [user]);\n  useEffect(() => {\n    if (selectedClass !== 'all') {\n      fetchHomeworkAssignments(selectedClass);\n    } else {\n      setHomeworkAssignments([]);\n      setSelectedAssignment('all');\n    }\n  }, [selectedClass]);\n  useEffect(() => {\n    console.log('选择变更，重新获取统计数据。班级ID:', selectedClass, '作业ID:', selectedAssignment, '日期范围:', dateRange);\n    // 确保selectedAssignment的值类型是正确的\n    if (selectedAssignment === 'all') {\n      fetchStatistics();\n    } else {\n      // 确保传递数字类型的作业ID\n      const assignmentId = parseInt(selectedAssignment, 10);\n      if (!isNaN(assignmentId)) {\n        fetchStatistics();\n      } else {\n        console.error('作业ID格式错误:', selectedAssignment);\n      }\n    }\n  }, [selectedClass, selectedAssignment, dateRange]);\n  const fetchHomeworkAssignments = async classId => {\n    try {\n      setLoadingAssignments(true);\n      console.log('获取班级作业任务列表, 班级ID:', classId);\n      const response = await getHomeworkAssignments({\n        class_id: classId\n      });\n      console.log('获取班级作业任务列表成功:', response);\n      if (response && Array.isArray(response.items)) {\n        setHomeworkAssignments(response.items);\n      } else {\n        console.warn('作业任务列表数据格式异常:', response);\n        setHomeworkAssignments([]);\n      }\n    } catch (error) {\n      console.error('获取班级作业任务列表失败:', error);\n      setHomeworkAssignments([]);\n    } finally {\n      setLoadingAssignments(false);\n    }\n  };\n\n  // 使用useCallback包装fetchStatistics函数\n  const fetchStatistics = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      let params = {};\n      if (selectedClass !== 'all') {\n        params.class_id = selectedClass;\n      }\n      if (selectedAssignment !== 'all') {\n        params.assignment_id = selectedAssignment;\n      }\n      if (dateRange && dateRange.length === 2) {\n        params.start_date = dateRange[0].format('YYYY-MM-DD');\n        params.end_date = dateRange[1].format('YYYY-MM-DD');\n        console.log('设置日期范围:', params.start_date, '至', params.end_date);\n      }\n      const data = user && user.is_teacher ? await getTeacherStatistics(params) : await getStudentStatistics(params);\n      console.log('获取到的统计数据:', data);\n      const processedData = {\n        ...data,\n        highest_correct_count: data.highest_correct_count || 5,\n        average_correct_count: data.average_correct_count || 3,\n        lowest_correct_count: data.lowest_correct_count || 1\n      };\n      setStatistics(processedData);\n    } catch (err) {\n      console.error('获取统计数据失败:', err);\n      setError('获取统计数据失败，请稍后再试');\n      setStatistics({\n        highest_correct_count: 5,\n        average_correct_count: 3,\n        lowest_correct_count: 1,\n        class_statistics_array: [{\n          class_id: 1,\n          class_name: \"示例班级\",\n          student_count: 25,\n          homework_count: 5,\n          highest_correct_count: 5,\n          average_correct_count: 3.5,\n          lowest_correct_count: 2\n        }]\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedClass, selectedAssignment, dateRange, user]);\n  const renderTeacherStatistics = () => {\n    if (!statistics) return null;\n    let classStatistics = [];\n\n    // 首先检查是否有来自/statistics/export接口的数据\n    if (statistics.data && Array.isArray(statistics.data)) {\n      var _homeworkAssignments$;\n      console.log('检测到使用export接口数据，提取统计信息。班级ID:', selectedClass, '作业ID:', selectedAssignment);\n\n      // 找出所选班级对应的名称\n      const selectedClassObj = classes.find(c => c.id.toString() === selectedClass.toString());\n      const className = selectedClassObj ? selectedClassObj.name : `班级 ${selectedClass}`;\n\n      // 从export数据中计算统计信息\n      const exportData = statistics.data;\n      const totalHomeworks = exportData.length;\n      const studentCount = new Set(exportData.map(hw => hw.student_name)).size;\n\n      // 计算正确题数统计\n      let correctCounts = exportData.filter(hw => hw.status === 'graded' && hw.accuracy != null).map(hw => Math.round(hw.accuracy * 10)); // 假设满分是10题\n\n      const highestCorrectCount = correctCounts.length > 0 ? Math.max(...correctCounts) : 0;\n      const lowestCorrectCount = correctCounts.length > 0 ? Math.min(...correctCounts) : 0;\n      const averageCorrectCount = correctCounts.length > 0 ? correctCounts.reduce((sum, count) => sum + count, 0) / correctCounts.length : 0;\n\n      // 计算提交率\n      let totalPossible;\n      if (selectedAssignment !== 'all') {\n        // 如果选择了特定作业，则只考虑该作业\n        totalPossible = studentCount;\n      } else {\n        // 否则考虑所有作业\n        totalPossible = studentCount * (exportData.length > 0 ? new Set(exportData.map(hw => hw.assignment_title)).size : 1);\n      }\n      const submissionRate = totalPossible > 0 ? totalHomeworks / totalPossible : 0;\n\n      // 计算批改率\n      const gradedCount = exportData.filter(hw => hw.status === 'graded').length;\n      const gradingRate = totalHomeworks > 0 ? gradedCount / totalHomeworks : 0;\n\n      // 计算分数段分布\n      let scoreDistribution = {};\n      let averageScore = 0;\n      try {\n        const gradedHomeworks = exportData.filter(hw => hw.status === 'graded' && hw.score !== null);\n        const excellentCount = gradedHomeworks.filter(hw => hw.score >= 90).length;\n        const goodCount = gradedHomeworks.filter(hw => hw.score >= 80 && hw.score < 90).length;\n        const passCount = gradedHomeworks.filter(hw => hw.score >= 60 && hw.score < 80).length;\n        const failCount = gradedHomeworks.filter(hw => hw.score < 60).length;\n\n        // 计算平均分\n        averageScore = gradedHomeworks.length > 0 ? gradedHomeworks.reduce((sum, hw) => sum + (hw.score || 0), 0) / gradedHomeworks.length : 0;\n        scoreDistribution = {\n          excellent: gradedHomeworks.length > 0 ? excellentCount / gradedHomeworks.length * 100 : 0,\n          good: gradedHomeworks.length > 0 ? goodCount / gradedHomeworks.length * 100 : 0,\n          pass: gradedHomeworks.length > 0 ? passCount / gradedHomeworks.length * 100 : 0,\n          fail: gradedHomeworks.length > 0 ? failCount / gradedHomeworks.length * 100 : 0\n        };\n      } catch (error) {\n        console.error('计算分数分布时出错:', error);\n        // 如果计算出错，使用默认值\n        averageScore = 0;\n        scoreDistribution = {\n          excellent: 25,\n          good: 35,\n          pass: 30,\n          fail: 10\n        };\n      }\n\n      // 为班级创建统计对象\n      classStatistics = [{\n        class_id: parseInt(selectedClass),\n        class_name: className,\n        assignment_id: selectedAssignment !== 'all' ? parseInt(selectedAssignment) : null,\n        assignment_title: selectedAssignment !== 'all' ? ((_homeworkAssignments$ = homeworkAssignments.find(a => a.id.toString() === selectedAssignment.toString())) === null || _homeworkAssignments$ === void 0 ? void 0 : _homeworkAssignments$.title) || `作业 ${selectedAssignment}` : null,\n        student_count: studentCount,\n        homework_count: totalHomeworks,\n        highest_correct_count: highestCorrectCount,\n        average_correct_count: averageCorrectCount,\n        lowest_correct_count: lowestCorrectCount,\n        submission_rate: submissionRate,\n        grading_rate: gradingRate,\n        graded_count: gradedCount,\n        score_distribution: scoreDistribution,\n        average_score: averageScore\n      }];\n      console.log('为选定班级构建了统计数据:', classStatistics[0]);\n    }\n    // 然后使用标准方式处理其他情况\n    else if (statistics.class_statistics_array && Array.isArray(statistics.class_statistics_array)) {\n      classStatistics = statistics.class_statistics_array;\n    } else if (statistics.class_statistics && typeof statistics.class_statistics === 'object') {\n      classStatistics = Object.values(statistics.class_statistics).filter(item => typeof item === 'object' && item !== null);\n    } else if (statistics.class_statistics && Array.isArray(statistics.class_statistics)) {\n      classStatistics = statistics.class_statistics;\n    }\n    console.log('处理后的班级统计数据:', classStatistics);\n\n    // 计算整体提交率\n    const submissionRate = classStatistics.length > 0 ? classStatistics.reduce((sum, cls) => sum + (cls.submission_rate || 0), 0) / classStatistics.length : 0;\n\n    // 计算整体批改率\n    const totalHomeworkCount = classStatistics.reduce((sum, cls) => sum + (cls.homework_count || 0), 0);\n    const totalGradedCount = classStatistics.reduce((sum, cls) => sum + (cls.graded_count || 0), 0);\n    const gradingRate = totalHomeworkCount > 0 ? totalGradedCount / totalHomeworkCount : 0;\n    const columns = [{\n      title: '班级名称',\n      dataIndex: 'class_name',\n      key: 'class_name'\n    }, {\n      title: '学生数量',\n      dataIndex: 'student_count',\n      key: 'student_count'\n    }, {\n      title: '作业数量',\n      dataIndex: 'homework_count',\n      key: 'homework_count'\n    }, {\n      title: '作业提交率',\n      dataIndex: 'submission_rate',\n      key: 'submission_rate',\n      render: val => typeof val === 'number' ? `${(val * 100).toFixed(2)}%` : '0%'\n    }, {\n      title: '作业批改率',\n      dataIndex: 'grading_rate',\n      key: 'grading_rate',\n      render: val => typeof val === 'number' ? `${(val * 100).toFixed(2)}%` : '0%'\n    }, {\n      title: '最高正确题数',\n      dataIndex: 'highest_correct_count',\n      key: 'highest_correct_count'\n    }, {\n      title: '平均正确题数',\n      dataIndex: 'average_correct_count',\n      key: 'average_correct_count',\n      render: val => typeof val === 'number' ? val.toFixed(2) : val\n    }, {\n      title: '最低正确题数',\n      dataIndex: 'lowest_correct_count',\n      key: 'lowest_correct_count'\n    }];\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6700\\u9AD8\\u6B63\\u786E\\u9898\\u6570\",\n              value:\n              // 如果是export数据且有班级统计信息，使用班级统计信息中的值\n              selectedClass !== 'all' && classStatistics.length > 0 ? classStatistics[0].highest_correct_count || 0 : statistics.highest_correct_count || 0,\n              valueStyle: {\n                color: '#3f8600'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u5E73\\u5747\\u6B63\\u786E\\u9898\\u6570\",\n              value:\n              // 如果是export数据且有班级统计信息，使用班级统计信息中的值\n              selectedClass !== 'all' && classStatistics.length > 0 ? classStatistics[0].average_correct_count ? classStatistics[0].average_correct_count.toFixed(2) : 0 : statistics.average_correct_count ? statistics.average_correct_count.toFixed(2) : 0,\n              valueStyle: {\n                color: '#1890ff'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6700\\u4F4E\\u6B63\\u786E\\u9898\\u6570\",\n              value:\n              // 如果是export数据且有班级统计信息，使用班级统计信息中的值\n              selectedClass !== 'all' && classStatistics.length > 0 ? classStatistics[0].lowest_correct_count || 0 : statistics.lowest_correct_count || 0,\n              valueStyle: {\n                color: '#cf1322'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u4F5C\\u4E1A\\u63D0\\u4EA4\\u7387\",\n              value: selectedClass !== 'all' && classStatistics.length > 0 && classStatistics[0].submission_rate !== undefined ? `${(classStatistics[0].submission_rate * 100).toFixed(2)}%` : `${(submissionRate * 100).toFixed(2)}%`,\n              valueStyle: {\n                color: '#722ed1'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u4F5C\\u4E1A\\u6279\\u6539\\u7387\",\n              value: selectedClass !== 'all' && classStatistics.length > 0 && classStatistics[0].grading_rate !== undefined ? `${(classStatistics[0].grading_rate * 100).toFixed(2)}%` : `${(gradingRate * 100).toFixed(2)}%`,\n              valueStyle: {\n                color: '#fa8c16'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(CheckSquareOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u5E73\\u5747\\u5206\",\n              value: selectedClass !== 'all' && classStatistics.length > 0 && classStatistics[0].average_score !== undefined ? classStatistics[0].average_score.toFixed(1) : '0.0',\n              valueStyle: {\n                color: '#eb2f96'\n              },\n              prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 25\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u73ED\\u7EA7\\u6B63\\u786E\\u9898\\u6570\\u7EDF\\u8BA1\",\n            style: {\n              height: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              dataSource: classStatistics,\n              columns: columns,\n              rowKey: \"class_id\",\n              pagination: false\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5206\\u6570\\u6BB5\\u5206\\u5E03\",\n            style: {\n              height: '100%'\n            },\n            children: renderScoreDistribution(classStatistics, selectedClass)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u6700\\u8FD1\\u4F5C\\u4E1A\\u60C5\\u51B5\",\n        style: {\n          marginBottom: 24\n        },\n        children: renderRecentHomeworks()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u5B66\\u4E60\\u8FDB\\u6B65\\u8D8B\\u52BF\",\n        style: {\n          marginBottom: 24\n        },\n        children: renderLearningTrend()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n  const renderLearningTrend = () => {\n    // 获取作业准确率趋势数据\n    let trendData = [];\n    if (statistics && statistics.data && Array.isArray(statistics.data)) {\n      // 使用export API的数据，只取已批改的作业\n      trendData = [...statistics.data].filter(hw => hw.status === 'graded' && hw.accuracy != null).sort((a, b) => new Date(a.submitted_at) - new Date(b.submitted_at)).slice(0, 10).map(hw => ({\n        title: hw.title || hw.assignment_title || `作业 ${hw.id}`,\n        accuracy: hw.accuracy,\n        date: new Date(hw.submitted_at).toLocaleDateString()\n      }));\n    } else if (statistics && statistics.accuracy_trend && Array.isArray(statistics.accuracy_trend)) {\n      // 使用student API的数据\n      trendData = statistics.accuracy_trend;\n    }\n    if (trendData.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Empty, {\n        description: \"\\u6682\\u65E0\\u8D8B\\u52BF\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 14\n      }, this);\n    }\n\n    // 计算最大值以便正确缩放\n    const maxAccuracy = Math.max(...trendData.map(item => item.accuracy || 0), 0.1);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          height: '200px',\n          alignItems: 'flex-end',\n          marginBottom: '10px'\n        },\n        children: trendData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            textAlign: 'center',\n            padding: '0 5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: `${item.accuracy / maxAccuracy * 180}px`,\n              backgroundColor: item.accuracy >= 0.6 ? '#52c41a' : '#ff4d4f',\n              position: 'relative',\n              minHeight: '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'absolute',\n                top: '-20px',\n                left: '0',\n                right: '0',\n                textAlign: 'center',\n                fontSize: '12px'\n              },\n              children: [(item.accuracy * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex'\n        },\n        children: trendData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            textAlign: 'center',\n            fontSize: '12px',\n            padding: '0 5px',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n            whiteSpace: 'nowrap'\n          },\n          children: item.date || '未知'\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this);\n  };\n  const renderRecentHomeworks = () => {\n    // 获取最近作业数据\n    let recentHomeworks = [];\n    if (statistics && statistics.data && Array.isArray(statistics.data)) {\n      // 使用export API的数据\n      recentHomeworks = [...statistics.data].sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at)).slice(0, 10);\n    } else if (statistics && statistics.recent_homeworks && Array.isArray(statistics.recent_homeworks)) {\n      // 使用teacher API的数据\n      recentHomeworks = statistics.recent_homeworks;\n    } else {\n      // 使用默认数据\n      recentHomeworks = [{\n        id: 1,\n        title: \"示例作业1\",\n        student_name: \"学生1\",\n        status: \"graded\",\n        score: 95,\n        submitted_at: new Date().toISOString()\n      }, {\n        id: 2,\n        title: \"示例作业2\",\n        student_name: \"学生2\",\n        status: \"pending\",\n        submitted_at: new Date(Date.now() - 86400000).toISOString()\n      }];\n    }\n    const columns = [{\n      title: '作业标题',\n      dataIndex: 'title',\n      key: 'title'\n    }, {\n      title: '学生姓名',\n      dataIndex: 'student_name',\n      key: 'student_name'\n    }, {\n      title: '提交时间',\n      dataIndex: 'submitted_at',\n      key: 'submitted_at',\n      render: val => val ? new Date(val).toLocaleString() : '未知'\n    }, {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: val => {\n        const statusMap = {\n          'pending': {\n            text: '待批改',\n            color: '#faad14'\n          },\n          'grading': {\n            text: '批改中',\n            color: '#1890ff'\n          },\n          'graded': {\n            text: '已批改',\n            color: '#52c41a'\n          },\n          'error': {\n            text: '错误',\n            color: '#ff4d4f'\n          }\n        };\n        const status = statusMap[val] || {\n          text: '未知',\n          color: '#666'\n        };\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: status.color\n          },\n          children: status.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this);\n      }\n    }, {\n      title: '分数',\n      dataIndex: 'score',\n      key: 'score',\n      render: (val, record) => {\n        if (record.status !== 'graded' || val === null || val === undefined) {\n          return '-';\n        }\n        return val;\n      }\n    }];\n    return /*#__PURE__*/_jsxDEV(Table, {\n      dataSource: recentHomeworks,\n      columns: columns,\n      rowKey: \"id\",\n      pagination: {\n        pageSize: 5\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 7\n    }, this);\n  };\n  const renderScoreDistribution = (classStats, selectedClassId) => {\n    // 确定要显示哪个班级的分数分布\n    let distribution = null;\n    if (selectedClassId !== 'all' && classStats.length > 0) {\n      // 显示选中班级的分数分布\n      // 检查score_distribution是否存在\n      if (classStats[0].score_distribution) {\n        distribution = classStats[0].score_distribution;\n      } else {\n        // 如果选中班级没有分数分布数据，尝试从export数据生成\n        if (statistics && statistics.data && Array.isArray(statistics.data)) {\n          // 过滤出选中班级的数据\n          const classHomeworks = statistics.data.filter(hw => classStats[0].class_id && hw.class_id && hw.class_id.toString() === classStats[0].class_id.toString());\n          if (classHomeworks.length > 0) {\n            const gradedHomeworks = classHomeworks.filter(hw => hw.status === 'graded' && hw.score !== null);\n            const excellentCount = gradedHomeworks.filter(hw => hw.score >= 90).length;\n            const goodCount = gradedHomeworks.filter(hw => hw.score >= 80 && hw.score < 90).length;\n            const passCount = gradedHomeworks.filter(hw => hw.score >= 60 && hw.score < 80).length;\n            const failCount = gradedHomeworks.filter(hw => hw.score < 60).length;\n            distribution = {\n              excellent: gradedHomeworks.length > 0 ? excellentCount / gradedHomeworks.length * 100 : 0,\n              good: gradedHomeworks.length > 0 ? goodCount / gradedHomeworks.length * 100 : 0,\n              pass: gradedHomeworks.length > 0 ? passCount / gradedHomeworks.length * 100 : 0,\n              fail: gradedHomeworks.length > 0 ? failCount / gradedHomeworks.length * 100 : 0\n            };\n          } else {\n            // 使用默认分布数据\n            distribution = {\n              excellent: 25,\n              good: 35,\n              pass: 30,\n              fail: 10\n            };\n          }\n        } else {\n          // 使用默认分布数据\n          distribution = {\n            excellent: 25,\n            good: 35,\n            pass: 30,\n            fail: 10\n          };\n        }\n      }\n    } else if (statistics && statistics.data && Array.isArray(statistics.data)) {\n      // 从export数据中计算分数分布\n      try {\n        const gradedHomeworks = statistics.data.filter(hw => hw.status === 'graded' && hw.score !== null);\n        const excellentCount = gradedHomeworks.filter(hw => hw.score >= 90).length;\n        const goodCount = gradedHomeworks.filter(hw => hw.score >= 80 && hw.score < 90).length;\n        const passCount = gradedHomeworks.filter(hw => hw.score >= 60 && hw.score < 80).length;\n        const failCount = gradedHomeworks.filter(hw => hw.score < 60).length;\n        distribution = {\n          excellent: gradedHomeworks.length > 0 ? excellentCount / gradedHomeworks.length * 100 : 0,\n          good: gradedHomeworks.length > 0 ? goodCount / gradedHomeworks.length * 100 : 0,\n          pass: gradedHomeworks.length > 0 ? passCount / gradedHomeworks.length * 100 : 0,\n          fail: gradedHomeworks.length > 0 ? failCount / gradedHomeworks.length * 100 : 0\n        };\n      } catch (error) {\n        console.error('计算总体分数分布时出错:', error);\n        // 如果计算出错，使用默认分布\n        distribution = {\n          excellent: 25,\n          good: 35,\n          pass: 30,\n          fail: 10\n        };\n      }\n    } else {\n      // 使用默认分布数据\n      distribution = {\n        excellent: 25,\n        good: 35,\n        pass: 30,\n        fail: 10\n      };\n    }\n\n    // 确保distribution对象存在并且有所有必要的属性\n    if (!distribution || distribution.excellent === undefined || distribution.good === undefined || distribution.pass === undefined || distribution.fail === undefined) {\n      // 如果缺少任何必要的属性，使用默认分布\n      distribution = {\n        excellent: 25,\n        good: 35,\n        pass: 30,\n        fail: 10\n      };\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [0, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '80px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u4F18\\u79C0 (90+):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.round(distribution.excellent),\n                strokeColor: \"#52c41a\",\n                format: percent => `${percent}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '80px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u826F\\u597D (80-89):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.round(distribution.good),\n                strokeColor: \"#1890ff\",\n                format: percent => `${percent}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '80px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u5408\\u683C (60-79):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.round(distribution.pass),\n                strokeColor: \"#faad14\",\n                format: percent => `${percent}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 24,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '80px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u4E0D\\u53CA\\u683C (<60):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.round(distribution.fail),\n                strokeColor: \"#f5222d\",\n                format: percent => `${percent}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 691,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '16px',\n          textAlign: 'center',\n          color: '#888'\n        },\n        children: selectedClassId !== 'all' ? '所选班级作业成绩分布' : '所有班级作业成绩分布'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 690,\n      columnNumber: 7\n    }, this);\n  };\n  const renderStudentStatistics = () => {\n    if (!statistics) return null;\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6700\\u9AD8\\u6B63\\u786E\\u9898\\u6570\",\n            value: statistics.highest_correct_count || 0,\n            valueStyle: {\n              color: '#3f8600'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u6B63\\u786E\\u9898\\u6570\",\n            value: statistics.average_correct_count ? statistics.average_correct_count.toFixed(2) : 0,\n            valueStyle: {\n              color: '#1890ff'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(QuestionCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6700\\u4F4E\\u6B63\\u786E\\u9898\\u6570\",\n            value: statistics.lowest_correct_count || 0,\n            valueStyle: {\n              color: '#cf1322'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFilters = () => {\n    return /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [user.is_teacher && /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u9009\\u62E9\\u73ED\\u7EA7\",\n          value: selectedClass,\n          onChange: value => {\n            console.log('选择班级变更为:', value);\n            setSelectedClass(value);\n            setSelectedAssignment('all');\n          },\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"all\",\n            children: \"\\u6240\\u6709\\u73ED\\u7EA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 15\n          }, this), classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n            value: cls.id,\n            children: cls.name\n          }, cls.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 11\n      }, this), selectedClass !== 'all' && user.is_teacher && /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u9009\\u62E9\\u4F5C\\u4E1A\",\n          value: selectedAssignment,\n          onChange: value => {\n            console.log('选择作业变更为:', value);\n            setSelectedAssignment(value);\n          },\n          loading: loadingAssignments,\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"all\",\n            children: \"\\u6240\\u6709\\u4F5C\\u4E1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 15\n          }, this), homeworkAssignments.map(assignment => /*#__PURE__*/_jsxDEV(Option, {\n            value: assignment.id,\n            children: assignment.title || `作业 ${assignment.id}`\n          }, assignment.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(RangePicker, {\n          style: {\n            width: '100%'\n          },\n          placeholder: ['开始日期', '结束日期'],\n          onChange: setDateRange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Spin, {\n      tip: \"\\u52A0\\u8F7D\\u7EDF\\u8BA1\\u6570\\u636E\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u9519\\u8BEF\",\n      description: error,\n      type: \"error\",\n      showIcon: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 生成标题\n  const getTitle = () => {\n    let title = \"作业正确题数分析\";\n    const selectedClassObj = classes.find(c => c.id.toString() === selectedClass.toString());\n    if (selectedClass !== 'all' && selectedClassObj) {\n      title += ` - ${selectedClassObj.name}`;\n      if (selectedAssignment !== 'all') {\n        const selectedAssignmentObj = homeworkAssignments.find(a => a.id.toString() === selectedAssignment.toString());\n        if (selectedAssignmentObj) {\n          title += ` - ${selectedAssignmentObj.title || `作业 ${selectedAssignment}`}`;\n        }\n      }\n    }\n    return title;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homework-statistics\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: getTitle()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 872,\n      columnNumber: 7\n    }, this), renderFilters(), user.is_teacher ? renderTeacherStatistics() : renderStudentStatistics()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 871,\n    columnNumber: 5\n  }, this);\n};\n_s(HomeworkStatistics, \"g/VjJFdaFFZDNvdJ9jiHylmxX0U=\");\n_c = HomeworkStatistics;\nexport default HomeworkStatistics;\nvar _c;\n$RefreshReg$(_c, \"HomeworkStatistics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Card", "Table", "Row", "Col", "Statistic", "Spin", "<PERSON><PERSON>", "Select", "DatePicker", "Progress", "Empty", "CheckCircleOutlined", "ExclamationCircleOutlined", "QuestionCircleOutlined", "FileTextOutlined", "CheckSquareOutlined", "TrophyOutlined", "getTeacherStatistics", "getStudentStatistics", "getClasses", "getHomeworkAssignments", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "RangePicker", "HomeworkStatistics", "user", "_s", "loading", "setLoading", "error", "setError", "statistics", "setStatistics", "selectedClass", "setSelectedClass", "classes", "setClasses", "date<PERSON><PERSON><PERSON>", "setDateRange", "homeworkAssignments", "setHomeworkAssignments", "selectedAssignment", "setSelectedAssignment", "loadingAssignments", "setLoadingAssignments", "fetchClasses", "console", "log", "classData", "Array", "isArray", "items", "warn", "id", "name", "err", "is_teacher", "fetchStatistics", "fetchHomeworkAssignments", "assignmentId", "parseInt", "isNaN", "classId", "response", "class_id", "params", "assignment_id", "length", "start_date", "format", "end_date", "data", "processedData", "highest_correct_count", "average_correct_count", "lowest_correct_count", "class_statistics_array", "class_name", "student_count", "homework_count", "renderTeacherStatistics", "classStatistics", "_homeworkAssignments$", "selectedClassObj", "find", "c", "toString", "className", "exportData", "totalHomeworks", "studentCount", "Set", "map", "hw", "student_name", "size", "correctCounts", "filter", "status", "accuracy", "Math", "round", "highestCorrectCount", "max", "lowestCorrectCount", "min", "averageCorrectCount", "reduce", "sum", "count", "totalPossible", "assignment_title", "submissionRate", "gradedCount", "gradingRate", "scoreDistribution", "averageScore", "gradedHomeworks", "score", "excellentCount", "goodCount", "passCount", "failCount", "excellent", "good", "pass", "fail", "a", "title", "submission_rate", "grading_rate", "graded_count", "score_distribution", "average_score", "class_statistics", "Object", "values", "item", "cls", "totalHomeworkCount", "totalGradedCount", "columns", "dataIndex", "key", "render", "val", "toFixed", "children", "gutter", "style", "marginBottom", "span", "value", "valueStyle", "color", "prefix", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "undefined", "height", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "renderScoreDistribution", "renderRecentHomeworks", "renderLearningTrend", "trendData", "sort", "b", "Date", "submitted_at", "slice", "date", "toLocaleDateString", "accuracy_trend", "description", "maxAccuracy", "padding", "display", "alignItems", "index", "flex", "textAlign", "backgroundColor", "position", "minHeight", "top", "left", "right", "fontSize", "overflow", "textOverflow", "whiteSpace", "recentHomeworks", "recent_homeworks", "toISOString", "now", "toLocaleString", "statusMap", "text", "record", "pageSize", "classStats", "selectedClassId", "distribution", "classHomeworks", "width", "fontWeight", "percent", "strokeColor", "marginTop", "renderStudentStatistics", "renderFilters", "placeholder", "onChange", "assignment", "tip", "message", "type", "showIcon", "getTitle", "selectedAssignmentObj", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/HomeworkStatistics.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Table, Row, Col, Statistic, Spin, Alert, Select, DatePicker, Progress, Empty } from 'antd';\nimport { \n  CheckCircleOutlined, \n  ExclamationCircleOutlined, \n  QuestionCircleOutlined,\n  FileTextOutlined,\n  CheckSquareOutlined,\n  TrophyOutlined\n} from '@ant-design/icons';\nimport { getTeacherStatistics, getStudentStatistics, getClasses, getHomeworkAssignments } from '../utils/api';\n\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\nconst HomeworkStatistics = ({ user }) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [statistics, setStatistics] = useState(null);\n  const [selectedClass, setSelectedClass] = useState('all');\n  const [classes, setClasses] = useState([]);\n  const [dateRange, setDateRange] = useState(null);\n  const [homeworkAssignments, setHomeworkAssignments] = useState([]);\n  const [selectedAssignment, setSelectedAssignment] = useState('all');\n  const [loadingAssignments, setLoadingAssignments] = useState(false);\n\n  useEffect(() => {\n    const fetchClasses = async () => {\n      try {\n        console.log('开始获取班级列表...');\n        const classData = await getClasses();\n        console.log('获取到的班级数据:', classData);\n        \n        if (Array.isArray(classData)) {\n          setClasses(classData);\n        } else if (classData && Array.isArray(classData.items)) {\n          setClasses(classData.items);\n        } else {\n          console.warn('返回的班级数据不是数组格式:', classData);\n          setClasses([\n            { id: 1, name: '示例班级1' },\n            { id: 2, name: '示例班级2' }\n          ]);\n        }\n      } catch (err) {\n        console.error('获取班级列表失败:', err);\n        setClasses([\n          { id: 1, name: '示例班级1' },\n          { id: 2, name: '示例班级2' }\n        ]);\n      }\n    };\n\n    if (user && user.is_teacher) {\n      fetchClasses();\n    }\n    fetchStatistics();\n  }, [user]);\n\n  useEffect(() => {\n    if (selectedClass !== 'all') {\n      fetchHomeworkAssignments(selectedClass);\n    } else {\n      setHomeworkAssignments([]);\n      setSelectedAssignment('all');\n    }\n  }, [selectedClass]);\n\n  useEffect(() => {\n    console.log('选择变更，重新获取统计数据。班级ID:', selectedClass, '作业ID:', selectedAssignment, '日期范围:', dateRange);\n    // 确保selectedAssignment的值类型是正确的\n    if (selectedAssignment === 'all') {\n      fetchStatistics();\n    } else {\n      // 确保传递数字类型的作业ID\n      const assignmentId = parseInt(selectedAssignment, 10);\n      if (!isNaN(assignmentId)) {\n    fetchStatistics();\n      } else {\n        console.error('作业ID格式错误:', selectedAssignment);\n      }\n    }\n  }, [selectedClass, selectedAssignment, dateRange]);\n\n  const fetchHomeworkAssignments = async (classId) => {\n    try {\n      setLoadingAssignments(true);\n      console.log('获取班级作业任务列表, 班级ID:', classId);\n      \n      const response = await getHomeworkAssignments({ class_id: classId });\n      console.log('获取班级作业任务列表成功:', response);\n      \n      if (response && Array.isArray(response.items)) {\n        setHomeworkAssignments(response.items);\n      } else {\n        console.warn('作业任务列表数据格式异常:', response);\n        setHomeworkAssignments([]);\n      }\n    } catch (error) {\n      console.error('获取班级作业任务列表失败:', error);\n      setHomeworkAssignments([]);\n    } finally {\n      setLoadingAssignments(false);\n    }\n  };\n\n  // 使用useCallback包装fetchStatistics函数\n  const fetchStatistics = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      let params = {};\n      \n      if (selectedClass !== 'all') {\n        params.class_id = selectedClass;\n      }\n      \n      if (selectedAssignment !== 'all') {\n        params.assignment_id = selectedAssignment;\n      }\n      \n      if (dateRange && dateRange.length === 2) {\n        params.start_date = dateRange[0].format('YYYY-MM-DD');\n        params.end_date = dateRange[1].format('YYYY-MM-DD');\n        console.log('设置日期范围:', params.start_date, '至', params.end_date);\n      }\n      \n      const data = user && user.is_teacher \n        ? await getTeacherStatistics(params)\n        : await getStudentStatistics(params);\n      \n      console.log('获取到的统计数据:', data);\n      \n      const processedData = {\n        ...data,\n        highest_correct_count: data.highest_correct_count || 5,\n        average_correct_count: data.average_correct_count || 3,\n        lowest_correct_count: data.lowest_correct_count || 1\n      };\n      \n      setStatistics(processedData);\n    } catch (err) {\n      console.error('获取统计数据失败:', err);\n      setError('获取统计数据失败，请稍后再试');\n      \n      setStatistics({\n        highest_correct_count: 5,\n        average_correct_count: 3,\n        lowest_correct_count: 1,\n        class_statistics_array: [{\n          class_id: 1,\n          class_name: \"示例班级\",\n          student_count: 25,\n          homework_count: 5,\n          highest_correct_count: 5,\n          average_correct_count: 3.5,\n          lowest_correct_count: 2\n        }]\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedClass, selectedAssignment, dateRange, user]);\n\n  const renderTeacherStatistics = () => {\n    if (!statistics) return null;\n    \n    let classStatistics = [];\n    \n    // 首先检查是否有来自/statistics/export接口的数据\n    if (statistics.data && Array.isArray(statistics.data)) {\n      console.log('检测到使用export接口数据，提取统计信息。班级ID:', selectedClass, '作业ID:', selectedAssignment);\n      \n      // 找出所选班级对应的名称\n      const selectedClassObj = classes.find(c => c.id.toString() === selectedClass.toString());\n      const className = selectedClassObj ? selectedClassObj.name : `班级 ${selectedClass}`;\n      \n      // 从export数据中计算统计信息\n      const exportData = statistics.data;\n      const totalHomeworks = exportData.length;\n      const studentCount = new Set(exportData.map(hw => hw.student_name)).size;\n      \n      // 计算正确题数统计\n      let correctCounts = exportData.filter(hw => hw.status === 'graded' && hw.accuracy != null)\n                                  .map(hw => Math.round(hw.accuracy * 10)); // 假设满分是10题\n      \n      const highestCorrectCount = correctCounts.length > 0 ? Math.max(...correctCounts) : 0;\n      const lowestCorrectCount = correctCounts.length > 0 ? Math.min(...correctCounts) : 0;\n      const averageCorrectCount = correctCounts.length > 0 \n        ? correctCounts.reduce((sum, count) => sum + count, 0) / correctCounts.length \n        : 0;\n      \n      // 计算提交率\n      let totalPossible;\n      if (selectedAssignment !== 'all') {\n        // 如果选择了特定作业，则只考虑该作业\n        totalPossible = studentCount;\n      } else {\n        // 否则考虑所有作业\n        totalPossible = studentCount * (exportData.length > 0 ? \n        new Set(exportData.map(hw => hw.assignment_title)).size : 1);\n      }\n      const submissionRate = totalPossible > 0 ? totalHomeworks / totalPossible : 0;\n      \n      // 计算批改率\n      const gradedCount = exportData.filter(hw => hw.status === 'graded').length;\n      const gradingRate = totalHomeworks > 0 ? gradedCount / totalHomeworks : 0;\n      \n      // 计算分数段分布\n      let scoreDistribution = {};\n      let averageScore = 0;\n      \n      try {\n        const gradedHomeworks = exportData.filter(hw => hw.status === 'graded' && hw.score !== null);\n        const excellentCount = gradedHomeworks.filter(hw => hw.score >= 90).length;\n        const goodCount = gradedHomeworks.filter(hw => hw.score >= 80 && hw.score < 90).length;\n        const passCount = gradedHomeworks.filter(hw => hw.score >= 60 && hw.score < 80).length;\n        const failCount = gradedHomeworks.filter(hw => hw.score < 60).length;\n        \n        // 计算平均分\n        averageScore = gradedHomeworks.length > 0\n          ? gradedHomeworks.reduce((sum, hw) => sum + (hw.score || 0), 0) / gradedHomeworks.length\n          : 0;\n        \n        scoreDistribution = {\n          excellent: gradedHomeworks.length > 0 ? (excellentCount / gradedHomeworks.length) * 100 : 0,\n          good: gradedHomeworks.length > 0 ? (goodCount / gradedHomeworks.length) * 100 : 0,\n          pass: gradedHomeworks.length > 0 ? (passCount / gradedHomeworks.length) * 100 : 0,\n          fail: gradedHomeworks.length > 0 ? (failCount / gradedHomeworks.length) * 100 : 0\n        };\n      } catch (error) {\n        console.error('计算分数分布时出错:', error);\n        // 如果计算出错，使用默认值\n        averageScore = 0;\n        scoreDistribution = {\n          excellent: 25,\n          good: 35,\n          pass: 30,\n          fail: 10\n        };\n      }\n      \n      // 为班级创建统计对象\n      classStatistics = [{\n        class_id: parseInt(selectedClass),\n        class_name: className,\n        assignment_id: selectedAssignment !== 'all' ? parseInt(selectedAssignment) : null,\n        assignment_title: selectedAssignment !== 'all' ? \n          homeworkAssignments.find(a => a.id.toString() === selectedAssignment.toString())?.title || `作业 ${selectedAssignment}` \n          : null,\n        student_count: studentCount,\n        homework_count: totalHomeworks,\n        highest_correct_count: highestCorrectCount,\n        average_correct_count: averageCorrectCount,\n        lowest_correct_count: lowestCorrectCount,\n        submission_rate: submissionRate,\n        grading_rate: gradingRate,\n        graded_count: gradedCount,\n        score_distribution: scoreDistribution,\n        average_score: averageScore\n      }];\n      \n      console.log('为选定班级构建了统计数据:', classStatistics[0]);\n    } \n    // 然后使用标准方式处理其他情况\n    else if (statistics.class_statistics_array && Array.isArray(statistics.class_statistics_array)) {\n      classStatistics = statistics.class_statistics_array;\n    } else if (statistics.class_statistics && typeof statistics.class_statistics === 'object') {\n      classStatistics = Object.values(statistics.class_statistics)\n        .filter(item => typeof item === 'object' && item !== null);\n    } else if (statistics.class_statistics && Array.isArray(statistics.class_statistics)) {\n      classStatistics = statistics.class_statistics;\n    }\n    \n    console.log('处理后的班级统计数据:', classStatistics);\n    \n    // 计算整体提交率\n    const submissionRate = classStatistics.length > 0 \n      ? classStatistics.reduce((sum, cls) => sum + (cls.submission_rate || 0), 0) / classStatistics.length\n      : 0;\n      \n    // 计算整体批改率\n    const totalHomeworkCount = classStatistics.reduce((sum, cls) => sum + (cls.homework_count || 0), 0);\n    const totalGradedCount = classStatistics.reduce((sum, cls) => sum + (cls.graded_count || 0), 0);\n    const gradingRate = totalHomeworkCount > 0 ? totalGradedCount / totalHomeworkCount : 0;\n    \n    const columns = [\n      { title: '班级名称', dataIndex: 'class_name', key: 'class_name' },\n      { title: '学生数量', dataIndex: 'student_count', key: 'student_count' },\n      { title: '作业数量', dataIndex: 'homework_count', key: 'homework_count' },\n      { \n        title: '作业提交率', \n        dataIndex: 'submission_rate', \n        key: 'submission_rate',\n        render: val => typeof val === 'number' ? `${(val * 100).toFixed(2)}%` : '0%'\n      },\n      { \n        title: '作业批改率', \n        dataIndex: 'grading_rate', \n        key: 'grading_rate',\n        render: val => typeof val === 'number' ? `${(val * 100).toFixed(2)}%` : '0%'\n      },\n      { title: '最高正确题数', dataIndex: 'highest_correct_count', key: 'highest_correct_count' },\n      { title: '平均正确题数', dataIndex: 'average_correct_count', key: 'average_correct_count', \n        render: val => typeof val === 'number' ? val.toFixed(2) : val },\n      { title: '最低正确题数', dataIndex: 'lowest_correct_count', key: 'lowest_correct_count' }\n    ];\n    \n    return (\n      <>\n        <Row gutter={16} style={{ marginBottom: 24 }}>\n          <Col span={4}>\n            <Card>\n              <Statistic\n                title=\"最高正确题数\"\n                value={\n                  // 如果是export数据且有班级统计信息，使用班级统计信息中的值\n                  selectedClass !== 'all' && classStatistics.length > 0 \n                    ? classStatistics[0].highest_correct_count || 0\n                    : statistics.highest_correct_count || 0\n                }\n                valueStyle={{ color: '#3f8600' }}\n                prefix={<CheckCircleOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={4}>\n            <Card>\n              <Statistic\n                title=\"平均正确题数\"\n                value={\n                  // 如果是export数据且有班级统计信息，使用班级统计信息中的值\n                  selectedClass !== 'all' && classStatistics.length > 0 \n                    ? (classStatistics[0].average_correct_count ? classStatistics[0].average_correct_count.toFixed(2) : 0)\n                    : (statistics.average_correct_count ? statistics.average_correct_count.toFixed(2) : 0)\n                }\n                valueStyle={{ color: '#1890ff' }}\n                prefix={<QuestionCircleOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={4}>\n            <Card>\n              <Statistic\n                title=\"最低正确题数\"\n                value={\n                  // 如果是export数据且有班级统计信息，使用班级统计信息中的值\n                  selectedClass !== 'all' && classStatistics.length > 0 \n                    ? classStatistics[0].lowest_correct_count || 0\n                    : statistics.lowest_correct_count || 0\n                }\n                valueStyle={{ color: '#cf1322' }}\n                prefix={<ExclamationCircleOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={4}>\n            <Card>\n              <Statistic\n                title=\"作业提交率\"\n                value={\n                  selectedClass !== 'all' && classStatistics.length > 0 && classStatistics[0].submission_rate !== undefined\n                    ? `${(classStatistics[0].submission_rate * 100).toFixed(2)}%`\n                    : `${(submissionRate * 100).toFixed(2)}%`\n                }\n                valueStyle={{ color: '#722ed1' }}\n                prefix={<FileTextOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={4}>\n            <Card>\n              <Statistic\n                title=\"作业批改率\"\n                value={\n                  selectedClass !== 'all' && classStatistics.length > 0 && classStatistics[0].grading_rate !== undefined\n                    ? `${(classStatistics[0].grading_rate * 100).toFixed(2)}%`\n                    : `${(gradingRate * 100).toFixed(2)}%`\n                }\n                valueStyle={{ color: '#fa8c16' }}\n                prefix={<CheckSquareOutlined />}\n              />\n            </Card>\n          </Col>\n          <Col span={4}>\n            <Card>\n              <Statistic\n                title=\"平均分\"\n                value={\n                  selectedClass !== 'all' && classStatistics.length > 0 && classStatistics[0].average_score !== undefined\n                    ? classStatistics[0].average_score.toFixed(1)\n                    : '0.0'\n                }\n                valueStyle={{ color: '#eb2f96' }}\n                prefix={<TrophyOutlined />}\n              />\n            </Card>\n          </Col>\n        </Row>\n        \n        <Row gutter={16} style={{ marginBottom: 24 }}>\n          <Col span={12}>\n            <Card title=\"班级正确题数统计\" style={{ height: '100%' }}>\n              <Table \n                dataSource={classStatistics} \n                columns={columns} \n                rowKey=\"class_id\"\n                pagination={false}\n              />\n            </Card>\n          </Col>\n          <Col span={12}>\n            <Card title=\"分数段分布\" style={{ height: '100%' }}>\n              {renderScoreDistribution(classStatistics, selectedClass)}\n            </Card>\n          </Col>\n        </Row>\n        \n        <Card title=\"最近作业情况\" style={{ marginBottom: 24 }}>\n          {renderRecentHomeworks()}\n        </Card>\n        \n        <Card title=\"学习进步趋势\" style={{ marginBottom: 24 }}>\n          {renderLearningTrend()}\n        </Card>\n      </>\n    );\n  };\n\n  const renderLearningTrend = () => {\n    // 获取作业准确率趋势数据\n    let trendData = [];\n    \n    if (statistics && statistics.data && Array.isArray(statistics.data)) {\n      // 使用export API的数据，只取已批改的作业\n      trendData = [...statistics.data]\n        .filter(hw => hw.status === 'graded' && hw.accuracy != null)\n        .sort((a, b) => new Date(a.submitted_at) - new Date(b.submitted_at))\n        .slice(0, 10)\n        .map(hw => ({\n          title: hw.title || hw.assignment_title || `作业 ${hw.id}`,\n          accuracy: hw.accuracy,\n          date: new Date(hw.submitted_at).toLocaleDateString()\n        }));\n    } else if (statistics && statistics.accuracy_trend && Array.isArray(statistics.accuracy_trend)) {\n      // 使用student API的数据\n      trendData = statistics.accuracy_trend;\n    }\n    \n    if (trendData.length === 0) {\n      return <Empty description=\"暂无趋势数据\" />;\n    }\n    \n    // 计算最大值以便正确缩放\n    const maxAccuracy = Math.max(...trendData.map(item => item.accuracy || 0), 0.1);\n    \n    return (\n      <div style={{ padding: '20px 0' }}>\n        <div style={{ display: 'flex', height: '200px', alignItems: 'flex-end', marginBottom: '10px' }}>\n          {trendData.map((item, index) => (\n            <div key={index} style={{ flex: 1, textAlign: 'center', padding: '0 5px' }}>\n              <div \n                style={{ \n                  height: `${(item.accuracy / maxAccuracy) * 180}px`, \n                  backgroundColor: item.accuracy >= 0.6 ? '#52c41a' : '#ff4d4f',\n                  position: 'relative',\n                  minHeight: '10px'\n                }}\n              >\n                <div style={{ \n                  position: 'absolute', \n                  top: '-20px', \n                  left: '0', \n                  right: '0', \n                  textAlign: 'center',\n                  fontSize: '12px' \n                }}>\n                  {(item.accuracy * 100).toFixed(0)}%\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n        <div style={{ display: 'flex' }}>\n          {trendData.map((item, index) => (\n            <div key={index} style={{ flex: 1, textAlign: 'center', fontSize: '12px', padding: '0 5px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\n              {item.date || '未知'}\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  const renderRecentHomeworks = () => {\n    // 获取最近作业数据\n    let recentHomeworks = [];\n    \n    if (statistics && statistics.data && Array.isArray(statistics.data)) {\n      // 使用export API的数据\n      recentHomeworks = [...statistics.data]\n        .sort((a, b) => new Date(b.submitted_at) - new Date(a.submitted_at))\n        .slice(0, 10);\n    } else if (statistics && statistics.recent_homeworks && Array.isArray(statistics.recent_homeworks)) {\n      // 使用teacher API的数据\n      recentHomeworks = statistics.recent_homeworks;\n    } else {\n      // 使用默认数据\n      recentHomeworks = [\n        {\n          id: 1,\n          title: \"示例作业1\",\n          student_name: \"学生1\",\n          status: \"graded\",\n          score: 95,\n          submitted_at: new Date().toISOString()\n        },\n        {\n          id: 2,\n          title: \"示例作业2\",\n          student_name: \"学生2\",\n          status: \"pending\",\n          submitted_at: new Date(Date.now() - 86400000).toISOString()\n        }\n      ];\n    }\n    \n    const columns = [\n      {\n        title: '作业标题',\n        dataIndex: 'title',\n        key: 'title',\n      },\n      {\n        title: '学生姓名',\n        dataIndex: 'student_name',\n        key: 'student_name',\n      },\n      {\n        title: '提交时间',\n        dataIndex: 'submitted_at',\n        key: 'submitted_at',\n        render: val => val ? new Date(val).toLocaleString() : '未知'\n      },\n      {\n        title: '状态',\n        dataIndex: 'status',\n        key: 'status',\n        render: val => {\n          const statusMap = {\n            'pending': { text: '待批改', color: '#faad14' },\n            'grading': { text: '批改中', color: '#1890ff' },\n            'graded': { text: '已批改', color: '#52c41a' },\n            'error': { text: '错误', color: '#ff4d4f' }\n          };\n          \n          const status = statusMap[val] || { text: '未知', color: '#666' };\n          \n          return (\n            <span style={{ color: status.color }}>\n              {status.text}\n            </span>\n          );\n        }\n      },\n      {\n        title: '分数',\n        dataIndex: 'score',\n        key: 'score',\n        render: (val, record) => {\n          if (record.status !== 'graded' || val === null || val === undefined) {\n            return '-';\n          }\n          return val;\n        }\n      }\n    ];\n    \n    return (\n      <Table \n        dataSource={recentHomeworks}\n        columns={columns}\n        rowKey=\"id\"\n        pagination={{ pageSize: 5 }}\n      />\n    );\n  };\n\n  const renderScoreDistribution = (classStats, selectedClassId) => {\n    // 确定要显示哪个班级的分数分布\n    let distribution = null;\n    \n    if (selectedClassId !== 'all' && classStats.length > 0) {\n      // 显示选中班级的分数分布\n      // 检查score_distribution是否存在\n      if (classStats[0].score_distribution) {\n        distribution = classStats[0].score_distribution;\n      } else {\n        // 如果选中班级没有分数分布数据，尝试从export数据生成\n        if (statistics && statistics.data && Array.isArray(statistics.data)) {\n          // 过滤出选中班级的数据\n          const classHomeworks = statistics.data.filter(hw => \n            classStats[0].class_id && hw.class_id && hw.class_id.toString() === classStats[0].class_id.toString()\n          );\n          \n          if (classHomeworks.length > 0) {\n            const gradedHomeworks = classHomeworks.filter(hw => hw.status === 'graded' && hw.score !== null);\n            const excellentCount = gradedHomeworks.filter(hw => hw.score >= 90).length;\n            const goodCount = gradedHomeworks.filter(hw => hw.score >= 80 && hw.score < 90).length;\n            const passCount = gradedHomeworks.filter(hw => hw.score >= 60 && hw.score < 80).length;\n            const failCount = gradedHomeworks.filter(hw => hw.score < 60).length;\n            \n            distribution = {\n              excellent: gradedHomeworks.length > 0 ? (excellentCount / gradedHomeworks.length) * 100 : 0,\n              good: gradedHomeworks.length > 0 ? (goodCount / gradedHomeworks.length) * 100 : 0,\n              pass: gradedHomeworks.length > 0 ? (passCount / gradedHomeworks.length) * 100 : 0,\n              fail: gradedHomeworks.length > 0 ? (failCount / gradedHomeworks.length) * 100 : 0\n            };\n          } else {\n            // 使用默认分布数据\n            distribution = {\n              excellent: 25,\n              good: 35,\n              pass: 30,\n              fail: 10\n            };\n          }\n        } else {\n          // 使用默认分布数据\n          distribution = {\n            excellent: 25,\n            good: 35,\n            pass: 30,\n            fail: 10\n          };\n        }\n      }\n    } else if (statistics && statistics.data && Array.isArray(statistics.data)) {\n      // 从export数据中计算分数分布\n      try {\n        const gradedHomeworks = statistics.data.filter(hw => hw.status === 'graded' && hw.score !== null);\n        const excellentCount = gradedHomeworks.filter(hw => hw.score >= 90).length;\n        const goodCount = gradedHomeworks.filter(hw => hw.score >= 80 && hw.score < 90).length;\n        const passCount = gradedHomeworks.filter(hw => hw.score >= 60 && hw.score < 80).length;\n        const failCount = gradedHomeworks.filter(hw => hw.score < 60).length;\n        \n        distribution = {\n          excellent: gradedHomeworks.length > 0 ? (excellentCount / gradedHomeworks.length) * 100 : 0,\n          good: gradedHomeworks.length > 0 ? (goodCount / gradedHomeworks.length) * 100 : 0,\n          pass: gradedHomeworks.length > 0 ? (passCount / gradedHomeworks.length) * 100 : 0,\n          fail: gradedHomeworks.length > 0 ? (failCount / gradedHomeworks.length) * 100 : 0\n        };\n      } catch (error) {\n        console.error('计算总体分数分布时出错:', error);\n        // 如果计算出错，使用默认分布\n        distribution = {\n          excellent: 25,\n          good: 35,\n          pass: 30,\n          fail: 10\n        };\n      }\n    } else {\n      // 使用默认分布数据\n      distribution = {\n        excellent: 25,\n        good: 35,\n        pass: 30,\n        fail: 10\n      };\n    }\n    \n    // 确保distribution对象存在并且有所有必要的属性\n    if (!distribution || \n        distribution.excellent === undefined || \n        distribution.good === undefined || \n        distribution.pass === undefined || \n        distribution.fail === undefined) {\n      // 如果缺少任何必要的属性，使用默认分布\n      distribution = {\n        excellent: 25,\n        good: 35,\n        pass: 30,\n        fail: 10\n      };\n    }\n    \n    return (\n      <div>\n        <Row gutter={[0, 16]}>\n          <Col span={24}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n              <div style={{ width: '80px', fontWeight: 'bold' }}>优秀 (90+):</div>\n              <div style={{ flex: 1 }}>\n                <Progress \n                  percent={Math.round(distribution.excellent)} \n                  strokeColor=\"#52c41a\" \n                  format={percent => `${percent}%`}\n                />\n              </div>\n            </div>\n          </Col>\n          <Col span={24}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n              <div style={{ width: '80px', fontWeight: 'bold' }}>良好 (80-89):</div>\n              <div style={{ flex: 1 }}>\n                <Progress \n                  percent={Math.round(distribution.good)} \n                  strokeColor=\"#1890ff\" \n                  format={percent => `${percent}%`}\n                />\n              </div>\n            </div>\n          </Col>\n          <Col span={24}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n              <div style={{ width: '80px', fontWeight: 'bold' }}>合格 (60-79):</div>\n              <div style={{ flex: 1 }}>\n                <Progress \n                  percent={Math.round(distribution.pass)} \n                  strokeColor=\"#faad14\" \n                  format={percent => `${percent}%`}\n                />\n              </div>\n            </div>\n          </Col>\n          <Col span={24}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\n              <div style={{ width: '80px', fontWeight: 'bold' }}>不及格 (&lt;60):</div>\n              <div style={{ flex: 1 }}>\n                <Progress \n                  percent={Math.round(distribution.fail)} \n                  strokeColor=\"#f5222d\" \n                  format={percent => `${percent}%`}\n                />\n              </div>\n            </div>\n          </Col>\n        </Row>\n        <div style={{ marginTop: '16px', textAlign: 'center', color: '#888' }}>\n          {selectedClassId !== 'all' ? '所选班级作业成绩分布' : '所有班级作业成绩分布'}\n        </div>\n      </div>\n    );\n  };\n\n  const renderStudentStatistics = () => {\n    if (!statistics) return null;\n    \n    return (\n      <Row gutter={16}>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"最高正确题数\"\n              value={statistics.highest_correct_count || 0}\n              valueStyle={{ color: '#3f8600' }}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"平均正确题数\"\n              value={statistics.average_correct_count ? statistics.average_correct_count.toFixed(2) : 0}\n              valueStyle={{ color: '#1890ff' }}\n              prefix={<QuestionCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"最低正确题数\"\n              value={statistics.lowest_correct_count || 0}\n              valueStyle={{ color: '#cf1322' }}\n              prefix={<ExclamationCircleOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n    );\n  };\n\n  const renderFilters = () => {\n    return (\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        {user.is_teacher && (\n          <Col span={8}>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择班级\"\n              value={selectedClass}\n              onChange={(value) => {\n                console.log('选择班级变更为:', value);\n                setSelectedClass(value);\n                setSelectedAssignment('all');\n              }}\n            >\n              <Option value=\"all\">所有班级</Option>\n              {classes.map(cls => (\n                <Option key={cls.id} value={cls.id}>{cls.name}</Option>\n              ))}\n            </Select>\n          </Col>\n        )}\n        \n        {selectedClass !== 'all' && user.is_teacher && (\n          <Col span={8}>\n            <Select\n              style={{ width: '100%' }}\n              placeholder=\"选择作业\"\n              value={selectedAssignment}\n              onChange={(value) => {\n                console.log('选择作业变更为:', value);\n                setSelectedAssignment(value);\n              }}\n              loading={loadingAssignments}\n            >\n              <Option value=\"all\">所有作业</Option>\n              {homeworkAssignments.map(assignment => (\n                <Option key={assignment.id} value={assignment.id}>\n                  {assignment.title || `作业 ${assignment.id}`}\n                </Option>\n              ))}\n            </Select>\n          </Col>\n        )}\n        \n        <Col span={8}>\n          <RangePicker\n            style={{ width: '100%' }}\n            placeholder={['开始日期', '结束日期']}\n            onChange={setDateRange}\n          />\n        </Col>\n      </Row>\n    );\n  };\n\n  if (loading) {\n    return <Spin tip=\"加载统计数据中...\" />;\n  }\n\n  if (error) {\n    return <Alert message=\"错误\" description={error} type=\"error\" showIcon />;\n  }\n\n  // 生成标题\n  const getTitle = () => {\n    let title = \"作业正确题数分析\";\n    const selectedClassObj = classes.find(c => c.id.toString() === selectedClass.toString());\n    \n    if (selectedClass !== 'all' && selectedClassObj) {\n      title += ` - ${selectedClassObj.name}`;\n      \n      if (selectedAssignment !== 'all') {\n        const selectedAssignmentObj = homeworkAssignments.find(a => a.id.toString() === selectedAssignment.toString());\n        if (selectedAssignmentObj) {\n          title += ` - ${selectedAssignmentObj.title || `作业 ${selectedAssignment}`}`;\n        }\n      }\n    }\n    \n    return title;\n  };\n\n  return (\n    <div className=\"homework-statistics\">\n      <h2>{getTitle()}</h2>\n      {renderFilters()}\n      {user.is_teacher ? renderTeacherStatistics() : renderStudentStatistics()}\n    </div>\n  );\n};\n\nexport default HomeworkStatistics; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,MAAM;AACzG,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,sBAAsB,EACtBC,gBAAgB,EAChBC,mBAAmB,EACnBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,oBAAoB,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,sBAAsB,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9G,MAAM;EAAEC;AAAO,CAAC,GAAGlB,MAAM;AACzB,MAAM;EAAEmB;AAAY,CAAC,GAAGlB,UAAU;AAElC,MAAMmB,kBAAkB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAEnEC,SAAS,CAAC,MAAM;IACd,MAAMkD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B,MAAMC,SAAS,GAAG,MAAMhC,UAAU,CAAC,CAAC;QACpC8B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,SAAS,CAAC;QAEnC,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;UAC5BZ,UAAU,CAACY,SAAS,CAAC;QACvB,CAAC,MAAM,IAAIA,SAAS,IAAIC,KAAK,CAACC,OAAO,CAACF,SAAS,CAACG,KAAK,CAAC,EAAE;UACtDf,UAAU,CAACY,SAAS,CAACG,KAAK,CAAC;QAC7B,CAAC,MAAM;UACLL,OAAO,CAACM,IAAI,CAAC,gBAAgB,EAAEJ,SAAS,CAAC;UACzCZ,UAAU,CAAC,CACT;YAAEiB,EAAE,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAQ,CAAC,EACxB;YAAED,EAAE,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAQ,CAAC,CACzB,CAAC;QACJ;MACF,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZT,OAAO,CAACjB,KAAK,CAAC,WAAW,EAAE0B,GAAG,CAAC;QAC/BnB,UAAU,CAAC,CACT;UAAEiB,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE;QAAQ,CAAC,EACxB;UAAED,EAAE,EAAE,CAAC;UAAEC,IAAI,EAAE;QAAQ,CAAC,CACzB,CAAC;MACJ;IACF,CAAC;IAED,IAAI7B,IAAI,IAAIA,IAAI,CAAC+B,UAAU,EAAE;MAC3BX,YAAY,CAAC,CAAC;IAChB;IACAY,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChC,IAAI,CAAC,CAAC;EAEV9B,SAAS,CAAC,MAAM;IACd,IAAIsC,aAAa,KAAK,KAAK,EAAE;MAC3ByB,wBAAwB,CAACzB,aAAa,CAAC;IACzC,CAAC,MAAM;MACLO,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAACT,aAAa,CAAC,CAAC;EAEnBtC,SAAS,CAAC,MAAM;IACdmD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEd,aAAa,EAAE,OAAO,EAAEQ,kBAAkB,EAAE,OAAO,EAAEJ,SAAS,CAAC;IAClG;IACA,IAAII,kBAAkB,KAAK,KAAK,EAAE;MAChCgB,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM;MACL;MACA,MAAME,YAAY,GAAGC,QAAQ,CAACnB,kBAAkB,EAAE,EAAE,CAAC;MACrD,IAAI,CAACoB,KAAK,CAACF,YAAY,CAAC,EAAE;QAC5BF,eAAe,CAAC,CAAC;MACf,CAAC,MAAM;QACLX,OAAO,CAACjB,KAAK,CAAC,WAAW,EAAEY,kBAAkB,CAAC;MAChD;IACF;EACF,CAAC,EAAE,CAACR,aAAa,EAAEQ,kBAAkB,EAAEJ,SAAS,CAAC,CAAC;EAElD,MAAMqB,wBAAwB,GAAG,MAAOI,OAAO,IAAK;IAClD,IAAI;MACFlB,qBAAqB,CAAC,IAAI,CAAC;MAC3BE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,OAAO,CAAC;MAEzC,MAAMC,QAAQ,GAAG,MAAM9C,sBAAsB,CAAC;QAAE+C,QAAQ,EAAEF;MAAQ,CAAC,CAAC;MACpEhB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,QAAQ,CAAC;MAEtC,IAAIA,QAAQ,IAAId,KAAK,CAACC,OAAO,CAACa,QAAQ,CAACZ,KAAK,CAAC,EAAE;QAC7CX,sBAAsB,CAACuB,QAAQ,CAACZ,KAAK,CAAC;MACxC,CAAC,MAAM;QACLL,OAAO,CAACM,IAAI,CAAC,eAAe,EAAEW,QAAQ,CAAC;QACvCvB,sBAAsB,CAAC,EAAE,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCW,sBAAsB,CAAC,EAAE,CAAC;IAC5B,CAAC,SAAS;MACRI,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMa,eAAe,GAAG7D,WAAW,CAAC,YAAY;IAC9CgC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,IAAImC,MAAM,GAAG,CAAC,CAAC;MAEf,IAAIhC,aAAa,KAAK,KAAK,EAAE;QAC3BgC,MAAM,CAACD,QAAQ,GAAG/B,aAAa;MACjC;MAEA,IAAIQ,kBAAkB,KAAK,KAAK,EAAE;QAChCwB,MAAM,CAACC,aAAa,GAAGzB,kBAAkB;MAC3C;MAEA,IAAIJ,SAAS,IAAIA,SAAS,CAAC8B,MAAM,KAAK,CAAC,EAAE;QACvCF,MAAM,CAACG,UAAU,GAAG/B,SAAS,CAAC,CAAC,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;QACrDJ,MAAM,CAACK,QAAQ,GAAGjC,SAAS,CAAC,CAAC,CAAC,CAACgC,MAAM,CAAC,YAAY,CAAC;QACnDvB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkB,MAAM,CAACG,UAAU,EAAE,GAAG,EAAEH,MAAM,CAACK,QAAQ,CAAC;MACjE;MAEA,MAAMC,IAAI,GAAG9C,IAAI,IAAIA,IAAI,CAAC+B,UAAU,GAChC,MAAM1C,oBAAoB,CAACmD,MAAM,CAAC,GAClC,MAAMlD,oBAAoB,CAACkD,MAAM,CAAC;MAEtCnB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwB,IAAI,CAAC;MAE9B,MAAMC,aAAa,GAAG;QACpB,GAAGD,IAAI;QACPE,qBAAqB,EAAEF,IAAI,CAACE,qBAAqB,IAAI,CAAC;QACtDC,qBAAqB,EAAEH,IAAI,CAACG,qBAAqB,IAAI,CAAC;QACtDC,oBAAoB,EAAEJ,IAAI,CAACI,oBAAoB,IAAI;MACrD,CAAC;MAED3C,aAAa,CAACwC,aAAa,CAAC;IAC9B,CAAC,CAAC,OAAOjB,GAAG,EAAE;MACZT,OAAO,CAACjB,KAAK,CAAC,WAAW,EAAE0B,GAAG,CAAC;MAC/BzB,QAAQ,CAAC,gBAAgB,CAAC;MAE1BE,aAAa,CAAC;QACZyC,qBAAqB,EAAE,CAAC;QACxBC,qBAAqB,EAAE,CAAC;QACxBC,oBAAoB,EAAE,CAAC;QACvBC,sBAAsB,EAAE,CAAC;UACvBZ,QAAQ,EAAE,CAAC;UACXa,UAAU,EAAE,MAAM;UAClBC,aAAa,EAAE,EAAE;UACjBC,cAAc,EAAE,CAAC;UACjBN,qBAAqB,EAAE,CAAC;UACxBC,qBAAqB,EAAE,GAAG;UAC1BC,oBAAoB,EAAE;QACxB,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,SAAS;MACR/C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACK,aAAa,EAAEQ,kBAAkB,EAAEJ,SAAS,EAAEZ,IAAI,CAAC,CAAC;EAExD,MAAMuD,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACjD,UAAU,EAAE,OAAO,IAAI;IAE5B,IAAIkD,eAAe,GAAG,EAAE;;IAExB;IACA,IAAIlD,UAAU,CAACwC,IAAI,IAAItB,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACwC,IAAI,CAAC,EAAE;MAAA,IAAAW,qBAAA;MACrDpC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEd,aAAa,EAAE,OAAO,EAAEQ,kBAAkB,CAAC;;MAEvF;MACA,MAAM0C,gBAAgB,GAAGhD,OAAO,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,CAACiC,QAAQ,CAAC,CAAC,KAAKrD,aAAa,CAACqD,QAAQ,CAAC,CAAC,CAAC;MACxF,MAAMC,SAAS,GAAGJ,gBAAgB,GAAGA,gBAAgB,CAAC7B,IAAI,GAAG,MAAMrB,aAAa,EAAE;;MAElF;MACA,MAAMuD,UAAU,GAAGzD,UAAU,CAACwC,IAAI;MAClC,MAAMkB,cAAc,GAAGD,UAAU,CAACrB,MAAM;MACxC,MAAMuB,YAAY,GAAG,IAAIC,GAAG,CAACH,UAAU,CAACI,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACC,YAAY,CAAC,CAAC,CAACC,IAAI;;MAExE;MACA,IAAIC,aAAa,GAAGR,UAAU,CAACS,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACK,MAAM,KAAK,QAAQ,IAAIL,EAAE,CAACM,QAAQ,IAAI,IAAI,CAAC,CAC7DP,GAAG,CAACC,EAAE,IAAIO,IAAI,CAACC,KAAK,CAACR,EAAE,CAACM,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEtE,MAAMG,mBAAmB,GAAGN,aAAa,CAAC7B,MAAM,GAAG,CAAC,GAAGiC,IAAI,CAACG,GAAG,CAAC,GAAGP,aAAa,CAAC,GAAG,CAAC;MACrF,MAAMQ,kBAAkB,GAAGR,aAAa,CAAC7B,MAAM,GAAG,CAAC,GAAGiC,IAAI,CAACK,GAAG,CAAC,GAAGT,aAAa,CAAC,GAAG,CAAC;MACpF,MAAMU,mBAAmB,GAAGV,aAAa,CAAC7B,MAAM,GAAG,CAAC,GAChD6B,aAAa,CAACW,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGb,aAAa,CAAC7B,MAAM,GAC3E,CAAC;;MAEL;MACA,IAAI2C,aAAa;MACjB,IAAIrE,kBAAkB,KAAK,KAAK,EAAE;QAChC;QACAqE,aAAa,GAAGpB,YAAY;MAC9B,CAAC,MAAM;QACL;QACAoB,aAAa,GAAGpB,YAAY,IAAIF,UAAU,CAACrB,MAAM,GAAG,CAAC,GACrD,IAAIwB,GAAG,CAACH,UAAU,CAACI,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACkB,gBAAgB,CAAC,CAAC,CAAChB,IAAI,GAAG,CAAC,CAAC;MAC9D;MACA,MAAMiB,cAAc,GAAGF,aAAa,GAAG,CAAC,GAAGrB,cAAc,GAAGqB,aAAa,GAAG,CAAC;;MAE7E;MACA,MAAMG,WAAW,GAAGzB,UAAU,CAACS,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACK,MAAM,KAAK,QAAQ,CAAC,CAAC/B,MAAM;MAC1E,MAAM+C,WAAW,GAAGzB,cAAc,GAAG,CAAC,GAAGwB,WAAW,GAAGxB,cAAc,GAAG,CAAC;;MAEzE;MACA,IAAI0B,iBAAiB,GAAG,CAAC,CAAC;MAC1B,IAAIC,YAAY,GAAG,CAAC;MAEpB,IAAI;QACF,MAAMC,eAAe,GAAG7B,UAAU,CAACS,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACK,MAAM,KAAK,QAAQ,IAAIL,EAAE,CAACyB,KAAK,KAAK,IAAI,CAAC;QAC5F,MAAMC,cAAc,GAAGF,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,CAAC,CAACnD,MAAM;QAC1E,MAAMqD,SAAS,GAAGH,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,IAAIzB,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;QACtF,MAAMsD,SAAS,GAAGJ,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,IAAIzB,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;QACtF,MAAMuD,SAAS,GAAGL,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;;QAEpE;QACAiD,YAAY,GAAGC,eAAe,CAAClD,MAAM,GAAG,CAAC,GACrCkD,eAAe,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEf,EAAE,KAAKe,GAAG,IAAIf,EAAE,CAACyB,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGD,eAAe,CAAClD,MAAM,GACtF,CAAC;QAELgD,iBAAiB,GAAG;UAClBQ,SAAS,EAAEN,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIoD,cAAc,GAAGF,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;UAC3FyD,IAAI,EAAEP,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIqD,SAAS,GAAGH,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;UACjF0D,IAAI,EAAER,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIsD,SAAS,GAAGJ,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;UACjF2D,IAAI,EAAET,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIuD,SAAS,GAAGL,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG;QAClF,CAAC;MACH,CAAC,CAAC,OAAOtC,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC;QACAuF,YAAY,GAAG,CAAC;QAChBD,iBAAiB,GAAG;UAClBQ,SAAS,EAAE,EAAE;UACbC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE;QACR,CAAC;MACH;;MAEA;MACA7C,eAAe,GAAG,CAAC;QACjBjB,QAAQ,EAAEJ,QAAQ,CAAC3B,aAAa,CAAC;QACjC4C,UAAU,EAAEU,SAAS;QACrBrB,aAAa,EAAEzB,kBAAkB,KAAK,KAAK,GAAGmB,QAAQ,CAACnB,kBAAkB,CAAC,GAAG,IAAI;QACjFsE,gBAAgB,EAAEtE,kBAAkB,KAAK,KAAK,GAC5C,EAAAyC,qBAAA,GAAA3C,mBAAmB,CAAC6C,IAAI,CAAC2C,CAAC,IAAIA,CAAC,CAAC1E,EAAE,CAACiC,QAAQ,CAAC,CAAC,KAAK7C,kBAAkB,CAAC6C,QAAQ,CAAC,CAAC,CAAC,cAAAJ,qBAAA,uBAAhFA,qBAAA,CAAkF8C,KAAK,KAAI,MAAMvF,kBAAkB,EAAE,GACnH,IAAI;QACRqC,aAAa,EAAEY,YAAY;QAC3BX,cAAc,EAAEU,cAAc;QAC9BhB,qBAAqB,EAAE6B,mBAAmB;QAC1C5B,qBAAqB,EAAEgC,mBAAmB;QAC1C/B,oBAAoB,EAAE6B,kBAAkB;QACxCyB,eAAe,EAAEjB,cAAc;QAC/BkB,YAAY,EAAEhB,WAAW;QACzBiB,YAAY,EAAElB,WAAW;QACzBmB,kBAAkB,EAAEjB,iBAAiB;QACrCkB,aAAa,EAAEjB;MACjB,CAAC,CAAC;MAEFtE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkC,eAAe,CAAC,CAAC,CAAC,CAAC;IAClD;IACA;IAAA,KACK,IAAIlD,UAAU,CAAC6C,sBAAsB,IAAI3B,KAAK,CAACC,OAAO,CAACnB,UAAU,CAAC6C,sBAAsB,CAAC,EAAE;MAC9FK,eAAe,GAAGlD,UAAU,CAAC6C,sBAAsB;IACrD,CAAC,MAAM,IAAI7C,UAAU,CAACuG,gBAAgB,IAAI,OAAOvG,UAAU,CAACuG,gBAAgB,KAAK,QAAQ,EAAE;MACzFrD,eAAe,GAAGsD,MAAM,CAACC,MAAM,CAACzG,UAAU,CAACuG,gBAAgB,CAAC,CACzDrC,MAAM,CAACwC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,CAAC;IAC9D,CAAC,MAAM,IAAI1G,UAAU,CAACuG,gBAAgB,IAAIrF,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACuG,gBAAgB,CAAC,EAAE;MACpFrD,eAAe,GAAGlD,UAAU,CAACuG,gBAAgB;IAC/C;IAEAxF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkC,eAAe,CAAC;;IAE3C;IACA,MAAM+B,cAAc,GAAG/B,eAAe,CAACd,MAAM,GAAG,CAAC,GAC7Cc,eAAe,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAE8B,GAAG,KAAK9B,GAAG,IAAI8B,GAAG,CAACT,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGhD,eAAe,CAACd,MAAM,GAClG,CAAC;;IAEL;IACA,MAAMwE,kBAAkB,GAAG1D,eAAe,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAE8B,GAAG,KAAK9B,GAAG,IAAI8B,GAAG,CAAC3D,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAM6D,gBAAgB,GAAG3D,eAAe,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAE8B,GAAG,KAAK9B,GAAG,IAAI8B,GAAG,CAACP,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/F,MAAMjB,WAAW,GAAGyB,kBAAkB,GAAG,CAAC,GAAGC,gBAAgB,GAAGD,kBAAkB,GAAG,CAAC;IAEtF,MAAME,OAAO,GAAG,CACd;MAAEb,KAAK,EAAE,MAAM;MAAEc,SAAS,EAAE,YAAY;MAAEC,GAAG,EAAE;IAAa,CAAC,EAC7D;MAAEf,KAAK,EAAE,MAAM;MAAEc,SAAS,EAAE,eAAe;MAAEC,GAAG,EAAE;IAAgB,CAAC,EACnE;MAAEf,KAAK,EAAE,MAAM;MAAEc,SAAS,EAAE,gBAAgB;MAAEC,GAAG,EAAE;IAAiB,CAAC,EACrE;MACEf,KAAK,EAAE,OAAO;MACdc,SAAS,EAAE,iBAAiB;MAC5BC,GAAG,EAAE,iBAAiB;MACtBC,MAAM,EAAEC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAG,GAAG,CAACA,GAAG,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1E,CAAC,EACD;MACElB,KAAK,EAAE,OAAO;MACdc,SAAS,EAAE,cAAc;MACzBC,GAAG,EAAE,cAAc;MACnBC,MAAM,EAAEC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAG,GAAG,CAACA,GAAG,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC1E,CAAC,EACD;MAAElB,KAAK,EAAE,QAAQ;MAAEc,SAAS,EAAE,uBAAuB;MAAEC,GAAG,EAAE;IAAwB,CAAC,EACrF;MAAEf,KAAK,EAAE,QAAQ;MAAEc,SAAS,EAAE,uBAAuB;MAAEC,GAAG,EAAE,uBAAuB;MACjFC,MAAM,EAAEC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGD;IAAI,CAAC,EACjE;MAAEjB,KAAK,EAAE,QAAQ;MAAEc,SAAS,EAAE,sBAAsB;MAAEC,GAAG,EAAE;IAAuB,CAAC,CACpF;IAED,oBACE5H,OAAA,CAAAE,SAAA;MAAA8H,QAAA,gBACEhI,OAAA,CAACpB,GAAG;QAACqJ,MAAM,EAAE,EAAG;QAACC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,gBAC3ChI,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;YAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;cACR+H,KAAK,EAAC,sCAAQ;cACdwB,KAAK;cACH;cACAvH,aAAa,KAAK,KAAK,IAAIgD,eAAe,CAACd,MAAM,GAAG,CAAC,GACjDc,eAAe,CAAC,CAAC,CAAC,CAACR,qBAAqB,IAAI,CAAC,GAC7C1C,UAAU,CAAC0C,qBAAqB,IAAI,CACzC;cACDgF,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cACjCC,MAAM,eAAExI,OAAA,CAACX,mBAAmB;gBAAAoJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;YAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;cACR+H,KAAK,EAAC,sCAAQ;cACdwB,KAAK;cACH;cACAvH,aAAa,KAAK,KAAK,IAAIgD,eAAe,CAACd,MAAM,GAAG,CAAC,GAChDc,eAAe,CAAC,CAAC,CAAC,CAACP,qBAAqB,GAAGO,eAAe,CAAC,CAAC,CAAC,CAACP,qBAAqB,CAACwE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAClGnH,UAAU,CAAC2C,qBAAqB,GAAG3C,UAAU,CAAC2C,qBAAqB,CAACwE,OAAO,CAAC,CAAC,CAAC,GAAG,CACvF;cACDO,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cACjCC,MAAM,eAAExI,OAAA,CAACT,sBAAsB;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;YAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;cACR+H,KAAK,EAAC,sCAAQ;cACdwB,KAAK;cACH;cACAvH,aAAa,KAAK,KAAK,IAAIgD,eAAe,CAACd,MAAM,GAAG,CAAC,GACjDc,eAAe,CAAC,CAAC,CAAC,CAACN,oBAAoB,IAAI,CAAC,GAC5C5C,UAAU,CAAC4C,oBAAoB,IAAI,CACxC;cACD8E,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cACjCC,MAAM,eAAExI,OAAA,CAACV,yBAAyB;gBAAAmJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;YAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;cACR+H,KAAK,EAAC,gCAAO;cACbwB,KAAK,EACHvH,aAAa,KAAK,KAAK,IAAIgD,eAAe,CAACd,MAAM,GAAG,CAAC,IAAIc,eAAe,CAAC,CAAC,CAAC,CAACgD,eAAe,KAAK+B,SAAS,GACrG,GAAG,CAAC/E,eAAe,CAAC,CAAC,CAAC,CAACgD,eAAe,GAAG,GAAG,EAAEiB,OAAO,CAAC,CAAC,CAAC,GAAG,GAC3D,GAAG,CAAClC,cAAc,GAAG,GAAG,EAAEkC,OAAO,CAAC,CAAC,CAAC,GACzC;cACDO,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cACjCC,MAAM,eAAExI,OAAA,CAACR,gBAAgB;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;YAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;cACR+H,KAAK,EAAC,gCAAO;cACbwB,KAAK,EACHvH,aAAa,KAAK,KAAK,IAAIgD,eAAe,CAACd,MAAM,GAAG,CAAC,IAAIc,eAAe,CAAC,CAAC,CAAC,CAACiD,YAAY,KAAK8B,SAAS,GAClG,GAAG,CAAC/E,eAAe,CAAC,CAAC,CAAC,CAACiD,YAAY,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC,GAAG,GACxD,GAAG,CAAChC,WAAW,GAAG,GAAG,EAAEgC,OAAO,CAAC,CAAC,CAAC,GACtC;cACDO,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cACjCC,MAAM,eAAExI,OAAA,CAACP,mBAAmB;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,CAAE;UAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;YAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;cACR+H,KAAK,EAAC,oBAAK;cACXwB,KAAK,EACHvH,aAAa,KAAK,KAAK,IAAIgD,eAAe,CAACd,MAAM,GAAG,CAAC,IAAIc,eAAe,CAAC,CAAC,CAAC,CAACoD,aAAa,KAAK2B,SAAS,GACnG/E,eAAe,CAAC,CAAC,CAAC,CAACoD,aAAa,CAACa,OAAO,CAAC,CAAC,CAAC,GAC3C,KACL;cACDO,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU,CAAE;cACjCC,MAAM,eAAExI,OAAA,CAACN,cAAc;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5I,OAAA,CAACpB,GAAG;QAACqJ,MAAM,EAAE,EAAG;QAACC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,gBAC3ChI,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZhI,OAAA,CAACtB,IAAI;YAACmI,KAAK,EAAC,kDAAU;YAACqB,KAAK,EAAE;cAAEY,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,eAC/ChI,OAAA,CAACrB,KAAK;cACJoK,UAAU,EAAEjF,eAAgB;cAC5B4D,OAAO,EAAEA,OAAQ;cACjBsB,MAAM,EAAC,UAAU;cACjBC,UAAU,EAAE;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZhI,OAAA,CAACtB,IAAI;YAACmI,KAAK,EAAC,gCAAO;YAACqB,KAAK,EAAE;cAAEY,MAAM,EAAE;YAAO,CAAE;YAAAd,QAAA,EAC3CkB,uBAAuB,CAACpF,eAAe,EAAEhD,aAAa;UAAC;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5I,OAAA,CAACtB,IAAI;QAACmI,KAAK,EAAC,sCAAQ;QAACqB,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,EAC9CmB,qBAAqB,CAAC;MAAC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eAEP5I,OAAA,CAACtB,IAAI;QAACmI,KAAK,EAAC,sCAAQ;QAACqB,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAH,QAAA,EAC9CoB,mBAAmB,CAAC;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA,eACP,CAAC;EAEP,CAAC;EAED,MAAMQ,mBAAmB,GAAGA,CAAA,KAAM;IAChC;IACA,IAAIC,SAAS,GAAG,EAAE;IAElB,IAAIzI,UAAU,IAAIA,UAAU,CAACwC,IAAI,IAAItB,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACwC,IAAI,CAAC,EAAE;MACnE;MACAiG,SAAS,GAAG,CAAC,GAAGzI,UAAU,CAACwC,IAAI,CAAC,CAC7B0B,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACK,MAAM,KAAK,QAAQ,IAAIL,EAAE,CAACM,QAAQ,IAAI,IAAI,CAAC,CAC3DsE,IAAI,CAAC,CAAC1C,CAAC,EAAE2C,CAAC,KAAK,IAAIC,IAAI,CAAC5C,CAAC,CAAC6C,YAAY,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,YAAY,CAAC,CAAC,CACnEC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZjF,GAAG,CAACC,EAAE,KAAK;QACVmC,KAAK,EAAEnC,EAAE,CAACmC,KAAK,IAAInC,EAAE,CAACkB,gBAAgB,IAAI,MAAMlB,EAAE,CAACxC,EAAE,EAAE;QACvD8C,QAAQ,EAAEN,EAAE,CAACM,QAAQ;QACrB2E,IAAI,EAAE,IAAIH,IAAI,CAAC9E,EAAE,CAAC+E,YAAY,CAAC,CAACG,kBAAkB,CAAC;MACrD,CAAC,CAAC,CAAC;IACP,CAAC,MAAM,IAAIhJ,UAAU,IAAIA,UAAU,CAACiJ,cAAc,IAAI/H,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACiJ,cAAc,CAAC,EAAE;MAC9F;MACAR,SAAS,GAAGzI,UAAU,CAACiJ,cAAc;IACvC;IAEA,IAAIR,SAAS,CAACrG,MAAM,KAAK,CAAC,EAAE;MAC1B,oBAAOhD,OAAA,CAACZ,KAAK;QAAC0K,WAAW,EAAC;MAAQ;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACvC;;IAEA;IACA,MAAMmB,WAAW,GAAG9E,IAAI,CAACG,GAAG,CAAC,GAAGiE,SAAS,CAAC5E,GAAG,CAAC6C,IAAI,IAAIA,IAAI,CAACtC,QAAQ,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC;IAE/E,oBACEhF,OAAA;MAAKkI,KAAK,EAAE;QAAE8B,OAAO,EAAE;MAAS,CAAE;MAAAhC,QAAA,gBAChChI,OAAA;QAAKkI,KAAK,EAAE;UAAE+B,OAAO,EAAE,MAAM;UAAEnB,MAAM,EAAE,OAAO;UAAEoB,UAAU,EAAE,UAAU;UAAE/B,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,EAC5FqB,SAAS,CAAC5E,GAAG,CAAC,CAAC6C,IAAI,EAAE6C,KAAK,kBACzBnK,OAAA;UAAiBkI,KAAK,EAAE;YAAEkC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE,QAAQ;YAAEL,OAAO,EAAE;UAAQ,CAAE;UAAAhC,QAAA,eACzEhI,OAAA;YACEkI,KAAK,EAAE;cACLY,MAAM,EAAE,GAAIxB,IAAI,CAACtC,QAAQ,GAAG+E,WAAW,GAAI,GAAG,IAAI;cAClDO,eAAe,EAAEhD,IAAI,CAACtC,QAAQ,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS;cAC7DuF,QAAQ,EAAE,UAAU;cACpBC,SAAS,EAAE;YACb,CAAE;YAAAxC,QAAA,eAEFhI,OAAA;cAAKkI,KAAK,EAAE;gBACVqC,QAAQ,EAAE,UAAU;gBACpBE,GAAG,EAAE,OAAO;gBACZC,IAAI,EAAE,GAAG;gBACTC,KAAK,EAAE,GAAG;gBACVN,SAAS,EAAE,QAAQ;gBACnBO,QAAQ,EAAE;cACZ,CAAE;cAAA5C,QAAA,GACC,CAACV,IAAI,CAACtC,QAAQ,GAAG,GAAG,EAAE+C,OAAO,CAAC,CAAC,CAAC,EAAC,GACpC;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAnBEuB,KAAK;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN5I,OAAA;QAAKkI,KAAK,EAAE;UAAE+B,OAAO,EAAE;QAAO,CAAE;QAAAjC,QAAA,EAC7BqB,SAAS,CAAC5E,GAAG,CAAC,CAAC6C,IAAI,EAAE6C,KAAK,kBACzBnK,OAAA;UAAiBkI,KAAK,EAAE;YAAEkC,IAAI,EAAE,CAAC;YAAEC,SAAS,EAAE,QAAQ;YAAEO,QAAQ,EAAE,MAAM;YAAEZ,OAAO,EAAE,OAAO;YAAEa,QAAQ,EAAE,QAAQ;YAAEC,YAAY,EAAE,UAAU;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAA/C,QAAA,EAC9JV,IAAI,CAACqC,IAAI,IAAI;QAAI,GADVQ,KAAK;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMO,qBAAqB,GAAGA,CAAA,KAAM;IAClC;IACA,IAAI6B,eAAe,GAAG,EAAE;IAExB,IAAIpK,UAAU,IAAIA,UAAU,CAACwC,IAAI,IAAItB,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACwC,IAAI,CAAC,EAAE;MACnE;MACA4H,eAAe,GAAG,CAAC,GAAGpK,UAAU,CAACwC,IAAI,CAAC,CACnCkG,IAAI,CAAC,CAAC1C,CAAC,EAAE2C,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAACE,YAAY,CAAC,GAAG,IAAID,IAAI,CAAC5C,CAAC,CAAC6C,YAAY,CAAC,CAAC,CACnEC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACjB,CAAC,MAAM,IAAI9I,UAAU,IAAIA,UAAU,CAACqK,gBAAgB,IAAInJ,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACqK,gBAAgB,CAAC,EAAE;MAClG;MACAD,eAAe,GAAGpK,UAAU,CAACqK,gBAAgB;IAC/C,CAAC,MAAM;MACL;MACAD,eAAe,GAAG,CAChB;QACE9I,EAAE,EAAE,CAAC;QACL2E,KAAK,EAAE,OAAO;QACdlC,YAAY,EAAE,KAAK;QACnBI,MAAM,EAAE,QAAQ;QAChBoB,KAAK,EAAE,EAAE;QACTsD,YAAY,EAAE,IAAID,IAAI,CAAC,CAAC,CAAC0B,WAAW,CAAC;MACvC,CAAC,EACD;QACEhJ,EAAE,EAAE,CAAC;QACL2E,KAAK,EAAE,OAAO;QACdlC,YAAY,EAAE,KAAK;QACnBI,MAAM,EAAE,SAAS;QACjB0E,YAAY,EAAE,IAAID,IAAI,CAACA,IAAI,CAAC2B,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACD,WAAW,CAAC;MAC5D,CAAC,CACF;IACH;IAEA,MAAMxD,OAAO,GAAG,CACd;MACEb,KAAK,EAAE,MAAM;MACbc,SAAS,EAAE,OAAO;MAClBC,GAAG,EAAE;IACP,CAAC,EACD;MACEf,KAAK,EAAE,MAAM;MACbc,SAAS,EAAE,cAAc;MACzBC,GAAG,EAAE;IACP,CAAC,EACD;MACEf,KAAK,EAAE,MAAM;MACbc,SAAS,EAAE,cAAc;MACzBC,GAAG,EAAE,cAAc;MACnBC,MAAM,EAAEC,GAAG,IAAIA,GAAG,GAAG,IAAI0B,IAAI,CAAC1B,GAAG,CAAC,CAACsD,cAAc,CAAC,CAAC,GAAG;IACxD,CAAC,EACD;MACEvE,KAAK,EAAE,IAAI;MACXc,SAAS,EAAE,QAAQ;MACnBC,GAAG,EAAE,QAAQ;MACbC,MAAM,EAAEC,GAAG,IAAI;QACb,MAAMuD,SAAS,GAAG;UAChB,SAAS,EAAE;YAAEC,IAAI,EAAE,KAAK;YAAE/C,KAAK,EAAE;UAAU,CAAC;UAC5C,SAAS,EAAE;YAAE+C,IAAI,EAAE,KAAK;YAAE/C,KAAK,EAAE;UAAU,CAAC;UAC5C,QAAQ,EAAE;YAAE+C,IAAI,EAAE,KAAK;YAAE/C,KAAK,EAAE;UAAU,CAAC;UAC3C,OAAO,EAAE;YAAE+C,IAAI,EAAE,IAAI;YAAE/C,KAAK,EAAE;UAAU;QAC1C,CAAC;QAED,MAAMxD,MAAM,GAAGsG,SAAS,CAACvD,GAAG,CAAC,IAAI;UAAEwD,IAAI,EAAE,IAAI;UAAE/C,KAAK,EAAE;QAAO,CAAC;QAE9D,oBACEvI,OAAA;UAAMkI,KAAK,EAAE;YAAEK,KAAK,EAAExD,MAAM,CAACwD;UAAM,CAAE;UAAAP,QAAA,EAClCjD,MAAM,CAACuG;QAAI;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAEX;IACF,CAAC,EACD;MACE/B,KAAK,EAAE,IAAI;MACXc,SAAS,EAAE,OAAO;MAClBC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAEA,CAACC,GAAG,EAAEyD,MAAM,KAAK;QACvB,IAAIA,MAAM,CAACxG,MAAM,KAAK,QAAQ,IAAI+C,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKe,SAAS,EAAE;UACnE,OAAO,GAAG;QACZ;QACA,OAAOf,GAAG;MACZ;IACF,CAAC,CACF;IAED,oBACE9H,OAAA,CAACrB,KAAK;MACJoK,UAAU,EAAEiC,eAAgB;MAC5BtD,OAAO,EAAEA,OAAQ;MACjBsB,MAAM,EAAC,IAAI;MACXC,UAAU,EAAE;QAAEuC,QAAQ,EAAE;MAAE;IAAE;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEN,CAAC;EAED,MAAMM,uBAAuB,GAAGA,CAACuC,UAAU,EAAEC,eAAe,KAAK;IAC/D;IACA,IAAIC,YAAY,GAAG,IAAI;IAEvB,IAAID,eAAe,KAAK,KAAK,IAAID,UAAU,CAACzI,MAAM,GAAG,CAAC,EAAE;MACtD;MACA;MACA,IAAIyI,UAAU,CAAC,CAAC,CAAC,CAACxE,kBAAkB,EAAE;QACpC0E,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC,CAACxE,kBAAkB;MACjD,CAAC,MAAM;QACL;QACA,IAAIrG,UAAU,IAAIA,UAAU,CAACwC,IAAI,IAAItB,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACwC,IAAI,CAAC,EAAE;UACnE;UACA,MAAMwI,cAAc,GAAGhL,UAAU,CAACwC,IAAI,CAAC0B,MAAM,CAACJ,EAAE,IAC9C+G,UAAU,CAAC,CAAC,CAAC,CAAC5I,QAAQ,IAAI6B,EAAE,CAAC7B,QAAQ,IAAI6B,EAAE,CAAC7B,QAAQ,CAACsB,QAAQ,CAAC,CAAC,KAAKsH,UAAU,CAAC,CAAC,CAAC,CAAC5I,QAAQ,CAACsB,QAAQ,CAAC,CACtG,CAAC;UAED,IAAIyH,cAAc,CAAC5I,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAMkD,eAAe,GAAG0F,cAAc,CAAC9G,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACK,MAAM,KAAK,QAAQ,IAAIL,EAAE,CAACyB,KAAK,KAAK,IAAI,CAAC;YAChG,MAAMC,cAAc,GAAGF,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,CAAC,CAACnD,MAAM;YAC1E,MAAMqD,SAAS,GAAGH,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,IAAIzB,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;YACtF,MAAMsD,SAAS,GAAGJ,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,IAAIzB,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;YACtF,MAAMuD,SAAS,GAAGL,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;YAEpE2I,YAAY,GAAG;cACbnF,SAAS,EAAEN,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIoD,cAAc,GAAGF,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;cAC3FyD,IAAI,EAAEP,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIqD,SAAS,GAAGH,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;cACjF0D,IAAI,EAAER,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIsD,SAAS,GAAGJ,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;cACjF2D,IAAI,EAAET,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIuD,SAAS,GAAGL,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG;YAClF,CAAC;UACH,CAAC,MAAM;YACL;YACA2I,YAAY,GAAG;cACbnF,SAAS,EAAE,EAAE;cACbC,IAAI,EAAE,EAAE;cACRC,IAAI,EAAE,EAAE;cACRC,IAAI,EAAE;YACR,CAAC;UACH;QACF,CAAC,MAAM;UACL;UACAgF,YAAY,GAAG;YACbnF,SAAS,EAAE,EAAE;YACbC,IAAI,EAAE,EAAE;YACRC,IAAI,EAAE,EAAE;YACRC,IAAI,EAAE;UACR,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI/F,UAAU,IAAIA,UAAU,CAACwC,IAAI,IAAItB,KAAK,CAACC,OAAO,CAACnB,UAAU,CAACwC,IAAI,CAAC,EAAE;MAC1E;MACA,IAAI;QACF,MAAM8C,eAAe,GAAGtF,UAAU,CAACwC,IAAI,CAAC0B,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACK,MAAM,KAAK,QAAQ,IAAIL,EAAE,CAACyB,KAAK,KAAK,IAAI,CAAC;QACjG,MAAMC,cAAc,GAAGF,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,CAAC,CAACnD,MAAM;QAC1E,MAAMqD,SAAS,GAAGH,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,IAAIzB,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;QACtF,MAAMsD,SAAS,GAAGJ,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,IAAI,EAAE,IAAIzB,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;QACtF,MAAMuD,SAAS,GAAGL,eAAe,CAACpB,MAAM,CAACJ,EAAE,IAAIA,EAAE,CAACyB,KAAK,GAAG,EAAE,CAAC,CAACnD,MAAM;QAEpE2I,YAAY,GAAG;UACbnF,SAAS,EAAEN,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIoD,cAAc,GAAGF,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;UAC3FyD,IAAI,EAAEP,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIqD,SAAS,GAAGH,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;UACjF0D,IAAI,EAAER,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIsD,SAAS,GAAGJ,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG,CAAC;UACjF2D,IAAI,EAAET,eAAe,CAAClD,MAAM,GAAG,CAAC,GAAIuD,SAAS,GAAGL,eAAe,CAAClD,MAAM,GAAI,GAAG,GAAG;QAClF,CAAC;MACH,CAAC,CAAC,OAAOtC,KAAK,EAAE;QACdiB,OAAO,CAACjB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC;QACAiL,YAAY,GAAG;UACbnF,SAAS,EAAE,EAAE;UACbC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE;QACR,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACAgF,YAAY,GAAG;QACbnF,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR,CAAC;IACH;;IAEA;IACA,IAAI,CAACgF,YAAY,IACbA,YAAY,CAACnF,SAAS,KAAKqC,SAAS,IACpC8C,YAAY,CAAClF,IAAI,KAAKoC,SAAS,IAC/B8C,YAAY,CAACjF,IAAI,KAAKmC,SAAS,IAC/B8C,YAAY,CAAChF,IAAI,KAAKkC,SAAS,EAAE;MACnC;MACA8C,YAAY,GAAG;QACbnF,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE;MACR,CAAC;IACH;IAEA,oBACE3G,OAAA;MAAAgI,QAAA,gBACEhI,OAAA,CAACpB,GAAG;QAACqJ,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAE;QAAAD,QAAA,gBACnBhI,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZhI,OAAA;YAAKkI,KAAK,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE/B,YAAY,EAAE;YAAM,CAAE;YAAAH,QAAA,gBACzEhI,OAAA;cAAKkI,KAAK,EAAE;gBAAE2D,KAAK,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAA9D,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClE5I,OAAA;cAAKkI,KAAK,EAAE;gBAAEkC,IAAI,EAAE;cAAE,CAAE;cAAApC,QAAA,eACtBhI,OAAA,CAACb,QAAQ;gBACP4M,OAAO,EAAE9G,IAAI,CAACC,KAAK,CAACyG,YAAY,CAACnF,SAAS,CAAE;gBAC5CwF,WAAW,EAAC,SAAS;gBACrB9I,MAAM,EAAE6I,OAAO,IAAI,GAAGA,OAAO;cAAI;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZhI,OAAA;YAAKkI,KAAK,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE/B,YAAY,EAAE;YAAM,CAAE;YAAAH,QAAA,gBACzEhI,OAAA;cAAKkI,KAAK,EAAE;gBAAE2D,KAAK,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAA9D,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpE5I,OAAA;cAAKkI,KAAK,EAAE;gBAAEkC,IAAI,EAAE;cAAE,CAAE;cAAApC,QAAA,eACtBhI,OAAA,CAACb,QAAQ;gBACP4M,OAAO,EAAE9G,IAAI,CAACC,KAAK,CAACyG,YAAY,CAAClF,IAAI,CAAE;gBACvCuF,WAAW,EAAC,SAAS;gBACrB9I,MAAM,EAAE6I,OAAO,IAAI,GAAGA,OAAO;cAAI;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZhI,OAAA;YAAKkI,KAAK,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE/B,YAAY,EAAE;YAAM,CAAE;YAAAH,QAAA,gBACzEhI,OAAA;cAAKkI,KAAK,EAAE;gBAAE2D,KAAK,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAA9D,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpE5I,OAAA;cAAKkI,KAAK,EAAE;gBAAEkC,IAAI,EAAE;cAAE,CAAE;cAAApC,QAAA,eACtBhI,OAAA,CAACb,QAAQ;gBACP4M,OAAO,EAAE9G,IAAI,CAACC,KAAK,CAACyG,YAAY,CAACjF,IAAI,CAAE;gBACvCsF,WAAW,EAAC,SAAS;gBACrB9I,MAAM,EAAE6I,OAAO,IAAI,GAAGA,OAAO;cAAI;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5I,OAAA,CAACnB,GAAG;UAACuJ,IAAI,EAAE,EAAG;UAAAJ,QAAA,eACZhI,OAAA;YAAKkI,KAAK,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE/B,YAAY,EAAE;YAAM,CAAE;YAAAH,QAAA,gBACzEhI,OAAA;cAAKkI,KAAK,EAAE;gBAAE2D,KAAK,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAA9D,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtE5I,OAAA;cAAKkI,KAAK,EAAE;gBAAEkC,IAAI,EAAE;cAAE,CAAE;cAAApC,QAAA,eACtBhI,OAAA,CAACb,QAAQ;gBACP4M,OAAO,EAAE9G,IAAI,CAACC,KAAK,CAACyG,YAAY,CAAChF,IAAI,CAAE;gBACvCqF,WAAW,EAAC,SAAS;gBACrB9I,MAAM,EAAE6I,OAAO,IAAI,GAAGA,OAAO;cAAI;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5I,OAAA;QAAKkI,KAAK,EAAE;UAAE+D,SAAS,EAAE,MAAM;UAAE5B,SAAS,EAAE,QAAQ;UAAE9B,KAAK,EAAE;QAAO,CAAE;QAAAP,QAAA,EACnE0D,eAAe,KAAK,KAAK,GAAG,YAAY,GAAG;MAAY;QAAAjD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMsD,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACtL,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACpB,GAAG;MAACqJ,MAAM,EAAE,EAAG;MAAAD,QAAA,gBACdhI,OAAA,CAACnB,GAAG;QAACuJ,IAAI,EAAE,CAAE;QAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;UAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,sCAAQ;YACdwB,KAAK,EAAEzH,UAAU,CAAC0C,qBAAqB,IAAI,CAAE;YAC7CgF,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YACjCC,MAAM,eAAExI,OAAA,CAACX,mBAAmB;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;QAACuJ,IAAI,EAAE,CAAE;QAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;UAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,sCAAQ;YACdwB,KAAK,EAAEzH,UAAU,CAAC2C,qBAAqB,GAAG3C,UAAU,CAAC2C,qBAAqB,CAACwE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE;YAC1FO,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YACjCC,MAAM,eAAExI,OAAA,CAACT,sBAAsB;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5I,OAAA,CAACnB,GAAG;QAACuJ,IAAI,EAAE,CAAE;QAAAJ,QAAA,eACXhI,OAAA,CAACtB,IAAI;UAAAsJ,QAAA,eACHhI,OAAA,CAAClB,SAAS;YACR+H,KAAK,EAAC,sCAAQ;YACdwB,KAAK,EAAEzH,UAAU,CAAC4C,oBAAoB,IAAI,CAAE;YAC5C8E,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU,CAAE;YACjCC,MAAM,eAAExI,OAAA,CAACV,yBAAyB;cAAAmJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,MAAMuD,aAAa,GAAGA,CAAA,KAAM;IAC1B,oBACEnM,OAAA,CAACpB,GAAG;MAACqJ,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,GAC1C1H,IAAI,CAAC+B,UAAU,iBACdrC,OAAA,CAACnB,GAAG;QAACuJ,IAAI,EAAE,CAAE;QAAAJ,QAAA,eACXhI,OAAA,CAACf,MAAM;UACLiJ,KAAK,EAAE;YAAE2D,KAAK,EAAE;UAAO,CAAE;UACzBO,WAAW,EAAC,0BAAM;UAClB/D,KAAK,EAAEvH,aAAc;UACrBuL,QAAQ,EAAGhE,KAAK,IAAK;YACnB1G,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyG,KAAK,CAAC;YAC9BtH,gBAAgB,CAACsH,KAAK,CAAC;YACvB9G,qBAAqB,CAAC,KAAK,CAAC;UAC9B,CAAE;UAAAyG,QAAA,gBAEFhI,OAAA,CAACG,MAAM;YAACkI,KAAK,EAAC,KAAK;YAAAL,QAAA,EAAC;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAChC5H,OAAO,CAACyD,GAAG,CAAC8C,GAAG,iBACdvH,OAAA,CAACG,MAAM;YAAckI,KAAK,EAAEd,GAAG,CAACrF,EAAG;YAAA8F,QAAA,EAAET,GAAG,CAACpF;UAAI,GAAhCoF,GAAG,CAACrF,EAAE;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA9H,aAAa,KAAK,KAAK,IAAIR,IAAI,CAAC+B,UAAU,iBACzCrC,OAAA,CAACnB,GAAG;QAACuJ,IAAI,EAAE,CAAE;QAAAJ,QAAA,eACXhI,OAAA,CAACf,MAAM;UACLiJ,KAAK,EAAE;YAAE2D,KAAK,EAAE;UAAO,CAAE;UACzBO,WAAW,EAAC,0BAAM;UAClB/D,KAAK,EAAE/G,kBAAmB;UAC1B+K,QAAQ,EAAGhE,KAAK,IAAK;YACnB1G,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyG,KAAK,CAAC;YAC9B9G,qBAAqB,CAAC8G,KAAK,CAAC;UAC9B,CAAE;UACF7H,OAAO,EAAEgB,kBAAmB;UAAAwG,QAAA,gBAE5BhI,OAAA,CAACG,MAAM;YAACkI,KAAK,EAAC,KAAK;YAAAL,QAAA,EAAC;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAChCxH,mBAAmB,CAACqD,GAAG,CAAC6H,UAAU,iBACjCtM,OAAA,CAACG,MAAM;YAAqBkI,KAAK,EAAEiE,UAAU,CAACpK,EAAG;YAAA8F,QAAA,EAC9CsE,UAAU,CAACzF,KAAK,IAAI,MAAMyF,UAAU,CAACpK,EAAE;UAAE,GAD/BoK,UAAU,CAACpK,EAAE;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAED5I,OAAA,CAACnB,GAAG;QAACuJ,IAAI,EAAE,CAAE;QAAAJ,QAAA,eACXhI,OAAA,CAACI,WAAW;UACV8H,KAAK,EAAE;YAAE2D,KAAK,EAAE;UAAO,CAAE;UACzBO,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAE;UAC9BC,QAAQ,EAAElL;QAAa;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,IAAIpI,OAAO,EAAE;IACX,oBAAOR,OAAA,CAACjB,IAAI;MAACwN,GAAG,EAAC;IAAY;MAAA9D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClC;EAEA,IAAIlI,KAAK,EAAE;IACT,oBAAOV,OAAA,CAAChB,KAAK;MAACwN,OAAO,EAAC,cAAI;MAAC1C,WAAW,EAAEpJ,KAAM;MAAC+L,IAAI,EAAC,OAAO;MAACC,QAAQ;IAAA;MAAAjE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzE;;EAEA;EACA,MAAM+D,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI9F,KAAK,GAAG,UAAU;IACtB,MAAM7C,gBAAgB,GAAGhD,OAAO,CAACiD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,CAACiC,QAAQ,CAAC,CAAC,KAAKrD,aAAa,CAACqD,QAAQ,CAAC,CAAC,CAAC;IAExF,IAAIrD,aAAa,KAAK,KAAK,IAAIkD,gBAAgB,EAAE;MAC/C6C,KAAK,IAAI,MAAM7C,gBAAgB,CAAC7B,IAAI,EAAE;MAEtC,IAAIb,kBAAkB,KAAK,KAAK,EAAE;QAChC,MAAMsL,qBAAqB,GAAGxL,mBAAmB,CAAC6C,IAAI,CAAC2C,CAAC,IAAIA,CAAC,CAAC1E,EAAE,CAACiC,QAAQ,CAAC,CAAC,KAAK7C,kBAAkB,CAAC6C,QAAQ,CAAC,CAAC,CAAC;QAC9G,IAAIyI,qBAAqB,EAAE;UACzB/F,KAAK,IAAI,MAAM+F,qBAAqB,CAAC/F,KAAK,IAAI,MAAMvF,kBAAkB,EAAE,EAAE;QAC5E;MACF;IACF;IAEA,OAAOuF,KAAK;EACd,CAAC;EAED,oBACE7G,OAAA;IAAKoE,SAAS,EAAC,qBAAqB;IAAA4D,QAAA,gBAClChI,OAAA;MAAAgI,QAAA,EAAK2E,QAAQ,CAAC;IAAC;MAAAlE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EACpBuD,aAAa,CAAC,CAAC,EACf7L,IAAI,CAAC+B,UAAU,GAAGwB,uBAAuB,CAAC,CAAC,GAAGqI,uBAAuB,CAAC,CAAC;EAAA;IAAAzD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrE,CAAC;AAEV,CAAC;AAACrI,EAAA,CA71BIF,kBAAkB;AAAAwM,EAAA,GAAlBxM,kBAAkB;AA+1BxB,eAAeA,kBAAkB;AAAC,IAAAwM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}