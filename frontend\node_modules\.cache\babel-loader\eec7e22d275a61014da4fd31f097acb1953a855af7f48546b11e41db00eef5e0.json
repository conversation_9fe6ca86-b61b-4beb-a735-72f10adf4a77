{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport IeCircleFilledSvg from \"@ant-design/icons-svg/es/asn/IeCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar IeCircleFilled = function IeCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: IeCircleFilledSvg\n  }));\n};\n\n/**![ie-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY5My42IDI4NC40Yy0yNCAwLTUxLjEgMTEuNy03Mi42IDIyIDQ2LjMgMTggODYgNTcuMyAxMTIuMyA5OS42IDcuMS0xOC45IDE0LjYtNDcuOSAxNC42LTY3LjkgMC0zMi0yMi44LTUzLjctNTQuMy01My43ek01MTIgNjRDMjY0LjYgNjQgNjQgMjY0LjYgNjQgNTEyczIwMC42IDQ0OCA0NDggNDQ4IDQ0OC0yMDAuNiA0NDgtNDQ4Uzc1OS40IDY0IDUxMiA2NHptMjUzLjkgNDkyLjlINDM3LjFjMCAxMDAuNCAxNDQuMyAxMzYgMTk2LjggNDcuNGgxMjAuOGMtMzIuNiA5MS43LTExOS43IDE0Ni0yMTYuOCAxNDYtMzUuMSAwLTcwLjMtLjEtMTAxLjctMTUuNi04Ny40IDQ0LjUtMTgwLjMgNTYuNi0xODAuMy00MiAwLTQ1LjggMjMuMi0xMDcuMSA0NC0xNDVDMzM1IDQ4NCAzODEuMyA0MjIuOCA0MzUuNiAzNzQuNWMtNDMuNyAxOC45LTkxLjEgNjYuMy0xMjIgMTAxLjIgMjUuOS0xMTIuOCAxMjkuNS0xOTMuNiAyMzcuMS0xODYuNSAxMzAtNTkuOCAyMDkuNy0zNC4xIDIwOS43IDM4LjYgMCAyNy40LTEwLjYgNjMuMy0yMS40IDg3LjkgMjUuMiA0NS41IDMzLjMgOTcuNiAyNi45IDE0MS4yek01NDAuNSAzOTkuMWMtNTMuNyAwLTEwMiAzOS43LTEwNCA5NC45aDIwOGMtMi01NS4xLTUwLjYtOTQuOS0xMDQtOTQuOXpNMzIwLjYgNjAyLjljLTczIDE1Mi40IDExLjUgMTcyLjIgMTAwLjMgMTIzLjMtNDYuNi0yNy41LTgyLjYtNzIuMi0xMDAuMy0xMjMuM3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(IeCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'IeCircleFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}