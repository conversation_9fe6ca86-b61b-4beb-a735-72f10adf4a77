{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    visible = props.visible,\n    maskProps = props.maskProps,\n    motionName = props.motionName,\n    className = props.className;\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    key: \"mask\",\n    visible: visible,\n    motionName: motionName,\n    leavedClassName: \"\".concat(prefixCls, \"-mask-hidden\")\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref,\n      style: _objectSpread(_objectSpread({}, motionStyle), style),\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionClassName, className)\n    }, maskProps));\n  });\n};\nexport default Mask;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}