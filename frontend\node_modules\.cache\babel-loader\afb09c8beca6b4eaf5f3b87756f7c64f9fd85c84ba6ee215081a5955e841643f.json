{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nfunction getUseId() {\n  // We need fully clone React function here to avoid webpack warning React 17 do not export `useId`\n  var fullClone = _objectSpread({}, React);\n  return fullClone.useId;\n}\nvar uuid = 0;\n\n/** @private Note only worked in develop env. Not work in production. */\nexport function resetUuid() {\n  if (process.env.NODE_ENV !== 'production') {\n    uuid = 0;\n  }\n}\nvar useOriginId = getUseId();\nexport default useOriginId ?\n// Use React `useId`\nfunction useId(id) {\n  var reactId = useOriginId();\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n  return reactId;\n} :\n// Use compatible of `useId`\nfunction useCompatId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState('ssr-id'),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    var nextId = uuid;\n    uuid += 1;\n    setInnerId(\"rc_unique_\".concat(nextId));\n  }, []);\n\n  // Developer passed id is single source of truth\n  if (id) {\n    return id;\n  }\n\n  // Test env always return mock id\n  if (process.env.NODE_ENV === 'test') {\n    return 'test-id';\n  }\n\n  // Return react native id or inner id\n  return innerId;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}