{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { useEvent, useMergedState } from 'rc-util';\nconst EMPTY_KEYS = [];\nfunction filterKeys(keys, dataKeys) {\n  const filteredKeys = keys.filter(key => dataKeys.has(key));\n  return keys.length === filteredKeys.length ? keys : filteredKeys;\n}\nfunction flattenKeys(keys) {\n  return Array.from(keys).join(';');\n}\nfunction useSelection(leftDataSource, rightDataSource, selectedKeys) {\n  // Prepare `dataSource` keys\n  const [leftKeys, rightKeys] = React.useMemo(() => [new Set(leftDataSource.map(src => src === null || src === void 0 ? void 0 : src.key)), new Set(rightDataSource.map(src => src === null || src === void 0 ? void 0 : src.key))], [leftDataSource, rightDataSource]);\n  // Selected Keys\n  const [mergedSelectedKeys, setMergedSelectedKeys] = useMergedState(EMPTY_KEYS, {\n    value: selectedKeys\n  });\n  const sourceSelectedKeys = React.useMemo(() => filterKeys(mergedSelectedKeys, leftKeys), [mergedSelectedKeys, leftKeys]);\n  const targetSelectedKeys = React.useMemo(() => filterKeys(mergedSelectedKeys, rightKeys), [mergedSelectedKeys, rightKeys]);\n  // // Reset when data changed\n  React.useEffect(() => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(filterKeys(mergedSelectedKeys, leftKeys)), _toConsumableArray(filterKeys(mergedSelectedKeys, rightKeys))));\n  }, [flattenKeys(leftKeys), flattenKeys(rightKeys)]);\n  // Update keys\n  const setSourceSelectedKeys = useEvent(nextSrcKeys => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(nextSrcKeys), _toConsumableArray(targetSelectedKeys)));\n  });\n  const setTargetSelectedKeys = useEvent(nextTargetKeys => {\n    setMergedSelectedKeys([].concat(_toConsumableArray(sourceSelectedKeys), _toConsumableArray(nextTargetKeys)));\n  });\n  return [\n  // Keys\n  sourceSelectedKeys, targetSelectedKeys,\n  // Updater\n  setSourceSelectedKeys, setTargetSelectedKeys];\n}\nexport default useSelection;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}