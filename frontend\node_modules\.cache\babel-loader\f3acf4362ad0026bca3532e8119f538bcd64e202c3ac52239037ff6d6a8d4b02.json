{"ast": null, "code": "export var toArray = function toArray(value) {\n  return Array.isArray(value) ? value : value !== undefined ? [value] : [];\n};\nexport var fillFieldNames = function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: value || 'value',\n    key: value || 'value',\n    children: children || 'children'\n  };\n};\nexport var isCheckDisabled = function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n};\nexport var getAllKeys = function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n  var dig = function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  };\n  dig(treeData);\n  return keys;\n};\nexport var isNil = function isNil(val) {\n  return val === null || val === undefined;\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}