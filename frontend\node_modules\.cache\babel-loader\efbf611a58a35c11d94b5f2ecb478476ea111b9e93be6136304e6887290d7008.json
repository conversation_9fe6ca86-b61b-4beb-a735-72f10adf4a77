{"ast": null, "code": "import * as React from 'react';\nimport LocaleContext from './context';\nimport defaultLocaleData from './en_US';\nconst useLocale = (componentName, defaultLocale) => {\n  const fullLocale = React.useContext(LocaleContext);\n  const getLocale = React.useMemo(() => {\n    var _a;\n    const locale = defaultLocale || defaultLocaleData[componentName];\n    const localeFromContext = (_a = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale[componentName]) !== null && _a !== void 0 ? _a : {};\n    return Object.assign(Object.assign({}, typeof locale === 'function' ? locale() : locale), localeFromContext || {});\n  }, [componentName, defaultLocale, fullLocale]);\n  const getLocaleCode = React.useMemo(() => {\n    const localeCode = fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.locale;\n    // Had use LocaleProvide but didn't set locale\n    if ((fullLocale === null || fullLocale === void 0 ? void 0 : fullLocale.exist) && !localeCode) {\n      return defaultLocaleData.locale;\n    }\n    return localeCode;\n  }, [fullLocale]);\n  return [getLocale, getLocaleCode];\n};\nexport default useLocale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}