{"ast": null, "code": "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { blankLine, content } from 'micromark-core-commonmark';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {InitialConstruct} */\nexport const flow = {\n  tokenize: initializeFlow\n};\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this;\n  const initial = effects.attempt(\n  // Try to parse a blank line.\n  blankLine, atBlankEnding,\n  // Try to parse initial flow (essentially, only code).\n  effects.attempt(this.parser.constructs.flowInitial, afterConstruct, factorySpace(effects, effects.attempt(this.parser.constructs.flow, afterConstruct, effects.attempt(content, afterConstruct)), types.linePrefix)));\n  return initial;\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(code === codes.eof || markdownLineEnding(code), 'expected eol or eof');\n    if (code === codes.eof) {\n      effects.consume(code);\n      return;\n    }\n    effects.enter(types.lineEndingBlank);\n    effects.consume(code);\n    effects.exit(types.lineEndingBlank);\n    self.currentConstruct = undefined;\n    return initial;\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(code === codes.eof || markdownLineEnding(code), 'expected eol or eof');\n    if (code === codes.eof) {\n      effects.consume(code);\n      return;\n    }\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    self.currentConstruct = undefined;\n    return initial;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}