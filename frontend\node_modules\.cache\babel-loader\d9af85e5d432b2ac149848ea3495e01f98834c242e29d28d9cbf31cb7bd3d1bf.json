{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\SchoolManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Tabs, Card, Table, Button, message, Spin, Modal, Form, Input, Select, Tag, Space, Tooltip, Divider } from 'antd';\nimport { EditOutlined, DeleteOutlined, PlusOutlined, UserOutlined, TeamOutlined, SettingOutlined, UserAddOutlined } from '@ant-design/icons';\nimport { getSchoolDetail, updateSchoolInfo, getClassesBySchool, createClassForSchool, updateClassInfo, deleteClassById, getSchoolRoles, assignRole, revokeRole, getSchoolUsers, getUsers, createUser, getSchools, deleteSchool } from '../utils/api';\nimport { useAuth } from '../utils/auth';\nimport { getUserRoleTags } from '../utils/roleUtils';\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Option\n} = Select;\nconst SchoolManagement = ({\n  schoolId\n}) => {\n  _s();\n  var _allSchools$find;\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('1');\n  const [school, setSchool] = useState(null);\n  const [classes, setClasses] = useState([]);\n  const [roles, setRoles] = useState([]);\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [classModalVisible, setClassModalVisible] = useState(false);\n  const [roleModalVisible, setRoleModalVisible] = useState(false);\n  const [newUserModalVisible, setNewUserModalVisible] = useState(false);\n  const [currentClass, setCurrentClass] = useState(null);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [form] = Form.useForm();\n  const [roleForm] = Form.useForm();\n  const [userForm] = Form.useForm();\n  const [allUsers, setAllUsers] = useState([]);\n  const [allSchools, setAllSchools] = useState([]);\n  const [selectedSchoolId, setSelectedSchoolId] = useState(schoolId);\n\n  // 获取所有学校列表（仅超级管理员）\n  useEffect(() => {\n    if (user && user.is_admin) {\n      fetchAllSchools();\n    }\n  }, [user]);\n\n  // 获取学校信息\n  useEffect(() => {\n    // 优先使用选择的学校ID，其次是传入的schoolId，最后是用户自己的school_id\n    const rawSchoolId = selectedSchoolId || schoolId || user && user.school_id;\n\n    // 确保学校ID是数字类型\n    let targetSchoolId = null;\n    if (rawSchoolId !== null && rawSchoolId !== undefined) {\n      targetSchoolId = parseInt(rawSchoolId, 10);\n      if (isNaN(targetSchoolId)) {\n        console.error('无效的学校ID:', rawSchoolId);\n        targetSchoolId = null;\n      }\n    }\n    console.log('SchoolManagement接收到的学校ID:', {\n      selectedSchoolId,\n      schoolId,\n      userSchoolId: user === null || user === void 0 ? void 0 : user.school_id,\n      rawSchoolId,\n      targetSchoolId,\n      schoolIdType: typeof schoolId\n    });\n\n    // 如果有目标学校ID，则获取学校数据\n    if (targetSchoolId !== null) {\n      console.log(`准备获取学校数据，ID: ${targetSchoolId}`);\n      fetchSchoolData(targetSchoolId);\n    } else if (user && user.is_admin) {\n      // 超级管理员但没有指定学校ID，显示学校列表\n      console.log('超级管理员没有指定学校ID，显示学校列表');\n    } else {\n      console.warn('无法确定要获取的学校ID');\n    }\n  }, [selectedSchoolId, schoolId, user]);\n\n  // 当组件挂载或schoolId变化时，更新selectedSchoolId\n  useEffect(() => {\n    console.log('SchoolManagement组件props变化，schoolId:', schoolId, '类型:', typeof schoolId);\n\n    // 检查schoolId是否有效\n    if (schoolId !== null && schoolId !== undefined) {\n      console.log('SchoolManagement组件接收到新的schoolId:', schoolId);\n\n      // 确保ID是数字类型\n      const numericSchoolId = parseInt(schoolId, 10);\n      if (isNaN(numericSchoolId)) {\n        console.error('传入的schoolId不是有效数字:', schoolId);\n        message.error('无效的学校ID');\n        return;\n      }\n      setSelectedSchoolId(numericSchoolId);\n      console.log(`已更新selectedSchoolId: ${numericSchoolId}`);\n\n      // 立即获取学校数据\n      fetchSchoolData(numericSchoolId);\n    } else {\n      console.warn('SchoolManagement组件没有接收到有效的schoolId');\n    }\n  }, [schoolId]);\n\n  // 获取所有学校列表\n  const fetchAllSchools = async () => {\n    try {\n      const schools = await getSchools();\n      console.log('获取所有学校列表:', schools);\n\n      // 处理不同格式的返回数据\n      let schoolsList = [];\n      if (Array.isArray(schools)) {\n        schoolsList = schools;\n      } else if (schools && Array.isArray(schools.items)) {\n        schoolsList = schools.items;\n      }\n      console.log('处理后的学校列表:', schoolsList);\n      setAllSchools(schoolsList);\n    } catch (error) {\n      console.error('获取学校列表失败:', error);\n    }\n  };\n\n  // 切换标签页时加载数据\n  useEffect(() => {\n    // 优先使用选择的学校ID，其次是传入的schoolId，最后是用户自己的school_id\n    const targetSchoolId = selectedSchoolId || schoolId || user && user.school_id;\n    // 超级管理员可以看到所有学校，不需要school_id\n    if (targetSchoolId || user && user.is_admin) {\n      if (activeTab === '2') {\n        fetchClasses(targetSchoolId);\n      } else if (activeTab === '3') {\n        fetchRoles();\n        fetchUsers(targetSchoolId);\n        fetchAllUsers(); // 获取所有用户，用于添加已有用户\n      }\n    }\n  }, [activeTab, selectedSchoolId, schoolId, user]);\n\n  // 当选择的学校ID变化时，重新获取学校信息、班级列表和用户列表\n  useEffect(() => {\n    if (selectedSchoolId) {\n      if (activeTab === '1') {\n        fetchSchoolData(selectedSchoolId);\n      } else if (activeTab === '2') {\n        fetchClasses(selectedSchoolId);\n      } else if (activeTab === '3') {\n        fetchUsers(selectedSchoolId);\n      }\n    }\n  }, [selectedSchoolId]);\n\n  // 获取学校数据\n  const fetchSchoolData = async targetSchoolId => {\n    setLoading(true);\n    try {\n      console.log('fetchSchoolData被调用，传入的学校ID:', targetSchoolId, '类型:', typeof targetSchoolId);\n      if (targetSchoolId === null || targetSchoolId === undefined) {\n        console.error('未提供学校ID，无法获取学校详情');\n        message.error('未提供学校ID，无法获取学校详情');\n        setLoading(false);\n        return;\n      }\n\n      // 确保学校ID是数字类型\n      const schoolId = parseInt(targetSchoolId, 10);\n      if (isNaN(schoolId)) {\n        console.error('无效的学校ID:', targetSchoolId);\n        message.error('无效的学校ID');\n        setLoading(false);\n        return;\n      }\n\n      // 获取指定学校的详情\n      console.log(`获取学校详情，ID: ${schoolId}`);\n      try {\n        const data = await getSchoolDetail(schoolId);\n        console.log('获取到学校详情:', data);\n        if (!data) {\n          console.error(`学校ID ${schoolId} 未返回有效数据`);\n          message.error('获取学校详情失败：服务器未返回数据');\n          setLoading(false);\n          return;\n        }\n        setSchool(data);\n\n        // 同时获取该学校的班级和用户\n        fetchClasses(schoolId);\n        if (activeTab === '3') {\n          fetchUsers(schoolId);\n        }\n      } catch (apiError) {\n        console.error(`获取学校详情API调用失败，ID: ${schoolId}:`, apiError);\n        message.error(`获取学校详情失败: ${apiError.message || '未知错误'}`);\n      }\n    } catch (error) {\n      message.error('获取学校信息失败');\n      console.error('获取学校信息失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取班级数据\n  const fetchClasses = async targetSchoolId => {\n    try {\n      setLoading(true);\n      console.log(`获取学校班级列表，学校ID: ${targetSchoolId}`);\n      const response = await getClassesBySchool(targetSchoolId);\n      console.log('获取班级列表成功:', response);\n      setClasses(response || []);\n    } catch (error) {\n      console.error('获取班级列表失败:', error);\n      message.error('获取班级列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取角色数据\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const data = await getSchoolRoles();\n      setRoles(data);\n    } catch (error) {\n      message.error('获取角色信息失败');\n      console.error('获取角色信息失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取用户数据\n  const fetchUsers = async targetSchoolId => {\n    setLoading(true);\n    try {\n      const data = await getSchoolUsers(targetSchoolId);\n      setUsers(data);\n    } catch (error) {\n      message.error('获取用户信息失败');\n      console.error('获取用户信息失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取所有用户（用于添加已有用户）\n  const fetchAllUsers = async () => {\n    try {\n      const data = await getUsers();\n      // 确保data是正确的格式，如果是新格式(带有items字段)，使用items数组\n      if (data && typeof data === 'object' && 'items' in data) {\n        setAllUsers(data.items || []);\n      } else {\n        // 如果是旧格式(直接是数组)或其他格式，直接使用\n        setAllUsers(data || []);\n      }\n    } catch (error) {\n      console.error('获取所有用户失败:', error);\n    }\n  };\n\n  // 更新学校信息\n  const handleUpdateSchool = async values => {\n    if (!school) return;\n    setLoading(true);\n    try {\n      await updateSchoolInfo(school.id, values);\n      message.success('学校信息更新成功');\n      fetchSchoolData(school.id);\n    } catch (error) {\n      message.error('学校信息更新失败');\n      console.error('学校信息更新失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 打开班级编辑模态框\n  const showClassModal = (record = null) => {\n    setCurrentClass(record);\n    form.resetFields();\n    if (record) {\n      form.setFieldsValue({\n        name: record.name,\n        grade: record.grade\n      });\n    }\n    setClassModalVisible(true);\n  };\n\n  // 关闭班级编辑模态框\n  const handleClassCancel = () => {\n    setClassModalVisible(false);\n    setCurrentClass(null);\n    form.resetFields();\n  };\n\n  // 保存班级信息\n  const handleClassSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n      if (currentClass) {\n        // 更新班级\n        console.log(`更新班级，ID: ${currentClass.id}, 数据:`, values);\n        await updateClassInfo(school.id, currentClass.id, values);\n        message.success('班级更新成功');\n      } else {\n        // 创建班级\n        console.log(`创建班级，学校ID: ${school.id}, 数据:`, values);\n        await createClassForSchool(school.id, {\n          ...values,\n          school_id: school.id\n        });\n        message.success('班级创建成功');\n      }\n      setClassModalVisible(false);\n      form.resetFields();\n      fetchClasses(school.id);\n    } catch (error) {\n      console.error('保存班级失败:', error);\n      message.error('保存班级失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 删除班级\n  const handleDeleteClass = async classId => {\n    try {\n      setLoading(true);\n      console.log(`删除班级，ID: ${classId}`);\n      await deleteClassById(school.id, classId);\n      message.success('班级删除成功');\n      fetchClasses(school.id);\n    } catch (error) {\n      console.error('删除班级失败:', error);\n      if (error.response && error.response.status === 400) {\n        message.error('班级中还有学生，无法删除');\n      } else {\n        message.error('删除班级失败: ' + (error.message || '未知错误'));\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 打开角色分配模态框\n  const showRoleModal = record => {\n    setCurrentUser(record);\n    roleForm.resetFields();\n    setRoleModalVisible(true);\n  };\n\n  // 关闭角色分配模态框\n  const handleRoleCancel = () => {\n    setRoleModalVisible(false);\n    setCurrentUser(null);\n    roleForm.resetFields();\n  };\n\n  // 分配角色\n  const handleRoleSubmit = async () => {\n    try {\n      const values = await roleForm.validateFields();\n      setLoading(true);\n\n      // 获取当前操作的学校ID\n      const targetSchoolId = schoolId || user && user.school_id;\n      await assignRole({\n        user_id: currentUser.id,\n        role_id: values.role_id,\n        school_id: targetSchoolId\n      });\n      message.success('角色分配成功');\n      setRoleModalVisible(false);\n      fetchUsers(targetSchoolId);\n    } catch (error) {\n      console.error('角色分配失败:', error);\n      message.error('角色分配失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 撤销角色\n  const handleRevokeRole = async (userId, roleId) => {\n    Modal.confirm({\n      title: '确认撤销',\n      content: '确定要撤销这个用户的角色吗？',\n      okText: '确认',\n      cancelText: '取消',\n      onOk: async () => {\n        setLoading(true);\n        try {\n          // 获取当前操作的学校ID\n          const targetSchoolId = schoolId || user && user.school_id;\n          await revokeRole({\n            user_id: userId,\n            role_id: roleId,\n            school_id: targetSchoolId\n          });\n          message.success('角色撤销成功');\n          fetchUsers(targetSchoolId);\n        } catch (error) {\n          message.error('角色撤销失败');\n          console.error('角色撤销失败:', error);\n        } finally {\n          setLoading(false);\n        }\n      }\n    });\n  };\n\n  // 打开新用户模态框\n  const showNewUserModal = () => {\n    userForm.resetFields();\n    setNewUserModalVisible(true);\n  };\n\n  // 关闭新用户模态框\n  const handleNewUserCancel = () => {\n    setNewUserModalVisible(false);\n    userForm.resetFields();\n  };\n\n  // 创建新用户\n  const handleNewUserSubmit = async () => {\n    try {\n      const values = await userForm.validateFields();\n      setLoading(true);\n\n      // 获取当前操作的学校ID\n      const targetSchoolId = schoolId || user && user.school_id;\n\n      // 确定用户类型\n      const isTeacher = ['teacher', 'class_teacher', 'lesson_group_leader', 'subject_leader', 'grade_leader', 'academic_director', 'vice_principal', 'principal'].includes(values.user_type);\n      const isAdmin = values.user_type === 'admin';\n\n      // 确定对应的角色ID\n      let roleId = values.role_id;\n      if (!roleId) {\n        var _roles$find, _roles$find2, _roles$find3, _roles$find4, _roles$find5, _roles$find6, _roles$find7, _roles$find8, _roles$find9, _roles$find0;\n        // 根据用户类型自动设置角色\n        const roleMap = {\n          'principal': (_roles$find = roles.find(r => r.code === 'principal')) === null || _roles$find === void 0 ? void 0 : _roles$find.id,\n          'vice_principal': (_roles$find2 = roles.find(r => r.code === 'vice_principal')) === null || _roles$find2 === void 0 ? void 0 : _roles$find2.id,\n          'academic_director': (_roles$find3 = roles.find(r => r.code === 'academic_director')) === null || _roles$find3 === void 0 ? void 0 : _roles$find3.id,\n          'grade_leader': (_roles$find4 = roles.find(r => r.code === 'grade_leader')) === null || _roles$find4 === void 0 ? void 0 : _roles$find4.id,\n          'subject_leader': (_roles$find5 = roles.find(r => r.code === 'subject_leader')) === null || _roles$find5 === void 0 ? void 0 : _roles$find5.id,\n          'lesson_group_leader': (_roles$find6 = roles.find(r => r.code === 'lesson_group_leader')) === null || _roles$find6 === void 0 ? void 0 : _roles$find6.id,\n          'class_teacher': (_roles$find7 = roles.find(r => r.code === 'class_teacher')) === null || _roles$find7 === void 0 ? void 0 : _roles$find7.id,\n          'teacher': (_roles$find8 = roles.find(r => r.code === 'teacher')) === null || _roles$find8 === void 0 ? void 0 : _roles$find8.id,\n          'student': (_roles$find9 = roles.find(r => r.code === 'student')) === null || _roles$find9 === void 0 ? void 0 : _roles$find9.id,\n          'admin': (_roles$find0 = roles.find(r => r.code === 'admin')) === null || _roles$find0 === void 0 ? void 0 : _roles$find0.id\n        };\n        roleId = roleMap[values.user_type];\n      }\n\n      // 创建新用户\n      const newUser = await createUser({\n        ...values,\n        password: values.password || '123456',\n        // 默认密码\n        school_id: targetSchoolId,\n        is_teacher: isTeacher,\n        is_admin: isAdmin\n      });\n      message.success('用户创建成功');\n      setNewUserModalVisible(false);\n\n      // 如果确定了角色，分配角色\n      if (roleId) {\n        try {\n          await assignRole({\n            user_id: newUser.id,\n            role_id: roleId,\n            school_id: targetSchoolId\n          });\n          message.success('角色分配成功');\n        } catch (roleError) {\n          console.error('角色分配失败:', roleError);\n          message.warning('用户创建成功，但角色分配失败');\n        }\n      }\n      fetchUsers(targetSchoolId);\n    } catch (error) {\n      console.error('创建用户失败:', error);\n      message.error('创建用户失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 为现有用户分配角色\n  const handleAssignExistingUser = async userId => {\n    // 获取当前操作的学校ID\n    const targetSchoolId = schoolId || user && user.school_id;\n    setCurrentUser(allUsers.find(u => u.id === userId));\n    roleForm.resetFields();\n    setRoleModalVisible(true);\n  };\n\n  // 班级表格列定义\n  const classColumns = [{\n    title: '班级名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '年级',\n    dataIndex: 'grade',\n    key: 'grade'\n  }, {\n    title: '学生数量',\n    dataIndex: 'student_count',\n    key: 'student_count',\n    render: text => text || 0\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => showClassModal(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => handleDeleteClass(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 用户表格列定义\n  const userColumns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '姓名',\n    dataIndex: 'full_name',\n    key: 'full_name'\n  }, {\n    title: '身份',\n    key: 'identity',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [record.is_admin && /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"red\",\n        children: \"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 31\n      }, this), record.is_teacher && (record.primary_role ? /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: record.primary_role.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 15\n      }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: \"\\u6559\\u5E08\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 15\n      }, this)), !record.is_admin && !record.is_teacher && /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"default\",\n        children: \"\\u5B66\\u751F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 54\n      }, this)]\n    }, void 0, true)\n  }, {\n    title: '主要角色',\n    dataIndex: 'primary_role',\n    key: 'primary_role',\n    render: (_, record) => record.primary_role ? /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: record.primary_role.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 31\n    }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"default\",\n      children: \"\\u672A\\u5206\\u914D\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 84\n    }, this)\n  }, {\n    title: '所有角色',\n    dataIndex: 'roles',\n    key: 'roles',\n    render: (roles, record) => getUserRoleTags(record)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 19\n        }, this),\n        size: \"small\",\n        onClick: () => showRoleModal(record),\n        children: \"\\u5206\\u914D\\u89D2\\u8272\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 11\n      }, this), record.roles && record.roles.length > 0 && record.roles.map(role => /*#__PURE__*/_jsxDEV(Button, {\n        danger: true,\n        size: \"small\",\n        onClick: () => handleRevokeRole(record.id, role.id),\n        children: [\"\\u64A4\\u9500 \", role.name]\n      }, role.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 604,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 渲染学校基本信息表单\n  const renderSchoolInfoForm = () => {\n    if (!school) return /*#__PURE__*/_jsxDEV(Spin, {\n      tip: \"\\u52A0\\u8F7D\\u5B66\\u6821\\u4FE1\\u606F\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 25\n    }, this);\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"vertical\",\n        initialValues: {\n          name: school.name,\n          province: school.province,\n          city: school.city,\n          district: school.district\n        },\n        onFinish: handleUpdateSchool,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u5B66\\u6821\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入学校名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"province\",\n          label: \"\\u7701\\u4EFD\",\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"city\",\n          label: \"\\u57CE\\u5E02\",\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"district\",\n          label: \"\\u533A\\u53BF\",\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"\\u4FDD\\u5B58\\u4FEE\\u6539\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this)\n    }, void 0, false);\n  };\n\n  // 渲染班级管理表格\n  const renderClassesTable = () => {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 19\n          }, this),\n          onClick: () => showClassModal(),\n          children: \"\\u6DFB\\u52A0\\u73ED\\u7EA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: classColumns,\n        dataSource: classes,\n        rowKey: \"id\",\n        loading: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: currentClass ? \"编辑班级\" : \"添加班级\",\n        open: classModalVisible,\n        onOk: handleClassSubmit,\n        onCancel: handleClassCancel,\n        confirmLoading: loading,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"\\u73ED\\u7EA7\\u540D\\u79F0\",\n            rules: [{\n              required: true,\n              message: '请输入班级名称'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u4F8B\\u5982\\uFF1A\\u4E03\\u5E74\\u7EA71\\u73ED\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"grade\",\n            label: \"\\u5E74\\u7EA7\",\n            rules: [{\n              required: true,\n              message: '请输入年级'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u5E74\\u7EA7\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u4E00\\u5E74\\u7EA7\",\n                children: \"\\u4E00\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u4E8C\\u5E74\\u7EA7\",\n                children: \"\\u4E8C\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u4E09\\u5E74\\u7EA7\",\n                children: \"\\u4E09\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u56DB\\u5E74\\u7EA7\",\n                children: \"\\u56DB\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u4E94\\u5E74\\u7EA7\",\n                children: \"\\u4E94\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u516D\\u5E74\\u7EA7\",\n                children: \"\\u516D\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u4E03\\u5E74\\u7EA7\",\n                children: \"\\u4E03\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u516B\\u5E74\\u7EA7\",\n                children: \"\\u516B\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u4E5D\\u5E74\\u7EA7\",\n                children: \"\\u4E5D\\u5E74\\u7EA7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u9AD8\\u4E00\",\n                children: \"\\u9AD8\\u4E00\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u9AD8\\u4E8C\",\n                children: \"\\u9AD8\\u4E8C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"\\u9AD8\\u4E09\",\n                children: \"\\u9AD8\\u4E09\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 700,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n\n  // 渲染角色管理表格\n  const renderRolesTable = () => {\n    const isAdmin = user && user.is_admin;\n    const isPrincipal = user && user.roles && user.roles.some(r => r.code === 'principal');\n    const canManageUsers = isAdmin || isPrincipal;\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [canManageUsers && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 21\n          }, this),\n          onClick: showNewUserModal,\n          style: {\n            marginRight: 8\n          },\n          children: \"\\u521B\\u5EFA\\u65B0\\u7528\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 13\n        }, this), isAdmin && /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u6DFB\\u52A0\\u5DF2\\u6709\\u7528\\u6237\\u5230\\u672C\\u6821\",\n          style: {\n            width: 300\n          },\n          showSearch: true,\n          filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n          onChange: handleAssignExistingUser,\n          value: undefined,\n          children: allUsers.filter(u => !users.some(su => su.id === u.id)) // 过滤掉已在学校的用户\n          .map(u => /*#__PURE__*/_jsxDEV(Option, {\n            value: u.id,\n            children: [u.full_name || u.username, \" (\", u.is_admin ? '管理员' : u.is_teacher ? '教师' : '学生', \")\"]\n          }, u.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: userColumns,\n        dataSource: users,\n        rowKey: \"id\",\n        loading: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: \"\\u5206\\u914D\\u89D2\\u8272\",\n        open: roleModalVisible,\n        onOk: handleRoleSubmit,\n        onCancel: handleRoleCancel,\n        confirmLoading: loading,\n        children: currentUser && /*#__PURE__*/_jsxDEV(Form, {\n          form: roleForm,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u4E3A\\u7528\\u6237 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: currentUser.full_name || currentUser.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 22\n            }, this), \" \\u5206\\u914D\\u89D2\\u8272\\uFF1A\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"role_id\",\n            label: \"\\u89D2\\u8272\",\n            rules: [{\n              required: true,\n              message: '请选择角色'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n              children: roles.filter(role => isAdmin || role.level < 90).map(role => /*#__PURE__*/_jsxDEV(Option, {\n                value: role.id,\n                children: [role.name, \" (\", role.description || role.code, \")\"]\n              }, role.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 796,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: \"\\u521B\\u5EFA\\u65B0\\u7528\\u6237\",\n        open: newUserModalVisible,\n        onOk: handleNewUserSubmit,\n        onCancel: handleNewUserCancel,\n        confirmLoading: loading,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: userForm,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"username\",\n            label: \"\\u7528\\u6237\\u540D\",\n            rules: [{\n              required: true,\n              message: '请输入用户名'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"full_name\",\n            label: \"\\u59D3\\u540D\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u59D3\\u540D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"email\",\n            label: \"\\u90AE\\u7BB1\",\n            rules: [{\n              type: 'email',\n              message: '请输入有效的邮箱地址'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            label: \"\\u5BC6\\u7801\",\n            extra: \"\\u5982\\u4E0D\\u586B\\u5199\\uFF0C\\u5C06\\u4F7F\\u7528\\u9ED8\\u8BA4\\u5BC6\\u7801: 123456\",\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"user_type\",\n            label: \"\\u7528\\u6237\\u7C7B\\u578B\",\n            rules: [{\n              required: true,\n              message: '请选择用户类型'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\\u7C7B\\u578B\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"student\",\n                children: \"\\u5B66\\u751F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"teacher\",\n                children: \"\\u666E\\u901A\\u6559\\u5E08\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"class_teacher\",\n                children: \"\\u73ED\\u4E3B\\u4EFB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"lesson_group_leader\",\n                children: \"\\u5907\\u8BFE\\u7EC4\\u957F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"subject_leader\",\n                children: \"\\u6559\\u7814\\u7EC4\\u957F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"grade_leader\",\n                children: \"\\u5E74\\u7EA7\\u7EC4\\u957F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"academic_director\",\n                children: \"\\u6559\\u52A1\\u5904\\u4E3B\\u4EFB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"vice_principal\",\n                children: \"\\u526F\\u6821\\u957F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"principal\",\n                children: \"\\u6821\\u957F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 17\n              }, this), isAdmin && /*#__PURE__*/_jsxDEV(Option, {\n                value: \"admin\",\n                children: \"\\u7CFB\\u7EDF\\u7BA1\\u7406\\u5458\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"role_id\",\n            label: \"\\u521D\\u59CB\\u89D2\\u8272\",\n            extra: \"\\u53EF\\u9009\\uFF0C\\u521B\\u5EFA\\u7528\\u6237\\u540E\\u76F4\\u63A5\\u5206\\u914D\\u89D2\\u8272\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n              children: roles.filter(role => isAdmin || role.level < 90).map(role => /*#__PURE__*/_jsxDEV(Option, {\n                value: role.id,\n                children: [role.name, \" (\", role.description || role.code, \")\"]\n              }, role.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"school-management\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B66\\u6821\\u7BA1\\u7406\",\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading && !classModalVisible && !roleModalVisible && !newUserModalVisible,\n        children: user && user.is_admin ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\u5B66\\u6821\\u5217\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 917,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: allSchools,\n            rowKey: \"id\",\n            pagination: {\n              pageSize: 10\n            },\n            columns: [{\n              title: '学校名称',\n              dataIndex: 'name',\n              key: 'name'\n            }, {\n              title: '省份',\n              dataIndex: 'province',\n              key: 'province'\n            }, {\n              title: '城市',\n              dataIndex: 'city',\n              key: 'city'\n            }, {\n              title: '区县',\n              dataIndex: 'district',\n              key: 'district'\n            }, {\n              title: '操作',\n              key: 'action',\n              render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  onClick: () => {\n                    setSelectedSchoolId(record.id);\n                    fetchSchoolData(record.id);\n                  },\n                  children: \"\\u7BA1\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  danger: true,\n                  onClick: () => {\n                    Modal.confirm({\n                      title: '确认删除',\n                      content: `确定要删除学校 \"${record.name}\" 吗？此操作不可恢复，且会影响所有关联的班级和用户。`,\n                      okText: '确认删除',\n                      okType: 'danger',\n                      cancelText: '取消',\n                      onOk: async () => {\n                        try {\n                          await deleteSchool(record.id);\n                          message.success('学校删除成功');\n                          // 如果删除的是当前选中的学校，清除选择\n                          if (selectedSchoolId === record.id) {\n                            setSelectedSchoolId(null);\n                            setSchool(null);\n                          }\n                          // 重新获取学校列表\n                          fetchAllSchools();\n                        } catch (error) {\n                          message.error('删除学校失败: ' + (error.message || '未知错误'));\n                          console.error('删除学校失败:', error);\n                        }\n                      }\n                    });\n                  },\n                  children: \"\\u5220\\u9664\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 23\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 15\n          }, this), selectedSchoolId && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 20\n            },\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: ((_allSchools$find = allSchools.find(s => s.id === selectedSchoolId)) === null || _allSchools$find === void 0 ? void 0 : _allSchools$find.name) || '学校详情'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 996,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n              activeKey: activeTab,\n              onChange: setActiveTab,\n              children: [/*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 27\n                  }, this), \"\\u5B66\\u6821\\u57FA\\u672C\\u4FE1\\u606F\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 25\n                }, this),\n                children: renderSchoolInfoForm()\n              }, \"1\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 999,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 27\n                  }, this), \"\\u73ED\\u7EA7\\u7BA1\\u7406\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 25\n                }, this),\n                children: renderClassesTable()\n              }, \"2\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n                tab: /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 27\n                  }, this), \"\\u89D2\\u8272\\u7BA1\\u7406\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 25\n                }, this),\n                children: renderRolesTable()\n              }, \"3\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: activeTab,\n          onChange: setActiveTab,\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 21\n              }, this), \"\\u5B66\\u6821\\u57FA\\u672C\\u4FE1\\u606F\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 19\n            }, this),\n            children: renderSchoolInfoForm()\n          }, \"1\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 21\n              }, this), \"\\u73ED\\u7EA7\\u7BA1\\u7406\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 19\n            }, this),\n            children: renderClassesTable()\n          }, \"2\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 21\n              }, this), \"\\u89D2\\u8272\\u7BA1\\u7406\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1062,\n              columnNumber: 19\n            }, this),\n            children: renderRolesTable()\n          }, \"3\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 1060,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1037,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 914,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 913,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 912,\n    columnNumber: 5\n  }, this);\n};\n_s(SchoolManagement, \"SbcSC6sdWUG45ZSd5kD9Qco4EV0=\", false, function () {\n  return [useAuth, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = SchoolManagement;\nexport default SchoolManagement;\nvar _c;\n$RefreshReg$(_c, \"SchoolManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Tabs", "Card", "Table", "<PERSON><PERSON>", "message", "Spin", "Modal", "Form", "Input", "Select", "Tag", "Space", "<PERSON><PERSON><PERSON>", "Divider", "EditOutlined", "DeleteOutlined", "PlusOutlined", "UserOutlined", "TeamOutlined", "SettingOutlined", "UserAddOutlined", "getSchoolDetail", "updateSchoolInfo", "getClassesBySchool", "createClassForSchool", "updateClassInfo", "deleteClassById", "getSchoolRoles", "assignRole", "revokeRole", "getSchoolUsers", "getUsers", "createUser", "getSchools", "deleteSchool", "useAuth", "getUserRoleTags", "ExclamationCircleOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TabPane", "Option", "SchoolManagement", "schoolId", "_s", "_allSchools$find", "user", "activeTab", "setActiveTab", "school", "setSchool", "classes", "setClasses", "roles", "setRoles", "users", "setUsers", "loading", "setLoading", "classModalVisible", "setClassModalVisible", "roleModalVisible", "setRoleModalVisible", "newUserModalVisible", "setNewUserModalVisible", "currentClass", "setCurrentClass", "currentUser", "setCurrentUser", "form", "useForm", "roleForm", "userForm", "allUsers", "setAllUsers", "allSchools", "setAllSchools", "selectedSchoolId", "setSelectedSchoolId", "is_admin", "fetchAllSchools", "rawSchoolId", "school_id", "targetSchoolId", "undefined", "parseInt", "isNaN", "console", "error", "log", "userSchoolId", "schoolIdType", "fetchSchoolData", "warn", "numericSchoolId", "schools", "schoolsList", "Array", "isArray", "items", "fetchClasses", "fetchRoles", "fetchUsers", "fetchAllUsers", "data", "apiError", "response", "handleUpdateSchool", "values", "id", "success", "showClassModal", "record", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "grade", "handleClassCancel", "handleClassSubmit", "validateFields", "handleDeleteClass", "classId", "status", "showRoleModal", "handleRoleCancel", "handleRoleSubmit", "user_id", "role_id", "handleRevokeRole", "userId", "roleId", "confirm", "title", "content", "okText", "cancelText", "onOk", "showNewUserModal", "handleNewUserCancel", "handleNewUserSubmit", "<PERSON><PERSON><PERSON>er", "includes", "user_type", "isAdmin", "_roles$find", "_roles$find2", "_roles$find3", "_roles$find4", "_roles$find5", "_roles$find6", "_roles$find7", "_roles$find8", "_roles$find9", "_roles$find0", "roleMap", "find", "r", "code", "newUser", "password", "is_teacher", "roleError", "warning", "handleAssignExistingUser", "u", "classColumns", "dataIndex", "key", "render", "text", "_", "size", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "danger", "userColumns", "color", "primary_role", "length", "map", "role", "renderSchoolInfoForm", "tip", "layout", "initialValues", "province", "city", "district", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "htmlType", "renderClassesTable", "style", "marginBottom", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onCancel", "confirmLoading", "placeholder", "value", "renderRolesTable", "is<PERSON><PERSON><PERSON>pal", "some", "canManageUsers", "marginRight", "width", "showSearch", "filterOption", "input", "option", "toLowerCase", "indexOf", "onChange", "filter", "su", "full_name", "username", "level", "description", "extra", "Password", "className", "spinning", "pagination", "pageSize", "okType", "marginTop", "s", "active<PERSON><PERSON>", "tab", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/SchoolManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Tabs, Card, Table, Button, message, Spin, Modal, Form, Input, Select, Tag, Space, Tooltip, Divider } from 'antd';\r\nimport { EditOutlined, DeleteOutlined, PlusOutlined, UserOutlined, TeamOutlined, SettingOutlined, UserAddOutlined } from '@ant-design/icons';\r\nimport { getSchoolDetail, updateSchoolInfo, getClassesBySchool, createClassForSchool, updateClassInfo, deleteClassById,\r\n         getSchoolRoles, assignRole, revokeRole, getSchoolUsers, getUsers, createUser, getSchools, deleteSchool } from '../utils/api';\r\nimport { useAuth } from '../utils/auth';\r\nimport { getUserRoleTags } from '../utils/roleUtils';\r\nimport { ExclamationCircleOutlined } from '@ant-design/icons';\r\n\r\nconst { TabPane } = Tabs;\r\nconst { Option } = Select;\r\n\r\nconst SchoolManagement = ({ schoolId }) => {\r\n  const { user } = useAuth();\r\n  const [activeTab, setActiveTab] = useState('1');\r\n  const [school, setSchool] = useState(null);\r\n  const [classes, setClasses] = useState([]);\r\n  const [roles, setRoles] = useState([]);\r\n  const [users, setUsers] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [classModalVisible, setClassModalVisible] = useState(false);\r\n  const [roleModalVisible, setRoleModalVisible] = useState(false);\r\n  const [newUserModalVisible, setNewUserModalVisible] = useState(false);\r\n  const [currentClass, setCurrentClass] = useState(null);\r\n  const [currentUser, setCurrentUser] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [roleForm] = Form.useForm();\r\n  const [userForm] = Form.useForm();\r\n  const [allUsers, setAllUsers] = useState([]);\r\n  const [allSchools, setAllSchools] = useState([]);\r\n  const [selectedSchoolId, setSelectedSchoolId] = useState(schoolId);\r\n\r\n  // 获取所有学校列表（仅超级管理员）\r\n  useEffect(() => {\r\n    if (user && user.is_admin) {\r\n      fetchAllSchools();\r\n    }\r\n  }, [user]);\r\n\r\n  // 获取学校信息\r\n  useEffect(() => {\r\n    // 优先使用选择的学校ID，其次是传入的schoolId，最后是用户自己的school_id\r\n    const rawSchoolId = selectedSchoolId || schoolId || (user && user.school_id);\r\n    \r\n    // 确保学校ID是数字类型\r\n    let targetSchoolId = null;\r\n    if (rawSchoolId !== null && rawSchoolId !== undefined) {\r\n      targetSchoolId = parseInt(rawSchoolId, 10);\r\n      if (isNaN(targetSchoolId)) {\r\n        console.error('无效的学校ID:', rawSchoolId);\r\n        targetSchoolId = null;\r\n      }\r\n    }\r\n    \r\n    console.log('SchoolManagement接收到的学校ID:', { \r\n      selectedSchoolId, \r\n      schoolId, \r\n      userSchoolId: user?.school_id, \r\n      rawSchoolId,\r\n      targetSchoolId,\r\n      schoolIdType: typeof schoolId\r\n    });\r\n    \r\n    // 如果有目标学校ID，则获取学校数据\r\n    if (targetSchoolId !== null) {\r\n      console.log(`准备获取学校数据，ID: ${targetSchoolId}`);\r\n      fetchSchoolData(targetSchoolId);\r\n    } else if (user && user.is_admin) {\r\n      // 超级管理员但没有指定学校ID，显示学校列表\r\n      console.log('超级管理员没有指定学校ID，显示学校列表');\r\n    } else {\r\n      console.warn('无法确定要获取的学校ID');\r\n    }\r\n  }, [selectedSchoolId, schoolId, user]);\r\n  \r\n  // 当组件挂载或schoolId变化时，更新selectedSchoolId\r\n  useEffect(() => {\r\n    console.log('SchoolManagement组件props变化，schoolId:', schoolId, '类型:', typeof schoolId);\r\n    \r\n    // 检查schoolId是否有效\r\n    if (schoolId !== null && schoolId !== undefined) {\r\n      console.log('SchoolManagement组件接收到新的schoolId:', schoolId);\r\n      \r\n      // 确保ID是数字类型\r\n      const numericSchoolId = parseInt(schoolId, 10);\r\n      if (isNaN(numericSchoolId)) {\r\n        console.error('传入的schoolId不是有效数字:', schoolId);\r\n        message.error('无效的学校ID');\r\n        return;\r\n      }\r\n      \r\n      setSelectedSchoolId(numericSchoolId);\r\n      console.log(`已更新selectedSchoolId: ${numericSchoolId}`);\r\n      \r\n      // 立即获取学校数据\r\n      fetchSchoolData(numericSchoolId);\r\n    } else {\r\n      console.warn('SchoolManagement组件没有接收到有效的schoolId');\r\n    }\r\n  }, [schoolId]);\r\n  \r\n  // 获取所有学校列表\r\n  const fetchAllSchools = async () => {\r\n    try {\r\n      const schools = await getSchools();\r\n      console.log('获取所有学校列表:', schools);\r\n      \r\n      // 处理不同格式的返回数据\r\n      let schoolsList = [];\r\n      if (Array.isArray(schools)) {\r\n        schoolsList = schools;\r\n      } else if (schools && Array.isArray(schools.items)) {\r\n        schoolsList = schools.items;\r\n      }\r\n      \r\n      console.log('处理后的学校列表:', schoolsList);\r\n      setAllSchools(schoolsList);\r\n    } catch (error) {\r\n      console.error('获取学校列表失败:', error);\r\n    }\r\n  };\r\n\r\n  // 切换标签页时加载数据\r\n  useEffect(() => {\r\n    // 优先使用选择的学校ID，其次是传入的schoolId，最后是用户自己的school_id\r\n    const targetSchoolId = selectedSchoolId || schoolId || (user && user.school_id);\r\n    // 超级管理员可以看到所有学校，不需要school_id\r\n    if (targetSchoolId || (user && user.is_admin)) {\r\n      if (activeTab === '2') {\r\n        fetchClasses(targetSchoolId);\r\n      } else if (activeTab === '3') {\r\n        fetchRoles();\r\n        fetchUsers(targetSchoolId);\r\n        fetchAllUsers(); // 获取所有用户，用于添加已有用户\r\n      }\r\n    }\r\n  }, [activeTab, selectedSchoolId, schoolId, user]);\r\n  \r\n  // 当选择的学校ID变化时，重新获取学校信息、班级列表和用户列表\r\n  useEffect(() => {\r\n    if (selectedSchoolId) {\r\n      if (activeTab === '1') {\r\n        fetchSchoolData(selectedSchoolId);\r\n      } else if (activeTab === '2') {\r\n        fetchClasses(selectedSchoolId);\r\n      } else if (activeTab === '3') {\r\n        fetchUsers(selectedSchoolId);\r\n      }\r\n    }\r\n  }, [selectedSchoolId]);\r\n\r\n  // 获取学校数据\r\n  const fetchSchoolData = async (targetSchoolId) => {\r\n    setLoading(true);\r\n    try {\r\n      console.log('fetchSchoolData被调用，传入的学校ID:', targetSchoolId, '类型:', typeof targetSchoolId);\r\n      \r\n      if (targetSchoolId === null || targetSchoolId === undefined) {\r\n        console.error('未提供学校ID，无法获取学校详情');\r\n        message.error('未提供学校ID，无法获取学校详情');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 确保学校ID是数字类型\r\n      const schoolId = parseInt(targetSchoolId, 10);\r\n      if (isNaN(schoolId)) {\r\n        console.error('无效的学校ID:', targetSchoolId);\r\n        message.error('无效的学校ID');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 获取指定学校的详情\r\n      console.log(`获取学校详情，ID: ${schoolId}`);\r\n      try {\r\n        const data = await getSchoolDetail(schoolId);\r\n        console.log('获取到学校详情:', data);\r\n        \r\n        if (!data) {\r\n          console.error(`学校ID ${schoolId} 未返回有效数据`);\r\n          message.error('获取学校详情失败：服务器未返回数据');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n        \r\n        setSchool(data);\r\n        \r\n        // 同时获取该学校的班级和用户\r\n        fetchClasses(schoolId);\r\n        if (activeTab === '3') {\r\n          fetchUsers(schoolId);\r\n        }\r\n      } catch (apiError) {\r\n        console.error(`获取学校详情API调用失败，ID: ${schoolId}:`, apiError);\r\n        message.error(`获取学校详情失败: ${apiError.message || '未知错误'}`);\r\n      }\r\n    } catch (error) {\r\n      message.error('获取学校信息失败');\r\n      console.error('获取学校信息失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取班级数据\r\n  const fetchClasses = async (targetSchoolId) => {\r\n    try {\r\n      setLoading(true);\r\n      console.log(`获取学校班级列表，学校ID: ${targetSchoolId}`);\r\n      const response = await getClassesBySchool(targetSchoolId);\r\n      console.log('获取班级列表成功:', response);\r\n      setClasses(response || []);\r\n    } catch (error) {\r\n      console.error('获取班级列表失败:', error);\r\n      message.error('获取班级列表失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取角色数据\r\n  const fetchRoles = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const data = await getSchoolRoles();\r\n      setRoles(data);\r\n    } catch (error) {\r\n      message.error('获取角色信息失败');\r\n      console.error('获取角色信息失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取用户数据\r\n  const fetchUsers = async (targetSchoolId) => {\r\n    setLoading(true);\r\n    try {\r\n      const data = await getSchoolUsers(targetSchoolId);\r\n      setUsers(data);\r\n    } catch (error) {\r\n      message.error('获取用户信息失败');\r\n      console.error('获取用户信息失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 获取所有用户（用于添加已有用户）\r\n  const fetchAllUsers = async () => {\r\n    try {\r\n      const data = await getUsers();\r\n      // 确保data是正确的格式，如果是新格式(带有items字段)，使用items数组\r\n      if (data && typeof data === 'object' && 'items' in data) {\r\n        setAllUsers(data.items || []);\r\n      } else {\r\n        // 如果是旧格式(直接是数组)或其他格式，直接使用\r\n        setAllUsers(data || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取所有用户失败:', error);\r\n    }\r\n  };\r\n\r\n  // 更新学校信息\r\n  const handleUpdateSchool = async (values) => {\r\n    if (!school) return;\r\n    \r\n    setLoading(true);\r\n    try {\r\n      await updateSchoolInfo(school.id, values);\r\n      message.success('学校信息更新成功');\r\n      fetchSchoolData(school.id);\r\n    } catch (error) {\r\n      message.error('学校信息更新失败');\r\n      console.error('学校信息更新失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 打开班级编辑模态框\r\n  const showClassModal = (record = null) => {\r\n    setCurrentClass(record);\r\n    form.resetFields();\r\n    if (record) {\r\n      form.setFieldsValue({\r\n        name: record.name,\r\n        grade: record.grade,\r\n      });\r\n    }\r\n    setClassModalVisible(true);\r\n  };\r\n\r\n  // 关闭班级编辑模态框\r\n  const handleClassCancel = () => {\r\n    setClassModalVisible(false);\r\n    setCurrentClass(null);\r\n    form.resetFields();\r\n  };\r\n\r\n  // 保存班级信息\r\n  const handleClassSubmit = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      setLoading(true);\r\n      \r\n      if (currentClass) {\r\n        // 更新班级\r\n        console.log(`更新班级，ID: ${currentClass.id}, 数据:`, values);\r\n        await updateClassInfo(school.id, currentClass.id, values);\r\n        message.success('班级更新成功');\r\n      } else {\r\n        // 创建班级\r\n        console.log(`创建班级，学校ID: ${school.id}, 数据:`, values);\r\n        await createClassForSchool(school.id, { ...values, school_id: school.id });\r\n        message.success('班级创建成功');\r\n      }\r\n      \r\n      setClassModalVisible(false);\r\n      form.resetFields();\r\n      fetchClasses(school.id);\r\n    } catch (error) {\r\n      console.error('保存班级失败:', error);\r\n      message.error('保存班级失败: ' + (error.message || '未知错误'));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 删除班级\r\n  const handleDeleteClass = async (classId) => {\r\n    try {\r\n      setLoading(true);\r\n      console.log(`删除班级，ID: ${classId}`);\r\n      await deleteClassById(school.id, classId);\r\n      message.success('班级删除成功');\r\n      fetchClasses(school.id);\r\n    } catch (error) {\r\n      console.error('删除班级失败:', error);\r\n      if (error.response && error.response.status === 400) {\r\n        message.error('班级中还有学生，无法删除');\r\n      } else {\r\n        message.error('删除班级失败: ' + (error.message || '未知错误'));\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 打开角色分配模态框\r\n  const showRoleModal = (record) => {\r\n    setCurrentUser(record);\r\n    roleForm.resetFields();\r\n    setRoleModalVisible(true);\r\n  };\r\n\r\n  // 关闭角色分配模态框\r\n  const handleRoleCancel = () => {\r\n    setRoleModalVisible(false);\r\n    setCurrentUser(null);\r\n    roleForm.resetFields();\r\n  };\r\n\r\n  // 分配角色\r\n  const handleRoleSubmit = async () => {\r\n    try {\r\n      const values = await roleForm.validateFields();\r\n      setLoading(true);\r\n      \r\n      // 获取当前操作的学校ID\r\n      const targetSchoolId = schoolId || (user && user.school_id);\r\n      \r\n      await assignRole({\r\n        user_id: currentUser.id,\r\n        role_id: values.role_id,\r\n        school_id: targetSchoolId\r\n      });\r\n      \r\n      message.success('角色分配成功');\r\n      setRoleModalVisible(false);\r\n      fetchUsers(targetSchoolId);\r\n    } catch (error) {\r\n      console.error('角色分配失败:', error);\r\n      message.error('角色分配失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 撤销角色\r\n  const handleRevokeRole = async (userId, roleId) => {\r\n    Modal.confirm({\r\n      title: '确认撤销',\r\n      content: '确定要撤销这个用户的角色吗？',\r\n      okText: '确认',\r\n      cancelText: '取消',\r\n      onOk: async () => {\r\n        setLoading(true);\r\n        try {\r\n          // 获取当前操作的学校ID\r\n          const targetSchoolId = schoolId || (user && user.school_id);\r\n          \r\n          await revokeRole({\r\n            user_id: userId,\r\n            role_id: roleId,\r\n            school_id: targetSchoolId\r\n          });\r\n          \r\n          message.success('角色撤销成功');\r\n          fetchUsers(targetSchoolId);\r\n        } catch (error) {\r\n          message.error('角色撤销失败');\r\n          console.error('角色撤销失败:', error);\r\n        } finally {\r\n          setLoading(false);\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  // 打开新用户模态框\r\n  const showNewUserModal = () => {\r\n    userForm.resetFields();\r\n    setNewUserModalVisible(true);\r\n  };\r\n\r\n  // 关闭新用户模态框\r\n  const handleNewUserCancel = () => {\r\n    setNewUserModalVisible(false);\r\n    userForm.resetFields();\r\n  };\r\n\r\n  // 创建新用户\r\n  const handleNewUserSubmit = async () => {\r\n    try {\r\n      const values = await userForm.validateFields();\r\n      setLoading(true);\r\n      \r\n      // 获取当前操作的学校ID\r\n      const targetSchoolId = schoolId || (user && user.school_id);\r\n      \r\n      // 确定用户类型\r\n      const isTeacher = ['teacher', 'class_teacher', 'lesson_group_leader', 'subject_leader', \r\n                         'grade_leader', 'academic_director', 'vice_principal', 'principal'].includes(values.user_type);\r\n      const isAdmin = values.user_type === 'admin';\r\n      \r\n      // 确定对应的角色ID\r\n      let roleId = values.role_id;\r\n      if (!roleId) {\r\n        // 根据用户类型自动设置角色\r\n        const roleMap = {\r\n          'principal': roles.find(r => r.code === 'principal')?.id,\r\n          'vice_principal': roles.find(r => r.code === 'vice_principal')?.id,\r\n          'academic_director': roles.find(r => r.code === 'academic_director')?.id,\r\n          'grade_leader': roles.find(r => r.code === 'grade_leader')?.id,\r\n          'subject_leader': roles.find(r => r.code === 'subject_leader')?.id,\r\n          'lesson_group_leader': roles.find(r => r.code === 'lesson_group_leader')?.id,\r\n          'class_teacher': roles.find(r => r.code === 'class_teacher')?.id,\r\n          'teacher': roles.find(r => r.code === 'teacher')?.id,\r\n          'student': roles.find(r => r.code === 'student')?.id,\r\n          'admin': roles.find(r => r.code === 'admin')?.id\r\n        };\r\n        roleId = roleMap[values.user_type];\r\n      }\r\n      \r\n      // 创建新用户\r\n      const newUser = await createUser({\r\n        ...values,\r\n        password: values.password || '123456', // 默认密码\r\n        school_id: targetSchoolId,\r\n        is_teacher: isTeacher,\r\n        is_admin: isAdmin\r\n      });\r\n      \r\n      message.success('用户创建成功');\r\n      setNewUserModalVisible(false);\r\n      \r\n      // 如果确定了角色，分配角色\r\n      if (roleId) {\r\n        try {\r\n          await assignRole({\r\n            user_id: newUser.id,\r\n            role_id: roleId,\r\n            school_id: targetSchoolId\r\n          });\r\n          message.success('角色分配成功');\r\n        } catch (roleError) {\r\n          console.error('角色分配失败:', roleError);\r\n          message.warning('用户创建成功，但角色分配失败');\r\n        }\r\n      }\r\n      \r\n      fetchUsers(targetSchoolId);\r\n    } catch (error) {\r\n      console.error('创建用户失败:', error);\r\n      message.error('创建用户失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 为现有用户分配角色\r\n  const handleAssignExistingUser = async (userId) => {\r\n    // 获取当前操作的学校ID\r\n    const targetSchoolId = schoolId || (user && user.school_id);\r\n    \r\n    setCurrentUser(allUsers.find(u => u.id === userId));\r\n    roleForm.resetFields();\r\n    setRoleModalVisible(true);\r\n  };\r\n\r\n  // 班级表格列定义\r\n  const classColumns = [\r\n    {\r\n      title: '班级名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n    },\r\n    {\r\n      title: '年级',\r\n      dataIndex: 'grade',\r\n      key: 'grade',\r\n    },\r\n    {\r\n      title: '学生数量',\r\n      dataIndex: 'student_count',\r\n      key: 'student_count',\r\n      render: (text) => text || 0\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space size=\"middle\">\r\n          <Button \r\n            type=\"primary\" \r\n            icon={<EditOutlined />} \r\n            size=\"small\"\r\n            onClick={() => showClassModal(record)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Button \r\n            danger \r\n            icon={<DeleteOutlined />} \r\n            size=\"small\"\r\n            onClick={() => handleDeleteClass(record.id)}\r\n          >\r\n            删除\r\n          </Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 用户表格列定义\r\n  const userColumns = [\r\n    {\r\n      title: '用户名',\r\n      dataIndex: 'username',\r\n      key: 'username',\r\n    },\r\n    {\r\n      title: '姓名',\r\n      dataIndex: 'full_name',\r\n      key: 'full_name',\r\n    },\r\n    {\r\n      title: '身份',\r\n      key: 'identity',\r\n      render: (_, record) => (\r\n        <>\r\n          {record.is_admin && <Tag color=\"red\">系统管理员</Tag>}\r\n          {record.is_teacher && \r\n            (record.primary_role ? \r\n              <Tag color=\"blue\">{record.primary_role.name}</Tag> : \r\n              <Tag color=\"blue\">教师</Tag>\r\n            )\r\n          }\r\n          {!record.is_admin && !record.is_teacher && <Tag color=\"default\">学生</Tag>}\r\n        </>\r\n      )\r\n    },\r\n    {\r\n      title: '主要角色',\r\n      dataIndex: 'primary_role',\r\n      key: 'primary_role',\r\n      render: (_, record) => (\r\n        record.primary_role ? <Tag color=\"blue\">{record.primary_role.name}</Tag> : <Tag color=\"default\">未分配</Tag>\r\n      )\r\n    },\r\n    {\r\n      title: '所有角色',\r\n      dataIndex: 'roles',\r\n      key: 'roles',\r\n      render: (roles, record) => getUserRoleTags(record)\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space size=\"middle\">\r\n          <Button \r\n            type=\"primary\" \r\n            icon={<UserOutlined />} \r\n            size=\"small\"\r\n            onClick={() => showRoleModal(record)}\r\n          >\r\n            分配角色\r\n          </Button>\r\n          {record.roles && record.roles.length > 0 && record.roles.map(role => (\r\n            <Button \r\n              key={role.id}\r\n              danger \r\n              size=\"small\"\r\n              onClick={() => handleRevokeRole(record.id, role.id)}\r\n            >\r\n              撤销 {role.name}\r\n            </Button>\r\n          ))}\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 渲染学校基本信息表单\r\n  const renderSchoolInfoForm = () => {\r\n    if (!school) return <Spin tip=\"加载学校信息中...\" />;\r\n\r\n    return (\r\n      <>\r\n        <Form\r\n          layout=\"vertical\"\r\n          initialValues={{\r\n            name: school.name,\r\n            province: school.province,\r\n            city: school.city,\r\n            district: school.district,\r\n          }}\r\n          onFinish={handleUpdateSchool}\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"学校名称\"\r\n            rules={[{ required: true, message: '请输入学校名称' }]}\r\n          >\r\n            <Input />\r\n          </Form.Item>\r\n          <Form.Item\r\n            name=\"province\"\r\n            label=\"省份\"\r\n          >\r\n            <Input />\r\n          </Form.Item>\r\n          <Form.Item\r\n            name=\"city\"\r\n            label=\"城市\"\r\n          >\r\n            <Input />\r\n          </Form.Item>\r\n          <Form.Item\r\n            name=\"district\"\r\n            label=\"区县\"\r\n          >\r\n            <Input />\r\n          </Form.Item>\r\n          <Form.Item>\r\n            <Button type=\"primary\" htmlType=\"submit\">\r\n              保存修改\r\n            </Button>\r\n          </Form.Item>\r\n        </Form>\r\n      </>\r\n    );\r\n  };\r\n\r\n  // 渲染班级管理表格\r\n  const renderClassesTable = () => {\r\n    return (\r\n      <>\r\n        <div style={{ marginBottom: 16 }}>\r\n          <Button\r\n            type=\"primary\"\r\n            icon={<PlusOutlined />}\r\n            onClick={() => showClassModal()}\r\n          >\r\n            添加班级\r\n          </Button>\r\n        </div>\r\n        <Table\r\n          columns={classColumns}\r\n          dataSource={classes}\r\n          rowKey=\"id\"\r\n          loading={loading}\r\n        />\r\n\r\n        {/* 班级编辑模态框 */}\r\n        <Modal\r\n          title={currentClass ? \"编辑班级\" : \"添加班级\"}\r\n          open={classModalVisible}\r\n          onOk={handleClassSubmit}\r\n          onCancel={handleClassCancel}\r\n          confirmLoading={loading}\r\n        >\r\n          <Form\r\n            form={form}\r\n            layout=\"vertical\"\r\n          >\r\n            <Form.Item\r\n              name=\"name\"\r\n              label=\"班级名称\"\r\n              rules={[{ required: true, message: '请输入班级名称' }]}\r\n            >\r\n              <Input placeholder=\"例如：七年级1班\" />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"grade\"\r\n              label=\"年级\"\r\n              rules={[{ required: true, message: '请输入年级' }]}\r\n            >\r\n              <Select placeholder=\"请选择年级\">\r\n                <Option value=\"一年级\">一年级</Option>\r\n                <Option value=\"二年级\">二年级</Option>\r\n                <Option value=\"三年级\">三年级</Option>\r\n                <Option value=\"四年级\">四年级</Option>\r\n                <Option value=\"五年级\">五年级</Option>\r\n                <Option value=\"六年级\">六年级</Option>\r\n                <Option value=\"七年级\">七年级</Option>\r\n                <Option value=\"八年级\">八年级</Option>\r\n                <Option value=\"九年级\">九年级</Option>\r\n                <Option value=\"高一\">高一</Option>\r\n                <Option value=\"高二\">高二</Option>\r\n                <Option value=\"高三\">高三</Option>\r\n              </Select>\r\n            </Form.Item>\r\n          </Form>\r\n        </Modal>\r\n      </>\r\n    );\r\n  };\r\n\r\n  // 渲染角色管理表格\r\n  const renderRolesTable = () => {\r\n    const isAdmin = user && user.is_admin;\r\n    const isPrincipal = user && user.roles && user.roles.some(r => r.code === 'principal');\r\n    const canManageUsers = isAdmin || isPrincipal;\r\n    \r\n    return (\r\n      <>\r\n        {canManageUsers && (\r\n          <div style={{ marginBottom: 16 }}>\r\n            <Button\r\n              type=\"primary\"\r\n              icon={<UserAddOutlined />}\r\n              onClick={showNewUserModal}\r\n              style={{ marginRight: 8 }}\r\n            >\r\n              创建新用户\r\n            </Button>\r\n\r\n            {isAdmin && (\r\n              <Select \r\n                placeholder=\"添加已有用户到本校\" \r\n                style={{ width: 300 }}\r\n                showSearch\r\n                filterOption={(input, option) =>\r\n                  option.children && typeof option.children === 'string' ? \r\n                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                }\r\n                onChange={handleAssignExistingUser}\r\n                value={undefined}\r\n              >\r\n                {allUsers\r\n                  .filter(u => !users.some(su => su.id === u.id)) // 过滤掉已在学校的用户\r\n                  .map(u => (\r\n                    <Option key={u.id} value={u.id}>\r\n                      {u.full_name || u.username} ({u.is_admin ? '管理员' : u.is_teacher ? '教师' : '学生'})\r\n                    </Option>\r\n                  ))\r\n                }\r\n              </Select>\r\n            )}\r\n          </div>\r\n        )}\r\n        \r\n        <Table\r\n          columns={userColumns}\r\n          dataSource={users}\r\n          rowKey=\"id\"\r\n          loading={loading}\r\n        />\r\n\r\n        {/* 角色分配模态框 */}\r\n        <Modal\r\n          title=\"分配角色\"\r\n          open={roleModalVisible}\r\n          onOk={handleRoleSubmit}\r\n          onCancel={handleRoleCancel}\r\n          confirmLoading={loading}\r\n        >\r\n          {currentUser && (\r\n            <Form\r\n              form={roleForm}\r\n              layout=\"vertical\"\r\n            >\r\n              <p>为用户 <strong>{currentUser.full_name || currentUser.username}</strong> 分配角色：</p>\r\n              <Form.Item\r\n                name=\"role_id\"\r\n                label=\"角色\"\r\n                rules={[{ required: true, message: '请选择角色' }]}\r\n              >\r\n                <Select placeholder=\"请选择角色\">\r\n                  {/* 超级管理员可以分配所有角色，校长只能分配非超级管理员角色 */}\r\n                  {roles\r\n                    .filter(role => isAdmin || role.level < 90)\r\n                    .map(role => (\r\n                      <Option key={role.id} value={role.id}>\r\n                        {role.name} ({role.description || role.code})\r\n                      </Option>\r\n                    ))\r\n                  }\r\n                </Select>\r\n              </Form.Item>\r\n            </Form>\r\n          )}\r\n        </Modal>\r\n        \r\n        {/* 创建新用户模态框 */}\r\n        <Modal\r\n          title=\"创建新用户\"\r\n          open={newUserModalVisible}\r\n          onOk={handleNewUserSubmit}\r\n          onCancel={handleNewUserCancel}\r\n          confirmLoading={loading}\r\n        >\r\n          <Form\r\n            form={userForm}\r\n            layout=\"vertical\"\r\n          >\r\n            <Form.Item\r\n              name=\"username\"\r\n              label=\"用户名\"\r\n              rules={[{ required: true, message: '请输入用户名' }]}\r\n            >\r\n              <Input placeholder=\"请输入用户名\" />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"full_name\"\r\n              label=\"姓名\"\r\n            >\r\n              <Input placeholder=\"请输入姓名\" />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"email\"\r\n              label=\"邮箱\"\r\n              rules={[\r\n                { type: 'email', message: '请输入有效的邮箱地址' }\r\n              ]}\r\n            >\r\n              <Input placeholder=\"请输入邮箱\" />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"password\"\r\n              label=\"密码\"\r\n              extra=\"如不填写，将使用默认密码: 123456\"\r\n            >\r\n              <Input.Password placeholder=\"请输入密码\" />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"user_type\"\r\n              label=\"用户类型\"\r\n              rules={[{ required: true, message: '请选择用户类型' }]}\r\n            >\r\n              <Select placeholder=\"请选择用户类型\">\r\n                <Option value=\"student\">学生</Option>\r\n                <Option value=\"teacher\">普通教师</Option>\r\n                <Option value=\"class_teacher\">班主任</Option>\r\n                <Option value=\"lesson_group_leader\">备课组长</Option>\r\n                <Option value=\"subject_leader\">教研组长</Option>\r\n                <Option value=\"grade_leader\">年级组长</Option>\r\n                <Option value=\"academic_director\">教务处主任</Option>\r\n                <Option value=\"vice_principal\">副校长</Option>\r\n                <Option value=\"principal\">校长</Option>\r\n                {isAdmin && <Option value=\"admin\">系统管理员</Option>}\r\n              </Select>\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"role_id\"\r\n              label=\"初始角色\"\r\n              extra=\"可选，创建用户后直接分配角色\"\r\n            >\r\n              <Select placeholder=\"请选择角色\">\r\n                {roles\r\n                  .filter(role => isAdmin || role.level < 90)\r\n                  .map(role => (\r\n                    <Option key={role.id} value={role.id}>\r\n                      {role.name} ({role.description || role.code})\r\n                    </Option>\r\n                  ))\r\n                }\r\n              </Select>\r\n            </Form.Item>\r\n          </Form>\r\n        </Modal>\r\n      </>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"school-management\">\r\n      <Card title=\"学校管理\">\r\n        <Spin spinning={loading && !classModalVisible && !roleModalVisible && !newUserModalVisible}>\r\n          {user && user.is_admin ? (\r\n            <div>\r\n              <h2>学校列表</h2>\r\n              <Table\r\n                dataSource={allSchools}\r\n                rowKey=\"id\"\r\n                pagination={{ pageSize: 10 }}\r\n                columns={[\r\n                  {\r\n                    title: '学校名称',\r\n                    dataIndex: 'name',\r\n                    key: 'name',\r\n                  },\r\n                  {\r\n                    title: '省份',\r\n                    dataIndex: 'province',\r\n                    key: 'province',\r\n                  },\r\n                  {\r\n                    title: '城市',\r\n                    dataIndex: 'city',\r\n                    key: 'city',\r\n                  },\r\n                  {\r\n                    title: '区县',\r\n                    dataIndex: 'district',\r\n                    key: 'district',\r\n                  },\r\n                  {\r\n                    title: '操作',\r\n                    key: 'action',\r\n                    render: (_, record) => (\r\n                      <Space>\r\n                        <Button \r\n                          type=\"primary\"\r\n                          onClick={() => {\r\n                            setSelectedSchoolId(record.id);\r\n                            fetchSchoolData(record.id);\r\n                          }}\r\n                        >\r\n                          管理\r\n                        </Button>\r\n                        <Button \r\n                          danger\r\n                          onClick={() => {\r\n                            Modal.confirm({\r\n                              title: '确认删除',\r\n                              content: `确定要删除学校 \"${record.name}\" 吗？此操作不可恢复，且会影响所有关联的班级和用户。`,\r\n                              okText: '确认删除',\r\n                              okType: 'danger',\r\n                              cancelText: '取消',\r\n                              onOk: async () => {\r\n                                try {\r\n                                  await deleteSchool(record.id);\r\n                                  message.success('学校删除成功');\r\n                                  // 如果删除的是当前选中的学校，清除选择\r\n                                  if (selectedSchoolId === record.id) {\r\n                                    setSelectedSchoolId(null);\r\n                                    setSchool(null);\r\n                                  }\r\n                                  // 重新获取学校列表\r\n                                  fetchAllSchools();\r\n                                } catch (error) {\r\n                                  message.error('删除学校失败: ' + (error.message || '未知错误'));\r\n                                  console.error('删除学校失败:', error);\r\n                                }\r\n                              }\r\n                            });\r\n                          }}\r\n                        >\r\n                          删除\r\n                        </Button>\r\n                      </Space>\r\n                    ),\r\n                  },\r\n                ]}\r\n              />\r\n              \r\n              {selectedSchoolId && (\r\n                <div style={{ marginTop: 20 }}>\r\n                  <Divider>\r\n                    <h3>{allSchools.find(s => s.id === selectedSchoolId)?.name || '学校详情'}</h3>\r\n                  </Divider>\r\n                  <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n                    <TabPane\r\n                      tab={\r\n                        <span>\r\n                          <SettingOutlined />\r\n                          学校基本信息\r\n                        </span>\r\n                      }\r\n                      key=\"1\"\r\n                    >\r\n                      {renderSchoolInfoForm()}\r\n                    </TabPane>\r\n                    <TabPane\r\n                      tab={\r\n                        <span>\r\n                          <TeamOutlined />\r\n                          班级管理\r\n                        </span>\r\n                      }\r\n                      key=\"2\"\r\n                    >\r\n                      {renderClassesTable()}\r\n                    </TabPane>\r\n                    <TabPane\r\n                      tab={\r\n                        <span>\r\n                          <UserOutlined />\r\n                          角色管理\r\n                        </span>\r\n                      }\r\n                      key=\"3\"\r\n                    >\r\n                      {renderRolesTable()}\r\n                    </TabPane>\r\n                  </Tabs>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <Tabs activeKey={activeTab} onChange={setActiveTab}>\r\n              <TabPane\r\n                tab={\r\n                  <span>\r\n                    <SettingOutlined />\r\n                    学校基本信息\r\n                  </span>\r\n                }\r\n                key=\"1\"\r\n              >\r\n                {renderSchoolInfoForm()}\r\n              </TabPane>\r\n              <TabPane\r\n                tab={\r\n                  <span>\r\n                    <TeamOutlined />\r\n                    班级管理\r\n                  </span>\r\n                }\r\n                key=\"2\"\r\n              >\r\n                {renderClassesTable()}\r\n              </TabPane>\r\n              <TabPane\r\n                tab={\r\n                  <span>\r\n                    <UserOutlined />\r\n                    角色管理\r\n                  </span>\r\n                }\r\n                key=\"3\"\r\n              >\r\n                {renderRolesTable()}\r\n              </TabPane>\r\n            </Tabs>\r\n          )}\r\n        </Spin>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SchoolManagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,QAAQ,MAAM;AACzH,SAASC,YAAY,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,eAAe,QAAQ,mBAAmB;AAC5I,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,eAAe,EAC7GC,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,QAAQ,cAAc;AACrI,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,yBAAyB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAM;EAAEC;AAAQ,CAAC,GAAG1C,IAAI;AACxB,MAAM;EAAE2C;AAAO,CAAC,GAAGlC,MAAM;AAEzB,MAAMmC,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACzC,MAAM;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,GAAG,CAAC;EAC/C,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACmE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyE,IAAI,CAAC,GAAGhE,IAAI,CAACiE,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,QAAQ,CAAC,GAAGlE,IAAI,CAACiE,OAAO,CAAC,CAAC;EACjC,MAAM,CAACE,QAAQ,CAAC,GAAGnE,IAAI,CAACiE,OAAO,CAAC,CAAC;EACjC,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlF,QAAQ,CAAC+C,QAAQ,CAAC;;EAElE;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIiD,IAAI,IAAIA,IAAI,CAACiC,QAAQ,EAAE;MACzBC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAClC,IAAI,CAAC,CAAC;;EAEV;EACAjD,SAAS,CAAC,MAAM;IACd;IACA,MAAMoF,WAAW,GAAGJ,gBAAgB,IAAIlC,QAAQ,IAAKG,IAAI,IAAIA,IAAI,CAACoC,SAAU;;IAE5E;IACA,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKG,SAAS,EAAE;MACrDD,cAAc,GAAGE,QAAQ,CAACJ,WAAW,EAAE,EAAE,CAAC;MAC1C,IAAIK,KAAK,CAACH,cAAc,CAAC,EAAE;QACzBI,OAAO,CAACC,KAAK,CAAC,UAAU,EAAEP,WAAW,CAAC;QACtCE,cAAc,GAAG,IAAI;MACvB;IACF;IAEAI,OAAO,CAACE,GAAG,CAAC,2BAA2B,EAAE;MACvCZ,gBAAgB;MAChBlC,QAAQ;MACR+C,YAAY,EAAE5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,SAAS;MAC7BD,WAAW;MACXE,cAAc;MACdQ,YAAY,EAAE,OAAOhD;IACvB,CAAC,CAAC;;IAEF;IACA,IAAIwC,cAAc,KAAK,IAAI,EAAE;MAC3BI,OAAO,CAACE,GAAG,CAAC,gBAAgBN,cAAc,EAAE,CAAC;MAC7CS,eAAe,CAACT,cAAc,CAAC;IACjC,CAAC,MAAM,IAAIrC,IAAI,IAAIA,IAAI,CAACiC,QAAQ,EAAE;MAChC;MACAQ,OAAO,CAACE,GAAG,CAAC,sBAAsB,CAAC;IACrC,CAAC,MAAM;MACLF,OAAO,CAACM,IAAI,CAAC,cAAc,CAAC;IAC9B;EACF,CAAC,EAAE,CAAChB,gBAAgB,EAAElC,QAAQ,EAAEG,IAAI,CAAC,CAAC;;EAEtC;EACAjD,SAAS,CAAC,MAAM;IACd0F,OAAO,CAACE,GAAG,CAAC,qCAAqC,EAAE9C,QAAQ,EAAE,KAAK,EAAE,OAAOA,QAAQ,CAAC;;IAEpF;IACA,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKyC,SAAS,EAAE;MAC/CG,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAE9C,QAAQ,CAAC;;MAEzD;MACA,MAAMmD,eAAe,GAAGT,QAAQ,CAAC1C,QAAQ,EAAE,EAAE,CAAC;MAC9C,IAAI2C,KAAK,CAACQ,eAAe,CAAC,EAAE;QAC1BP,OAAO,CAACC,KAAK,CAAC,oBAAoB,EAAE7C,QAAQ,CAAC;QAC7CzC,OAAO,CAACsF,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;MAEAV,mBAAmB,CAACgB,eAAe,CAAC;MACpCP,OAAO,CAACE,GAAG,CAAC,wBAAwBK,eAAe,EAAE,CAAC;;MAEtD;MACAF,eAAe,CAACE,eAAe,CAAC;IAClC,CAAC,MAAM;MACLP,OAAO,CAACM,IAAI,CAAC,oCAAoC,CAAC;IACpD;EACF,CAAC,EAAE,CAAClD,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMe,OAAO,GAAG,MAAMhE,UAAU,CAAC,CAAC;MAClCwD,OAAO,CAACE,GAAG,CAAC,WAAW,EAAEM,OAAO,CAAC;;MAEjC;MACA,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;QAC1BC,WAAW,GAAGD,OAAO;MACvB,CAAC,MAAM,IAAIA,OAAO,IAAIE,KAAK,CAACC,OAAO,CAACH,OAAO,CAACI,KAAK,CAAC,EAAE;QAClDH,WAAW,GAAGD,OAAO,CAACI,KAAK;MAC7B;MAEAZ,OAAO,CAACE,GAAG,CAAC,WAAW,EAAEO,WAAW,CAAC;MACrCpB,aAAa,CAACoB,WAAW,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA3F,SAAS,CAAC,MAAM;IACd;IACA,MAAMsF,cAAc,GAAGN,gBAAgB,IAAIlC,QAAQ,IAAKG,IAAI,IAAIA,IAAI,CAACoC,SAAU;IAC/E;IACA,IAAIC,cAAc,IAAKrC,IAAI,IAAIA,IAAI,CAACiC,QAAS,EAAE;MAC7C,IAAIhC,SAAS,KAAK,GAAG,EAAE;QACrBqD,YAAY,CAACjB,cAAc,CAAC;MAC9B,CAAC,MAAM,IAAIpC,SAAS,KAAK,GAAG,EAAE;QAC5BsD,UAAU,CAAC,CAAC;QACZC,UAAU,CAACnB,cAAc,CAAC;QAC1BoB,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF,CAAC,EAAE,CAACxD,SAAS,EAAE8B,gBAAgB,EAAElC,QAAQ,EAAEG,IAAI,CAAC,CAAC;;EAEjD;EACAjD,SAAS,CAAC,MAAM;IACd,IAAIgF,gBAAgB,EAAE;MACpB,IAAI9B,SAAS,KAAK,GAAG,EAAE;QACrB6C,eAAe,CAACf,gBAAgB,CAAC;MACnC,CAAC,MAAM,IAAI9B,SAAS,KAAK,GAAG,EAAE;QAC5BqD,YAAY,CAACvB,gBAAgB,CAAC;MAChC,CAAC,MAAM,IAAI9B,SAAS,KAAK,GAAG,EAAE;QAC5BuD,UAAU,CAACzB,gBAAgB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMe,eAAe,GAAG,MAAOT,cAAc,IAAK;IAChDzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF6B,OAAO,CAACE,GAAG,CAAC,6BAA6B,EAAEN,cAAc,EAAE,KAAK,EAAE,OAAOA,cAAc,CAAC;MAExF,IAAIA,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKC,SAAS,EAAE;QAC3DG,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC;QACjCtF,OAAO,CAACsF,KAAK,CAAC,kBAAkB,CAAC;QACjC9B,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMf,QAAQ,GAAG0C,QAAQ,CAACF,cAAc,EAAE,EAAE,CAAC;MAC7C,IAAIG,KAAK,CAAC3C,QAAQ,CAAC,EAAE;QACnB4C,OAAO,CAACC,KAAK,CAAC,UAAU,EAAEL,cAAc,CAAC;QACzCjF,OAAO,CAACsF,KAAK,CAAC,SAAS,CAAC;QACxB9B,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA6B,OAAO,CAACE,GAAG,CAAC,cAAc9C,QAAQ,EAAE,CAAC;MACrC,IAAI;QACF,MAAM6D,IAAI,GAAG,MAAMrF,eAAe,CAACwB,QAAQ,CAAC;QAC5C4C,OAAO,CAACE,GAAG,CAAC,UAAU,EAAEe,IAAI,CAAC;QAE7B,IAAI,CAACA,IAAI,EAAE;UACTjB,OAAO,CAACC,KAAK,CAAC,QAAQ7C,QAAQ,UAAU,CAAC;UACzCzC,OAAO,CAACsF,KAAK,CAAC,mBAAmB,CAAC;UAClC9B,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAR,SAAS,CAACsD,IAAI,CAAC;;QAEf;QACAJ,YAAY,CAACzD,QAAQ,CAAC;QACtB,IAAII,SAAS,KAAK,GAAG,EAAE;UACrBuD,UAAU,CAAC3D,QAAQ,CAAC;QACtB;MACF,CAAC,CAAC,OAAO8D,QAAQ,EAAE;QACjBlB,OAAO,CAACC,KAAK,CAAC,qBAAqB7C,QAAQ,GAAG,EAAE8D,QAAQ,CAAC;QACzDvG,OAAO,CAACsF,KAAK,CAAC,aAAaiB,QAAQ,CAACvG,OAAO,IAAI,MAAM,EAAE,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOsF,KAAK,EAAE;MACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC;MACzBD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0C,YAAY,GAAG,MAAOjB,cAAc,IAAK;IAC7C,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChB6B,OAAO,CAACE,GAAG,CAAC,kBAAkBN,cAAc,EAAE,CAAC;MAC/C,MAAMuB,QAAQ,GAAG,MAAMrF,kBAAkB,CAAC8D,cAAc,CAAC;MACzDI,OAAO,CAACE,GAAG,CAAC,WAAW,EAAEiB,QAAQ,CAAC;MAClCtD,UAAU,CAACsD,QAAQ,IAAI,EAAE,CAAC;IAC5B,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2C,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B3C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM8C,IAAI,GAAG,MAAM/E,cAAc,CAAC,CAAC;MACnC6B,QAAQ,CAACkD,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC;MACzBD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4C,UAAU,GAAG,MAAOnB,cAAc,IAAK;IAC3CzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM8C,IAAI,GAAG,MAAM5E,cAAc,CAACuD,cAAc,CAAC;MACjD3B,QAAQ,CAACgD,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC;MACzBD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,IAAI,GAAG,MAAM3E,QAAQ,CAAC,CAAC;MAC7B;MACA,IAAI2E,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAIA,IAAI,EAAE;QACvD9B,WAAW,CAAC8B,IAAI,CAACL,KAAK,IAAI,EAAE,CAAC;MAC/B,CAAC,MAAM;QACL;QACAzB,WAAW,CAAC8B,IAAI,IAAI,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMmB,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAI,CAAC3D,MAAM,EAAE;IAEbS,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMtC,gBAAgB,CAAC6B,MAAM,CAAC4D,EAAE,EAAED,MAAM,CAAC;MACzC1G,OAAO,CAAC4G,OAAO,CAAC,UAAU,CAAC;MAC3BlB,eAAe,CAAC3C,MAAM,CAAC4D,EAAE,CAAC;IAC5B,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,CAAC;MACzBD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqD,cAAc,GAAGA,CAACC,MAAM,GAAG,IAAI,KAAK;IACxC9C,eAAe,CAAC8C,MAAM,CAAC;IACvB3C,IAAI,CAAC4C,WAAW,CAAC,CAAC;IAClB,IAAID,MAAM,EAAE;MACV3C,IAAI,CAAC6C,cAAc,CAAC;QAClBC,IAAI,EAAEH,MAAM,CAACG,IAAI;QACjBC,KAAK,EAAEJ,MAAM,CAACI;MAChB,CAAC,CAAC;IACJ;IACAxD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMyD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,oBAAoB,CAAC,KAAK,CAAC;IAC3BM,eAAe,CAAC,IAAI,CAAC;IACrBG,IAAI,CAAC4C,WAAW,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMV,MAAM,GAAG,MAAMvC,IAAI,CAACkD,cAAc,CAAC,CAAC;MAC1C7D,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,YAAY,EAAE;QAChB;QACAsB,OAAO,CAACE,GAAG,CAAC,YAAYxB,YAAY,CAAC4C,EAAE,OAAO,EAAED,MAAM,CAAC;QACvD,MAAMrF,eAAe,CAAC0B,MAAM,CAAC4D,EAAE,EAAE5C,YAAY,CAAC4C,EAAE,EAAED,MAAM,CAAC;QACzD1G,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACAvB,OAAO,CAACE,GAAG,CAAC,cAAcxC,MAAM,CAAC4D,EAAE,OAAO,EAAED,MAAM,CAAC;QACnD,MAAMtF,oBAAoB,CAAC2B,MAAM,CAAC4D,EAAE,EAAE;UAAE,GAAGD,MAAM;UAAE1B,SAAS,EAAEjC,MAAM,CAAC4D;QAAG,CAAC,CAAC;QAC1E3G,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAlD,oBAAoB,CAAC,KAAK,CAAC;MAC3BS,IAAI,CAAC4C,WAAW,CAAC,CAAC;MAClBb,YAAY,CAACnD,MAAM,CAAC4D,EAAE,CAAC;IACzB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtF,OAAO,CAACsF,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACtF,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD,CAAC,SAAS;MACRwD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAG,MAAOC,OAAO,IAAK;IAC3C,IAAI;MACF/D,UAAU,CAAC,IAAI,CAAC;MAChB6B,OAAO,CAACE,GAAG,CAAC,YAAYgC,OAAO,EAAE,CAAC;MAClC,MAAMjG,eAAe,CAACyB,MAAM,CAAC4D,EAAE,EAAEY,OAAO,CAAC;MACzCvH,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;MACzBV,YAAY,CAACnD,MAAM,CAAC4D,EAAE,CAAC;IACzB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAACkB,QAAQ,IAAIlB,KAAK,CAACkB,QAAQ,CAACgB,MAAM,KAAK,GAAG,EAAE;QACnDxH,OAAO,CAACsF,KAAK,CAAC,cAAc,CAAC;MAC/B,CAAC,MAAM;QACLtF,OAAO,CAACsF,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACtF,OAAO,IAAI,MAAM,CAAC,CAAC;MACvD;IACF,CAAC,SAAS;MACRwD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiE,aAAa,GAAIX,MAAM,IAAK;IAChC5C,cAAc,CAAC4C,MAAM,CAAC;IACtBzC,QAAQ,CAAC0C,WAAW,CAAC,CAAC;IACtBnD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM8D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9D,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,cAAc,CAAC,IAAI,CAAC;IACpBG,QAAQ,CAAC0C,WAAW,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMjB,MAAM,GAAG,MAAMrC,QAAQ,CAACgD,cAAc,CAAC,CAAC;MAC9C7D,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMyB,cAAc,GAAGxC,QAAQ,IAAKG,IAAI,IAAIA,IAAI,CAACoC,SAAU;MAE3D,MAAMxD,UAAU,CAAC;QACfoG,OAAO,EAAE3D,WAAW,CAAC0C,EAAE;QACvBkB,OAAO,EAAEnB,MAAM,CAACmB,OAAO;QACvB7C,SAAS,EAAEC;MACb,CAAC,CAAC;MAEFjF,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;MACzBhD,mBAAmB,CAAC,KAAK,CAAC;MAC1BwC,UAAU,CAACnB,cAAc,CAAC;IAC5B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtF,OAAO,CAACsF,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsE,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,MAAM,KAAK;IACjD9H,KAAK,CAAC+H,OAAO,CAAC;MACZC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,gBAAgB;MACzBC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB9E,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF;UACA,MAAMyB,cAAc,GAAGxC,QAAQ,IAAKG,IAAI,IAAIA,IAAI,CAACoC,SAAU;UAE3D,MAAMvD,UAAU,CAAC;YACfmG,OAAO,EAAEG,MAAM;YACfF,OAAO,EAAEG,MAAM;YACfhD,SAAS,EAAEC;UACb,CAAC,CAAC;UAEFjF,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;UACzBR,UAAU,CAACnB,cAAc,CAAC;QAC5B,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdtF,OAAO,CAACsF,KAAK,CAAC,QAAQ,CAAC;UACvBD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC,CAAC,SAAS;UACR9B,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM+E,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjE,QAAQ,CAACyC,WAAW,CAAC,CAAC;IACtBjD,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM0E,mBAAmB,GAAGA,CAAA,KAAM;IAChC1E,sBAAsB,CAAC,KAAK,CAAC;IAC7BQ,QAAQ,CAACyC,WAAW,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,MAAM0B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAM/B,MAAM,GAAG,MAAMpC,QAAQ,CAAC+C,cAAc,CAAC,CAAC;MAC9C7D,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMyB,cAAc,GAAGxC,QAAQ,IAAKG,IAAI,IAAIA,IAAI,CAACoC,SAAU;;MAE3D;MACA,MAAM0D,SAAS,GAAG,CAAC,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,gBAAgB,EACnE,cAAc,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACjC,MAAM,CAACkC,SAAS,CAAC;MACjH,MAAMC,OAAO,GAAGnC,MAAM,CAACkC,SAAS,KAAK,OAAO;;MAE5C;MACA,IAAIZ,MAAM,GAAGtB,MAAM,CAACmB,OAAO;MAC3B,IAAI,CAACG,MAAM,EAAE;QAAA,IAAAc,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;QACX;QACA,MAAMC,OAAO,GAAG;UACd,WAAW,GAAAV,WAAA,GAAE3F,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,WAAW,CAAC,cAAAb,WAAA,uBAAvCA,WAAA,CAAyCnC,EAAE;UACxD,gBAAgB,GAAAoC,YAAA,GAAE5F,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,gBAAgB,CAAC,cAAAZ,YAAA,uBAA5CA,YAAA,CAA8CpC,EAAE;UAClE,mBAAmB,GAAAqC,YAAA,GAAE7F,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,mBAAmB,CAAC,cAAAX,YAAA,uBAA/CA,YAAA,CAAiDrC,EAAE;UACxE,cAAc,GAAAsC,YAAA,GAAE9F,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,cAAc,CAAC,cAAAV,YAAA,uBAA1CA,YAAA,CAA4CtC,EAAE;UAC9D,gBAAgB,GAAAuC,YAAA,GAAE/F,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,gBAAgB,CAAC,cAAAT,YAAA,uBAA5CA,YAAA,CAA8CvC,EAAE;UAClE,qBAAqB,GAAAwC,YAAA,GAAEhG,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,qBAAqB,CAAC,cAAAR,YAAA,uBAAjDA,YAAA,CAAmDxC,EAAE;UAC5E,eAAe,GAAAyC,YAAA,GAAEjG,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,eAAe,CAAC,cAAAP,YAAA,uBAA3CA,YAAA,CAA6CzC,EAAE;UAChE,SAAS,GAAA0C,YAAA,GAAElG,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,cAAAN,YAAA,uBAArCA,YAAA,CAAuC1C,EAAE;UACpD,SAAS,GAAA2C,YAAA,GAAEnG,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC,cAAAL,YAAA,uBAArCA,YAAA,CAAuC3C,EAAE;UACpD,OAAO,GAAA4C,YAAA,GAAEpG,KAAK,CAACsG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,OAAO,CAAC,cAAAJ,YAAA,uBAAnCA,YAAA,CAAqC5C;QAChD,CAAC;QACDqB,MAAM,GAAGwB,OAAO,CAAC9C,MAAM,CAACkC,SAAS,CAAC;MACpC;;MAEA;MACA,MAAMgB,OAAO,GAAG,MAAMhI,UAAU,CAAC;QAC/B,GAAG8E,MAAM;QACTmD,QAAQ,EAAEnD,MAAM,CAACmD,QAAQ,IAAI,QAAQ;QAAE;QACvC7E,SAAS,EAAEC,cAAc;QACzB6E,UAAU,EAAEpB,SAAS;QACrB7D,QAAQ,EAAEgE;MACZ,CAAC,CAAC;MAEF7I,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;MACzB9C,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA,IAAIkE,MAAM,EAAE;QACV,IAAI;UACF,MAAMxG,UAAU,CAAC;YACfoG,OAAO,EAAEgC,OAAO,CAACjD,EAAE;YACnBkB,OAAO,EAAEG,MAAM;YACfhD,SAAS,EAAEC;UACb,CAAC,CAAC;UACFjF,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;QAC3B,CAAC,CAAC,OAAOmD,SAAS,EAAE;UAClB1E,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEyE,SAAS,CAAC;UACnC/J,OAAO,CAACgK,OAAO,CAAC,gBAAgB,CAAC;QACnC;MACF;MAEA5D,UAAU,CAACnB,cAAc,CAAC;IAC5B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtF,OAAO,CAACsF,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyG,wBAAwB,GAAG,MAAOlC,MAAM,IAAK;IACjD;IACA,MAAM9C,cAAc,GAAGxC,QAAQ,IAAKG,IAAI,IAAIA,IAAI,CAACoC,SAAU;IAE3Dd,cAAc,CAACK,QAAQ,CAACkF,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACvD,EAAE,KAAKoB,MAAM,CAAC,CAAC;IACnD1D,QAAQ,CAAC0C,WAAW,CAAC,CAAC;IACtBnD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMuG,YAAY,GAAG,CACnB;IACEjC,KAAK,EAAE,MAAM;IACbkC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEnC,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEnC,KAAK,EAAE,MAAM;IACbkC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAGC,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACErC,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACE,CAAC,EAAE1D,MAAM,kBAChB3E,OAAA,CAAC5B,KAAK;MAACkK,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClBvI,OAAA,CAACpC,MAAM;QACL4K,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEzI,OAAA,CAACzB,YAAY;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBP,IAAI,EAAC,OAAO;QACZQ,OAAO,EAAEA,CAAA,KAAMpE,cAAc,CAACC,MAAM,CAAE;QAAA4D,QAAA,EACvC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7I,OAAA,CAACpC,MAAM;QACLmL,MAAM;QACNN,IAAI,eAAEzI,OAAA,CAACxB,cAAc;UAAAkK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBP,IAAI,EAAC,OAAO;QACZQ,OAAO,EAAEA,CAAA,KAAM3D,iBAAiB,CAACR,MAAM,CAACH,EAAE,CAAE;QAAA+D,QAAA,EAC7C;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMG,WAAW,GAAG,CAClB;IACEjD,KAAK,EAAE,KAAK;IACZkC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEnC,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEnC,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAEA,CAACE,CAAC,EAAE1D,MAAM,kBAChB3E,OAAA,CAAAE,SAAA;MAAAqI,QAAA,GACG5D,MAAM,CAACjC,QAAQ,iBAAI1C,OAAA,CAAC7B,GAAG;QAAC8K,KAAK,EAAC,KAAK;QAAAV,QAAA,EAAC;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAC/ClE,MAAM,CAACgD,UAAU,KACfhD,MAAM,CAACuE,YAAY,gBAClBlJ,OAAA,CAAC7B,GAAG;QAAC8K,KAAK,EAAC,MAAM;QAAAV,QAAA,EAAE5D,MAAM,CAACuE,YAAY,CAACpE;MAAI;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAClD7I,OAAA,CAAC7B,GAAG;QAAC8K,KAAK,EAAC,MAAM;QAAAV,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,CAC3B,EAEF,CAAClE,MAAM,CAACjC,QAAQ,IAAI,CAACiC,MAAM,CAACgD,UAAU,iBAAI3H,OAAA,CAAC7B,GAAG;QAAC8K,KAAK,EAAC,SAAS;QAAAV,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA,eACxE;EAEN,CAAC,EACD;IACE9C,KAAK,EAAE,MAAM;IACbkC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAEA,CAACE,CAAC,EAAE1D,MAAM,KAChBA,MAAM,CAACuE,YAAY,gBAAGlJ,OAAA,CAAC7B,GAAG;MAAC8K,KAAK,EAAC,MAAM;MAAAV,QAAA,EAAE5D,MAAM,CAACuE,YAAY,CAACpE;IAAI;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,gBAAG7I,OAAA,CAAC7B,GAAG;MAAC8K,KAAK,EAAC,SAAS;MAAAV,QAAA,EAAC;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAE5G,CAAC,EACD;IACE9C,KAAK,EAAE,MAAM;IACbkC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACnH,KAAK,EAAE2D,MAAM,KAAK9E,eAAe,CAAC8E,MAAM;EACnD,CAAC,EACD;IACEoB,KAAK,EAAE,IAAI;IACXmC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACE,CAAC,EAAE1D,MAAM,kBAChB3E,OAAA,CAAC5B,KAAK;MAACkK,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClBvI,OAAA,CAACpC,MAAM;QACL4K,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEzI,OAAA,CAACtB,YAAY;UAAAgK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBP,IAAI,EAAC,OAAO;QACZQ,OAAO,EAAEA,CAAA,KAAMxD,aAAa,CAACX,MAAM,CAAE;QAAA4D,QAAA,EACtC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRlE,MAAM,CAAC3D,KAAK,IAAI2D,MAAM,CAAC3D,KAAK,CAACmI,MAAM,GAAG,CAAC,IAAIxE,MAAM,CAAC3D,KAAK,CAACoI,GAAG,CAACC,IAAI,iBAC/DrJ,OAAA,CAACpC,MAAM;QAELmL,MAAM;QACNT,IAAI,EAAC,OAAO;QACZQ,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAAChB,MAAM,CAACH,EAAE,EAAE6E,IAAI,CAAC7E,EAAE,CAAE;QAAA+D,QAAA,GACrD,eACI,EAACc,IAAI,CAACvE,IAAI;MAAA,GALRuE,IAAI,CAAC7E,EAAE;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMN,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAEX,CAAC,CACF;;EAED;EACA,MAAMS,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAC1I,MAAM,EAAE,oBAAOZ,OAAA,CAAClC,IAAI;MAACyL,GAAG,EAAC;IAAY;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAE7C,oBACE7I,OAAA,CAAAE,SAAA;MAAAqI,QAAA,eACEvI,OAAA,CAAChC,IAAI;QACHwL,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb3E,IAAI,EAAElE,MAAM,CAACkE,IAAI;UACjB4E,QAAQ,EAAE9I,MAAM,CAAC8I,QAAQ;UACzBC,IAAI,EAAE/I,MAAM,CAAC+I,IAAI;UACjBC,QAAQ,EAAEhJ,MAAM,CAACgJ;QACnB,CAAE;QACFC,QAAQ,EAAEvF,kBAAmB;QAAAiE,QAAA,gBAE7BvI,OAAA,CAAChC,IAAI,CAAC8L,IAAI;UACRhF,IAAI,EAAC,MAAM;UACXiF,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEpM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA0K,QAAA,eAEhDvI,OAAA,CAAC/B,KAAK;YAAAyK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;UACRhF,IAAI,EAAC,UAAU;UACfiF,KAAK,EAAC,cAAI;UAAAxB,QAAA,eAEVvI,OAAA,CAAC/B,KAAK;YAAAyK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;UACRhF,IAAI,EAAC,MAAM;UACXiF,KAAK,EAAC,cAAI;UAAAxB,QAAA,eAEVvI,OAAA,CAAC/B,KAAK;YAAAyK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;UACRhF,IAAI,EAAC,UAAU;UACfiF,KAAK,EAAC,cAAI;UAAAxB,QAAA,eAEVvI,OAAA,CAAC/B,KAAK;YAAAyK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;UAAAvB,QAAA,eACRvI,OAAA,CAACpC,MAAM;YAAC4K,IAAI,EAAC,SAAS;YAAC0B,QAAQ,EAAC,QAAQ;YAAA3B,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC,gBACP,CAAC;EAEP,CAAC;;EAED;EACA,MAAMsB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,oBACEnK,OAAA,CAAAE,SAAA;MAAAqI,QAAA,gBACEvI,OAAA;QAAKoK,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAA9B,QAAA,eAC/BvI,OAAA,CAACpC,MAAM;UACL4K,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEzI,OAAA,CAACvB,YAAY;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAMpE,cAAc,CAAC,CAAE;UAAA6D,QAAA,EACjC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN7I,OAAA,CAACrC,KAAK;QACJ2M,OAAO,EAAEtC,YAAa;QACtBuC,UAAU,EAAEzJ,OAAQ;QACpB0J,MAAM,EAAC,IAAI;QACXpJ,OAAO,EAAEA;MAAQ;QAAAsH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAGF7I,OAAA,CAACjC,KAAK;QACJgI,KAAK,EAAEnE,YAAY,GAAG,MAAM,GAAG,MAAO;QACtC6I,IAAI,EAAEnJ,iBAAkB;QACxB6E,IAAI,EAAElB,iBAAkB;QACxByF,QAAQ,EAAE1F,iBAAkB;QAC5B2F,cAAc,EAAEvJ,OAAQ;QAAAmH,QAAA,eAExBvI,OAAA,CAAChC,IAAI;UACHgE,IAAI,EAAEA,IAAK;UACXwH,MAAM,EAAC,UAAU;UAAAjB,QAAA,gBAEjBvI,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,MAAM;YACXiF,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpM,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA0K,QAAA,eAEhDvI,OAAA,CAAC/B,KAAK;cAAC2M,WAAW,EAAC;YAAU;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,OAAO;YACZiF,KAAK,EAAC,cAAI;YACVC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpM,OAAO,EAAE;YAAQ,CAAC,CAAE;YAAA0K,QAAA,eAE9CvI,OAAA,CAAC9B,MAAM;cAAC0M,WAAW,EAAC,gCAAO;cAAArC,QAAA,gBACzBvI,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,oBAAK;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,cAAI;gBAAAtC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,cAAI;gBAAAtC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9B7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,cAAI;gBAAAtC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA,eACR,CAAC;EAEP,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMpE,OAAO,GAAGjG,IAAI,IAAIA,IAAI,CAACiC,QAAQ;IACrC,MAAMqI,WAAW,GAAGtK,IAAI,IAAIA,IAAI,CAACO,KAAK,IAAIP,IAAI,CAACO,KAAK,CAACgK,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,WAAW,CAAC;IACtF,MAAMyD,cAAc,GAAGvE,OAAO,IAAIqE,WAAW;IAE7C,oBACE/K,OAAA,CAAAE,SAAA;MAAAqI,QAAA,GACG0C,cAAc,iBACbjL,OAAA;QAAKoK,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAA9B,QAAA,gBAC/BvI,OAAA,CAACpC,MAAM;UACL4K,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEzI,OAAA,CAACnB,eAAe;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BC,OAAO,EAAE1C,gBAAiB;UAC1BgE,KAAK,EAAE;YAAEc,WAAW,EAAE;UAAE,CAAE;UAAA3C,QAAA,EAC3B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAERnC,OAAO,iBACN1G,OAAA,CAAC9B,MAAM;UACL0M,WAAW,EAAC,wDAAW;UACvBR,KAAK,EAAE;YAAEe,KAAK,EAAE;UAAI,CAAE;UACtBC,UAAU;UACVC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAC1BA,MAAM,CAAChD,QAAQ,IAAI,OAAOgD,MAAM,CAAChD,QAAQ,KAAK,QAAQ,GACtDgD,MAAM,CAAChD,QAAQ,CAACiD,WAAW,CAAC,CAAC,CAACC,OAAO,CAACH,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;UACDE,QAAQ,EAAE5D,wBAAyB;UACnC+C,KAAK,EAAE9H,SAAU;UAAAwF,QAAA,EAEhBnG,QAAQ,CACNuJ,MAAM,CAAC5D,CAAC,IAAI,CAAC7G,KAAK,CAAC8J,IAAI,CAACY,EAAE,IAAIA,EAAE,CAACpH,EAAE,KAAKuD,CAAC,CAACvD,EAAE,CAAC,CAAC,CAAC;UAAA,CAC/C4E,GAAG,CAACrB,CAAC,iBACJ/H,OAAA,CAACI,MAAM;YAAYyK,KAAK,EAAE9C,CAAC,CAACvD,EAAG;YAAA+D,QAAA,GAC5BR,CAAC,CAAC8D,SAAS,IAAI9D,CAAC,CAAC+D,QAAQ,EAAC,IAAE,EAAC/D,CAAC,CAACrF,QAAQ,GAAG,KAAK,GAAGqF,CAAC,CAACJ,UAAU,GAAG,IAAI,GAAG,IAAI,EAAC,GAChF;UAAA,GAFaI,CAAC,CAACvD,EAAE;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAET,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEE,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAED7I,OAAA,CAACrC,KAAK;QACJ2M,OAAO,EAAEtB,WAAY;QACrBuB,UAAU,EAAErJ,KAAM;QAClBsJ,MAAM,EAAC,IAAI;QACXpJ,OAAO,EAAEA;MAAQ;QAAAsH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAGF7I,OAAA,CAACjC,KAAK;QACJgI,KAAK,EAAC,0BAAM;QACZ0E,IAAI,EAAEjJ,gBAAiB;QACvB2E,IAAI,EAAEX,gBAAiB;QACvBkF,QAAQ,EAAEnF,gBAAiB;QAC3BoF,cAAc,EAAEvJ,OAAQ;QAAAmH,QAAA,EAEvBzG,WAAW,iBACV9B,OAAA,CAAChC,IAAI;UACHgE,IAAI,EAAEE,QAAS;UACfsH,MAAM,EAAC,UAAU;UAAAjB,QAAA,gBAEjBvI,OAAA;YAAAuI,QAAA,GAAG,qBAAI,eAAAvI,OAAA;cAAAuI,QAAA,EAASzG,WAAW,CAAC+J,SAAS,IAAI/J,WAAW,CAACgK;YAAQ;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,mCAAM;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjF7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,SAAS;YACdiF,KAAK,EAAC,cAAI;YACVC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpM,OAAO,EAAE;YAAQ,CAAC,CAAE;YAAA0K,QAAA,eAE9CvI,OAAA,CAAC9B,MAAM;cAAC0M,WAAW,EAAC,gCAAO;cAAArC,QAAA,EAExBvH,KAAK,CACH2K,MAAM,CAACtC,IAAI,IAAI3C,OAAO,IAAI2C,IAAI,CAAC0C,KAAK,GAAG,EAAE,CAAC,CAC1C3C,GAAG,CAACC,IAAI,iBACPrJ,OAAA,CAACI,MAAM;gBAAeyK,KAAK,EAAExB,IAAI,CAAC7E,EAAG;gBAAA+D,QAAA,GAClCc,IAAI,CAACvE,IAAI,EAAC,IAAE,EAACuE,IAAI,CAAC2C,WAAW,IAAI3C,IAAI,CAAC7B,IAAI,EAAC,GAC9C;cAAA,GAFa6B,IAAI,CAAC7E,EAAE;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGR7I,OAAA,CAACjC,KAAK;QACJgI,KAAK,EAAC,gCAAO;QACb0E,IAAI,EAAE/I,mBAAoB;QAC1ByE,IAAI,EAAEG,mBAAoB;QAC1BoE,QAAQ,EAAErE,mBAAoB;QAC9BsE,cAAc,EAAEvJ,OAAQ;QAAAmH,QAAA,eAExBvI,OAAA,CAAChC,IAAI;UACHgE,IAAI,EAAEG,QAAS;UACfqH,MAAM,EAAC,UAAU;UAAAjB,QAAA,gBAEjBvI,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,UAAU;YACfiF,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpM,OAAO,EAAE;YAAS,CAAC,CAAE;YAAA0K,QAAA,eAE/CvI,OAAA,CAAC/B,KAAK;cAAC2M,WAAW,EAAC;YAAQ;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,WAAW;YAChBiF,KAAK,EAAC,cAAI;YAAAxB,QAAA,eAEVvI,OAAA,CAAC/B,KAAK;cAAC2M,WAAW,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,OAAO;YACZiF,KAAK,EAAC,cAAI;YACVC,KAAK,EAAE,CACL;cAAExB,IAAI,EAAE,OAAO;cAAE3K,OAAO,EAAE;YAAa,CAAC,CACxC;YAAA0K,QAAA,eAEFvI,OAAA,CAAC/B,KAAK;cAAC2M,WAAW,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,UAAU;YACfiF,KAAK,EAAC,cAAI;YACVkC,KAAK,EAAC,kFAAsB;YAAA1D,QAAA,eAE5BvI,OAAA,CAAC/B,KAAK,CAACiO,QAAQ;cAACtB,WAAW,EAAC;YAAO;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,WAAW;YAChBiF,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEpM,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA0K,QAAA,eAEhDvI,OAAA,CAAC9B,MAAM;cAAC0M,WAAW,EAAC,4CAAS;cAAArC,QAAA,gBAC3BvI,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,SAAS;gBAAAtC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,SAAS;gBAAAtC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrC7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,eAAe;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,qBAAqB;gBAAAtC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjD7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,gBAAgB;gBAAAtC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,cAAc;gBAAAtC,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,mBAAmB;gBAAAtC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,gBAAgB;gBAAAtC,QAAA,EAAC;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3C7I,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,WAAW;gBAAAtC,QAAA,EAAC;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACpCnC,OAAO,iBAAI1G,OAAA,CAACI,MAAM;gBAACyK,KAAK,EAAC,OAAO;gBAAAtC,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACZ7I,OAAA,CAAChC,IAAI,CAAC8L,IAAI;YACRhF,IAAI,EAAC,SAAS;YACdiF,KAAK,EAAC,0BAAM;YACZkC,KAAK,EAAC,sFAAgB;YAAA1D,QAAA,eAEtBvI,OAAA,CAAC9B,MAAM;cAAC0M,WAAW,EAAC,gCAAO;cAAArC,QAAA,EACxBvH,KAAK,CACH2K,MAAM,CAACtC,IAAI,IAAI3C,OAAO,IAAI2C,IAAI,CAAC0C,KAAK,GAAG,EAAE,CAAC,CAC1C3C,GAAG,CAACC,IAAI,iBACPrJ,OAAA,CAACI,MAAM;gBAAeyK,KAAK,EAAExB,IAAI,CAAC7E,EAAG;gBAAA+D,QAAA,GAClCc,IAAI,CAACvE,IAAI,EAAC,IAAE,EAACuE,IAAI,CAAC2C,WAAW,IAAI3C,IAAI,CAAC7B,IAAI,EAAC,GAC9C;cAAA,GAFa6B,IAAI,CAAC7E,EAAE;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA,eACR,CAAC;EAEP,CAAC;EAED,oBACE7I,OAAA;IAAKmM,SAAS,EAAC,mBAAmB;IAAA5D,QAAA,eAChCvI,OAAA,CAACtC,IAAI;MAACqI,KAAK,EAAC,0BAAM;MAAAwC,QAAA,eAChBvI,OAAA,CAAClC,IAAI;QAACsO,QAAQ,EAAEhL,OAAO,IAAI,CAACE,iBAAiB,IAAI,CAACE,gBAAgB,IAAI,CAACE,mBAAoB;QAAA6G,QAAA,EACxF9H,IAAI,IAAIA,IAAI,CAACiC,QAAQ,gBACpB1C,OAAA;UAAAuI,QAAA,gBACEvI,OAAA;YAAAuI,QAAA,EAAI;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACb7I,OAAA,CAACrC,KAAK;YACJ4M,UAAU,EAAEjI,UAAW;YACvBkI,MAAM,EAAC,IAAI;YACX6B,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAG,CAAE;YAC7BhC,OAAO,EAAE,CACP;cACEvE,KAAK,EAAE,MAAM;cACbkC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE;YACP,CAAC,EACD;cACEnC,KAAK,EAAE,IAAI;cACXkC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE;YACP,CAAC,EACD;cACEnC,KAAK,EAAE,IAAI;cACXkC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE;YACP,CAAC,EACD;cACEnC,KAAK,EAAE,IAAI;cACXkC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE;YACP,CAAC,EACD;cACEnC,KAAK,EAAE,IAAI;cACXmC,GAAG,EAAE,QAAQ;cACbC,MAAM,EAAEA,CAACE,CAAC,EAAE1D,MAAM,kBAChB3E,OAAA,CAAC5B,KAAK;gBAAAmK,QAAA,gBACJvI,OAAA,CAACpC,MAAM;kBACL4K,IAAI,EAAC,SAAS;kBACdM,OAAO,EAAEA,CAAA,KAAM;oBACbrG,mBAAmB,CAACkC,MAAM,CAACH,EAAE,CAAC;oBAC9BjB,eAAe,CAACoB,MAAM,CAACH,EAAE,CAAC;kBAC5B,CAAE;kBAAA+D,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7I,OAAA,CAACpC,MAAM;kBACLmL,MAAM;kBACND,OAAO,EAAEA,CAAA,KAAM;oBACb/K,KAAK,CAAC+H,OAAO,CAAC;sBACZC,KAAK,EAAE,MAAM;sBACbC,OAAO,EAAE,YAAYrB,MAAM,CAACG,IAAI,6BAA6B;sBAC7DmB,MAAM,EAAE,MAAM;sBACdsG,MAAM,EAAE,QAAQ;sBAChBrG,UAAU,EAAE,IAAI;sBAChBC,IAAI,EAAE,MAAAA,CAAA,KAAY;wBAChB,IAAI;0BACF,MAAMxG,YAAY,CAACgF,MAAM,CAACH,EAAE,CAAC;0BAC7B3G,OAAO,CAAC4G,OAAO,CAAC,QAAQ,CAAC;0BACzB;0BACA,IAAIjC,gBAAgB,KAAKmC,MAAM,CAACH,EAAE,EAAE;4BAClC/B,mBAAmB,CAAC,IAAI,CAAC;4BACzB5B,SAAS,CAAC,IAAI,CAAC;0BACjB;0BACA;0BACA8B,eAAe,CAAC,CAAC;wBACnB,CAAC,CAAC,OAAOQ,KAAK,EAAE;0BACdtF,OAAO,CAACsF,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACtF,OAAO,IAAI,MAAM,CAAC,CAAC;0BACrDqF,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;wBACjC;sBACF;oBACF,CAAC,CAAC;kBACJ,CAAE;kBAAAoF,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAEX,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDrG,gBAAgB,iBACfxC,OAAA;YAAKoK,KAAK,EAAE;cAAEoC,SAAS,EAAE;YAAG,CAAE;YAAAjE,QAAA,gBAC5BvI,OAAA,CAAC1B,OAAO;cAAAiK,QAAA,eACNvI,OAAA;gBAAAuI,QAAA,EAAK,EAAA/H,gBAAA,GAAA8B,UAAU,CAACgF,IAAI,CAACmF,CAAC,IAAIA,CAAC,CAACjI,EAAE,KAAKhC,gBAAgB,CAAC,cAAAhC,gBAAA,uBAA/CA,gBAAA,CAAiDsE,IAAI,KAAI;cAAM;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACV7I,OAAA,CAACvC,IAAI;cAACiP,SAAS,EAAEhM,SAAU;cAACgL,QAAQ,EAAE/K,YAAa;cAAA4H,QAAA,gBACjDvI,OAAA,CAACG,OAAO;gBACNwM,GAAG,eACD3M,OAAA;kBAAAuI,QAAA,gBACEvI,OAAA,CAACpB,eAAe;oBAAA8J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,wCAErB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;gBAAAN,QAAA,EAGAe,oBAAoB,CAAC;cAAC,GAFnB,GAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGA,CAAC,eACV7I,OAAA,CAACG,OAAO;gBACNwM,GAAG,eACD3M,OAAA;kBAAAuI,QAAA,gBACEvI,OAAA,CAACrB,YAAY;oBAAA+J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4BAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;gBAAAN,QAAA,EAGA4B,kBAAkB,CAAC;cAAC,GAFjB,GAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGA,CAAC,eACV7I,OAAA,CAACG,OAAO;gBACNwM,GAAG,eACD3M,OAAA;kBAAAuI,QAAA,gBACEvI,OAAA,CAACtB,YAAY;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,4BAElB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;gBAAAN,QAAA,EAGAuC,gBAAgB,CAAC;cAAC,GAFf,GAAG;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN7I,OAAA,CAACvC,IAAI;UAACiP,SAAS,EAAEhM,SAAU;UAACgL,QAAQ,EAAE/K,YAAa;UAAA4H,QAAA,gBACjDvI,OAAA,CAACG,OAAO;YACNwM,GAAG,eACD3M,OAAA;cAAAuI,QAAA,gBACEvI,OAAA,CAACpB,eAAe;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wCAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;YAAAN,QAAA,EAGAe,oBAAoB,CAAC;UAAC,GAFnB,GAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGA,CAAC,eACV7I,OAAA,CAACG,OAAO;YACNwM,GAAG,eACD3M,OAAA;cAAAuI,QAAA,gBACEvI,OAAA,CAACrB,YAAY;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;YAAAN,QAAA,EAGA4B,kBAAkB,CAAC;UAAC,GAFjB,GAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGA,CAAC,eACV7I,OAAA,CAACG,OAAO;YACNwM,GAAG,eACD3M,OAAA;cAAAuI,QAAA,gBACEvI,OAAA,CAACtB,YAAY;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;YAAAN,QAAA,EAGAuC,gBAAgB,CAAC;UAAC,GAFf,GAAG;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtI,EAAA,CAxiCIF,gBAAgB;EAAA,QACHT,OAAO,EAYT5B,IAAI,CAACiE,OAAO,EACRjE,IAAI,CAACiE,OAAO,EACZjE,IAAI,CAACiE,OAAO;AAAA;AAAA2K,EAAA,GAf3BvM,gBAAgB;AA0iCtB,eAAeA,gBAAgB;AAAC,IAAAuM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}