{"ast": null, "code": "import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genPresetColor, genStyleHooks, mergeToken } from '../../theme/internal';\nconst antStatusProcessing = new Keyframes('antStatusProcessing', {\n  '0%': {\n    transform: 'scale(0.8)',\n    opacity: 0.5\n  },\n  '100%': {\n    transform: 'scale(2.4)',\n    opacity: 0\n  }\n});\nconst antZoomBadgeIn = new Keyframes('antZoomBadgeIn', {\n  '0%': {\n    transform: 'scale(0) translate(50%, -50%)',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1) translate(50%, -50%)'\n  }\n});\nconst antZoomBadgeOut = new Keyframes('antZoomBadgeOut', {\n  '0%': {\n    transform: 'scale(1) translate(50%, -50%)'\n  },\n  '100%': {\n    transform: 'scale(0) translate(50%, -50%)',\n    opacity: 0\n  }\n});\nconst antNoWrapperZoomBadgeIn = new Keyframes('antNoWrapperZoomBadgeIn', {\n  '0%': {\n    transform: 'scale(0)',\n    opacity: 0\n  },\n  '100%': {\n    transform: 'scale(1)'\n  }\n});\nconst antNoWrapperZoomBadgeOut = new Keyframes('antNoWrapperZoomBadgeOut', {\n  '0%': {\n    transform: 'scale(1)'\n  },\n  '100%': {\n    transform: 'scale(0)',\n    opacity: 0\n  }\n});\nconst antBadgeLoadingCircle = new Keyframes('antBadgeLoadingCircle', {\n  '0%': {\n    transformOrigin: '50%'\n  },\n  '100%': {\n    transform: 'translate(50%, -50%) rotate(360deg)',\n    transformOrigin: '50%'\n  }\n});\nconst genSharedBadgeStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    antCls,\n    badgeShadowSize,\n    textFontSize,\n    textFontSizeSM,\n    statusSize,\n    dotSize,\n    textFontWeight,\n    indicatorHeight,\n    indicatorHeightSM,\n    marginXS,\n    calc\n  } = token;\n  const numberPrefixCls = `${antCls}-scroll-number`;\n  const colorPreset = genPresetColor(token, (colorKey, {\n    darkColor\n  }) => ({\n    [`&${componentCls} ${componentCls}-color-${colorKey}`]: {\n      background: darkColor,\n      [`&:not(${componentCls}-count)`]: {\n        color: darkColor\n      },\n      'a:hover &': {\n        background: darkColor\n      }\n    }\n  }));\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-block',\n      width: 'fit-content',\n      lineHeight: 1,\n      [`${componentCls}-count`]: {\n        display: 'inline-flex',\n        justifyContent: 'center',\n        zIndex: token.indicatorZIndex,\n        minWidth: indicatorHeight,\n        height: indicatorHeight,\n        color: token.badgeTextColor,\n        fontWeight: textFontWeight,\n        fontSize: textFontSize,\n        lineHeight: unit(indicatorHeight),\n        whiteSpace: 'nowrap',\n        textAlign: 'center',\n        background: token.badgeColor,\n        borderRadius: calc(indicatorHeight).div(2).equal(),\n        boxShadow: `0 0 0 ${unit(badgeShadowSize)} ${token.badgeShadowColor}`,\n        transition: `background ${token.motionDurationMid}`,\n        a: {\n          color: token.badgeTextColor\n        },\n        'a:hover': {\n          color: token.badgeTextColor\n        },\n        'a:hover &': {\n          background: token.badgeColorHover\n        }\n      },\n      [`${componentCls}-count-sm`]: {\n        minWidth: indicatorHeightSM,\n        height: indicatorHeightSM,\n        fontSize: textFontSizeSM,\n        lineHeight: unit(indicatorHeightSM),\n        borderRadius: calc(indicatorHeightSM).div(2).equal()\n      },\n      [`${componentCls}-multiple-words`]: {\n        padding: `0 ${unit(token.paddingXS)}`,\n        bdi: {\n          unicodeBidi: 'plaintext'\n        }\n      },\n      [`${componentCls}-dot`]: {\n        zIndex: token.indicatorZIndex,\n        width: dotSize,\n        minWidth: dotSize,\n        height: dotSize,\n        background: token.badgeColor,\n        borderRadius: '100%',\n        boxShadow: `0 0 0 ${unit(badgeShadowSize)} ${token.badgeShadowColor}`\n      },\n      [`${componentCls}-count, ${componentCls}-dot, ${numberPrefixCls}-custom-component`]: {\n        position: 'absolute',\n        top: 0,\n        insetInlineEnd: 0,\n        transform: 'translate(50%, -50%)',\n        transformOrigin: '100% 0%',\n        [`&${iconCls}-spin`]: {\n          animationName: antBadgeLoadingCircle,\n          animationDuration: '1s',\n          animationIterationCount: 'infinite',\n          animationTimingFunction: 'linear'\n        }\n      },\n      [`&${componentCls}-status`]: {\n        lineHeight: 'inherit',\n        verticalAlign: 'baseline',\n        [`${componentCls}-status-dot`]: {\n          position: 'relative',\n          top: -1,\n          // Magic number, but seems better experience\n          display: 'inline-block',\n          width: statusSize,\n          height: statusSize,\n          verticalAlign: 'middle',\n          borderRadius: '50%'\n        },\n        [`${componentCls}-status-success`]: {\n          backgroundColor: token.colorSuccess\n        },\n        [`${componentCls}-status-processing`]: {\n          overflow: 'visible',\n          color: token.colorInfo,\n          backgroundColor: token.colorInfo,\n          borderColor: 'currentcolor',\n          '&::after': {\n            position: 'absolute',\n            top: 0,\n            insetInlineStart: 0,\n            width: '100%',\n            height: '100%',\n            borderWidth: badgeShadowSize,\n            borderStyle: 'solid',\n            borderColor: 'inherit',\n            borderRadius: '50%',\n            animationName: antStatusProcessing,\n            animationDuration: token.badgeProcessingDuration,\n            animationIterationCount: 'infinite',\n            animationTimingFunction: 'ease-in-out',\n            content: '\"\"'\n          }\n        },\n        [`${componentCls}-status-default`]: {\n          backgroundColor: token.colorTextPlaceholder\n        },\n        [`${componentCls}-status-error`]: {\n          backgroundColor: token.colorError\n        },\n        [`${componentCls}-status-warning`]: {\n          backgroundColor: token.colorWarning\n        },\n        [`${componentCls}-status-text`]: {\n          marginInlineStart: marginXS,\n          color: token.colorText,\n          fontSize: token.fontSize\n        }\n      }\n    }), colorPreset), {\n      [`${componentCls}-zoom-appear, ${componentCls}-zoom-enter`]: {\n        animationName: antZoomBadgeIn,\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseOutBack,\n        animationFillMode: 'both'\n      },\n      [`${componentCls}-zoom-leave`]: {\n        animationName: antZoomBadgeOut,\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseOutBack,\n        animationFillMode: 'both'\n      },\n      [`&${componentCls}-not-a-wrapper`]: {\n        [`${componentCls}-zoom-appear, ${componentCls}-zoom-enter`]: {\n          animationName: antNoWrapperZoomBadgeIn,\n          animationDuration: token.motionDurationSlow,\n          animationTimingFunction: token.motionEaseOutBack\n        },\n        [`${componentCls}-zoom-leave`]: {\n          animationName: antNoWrapperZoomBadgeOut,\n          animationDuration: token.motionDurationSlow,\n          animationTimingFunction: token.motionEaseOutBack\n        },\n        [`&:not(${componentCls}-status)`]: {\n          verticalAlign: 'middle'\n        },\n        [`${numberPrefixCls}-custom-component, ${componentCls}-count`]: {\n          transform: 'none'\n        },\n        [`${numberPrefixCls}-custom-component, ${numberPrefixCls}`]: {\n          position: 'relative',\n          top: 'auto',\n          display: 'block',\n          transformOrigin: '50% 50%'\n        }\n      },\n      [numberPrefixCls]: {\n        overflow: 'hidden',\n        transition: `all ${token.motionDurationMid} ${token.motionEaseOutBack}`,\n        [`${numberPrefixCls}-only`]: {\n          position: 'relative',\n          display: 'inline-block',\n          height: indicatorHeight,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseOutBack}`,\n          WebkitTransformStyle: 'preserve-3d',\n          WebkitBackfaceVisibility: 'hidden',\n          [`> p${numberPrefixCls}-only-unit`]: {\n            height: indicatorHeight,\n            margin: 0,\n            WebkitTransformStyle: 'preserve-3d',\n            WebkitBackfaceVisibility: 'hidden'\n          }\n        },\n        [`${numberPrefixCls}-symbol`]: {\n          verticalAlign: 'top'\n        }\n      },\n      // ====================== RTL =======================\n      '&-rtl': {\n        direction: 'rtl',\n        [`${componentCls}-count, ${componentCls}-dot, ${numberPrefixCls}-custom-component`]: {\n          transform: 'translate(-50%, -50%)'\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareToken = token => {\n  const {\n    fontHeight,\n    lineWidth,\n    marginXS,\n    colorBorderBg\n  } = token;\n  const badgeFontHeight = fontHeight;\n  const badgeShadowSize = lineWidth;\n  const badgeTextColor = token.colorTextLightSolid;\n  const badgeColor = token.colorError;\n  const badgeColorHover = token.colorErrorHover;\n  const badgeToken = mergeToken(token, {\n    badgeFontHeight,\n    badgeShadowSize,\n    badgeTextColor,\n    badgeColor,\n    badgeColorHover,\n    badgeShadowColor: colorBorderBg,\n    badgeProcessingDuration: '1.2s',\n    badgeRibbonOffset: marginXS,\n    // Follow token just by Design. Not related with token\n    badgeRibbonCornerTransform: 'scaleY(0.75)',\n    badgeRibbonCornerFilter: `brightness(75%)`\n  });\n  return badgeToken;\n};\nexport const prepareComponentToken = token => {\n  const {\n    fontSize,\n    lineHeight,\n    fontSizeSM,\n    lineWidth\n  } = token;\n  return {\n    indicatorZIndex: 'auto',\n    indicatorHeight: Math.round(fontSize * lineHeight) - 2 * lineWidth,\n    indicatorHeightSM: fontSize,\n    dotSize: fontSizeSM / 2,\n    textFontSize: fontSizeSM,\n    textFontSizeSM: fontSizeSM,\n    textFontWeight: 'normal',\n    statusSize: fontSizeSM / 2\n  };\n};\nexport default genStyleHooks('Badge', token => {\n  const badgeToken = prepareToken(token);\n  return genSharedBadgeStyle(badgeToken);\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}