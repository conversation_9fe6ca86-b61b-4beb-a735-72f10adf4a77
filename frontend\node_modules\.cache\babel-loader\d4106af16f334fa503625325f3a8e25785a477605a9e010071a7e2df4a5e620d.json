{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\nimport structuredClone from '@ungap/structured-clone';\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nexport function defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{\n    type: 'text',\n    value: '↩'\n  }];\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{\n        type: 'text',\n        value: String(rereferenceIndex)\n      }]\n    });\n  }\n  return result;\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nexport function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return 'Back to reference ' + (referenceIndex + 1) + (rereferenceIndex > 1 ? '-' + rereferenceIndex : '');\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nexport function footer(state) {\n  const clobberPrefix = typeof state.options.clobberPrefix === 'string' ? state.options.clobberPrefix : 'user-content-';\n  const footnoteBackContent = state.options.footnoteBackContent || defaultFootnoteBackContent;\n  const footnoteBackLabel = state.options.footnoteBackLabel || defaultFootnoteBackLabel;\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes';\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2';\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  };\n  /** @type {Array<ElementContent>} */\n  const listItems = [];\n  let referenceIndex = -1;\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(state.footnoteOrder[referenceIndex]);\n    if (!definition) {\n      continue;\n    }\n    const content = state.all(definition);\n    const id = String(definition.identifier).toUpperCase();\n    const safeId = normalizeUri(id.toLowerCase());\n    let rereferenceIndex = 0;\n    /** @type {Array<ElementContent>} */\n    const backReferences = [];\n    const counts = state.footnoteCounts.get(id);\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({\n          type: 'text',\n          value: ' '\n        });\n      }\n      let children = typeof footnoteBackContent === 'string' ? footnoteBackContent : footnoteBackContent(referenceIndex, rereferenceIndex);\n      if (typeof children === 'string') {\n        children = {\n          type: 'text',\n          value: children\n        };\n      }\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href: '#' + clobberPrefix + 'fnref-' + safeId + (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel: typeof footnoteBackLabel === 'string' ? footnoteBackLabel : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      });\n    }\n    const tail = content[content.length - 1];\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1];\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' ';\n      } else {\n        tail.children.push({\n          type: 'text',\n          value: ' '\n        });\n      }\n      tail.children.push(...backReferences);\n    } else {\n      content.push(...backReferences);\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {\n        id: clobberPrefix + 'fn-' + safeId\n      },\n      children: state.wrap(content, true)\n    };\n    state.patch(definition, listItem);\n    listItems.push(listItem);\n  }\n  if (listItems.length === 0) {\n    return;\n  }\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {\n      dataFootnotes: true,\n      className: ['footnotes']\n    },\n    children: [{\n      type: 'element',\n      tagName: footnoteLabelTagName,\n      properties: {\n        ...structuredClone(footnoteLabelProperties),\n        id: 'footnote-label'\n      },\n      children: [{\n        type: 'text',\n        value: footnoteLabel\n      }]\n    }, {\n      type: 'text',\n      value: '\\n'\n    }, {\n      type: 'element',\n      tagName: 'ol',\n      properties: {},\n      children: state.wrap(listItems, true)\n    }, {\n      type: 'text',\n      value: '\\n'\n    }]\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}