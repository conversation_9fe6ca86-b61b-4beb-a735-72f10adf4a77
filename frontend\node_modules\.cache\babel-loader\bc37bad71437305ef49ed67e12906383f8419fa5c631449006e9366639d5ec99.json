{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\StandaloneRegister.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Form, Input, Button, Card, Typography, message, Spin, Radio, Select, Modal, Alert, Row, Col, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, HomeOutlined, TeamOutlined, PlusOutlined } from '@ant-design/icons';\nimport { register, getSchools, getRegions, getPublicClasses, getPublicSubjects, getRegistrationSettings, getAdvancedRegistrationSettings, getAvailableRoles, advancedRegister } from '../utils/api';\nimport { Link, useNavigate } from 'react-router-dom';\nimport ParentRegistration from './ParentRegistration'; // 导入ParentRegistration组件\nimport SchoolApplicationForm from './SchoolApplicationForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\n\n// 角色名称映射\nconst roleDisplayNames = {\n  'student': '学生',\n  'teacher': '教师',\n  'parent': '家长',\n  'class_teacher': '班主任',\n  'subject_leader': '备课组长',\n  'academic_director': '教务主任',\n  'vice_principal': '副校长',\n  'principal': '校长',\n  'school_admin': '学校管理员'\n};\n\n// 完全独立的注册组件，不依赖于任何认证逻辑\nconst StandaloneRegister = () => {\n  _s();\n  var _advancedSettings$rol4, _advancedSettings$rol5, _advancedSettings$rol6, _advancedSettings$rol7, _advancedSettings$rol8, _advancedSettings$rol9, _advancedSettings$rol0, _advancedSettings$rol1, _advancedSettings$rol10, _advancedSettings$rol11, _advancedSettings$rol12, _advancedSettings$rol13, _advancedSettings$rol14, _advancedSettings$rol15, _advancedSettings$rol16, _advancedSettings$rol17, _advancedSettings$rol18, _advancedSettings$rol19, _advancedSettings$rol20, _advancedSettings$rol21;\n  console.log('StandaloneRegister组件初始化');\n  const [loading, setLoading] = useState(false);\n  const [schools, setSchools] = useState([]);\n  const [customSchool, setCustomSchool] = useState(false);\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [selectedProvince, setSelectedProvince] = useState('');\n  const [selectedCity, setSelectedCity] = useState('');\n  const [selectedDistrict, setSelectedDistrict] = useState('');\n  const [classes, setClasses] = useState([]);\n  const [subjects, setSubjects] = useState([]);\n  const [isStudent, setIsStudent] = useState(true);\n  const [selectedRole, setSelectedRole] = useState('student');\n  const [selectedSchoolId, setSelectedSchoolId] = useState(null);\n  const [form] = Form.useForm();\n  const navigate = useNavigate();\n  const [registrationSettings, setRegistrationSettings] = useState({\n    allow_student_registration: true,\n    allow_teacher_registration: true\n  });\n  const [advancedSettings, setAdvancedSettings] = useState(null);\n  const [settingsLoading, setSettingsLoading] = useState(true);\n  const [availableRoles, setAvailableRoles] = useState(['student', 'teacher']);\n\n  // 家长注册组件的ref\n  const parentRegistrationRef = useRef();\n\n  // 在组件内部，添加新的状态变量\n  const [showSchoolApplicationModal, setShowSchoolApplicationModal] = useState(false);\n  const [globalRegistrationDisabled, setGlobalRegistrationDisabled] = useState(false);\n\n  // 清除localStorage中的token和user信息，确保不会自动登录\n  useEffect(() => {\n    console.log('StandaloneRegister - 清除localStorage');\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  }, []);\n\n  // 获取注册设置\n  useEffect(() => {\n    const fetchRegistrationSettings = async () => {\n      try {\n        console.log('正在获取注册设置...');\n        setSettingsLoading(true);\n        console.log('调用API: getRegistrationSettings()');\n        const settings = await getRegistrationSettings();\n        console.log('获取到注册设置:', settings);\n        console.log('学生注册状态:', settings.allow_student_registration);\n        console.log('教师注册状态:', settings.allow_teacher_registration);\n        console.log('全局注册开关状态:', settings.global_registration_enabled);\n        setRegistrationSettings(settings);\n\n        // 检查全局注册开关\n        if (settings.global_registration_enabled === false) {\n          console.log('全局注册开关已关闭，显示告示页面');\n          setGlobalRegistrationDisabled(true);\n          setSettingsLoading(false);\n          return;\n        }\n\n        // 获取高级注册设置\n        console.log('调用API: getAdvancedRegistrationSettings()');\n        const advSettings = await getAdvancedRegistrationSettings();\n        console.log('获取到高级注册设置:', advSettings);\n        setAdvancedSettings(advSettings);\n\n        // 直接获取可用角色\n        console.log('调用API: getAvailableRoles()');\n        const availableRolesData = await getAvailableRoles();\n        console.log('获取到可用角色数据:', availableRolesData);\n        if (availableRolesData && Array.isArray(availableRolesData.roles) && availableRolesData.roles.length > 0) {\n          // 提取角色名称\n          const enabledRoles = availableRolesData.roles.map(role => role.name);\n          console.log('可用角色列表:', enabledRoles);\n\n          // 记录每个角色的详细配置，方便调试\n          enabledRoles.forEach(roleName => {\n            const roleConfig = availableRolesData.roles.find(r => r.name === roleName);\n            console.log(`角色 ${roleName} 配置:`, roleConfig);\n          });\n          setAvailableRoles(enabledRoles);\n\n          // 如果当前选择的角色不在可用列表中，则选择第一个可用角色\n          if (!enabledRoles.includes(selectedRole)) {\n            const firstRole = enabledRoles[0];\n            console.log('当前选择的角色不可用，切换到:', firstRole);\n            setSelectedRole(firstRole);\n            setIsStudent(['student', 'parent'].includes(firstRole));\n            form.setFieldsValue({\n              role: firstRole,\n              is_teacher: firstRole === 'teacher'\n            });\n          }\n        } else {\n          // 回退到高级设置\n          if (advSettings && advSettings.roles) {\n            console.log('开始处理高级注册设置中的角色配置');\n            const enabledRoles = Object.keys(advSettings.roles).filter(role => advSettings.roles[role].enabled);\n            console.log('从高级设置提取的可用角色列表:', enabledRoles);\n            console.log('角色详细配置:', advSettings.roles);\n\n            // 确保至少有一个角色可用\n            if (enabledRoles.length > 0) {\n              console.log('设置可用角色:', enabledRoles);\n              setAvailableRoles(enabledRoles);\n\n              // 如果当前选择的角色不在可用列表中，则选择第一个可用角色\n              if (!enabledRoles.includes(selectedRole)) {\n                const firstRole = enabledRoles[0];\n                console.log('当前选择的角色不可用，切换到:', firstRole);\n                setSelectedRole(firstRole);\n                setIsStudent(firstRole === 'student');\n                form.setFieldsValue({\n                  role: firstRole\n                });\n              }\n            } else {\n              console.log('没有可用角色!');\n              // 如果没有可用角色，显示警告\n              Modal.warning({\n                title: '系统通知',\n                content: '系统当前未开放任何角色注册，请联系管理员',\n                onOk: () => navigate('/login')\n              });\n            }\n          } else {\n            console.log('未获取到高级注册设置或角色配置为空');\n            // 回退到基本设置\n            const basicRoles = [];\n            if (settings.allow_student_registration) basicRoles.push('student');\n            if (settings.allow_teacher_registration) basicRoles.push('teacher');\n            console.log('基于基本设置的可用角色:', basicRoles);\n            if (basicRoles.length > 0) {\n              setAvailableRoles(basicRoles);\n\n              // 如果当前选择的角色不在可用列表中，则选择第一个可用角色\n              if (!basicRoles.includes(selectedRole)) {\n                const firstRole = basicRoles[0];\n                setSelectedRole(firstRole);\n                setIsStudent(firstRole === 'student');\n                form.setFieldsValue({\n                  role: firstRole\n                });\n              }\n            }\n          }\n        }\n\n        // 检查是否所有注册功能都已关闭\n        if (!settings.allow_student_registration && !settings.allow_teacher_registration) {\n          console.log('所有注册功能已关闭，显示提示并重定向');\n          Modal.warning({\n            title: '系统通知',\n            content: '系统当前已关闭所有注册功能，请联系管理员创建账号',\n            onOk: () => navigate('/login')\n          });\n          return;\n        }\n        console.log('注册设置处理完成，当前isStudent状态:', isStudent);\n        console.log('当前选择的角色:', selectedRole);\n        console.log('表单值:', form.getFieldsValue());\n        console.log('最终可用角色列表:', availableRoles);\n      } catch (error) {\n        console.error('获取注册设置失败:', error);\n        // 出错时使用默认设置\n        setAvailableRoles(['student', 'teacher']);\n      } finally {\n        setSettingsLoading(false);\n        console.log('设置加载完成，settingsLoading设置为false');\n        console.log('最终注册设置状态:', registrationSettings);\n        console.log('最终isStudent状态:', isStudent);\n        console.log('最终选择的角色:', selectedRole);\n      }\n    };\n    fetchRegistrationSettings();\n  }, [navigate, form]);\n\n  // 表单初始化\n  useEffect(() => {\n    if (!settingsLoading && availableRoles.length > 0) {\n      const initialValues = getInitialValues();\n      console.log('设置表单初始值:', initialValues);\n      form.setFieldsValue(initialValues);\n\n      // 设置初始角色状态\n      setSelectedRole(initialValues.role);\n      setIsStudent(['student', 'parent'].includes(initialValues.role));\n    }\n  }, [settingsLoading, availableRoles, form]);\n  useEffect(() => {\n    // 页面加载时获取省份列表\n    const fetchProvinces = async () => {\n      console.log('正在获取省份列表...');\n      try {\n        const regionsData = await getRegions();\n        console.log('获取到省份列表:', regionsData);\n        if (regionsData.provinces && regionsData.provinces.length > 0) {\n          setProvinces(regionsData.provinces);\n        }\n      } catch (error) {\n        console.error('获取省份列表失败:', error);\n      }\n    };\n\n    // 使用setTimeout延迟执行，确保页面已经完全加载\n    const timer = setTimeout(() => {\n      fetchProvinces();\n    }, 200);\n    return () => clearTimeout(timer);\n  }, []);\n\n  // 当选择省份时，获取城市列表\n  useEffect(() => {\n    if (!selectedProvince) {\n      setCities([]);\n      setSelectedCity('');\n      return;\n    }\n    const fetchCities = async () => {\n      console.log(`正在获取${selectedProvince}的城市列表...`);\n      try {\n        const regionsData = await getRegions({\n          province: selectedProvince\n        });\n        console.log('获取到城市列表:', regionsData);\n        if (regionsData.cities && regionsData.cities.length > 0) {\n          setCities(regionsData.cities);\n        }\n      } catch (error) {\n        console.error('获取城市列表失败:', error);\n      }\n    };\n    fetchCities();\n  }, [selectedProvince]);\n\n  // 当选择城市时，获取区县列表\n  useEffect(() => {\n    if (!selectedCity) {\n      setDistricts([]);\n      setSelectedDistrict('');\n      return;\n    }\n    const fetchDistricts = async () => {\n      console.log(`正在获取${selectedProvince}${selectedCity}的区县列表...`);\n      try {\n        const regionsData = await getRegions({\n          province: selectedProvince,\n          city: selectedCity\n        });\n        console.log('获取到区县列表:', regionsData);\n        if (regionsData.districts && regionsData.districts.length > 0) {\n          setDistricts(regionsData.districts);\n        }\n      } catch (error) {\n        console.error('获取区县列表失败:', error);\n      }\n    };\n    fetchDistricts();\n  }, [selectedProvince, selectedCity]);\n\n  // 当选择区县时，获取该区县的学校列表\n  useEffect(() => {\n    if (!selectedDistrict) {\n      setSchools([]);\n      return;\n    }\n    const fetchSchools = async () => {\n      console.log(`正在获取${selectedProvince}${selectedCity}${selectedDistrict}的学校列表...`);\n      try {\n        const schoolsList = await getSchools({\n          province: selectedProvince,\n          city: selectedCity,\n          district: selectedDistrict\n        });\n        console.log('获取到学校列表:', schoolsList.length, '所学校');\n        setSchools(schoolsList);\n      } catch (error) {\n        console.error('获取学校列表失败:', error);\n        message.error('获取学校列表失败，请稍后重试');\n      }\n    };\n    fetchSchools();\n  }, [selectedProvince, selectedCity, selectedDistrict]);\n\n  // 当选择学校时，获取该学校的班级列表（所有角色都需要）\n  useEffect(() => {\n    const schoolId = form.getFieldValue('school_id');\n    if (!schoolId) {\n      setClasses([]);\n      return;\n    }\n    const fetchClasses = async () => {\n      console.log(`正在获取学校ID:${schoolId}的班级列表...`);\n      try {\n        // 确保使用正确的API端点和参数\n        const classesList = await getPublicClasses({\n          school_id: schoolId\n        });\n        console.log('获取到班级列表:', classesList);\n        if (Array.isArray(classesList) && classesList.length > 0) {\n          setClasses(classesList);\n        } else {\n          console.warn('班级列表为空或格式不正确:', classesList);\n          setClasses([]);\n          message.warning('该学校暂无班级数据');\n        }\n      } catch (error) {\n        console.error('获取班级列表失败:', error);\n        message.error('获取班级列表失败，请稍后重试');\n        setClasses([]);\n      }\n    };\n    fetchClasses();\n  }, [form, isStudent]);\n\n  // 不在初始加载时获取科目列表，等选择学校后再获取\n  // useEffect(() => {\n  //   const fetchSubjects = async () => {\n  //     try {\n  //       const subjectsList = await getPublicSubjects();\n  //       console.log('获取到科目列表:', subjectsList);\n  //       setSubjects(subjectsList);\n  //     } catch (error) {\n  //       console.error('获取科目列表失败:', error);\n  //     }\n  //   };\n  //\n  //   fetchSubjects();\n  // }, []);\n\n  // 添加家长绑定状态\n  const [parentBindingComplete, setParentBindingComplete] = useState(false);\n  const [boundStudent, setBoundStudent] = useState(null);\n\n  // 处理家长绑定完成的回调\n  const handleBindingComplete = (student, result) => {\n    setParentBindingComplete(true);\n    setBoundStudent(student);\n\n    // 更新表单中的学校和班级信息\n    if (student) {\n      form.setFieldsValue({\n        school_id: student.school_id,\n        school_name: student.school_name,\n        class_id: student.class_id\n      });\n    }\n    message.success('学生绑定成功，请继续完成注册');\n  };\n  const onFinish = async values => {\n    try {\n      // 防止重复提交\n      if (loading) {\n        return;\n      }\n      setLoading(true);\n      console.log('开始处理注册表单提交...');\n\n      // 确认密码在前端验证，不发送到服务器\n      const {\n        confirm,\n        province,\n        city,\n        district,\n        ...userData\n      } = values;\n\n      // 如果是家长注册，检查是否选择了学生和关系\n      if (userData.role === 'parent') {\n        var _parentRegistrationRe, _parentRegistrationRe2;\n        if (!((_parentRegistrationRe = parentRegistrationRef.current) !== null && _parentRegistrationRe !== void 0 && _parentRegistrationRe.isValid())) {\n          message.error('请先选择学生并设置关系');\n          setLoading(false);\n          return;\n        }\n\n        // 获取学生绑定信息\n        const studentInfo = (_parentRegistrationRe2 = parentRegistrationRef.current) === null || _parentRegistrationRe2 === void 0 ? void 0 : _parentRegistrationRe2.getSelectedStudentInfo();\n        if (studentInfo) {\n          userData.student_id = studentInfo.student_id;\n          userData.relationship = studentInfo.relationship;\n          userData.is_primary = studentInfo.is_primary;\n        }\n      }\n\n      // 确保学校名称正确传递\n      if (!userData.school_name) {\n        throw new Error('请选择或输入所属学校');\n      }\n\n      // 确保学校ID正确传递\n      if (!customSchool && !userData.school_id) {\n        throw new Error('请选择有效的学校');\n      }\n\n      // 根据角色类型检查必填字段\n      const role = userData.role || (userData.is_teacher ? 'teacher' : 'student');\n      const studentRoles = ['student', 'parent'];\n      const isStudentRole = studentRoles.includes(role);\n\n      // 如果是学生角色，确保班级ID正确传递\n      if (isStudentRole && !userData.class_id) {\n        throw new Error('请选择班级');\n      }\n\n      // 记录班级信息用于调试\n      if (userData.class_id) {\n        console.log('班级ID信息:', {\n          class_id: userData.class_id,\n          isArray: Array.isArray(userData.class_id),\n          role: selectedRole\n        });\n      }\n\n      // 如果是教师角色，确保科目正确传递\n      const teacherRoles = ['teacher', 'class_teacher', 'subject_leader'];\n      if (teacherRoles.includes(role) && !userData.subject) {\n        const roleDisplay = roleDisplayNames[role] || role;\n        throw new Error(`请选择${roleDisplay}教学科目`);\n      }\n\n      // 添加地区信息到用户数据\n      userData.province = selectedProvince;\n      userData.city = selectedCity;\n      userData.district = selectedDistrict;\n\n      // 添加角色信息\n      userData.role_name = role; // 高级注册API使用role_name而不是role\n\n      // 兼容旧版API，确保is_teacher字段正确\n      if (!userData.hasOwnProperty('is_teacher')) {\n        userData.is_teacher = role === 'teacher';\n      }\n      console.log('准备提交注册数据:', userData);\n      try {\n        // 使用高级注册API\n        const result = await advancedRegister(userData);\n        console.log('高级注册API返回结果:', result);\n        if (result) {\n          var _advancedSettings$rol;\n          // 获取角色配置，检查是否需要审核\n          const roleConfig = (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol = advancedSettings.roles) === null || _advancedSettings$rol === void 0 ? void 0 : _advancedSettings$rol[role]) || {};\n          const requiresApproval = roleConfig.requires_approval || false;\n          if (requiresApproval) {\n            // 如果需要审核，显示不同的成功消息\n            Modal.success({\n              title: '注册申请已提交',\n              content: '您的注册申请已提交，需要管理员审核后才能使用账号。审核通过后，您将收到通知。',\n              onOk: () => {\n                // 清除localStorage中的token和user信息，确保不会自动登录\n                localStorage.removeItem('token');\n                localStorage.removeItem('user');\n                // 跳转到登录页\n                window.location.href = '/login';\n              }\n            });\n          } else {\n            // 如果不需要审核，显示普通成功消息\n            message.success('注册成功！3秒后将跳转到登录页面...');\n            // 设置标记，告诉登录页这是从注册页来的\n            sessionStorage.setItem('from_register', 'true');\n            // 清除localStorage中的token和user信息，确保不会自动登录\n            localStorage.removeItem('token');\n            localStorage.removeItem('user');\n            // 延迟导航，确保用户能看到成功消息\n            setTimeout(() => {\n              // 使用window.location.href直接跳转，避免React Router的重定向逻辑\n              window.location.href = '/login';\n            }, 3000); // 增加到3秒，让用户有足够时间看到成功消息\n          }\n        } else {\n          throw new Error('注册失败，未收到服务器响应');\n        }\n      } catch (apiError) {\n        console.error('注册API调用失败:', apiError);\n        if (apiError.detail) {\n          message.error(apiError.detail);\n        } else {\n          message.error(apiError.message || '注册失败，请稍后重试');\n        }\n      }\n    } catch (error) {\n      console.error('注册过程中发生错误:', error);\n      message.error(error.message || '注册失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 计算表单初始值\n  const getInitialValues = () => {\n    // 如果有可用角色，选择第一个可用角色\n    if (availableRoles.length > 0) {\n      const firstRole = availableRoles[0];\n      return {\n        role: firstRole,\n        is_teacher: firstRole === 'teacher' // 兼容旧版API\n      };\n    }\n\n    // 默认情况\n    return {\n      role: 'student',\n      is_teacher: false\n    };\n  };\n\n  // 判断是否应该显示表单\n  const shouldShowForm = () => {\n    return registrationSettings.allow_student_registration || registrationSettings.allow_teacher_registration;\n  };\n\n  // 处理角色变更\n  const handleRoleChange = value => {\n    var _advancedSettings$rol2, _advancedSettings$rol3, _roleConfig$class, _roleConfig$subject;\n    console.log('角色变更:', value);\n    console.log('高级设置:', advancedSettings);\n\n    // 更新选中的角色\n    setSelectedRole(value);\n\n    // 学生相关角色\n    const studentRoles = ['student', 'parent'];\n    const isStudentRole = studentRoles.includes(value);\n    setIsStudent(isStudentRole);\n\n    // 获取角色的字段配置\n    const roleConfig = (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol2 = advancedSettings.roles) === null || _advancedSettings$rol2 === void 0 ? void 0 : (_advancedSettings$rol3 = _advancedSettings$rol2[value]) === null || _advancedSettings$rol3 === void 0 ? void 0 : _advancedSettings$rol3.fields) || {};\n    console.log(`角色 ${value} 的字段配置:`, roleConfig);\n\n    // 清除与角色相关的字段\n    const formValues = {\n      role: value\n    };\n\n    // 设置is_teacher字段（兼容旧版API）\n    formValues.is_teacher = value === 'teacher';\n\n    // 根据角色配置清除或保留字段\n    if (!((_roleConfig$class = roleConfig.class) !== null && _roleConfig$class !== void 0 && _roleConfig$class.required)) {\n      // 如果班级不是必填，则清除班级字段\n      formValues.class_id = undefined;\n    }\n    if ((_roleConfig$subject = roleConfig.subject) !== null && _roleConfig$subject !== void 0 && _roleConfig$subject.hidden) {\n      // 如果科目需要隐藏，则清除科目字段\n      formValues.subject = undefined;\n    }\n\n    // 更新表单值\n    console.log('更新表单值:', formValues);\n    form.setFieldsValue(formValues);\n\n    // 如果角色变更，可能需要重新加载相关数据\n    if (isStudentRole) {\n      // 学生角色可能需要加载班级数据\n      const schoolId = form.getFieldValue('school_id');\n      if (schoolId) {\n        console.log('重新加载班级数据, 学校ID:', schoolId);\n        getPublicClasses({\n          school_id: schoolId\n        }).then(classesList => {\n          setClasses(classesList || []);\n        }).catch(error => {\n          console.error('加载班级数据失败:', error);\n          setClasses([]);\n        });\n      }\n    } else {\n      var _roleConfig$subject2;\n      // 教师角色需要科目数据，但只有选择学校后才加载\n      if (!((_roleConfig$subject2 = roleConfig.subject) !== null && _roleConfig$subject2 !== void 0 && _roleConfig$subject2.hidden)) {\n        const currentSchoolId = form.getFieldValue('school_id');\n        if (currentSchoolId) {\n          console.log('重新加载科目数据, 学校ID:', currentSchoolId);\n          getPublicSubjects(currentSchoolId).then(subjectsList => {\n            setSubjects(subjectsList || []);\n          }).catch(error => {\n            console.error('加载科目数据失败:', error);\n            setSubjects([]);\n          });\n        } else {\n          console.log('未选择学校，清空科目数据');\n          setSubjects([]);\n        }\n      }\n    }\n  };\n\n  // 处理省份选择变化\n  const handleProvinceChange = value => {\n    setSelectedProvince(value);\n    form.setFieldsValue({\n      city: undefined,\n      district: undefined,\n      school_name: undefined,\n      school_id: undefined,\n      class_id: undefined\n    });\n    // 清空学校选择状态\n    setSelectedSchoolId(null);\n  };\n\n  // 处理城市选择变化\n  const handleCityChange = value => {\n    setSelectedCity(value);\n    form.setFieldsValue({\n      district: undefined,\n      school_name: undefined,\n      school_id: undefined,\n      class_id: undefined\n    });\n    // 清空学校选择状态\n    setSelectedSchoolId(null);\n  };\n\n  // 处理区县选择变化\n  const handleDistrictChange = value => {\n    setSelectedDistrict(value);\n    form.setFieldsValue({\n      school_name: undefined,\n      school_id: undefined,\n      class_id: undefined\n    });\n    // 清空学校选择状态\n    setSelectedSchoolId(null);\n  };\n\n  // 处理学校选择变化\n  const handleSchoolChange = (value, option) => {\n    console.log('选择学校:', value, option);\n\n    // 获取学校ID\n    let schoolId = null;\n\n    // 如果option是对象且有key属性\n    if (option && option.key) {\n      schoolId = option.key;\n      console.log('从option.key获取学校ID:', schoolId);\n    }\n    // 如果是Ant Design 4.x版本，可能是数组形式\n    else if (Array.isArray(option) && option.length > 0 && option[0].key) {\n      schoolId = option[0].key;\n      console.log('从option数组获取学校ID:', schoolId);\n    }\n    // 如果上述方法都失败，尝试从学校列表中查找\n    else {\n      const selectedSchool = schools.find(s => s.school_name === value);\n      if (selectedSchool) {\n        schoolId = selectedSchool.id;\n        console.log('通过学校名称查找学校ID:', value, schoolId);\n      }\n    }\n    if (!schoolId) {\n      console.error('无法获取学校ID');\n      message.error('选择学校失败，请重新选择');\n      return;\n    }\n    console.log('设置学校ID:', schoolId);\n    form.setFieldsValue({\n      school_id: schoolId,\n      class_id: undefined\n    });\n\n    // 更新选中的学校ID状态\n    setSelectedSchoolId(schoolId);\n\n    // 手动触发班级列表加载（所有角色都需要）\n    if (schoolId) {\n      console.log('手动触发班级列表加载, 学校ID:', schoolId, '角色:', selectedRole);\n      getPublicClasses({\n        school_id: schoolId\n      }).then(classesList => {\n        console.log('手动加载班级列表成功:', classesList);\n        if (Array.isArray(classesList) && classesList.length > 0) {\n          setClasses(classesList);\n        } else {\n          console.warn('手动加载的班级列表为空:', classesList);\n          setClasses([]);\n          // 只对需要班级的角色显示警告\n          if (isStudent || selectedRole === 'parent') {\n            message.warning('该学校暂无班级数据');\n          }\n        }\n      }).catch(error => {\n        console.error('手动加载班级列表失败:', error);\n        setClasses([]);\n        // 只对需要班级的角色显示错误\n        if (isStudent || selectedRole === 'parent') {\n          message.error('获取班级列表失败，请稍后重试');\n        }\n      });\n    }\n\n    // 如果是教师角色，加载该学校的科目列表\n    if (!isStudent && selectedRole !== 'parent') {\n      console.log('教师角色，加载学校科目列表, 学校ID:', schoolId);\n      getPublicSubjects(schoolId).then(subjectsList => {\n        console.log('加载学校科目列表成功:', subjectsList);\n        setSubjects(subjectsList || []);\n      }).catch(error => {\n        console.error('加载学校科目列表失败:', error);\n        setSubjects([]);\n      });\n    }\n  };\n\n  // 如果全局注册开关关闭，显示告示页面\n  if (globalRegistrationDisabled) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '100vh',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        style: {\n          width: 500,\n          padding: '40px',\n          textAlign: 'center',\n          borderRadius: '12px',\n          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\n          border: 'none'\n        },\n        bordered: false,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 30\n          },\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            style: {\n              color: '#667eea',\n              marginBottom: 8\n            },\n            children: \"\\u667A\\u6559\\u4E91\\u7AEF\\u667A\\u80FD\\u8F85\\u5BFC\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 4,\n            style: {\n              color: '#8b5cf6',\n              marginBottom: 0\n            },\n            children: \"\\u7CFB\\u7EDF\\u7EF4\\u62A4\\u901A\\u77E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 755,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n            padding: '30px',\n            borderRadius: '8px',\n            marginBottom: 30,\n            color: 'white'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '18px',\n              lineHeight: '1.6',\n              margin: 0,\n              fontWeight: '500'\n            },\n            children: \"\\u7531\\u4E8E\\u7CFB\\u7EDF\\u7EF4\\u62A4\\uFF0C\\u6682\\u505C\\u5F00\\u653E\\u6CE8\\u518C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f8fafc',\n            padding: '20px',\n            borderRadius: '8px',\n            marginBottom: 30\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '16px',\n              color: '#64748b',\n              lineHeight: '1.6',\n              margin: 0\n            },\n            children: \"\\u5177\\u4F53\\u5F00\\u653E\\u65F6\\u95F4\\u8BF7\\u8054\\u7CFB\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 15\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '16px',\n                color: '#1e293b',\n                margin: '8px 0',\n                fontWeight: '600'\n              },\n              children: \"\\u5FAE\\u4FE1\\<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '16px',\n                color: '#1e293b',\n                margin: '8px 0',\n                fontWeight: '600'\n              },\n              children: \"QQ\\uFF1A1965457418\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            onClick: () => navigate('/login'),\n            style: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              border: 'none',\n              borderRadius: '6px',\n              padding: '0 30px',\n              height: '44px',\n              fontSize: '16px'\n            },\n            children: \"\\u8FD4\\u56DE\\u767B\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 815,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100vh',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      background: 'linear-gradient(to right, #1890ff, #52c41a)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        width: 500,\n        maxWidth: '90vw',\n        padding: '16px',\n        maxHeight: '95vh',\n        overflowY: 'auto'\n      },\n      bordered: false,\n      className: \"register-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"style\", {\n        jsx: true,\n        children: `\n          .register-card .ant-form-item {\n            margin-bottom: 16px;\n          }\n          .register-card .ant-divider {\n            margin: 12px 0 16px 0;\n          }\n          .register-card .ant-divider-inner-text {\n            font-size: 14px;\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 3,\n          style: {\n            color: '#1890ff',\n            marginBottom: 8\n          },\n          children: \"\\u667A\\u6559\\u4E91\\u7AEF\\u667A\\u80FD\\u8F85\\u5BFC\\u5E73\\u53F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          style: {\n            marginBottom: 0\n          },\n          children: \"\\u521B\\u5EFA\\u65B0\\u8D26\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Spin, {\n        spinning: loading || settingsLoading,\n        children: [shouldShowForm() ? /*#__PURE__*/_jsxDEV(Form, {\n          name: \"register_form\",\n          initialValues: getInitialValues(),\n          onFinish: onFinish,\n          layout: \"vertical\",\n          form: form,\n          style: {\n            marginBottom: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Divider, {\n            orientation: \"left\",\n            style: {\n              margin: '12px 0 16px 0',\n              fontSize: '14px'\n            },\n            children: \"\\u57FA\\u672C\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"username\",\n                rules: [{\n                  required: true,\n                  message: '请输入用户名!'\n                }, {\n                  min: 4,\n                  message: '用户名至少4个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 31\n                  }, this),\n                  placeholder: \"\\u7528\\u6237\\u540D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"full_name\",\n                rules: [{\n                  required: true,\n                  message: '请输入姓名!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 31\n                  }, this),\n                  placeholder: \"\\u59D3\\u540D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"email\",\n                rules: [{\n                  required: true,\n                  message: '请输入邮箱!'\n                }, {\n                  type: 'email',\n                  message: '请输入有效的邮箱地址!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 31\n                  }, this),\n                  placeholder: \"\\u90AE\\u7BB1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"phone\",\n                rules: [{\n                  required: true,\n                  message: '请输入手机号码!'\n                }, {\n                  pattern: /^1[3-9]\\d{9}$/,\n                  message: '请输入有效的手机号码!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(PhoneOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 31\n                  }, this),\n                  placeholder: \"\\u624B\\u673A\\u53F7\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"password\",\n                rules: [{\n                  required: true,\n                  message: '请输入密码!'\n                }, {\n                  min: 6,\n                  message: '密码至少6个字符'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 954,\n                    columnNumber: 31\n                  }, this),\n                  type: \"password\",\n                  placeholder: \"\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"confirm\",\n                dependencies: ['password'],\n                rules: [{\n                  required: true,\n                  message: '请确认密码!'\n                }, ({\n                  getFieldValue\n                }) => ({\n                  validator(_, value) {\n                    if (!value || getFieldValue('password') === value) {\n                      return Promise.resolve();\n                    }\n                    return Promise.reject(new Error('两次输入的密码不一致!'));\n                  }\n                })],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                    className: \"site-form-item-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 31\n                  }, this),\n                  type: \"password\",\n                  placeholder: \"\\u786E\\u8BA4\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 15\n          }, this), availableRoles.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"\\u6CE8\\u518C\\u529F\\u80FD\\u6682\\u65F6\\u5173\\u95ED\",\n            description: \"\\u7CFB\\u7EDF\\u5F53\\u524D\\u672A\\u5F00\\u653E\\u4EFB\\u4F55\\u89D2\\u8272\\u6CE8\\u518C\\uFF0C\\u8BF7\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\",\n            type: \"warning\",\n            showIcon: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 987,\n            columnNumber: 17\n          }, this) : availableRoles.length > 1 ? /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"role\",\n            label: \"\\u8BF7\\u9009\\u62E9\\u8EAB\\u4EFD\",\n            rules: [{\n              required: true,\n              message: '请选择身份!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8EAB\\u4EFD\",\n              onChange: handleRoleChange,\n              style: {\n                width: '100%'\n              },\n              children: availableRoles.map(role => /*#__PURE__*/_jsxDEV(Option, {\n                value: role,\n                children: roleDisplayNames[role] || role\n              }, role, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 17\n          }, this) :\n          /*#__PURE__*/\n          /* 当只有一种角色可用时，显示当前选择的角色 */\n          _jsxDEV(Form.Item, {\n            label: \"\\u6CE8\\u518C\\u8EAB\\u4EFD\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              value: availableRoles.length > 0 ? roleDisplayNames[availableRoles[0]] || availableRoles[0] : '无可用角色',\n              disabled: true,\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 29\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"role\",\n              hidden: true,\n              initialValue: availableRoles.length > 0 ? availableRoles[0] : '',\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"is_teacher\",\n              hidden: true,\n              initialValue: availableRoles.length > 0 && availableRoles[0] === 'teacher',\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            orientation: \"left\",\n            style: {\n              margin: '12px 0 16px 0',\n              fontSize: '14px'\n            },\n            children: \"\\u5730\\u533A\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"province\",\n                rules: [{\n                  required: true,\n                  message: '请选择省份!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u7701\\u4EFD\",\n                  onChange: handleProvinceChange,\n                  allowClear: true,\n                  children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n                    value: province,\n                    children: province\n                  }, province, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1042,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1032,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"city\",\n                rules: [{\n                  required: true,\n                  message: '请选择城市!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u57CE\\u5E02\",\n                  onChange: handleCityChange,\n                  disabled: !selectedProvince,\n                  allowClear: true,\n                  children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n                    value: city,\n                    children: city\n                  }, city, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1059,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"district\",\n                rules: [{\n                  required: true,\n                  message: '请选择区县!'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u53BF\",\n                  onChange: handleDistrictChange,\n                  disabled: !selectedCity,\n                  allowClear: true,\n                  children: districts.map(district => /*#__PURE__*/_jsxDEV(Option, {\n                    value: district,\n                    children: district\n                  }, district, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 15\n          }, this), isStudent && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"left\",\n              style: {\n                margin: '12px 0 16px 0',\n                fontSize: '14px'\n              },\n              children: \"\\u5B66\\u6821\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 16,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"school_name\",\n                  rules: [{\n                    required: true,\n                    message: '请选择或输入所属学校!'\n                  }],\n                  children: customSchool ? /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                      className: \"site-form-item-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 37\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B66\\u6821\\u540D\\u79F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u5B66\\u6821\",\n                    onChange: handleSchoolChange,\n                    disabled: !selectedDistrict,\n                    allowClear: true,\n                    showSearch: true,\n                    filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                    children: schools.map(school => /*#__PURE__*/_jsxDEV(Option, {\n                      value: school.school_name,\n                      children: school.school_name\n                    }, school.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1113,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1101,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"class_id\",\n                  rules: [{\n                    required: true,\n                    message: '请选择班级!'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u73ED\\u7EA7\",\n                    disabled: !selectedSchoolId || customSchool,\n                    allowClear: true,\n                    children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                      value: cls.id,\n                      children: cls.name\n                    }, cls.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1130,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 19\n            }, this), !customSchool && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: -8,\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                style: {\n                  padding: 0\n                },\n                onClick: () => setShowSchoolApplicationModal(true),\n                children: \"\\u6CA1\\u6709\\u627E\\u5230\\u60A8\\u7684\\u5B66\\u6821\\uFF1F\\u70B9\\u51FB\\u7533\\u8BF7\\u6DFB\\u52A0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"school_id\",\n              hidden: true,\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1151,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), !isStudent && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"left\",\n              style: {\n                margin: '12px 0 16px 0',\n                fontSize: '14px'\n              },\n              children: \"\\u6559\\u5E08\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 16,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"school_name\",\n                  rules: [{\n                    required: true,\n                    message: '请选择或输入所属学校!'\n                  }],\n                  children: customSchool ? /*#__PURE__*/_jsxDEV(Input, {\n                    prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                      className: \"site-form-item-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1170,\n                      columnNumber: 37\n                    }, this),\n                    placeholder: \"\\u8BF7\\u8F93\\u5165\\u5B66\\u6821\\u540D\\u79F0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u5B66\\u6821\",\n                    onChange: handleSchoolChange,\n                    disabled: !selectedDistrict,\n                    allowClear: true,\n                    showSearch: true,\n                    filterOption: (input, option) => option.children && typeof option.children === 'string' ? option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false,\n                    children: schools.map(school => /*#__PURE__*/_jsxDEV(Option, {\n                      value: school.school_name,\n                      children: school.school_name\n                    }, school.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1186,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1174,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1163,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 8,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"subject\",\n                  rules: [{\n                    required: true,\n                    message: '请选择任教科目!'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    placeholder: \"\\u8BF7\\u9009\\u62E9\\u4EFB\\u6559\\u79D1\\u76EE\",\n                    allowClear: true,\n                    disabled: !selectedSchoolId,\n                    children: subjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n                      value: subject.name,\n                      children: subject.name\n                    }, subject.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1203,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1197,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1193,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1192,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1162,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: -8,\n                marginBottom: 8\n              },\n              children: !customSchool ? /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                onClick: () => setCustomSchool(true),\n                style: {\n                  padding: 0\n                },\n                children: \"\\u6CA1\\u6709\\u627E\\u5230\\u60A8\\u7684\\u5B66\\u6821\\uFF1F\\u70B9\\u51FB\\u624B\\u52A8\\u8F93\\u5165\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1212,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                onClick: () => setCustomSchool(false),\n                style: {\n                  padding: 0\n                },\n                children: \"\\u8FD4\\u56DE\\u9009\\u62E9\\u5DF2\\u6709\\u5B66\\u6821\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1221,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1210,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"school_id\",\n              hidden: true,\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 19\n            }, this), (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol4 = advancedSettings.roles) === null || _advancedSettings$rol4 === void 0 ? void 0 : (_advancedSettings$rol5 = _advancedSettings$rol4[selectedRole]) === null || _advancedSettings$rol5 === void 0 ? void 0 : (_advancedSettings$rol6 = _advancedSettings$rol5.fields) === null || _advancedSettings$rol6 === void 0 ? void 0 : (_advancedSettings$rol7 = _advancedSettings$rol6.class) === null || _advancedSettings$rol7 === void 0 ? void 0 : _advancedSettings$rol7.required) && /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"class_id\",\n              label: selectedRole === 'class_teacher' ? '管理班级' : '任教班级',\n              rules: [{\n                required: true,\n                message: '请选择班级!'\n              }],\n              extra: selectedRole === 'class_teacher' && (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol8 = advancedSettings.roles) === null || _advancedSettings$rol8 === void 0 ? void 0 : (_advancedSettings$rol9 = _advancedSettings$rol8[selectedRole]) === null || _advancedSettings$rol9 === void 0 ? void 0 : (_advancedSettings$rol0 = _advancedSettings$rol9.fields) === null || _advancedSettings$rol0 === void 0 ? void 0 : (_advancedSettings$rol1 = _advancedSettings$rol0.class) === null || _advancedSettings$rol1 === void 0 ? void 0 : _advancedSettings$rol1.max) > 1 ? '⚠️ 班主任提示：如果选择多个班级，第一个选择的班级将作为您的主要管理班级' : selectedRole === 'class_teacher' ? '💡 您将成为该班级的班主任，负责班级的日常管理工作' : (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol10 = advancedSettings.roles) === null || _advancedSettings$rol10 === void 0 ? void 0 : (_advancedSettings$rol11 = _advancedSettings$rol10[selectedRole]) === null || _advancedSettings$rol11 === void 0 ? void 0 : (_advancedSettings$rol12 = _advancedSettings$rol11.fields) === null || _advancedSettings$rol12 === void 0 ? void 0 : (_advancedSettings$rol13 = _advancedSettings$rol12.class) === null || _advancedSettings$rol13 === void 0 ? void 0 : _advancedSettings$rol13.max) > 1 ? '可选择多个任教班级' : undefined,\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: selectedRole === 'class_teacher' ? '请选择管理班级' : '请选择任教班级',\n                disabled: !selectedSchoolId || customSchool,\n                allowClear: true,\n                mode: (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol14 = advancedSettings.roles) === null || _advancedSettings$rol14 === void 0 ? void 0 : (_advancedSettings$rol15 = _advancedSettings$rol14[selectedRole]) === null || _advancedSettings$rol15 === void 0 ? void 0 : (_advancedSettings$rol16 = _advancedSettings$rol15.fields) === null || _advancedSettings$rol16 === void 0 ? void 0 : (_advancedSettings$rol17 = _advancedSettings$rol16.class) === null || _advancedSettings$rol17 === void 0 ? void 0 : _advancedSettings$rol17.max) > 1 ? \"multiple\" : undefined,\n                maxTagCount: (advancedSettings === null || advancedSettings === void 0 ? void 0 : (_advancedSettings$rol18 = advancedSettings.roles) === null || _advancedSettings$rol18 === void 0 ? void 0 : (_advancedSettings$rol19 = _advancedSettings$rol18[selectedRole]) === null || _advancedSettings$rol19 === void 0 ? void 0 : (_advancedSettings$rol20 = _advancedSettings$rol19.fields) === null || _advancedSettings$rol20 === void 0 ? void 0 : (_advancedSettings$rol21 = _advancedSettings$rol20.class) === null || _advancedSettings$rol21 === void 0 ? void 0 : _advancedSettings$rol21.max) || 1,\n                children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                  value: cls.id,\n                  children: cls.name\n                }, cls.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1261,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1239,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true), selectedRole === 'parent' && /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u5B66\\u751F\\u7ED1\\u5B9A\",\n            name: \"student_binding\",\n            rules: [{\n              required: true,\n              message: '请完成学生绑定'\n            }],\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              children: parentBindingComplete ? /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"\\u7ED1\\u5B9A\\u6210\\u529F\",\n                description: `已成功绑定学生: ${(boundStudent === null || boundStudent === void 0 ? void 0 : boundStudent.full_name) || '未知'}, 学校: ${(boundStudent === null || boundStudent === void 0 ? void 0 : boundStudent.school_name) || '未知'}, 班级: ${(boundStudent === null || boundStudent === void 0 ? void 0 : boundStudent.class_name) || '未知'}`,\n                type: \"success\",\n                showIcon: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1278,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(ParentRegistration, {\n                ref: parentRegistrationRef,\n                onBindingComplete: handleBindingComplete,\n                form: form,\n                initialValues: form.getFieldsValue(),\n                compact: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1285,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1276,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1271,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              className: \"login-form-button\",\n              loading: loading,\n              children: \"\\u6CE8\\u518C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1298,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1297,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 13\n        }, this) : !settingsLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6CE8\\u518C\\u529F\\u80FD\\u5DF2\\u5173\\u95ED\",\n          description: \"\\u7CFB\\u7EDF\\u5F53\\u524D\\u5DF2\\u5173\\u95ED\\u6240\\u6709\\u6CE8\\u518C\\u529F\\u80FD\\uFF0C\\u8BF7\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\\u521B\\u5EFA\\u8D26\\u53F7\",\n          type: \"error\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1310,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginTop: 16\n          },\n          children: [\"\\u5DF2\\u6709\\u8D26\\u53F7? \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/login\",\n            children: \"\\u7ACB\\u5373\\u767B\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1320,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 845,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u7533\\u8BF7\\u6DFB\\u52A0\\u65B0\\u5B66\\u6821\",\n      visible: showSchoolApplicationModal,\n      onCancel: () => setShowSchoolApplicationModal(false),\n      footer: null,\n      width: 800,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(SchoolApplicationForm, {\n        onSubmitSuccess: () => {\n          setShowSchoolApplicationModal(false);\n          message.success('学校申请已提交，请等待审核通过后再进行注册');\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1334,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1326,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 838,\n    columnNumber: 5\n  }, this);\n};\n_s(StandaloneRegister, \"tnbli7jlIVP9/nrRIR7MobUDL3k=\", false, function () {\n  return [Form.useForm, useNavigate];\n});\n_c = StandaloneRegister;\nexport default StandaloneRegister;\nvar _c;\n$RefreshReg$(_c, \"StandaloneRegister\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "message", "Spin", "Radio", "Select", "Modal", "<PERSON><PERSON>", "Row", "Col", "Divider", "UserOutlined", "LockOutlined", "MailOutlined", "PhoneOutlined", "HomeOutlined", "TeamOutlined", "PlusOutlined", "register", "getSchools", "getRegions", "getPublicClasses", "getPublicSubjects", "getRegistrationSettings", "getAdvancedRegistrationSettings", "getAvailableRoles", "advancedRegister", "Link", "useNavigate", "ParentRegistration", "SchoolApplicationForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Option", "roleDisplayNames", "StandaloneRegister", "_s", "_advancedSettings$rol4", "_advancedSettings$rol5", "_advancedSettings$rol6", "_advancedSettings$rol7", "_advancedSettings$rol8", "_advancedSettings$rol9", "_advancedSettings$rol0", "_advancedSettings$rol1", "_advancedSettings$rol10", "_advancedSettings$rol11", "_advancedSettings$rol12", "_advancedSettings$rol13", "_advancedSettings$rol14", "_advancedSettings$rol15", "_advancedSettings$rol16", "_advancedSettings$rol17", "_advancedSettings$rol18", "_advancedSettings$rol19", "_advancedSettings$rol20", "_advancedSettings$rol21", "console", "log", "loading", "setLoading", "schools", "setSchools", "customSchool", "setCustomSchool", "provinces", "setProvinces", "cities", "setCities", "districts", "setDistricts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedProvince", "selectedCity", "setSelectedCity", "selectedDistrict", "setSelectedDistrict", "classes", "setClasses", "subjects", "setSubjects", "isStudent", "setIsStudent", "selectedR<PERSON>", "setSelectedRole", "selectedSchoolId", "setSelectedSchoolId", "form", "useForm", "navigate", "registrationSettings", "setRegistrationSettings", "allow_student_registration", "allow_teacher_registration", "advancedSettings", "setAdvancedSettings", "settingsLoading", "setSettingsLoading", "availableRoles", "setAvailableRoles", "parentRegistrationRef", "showSchoolApplicationModal", "setShowSchoolApplicationModal", "globalRegistrationDisabled", "setGlobalRegistrationDisabled", "localStorage", "removeItem", "fetchRegistrationSettings", "settings", "global_registration_enabled", "advSettings", "availableRolesData", "Array", "isArray", "roles", "length", "enabledRoles", "map", "role", "name", "for<PERSON>ach", "<PERSON><PERSON><PERSON>", "roleConfig", "find", "r", "includes", "firstRole", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_teacher", "Object", "keys", "filter", "enabled", "warning", "title", "content", "onOk", "basicRoles", "push", "getFieldsValue", "error", "initialValues", "getInitialValues", "fetchProvinces", "regionsData", "timer", "setTimeout", "clearTimeout", "fetchCities", "province", "fetchDistricts", "city", "fetchSchools", "schoolsList", "district", "schoolId", "getFieldValue", "fetchClasses", "classesList", "school_id", "warn", "parentBindingComplete", "setParentBindingComplete", "boundStudent", "setBoundStudent", "handleBindingComplete", "student", "result", "school_name", "class_id", "success", "onFinish", "values", "confirm", "userData", "_parentRegistrationRe", "_parentRegistrationRe2", "current", "<PERSON><PERSON><PERSON><PERSON>", "studentInfo", "getSelectedStudentInfo", "student_id", "relationship", "is_primary", "Error", "studentRoles", "isStudentRole", "teacher<PERSON><PERSON><PERSON>", "subject", "roleDisplay", "role_name", "hasOwnProperty", "_advancedSettings$rol", "requiresApproval", "requires_approval", "window", "location", "href", "sessionStorage", "setItem", "apiError", "detail", "shouldShowForm", "handleRoleChange", "value", "_advancedSettings$rol2", "_advancedSettings$rol3", "_roleConfig$class", "_roleConfig$subject", "fields", "formValues", "class", "required", "undefined", "hidden", "then", "catch", "_roleConfig$subject2", "currentSchoolId", "subjectsList", "handleProvinceChange", "handleCityChange", "handleDistrictChange", "handleSchoolChange", "option", "key", "selectedSchool", "s", "id", "style", "height", "display", "justifyContent", "alignItems", "background", "fontFamily", "children", "width", "padding", "textAlign", "borderRadius", "boxShadow", "border", "bordered", "marginBottom", "level", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "lineHeight", "margin", "fontWeight", "marginTop", "type", "size", "onClick", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflowY", "className", "jsx", "spinning", "layout", "orientation", "gutter", "span", "<PERSON><PERSON>", "rules", "min", "prefix", "placeholder", "pattern", "dependencies", "validator", "_", "Promise", "resolve", "reject", "description", "showIcon", "label", "onChange", "disabled", "initialValue", "allowClear", "showSearch", "filterOption", "input", "toLowerCase", "indexOf", "school", "cls", "extra", "max", "mode", "maxTag<PERSON>ount", "full_name", "class_name", "ref", "onBindingComplete", "compact", "htmlType", "visible", "onCancel", "footer", "destroyOnClose", "onSubmitSuccess", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/StandaloneRegister.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Form, Input, Button, Card, Typography, message, Spin, Radio, Select, Modal, Alert, Row, Col, Divider } from 'antd';\r\nimport { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, HomeOutlined, TeamOutlined, PlusOutlined } from '@ant-design/icons';\r\nimport { register, getSchools, getRegions, getPublicClasses, getPublicSubjects, getRegistrationSettings, getAdvancedRegistrationSettings, getAvailableRoles, advancedRegister } from '../utils/api';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport ParentRegistration from './ParentRegistration'; // 导入ParentRegistration组件\r\nimport SchoolApplicationForm from './SchoolApplicationForm';\r\n\r\nconst { Title } = Typography;\r\nconst { Option } = Select;\r\n\r\n// 角色名称映射\r\nconst roleDisplayNames = {\r\n  'student': '学生',\r\n  'teacher': '教师',\r\n  'parent': '家长',\r\n  'class_teacher': '班主任',\r\n  'subject_leader': '备课组长',\r\n  'academic_director': '教务主任',\r\n  'vice_principal': '副校长',\r\n  'principal': '校长',\r\n  'school_admin': '学校管理员'\r\n};\r\n\r\n// 完全独立的注册组件，不依赖于任何认证逻辑\r\nconst StandaloneRegister = () => {\r\n  console.log('StandaloneRegister组件初始化');\r\n  const [loading, setLoading] = useState(false);\r\n  const [schools, setSchools] = useState([]);\r\n  const [customSchool, setCustomSchool] = useState(false);\r\n  const [provinces, setProvinces] = useState([]);\r\n  const [cities, setCities] = useState([]);\r\n  const [districts, setDistricts] = useState([]);\r\n  const [selectedProvince, setSelectedProvince] = useState('');\r\n  const [selectedCity, setSelectedCity] = useState('');\r\n  const [selectedDistrict, setSelectedDistrict] = useState('');\r\n  const [classes, setClasses] = useState([]);\r\n  const [subjects, setSubjects] = useState([]);\r\n  const [isStudent, setIsStudent] = useState(true);\r\n  const [selectedRole, setSelectedRole] = useState('student');\r\n  const [selectedSchoolId, setSelectedSchoolId] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const navigate = useNavigate();\r\n  const [registrationSettings, setRegistrationSettings] = useState({\r\n    allow_student_registration: true,\r\n    allow_teacher_registration: true\r\n  });\r\n  const [advancedSettings, setAdvancedSettings] = useState(null);\r\n  const [settingsLoading, setSettingsLoading] = useState(true);\r\n  const [availableRoles, setAvailableRoles] = useState(['student', 'teacher']);\r\n\r\n  // 家长注册组件的ref\r\n  const parentRegistrationRef = useRef();\r\n\r\n  // 在组件内部，添加新的状态变量\r\n  const [showSchoolApplicationModal, setShowSchoolApplicationModal] = useState(false);\r\n  const [globalRegistrationDisabled, setGlobalRegistrationDisabled] = useState(false);\r\n  \r\n  // 清除localStorage中的token和user信息，确保不会自动登录\r\n  useEffect(() => {\r\n    console.log('StandaloneRegister - 清除localStorage');\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('user');\r\n  }, []);\r\n\r\n  // 获取注册设置\r\n  useEffect(() => {\r\n    const fetchRegistrationSettings = async () => {\r\n      try {\r\n        console.log('正在获取注册设置...');\r\n        setSettingsLoading(true);\r\n        console.log('调用API: getRegistrationSettings()');\r\n        const settings = await getRegistrationSettings();\r\n        console.log('获取到注册设置:', settings);\r\n        console.log('学生注册状态:', settings.allow_student_registration);\r\n        console.log('教师注册状态:', settings.allow_teacher_registration);\r\n        console.log('全局注册开关状态:', settings.global_registration_enabled);\r\n        setRegistrationSettings(settings);\r\n\r\n        // 检查全局注册开关\r\n        if (settings.global_registration_enabled === false) {\r\n          console.log('全局注册开关已关闭，显示告示页面');\r\n          setGlobalRegistrationDisabled(true);\r\n          setSettingsLoading(false);\r\n          return;\r\n        }\r\n        \r\n        // 获取高级注册设置\r\n        console.log('调用API: getAdvancedRegistrationSettings()');\r\n        const advSettings = await getAdvancedRegistrationSettings();\r\n        console.log('获取到高级注册设置:', advSettings);\r\n        setAdvancedSettings(advSettings);\r\n        \r\n        // 直接获取可用角色\r\n        console.log('调用API: getAvailableRoles()');\r\n        const availableRolesData = await getAvailableRoles();\r\n        console.log('获取到可用角色数据:', availableRolesData);\r\n        \r\n        if (availableRolesData && Array.isArray(availableRolesData.roles) && availableRolesData.roles.length > 0) {\r\n          // 提取角色名称\r\n          const enabledRoles = availableRolesData.roles.map(role => role.name);\r\n          console.log('可用角色列表:', enabledRoles);\r\n          \r\n          // 记录每个角色的详细配置，方便调试\r\n          enabledRoles.forEach(roleName => {\r\n            const roleConfig = availableRolesData.roles.find(r => r.name === roleName);\r\n            console.log(`角色 ${roleName} 配置:`, roleConfig);\r\n          });\r\n          \r\n          setAvailableRoles(enabledRoles);\r\n          \r\n          // 如果当前选择的角色不在可用列表中，则选择第一个可用角色\r\n          if (!enabledRoles.includes(selectedRole)) {\r\n            const firstRole = enabledRoles[0];\r\n            console.log('当前选择的角色不可用，切换到:', firstRole);\r\n            setSelectedRole(firstRole);\r\n            setIsStudent(['student', 'parent'].includes(firstRole));\r\n            form.setFieldsValue({ \r\n              role: firstRole,\r\n              is_teacher: firstRole === 'teacher'\r\n            });\r\n          }\r\n        } else {\r\n          // 回退到高级设置\r\n          if (advSettings && advSettings.roles) {\r\n            console.log('开始处理高级注册设置中的角色配置');\r\n            const enabledRoles = Object.keys(advSettings.roles).filter(\r\n              role => advSettings.roles[role].enabled\r\n            );\r\n            console.log('从高级设置提取的可用角色列表:', enabledRoles);\r\n            console.log('角色详细配置:', advSettings.roles);\r\n            \r\n            // 确保至少有一个角色可用\r\n            if (enabledRoles.length > 0) {\r\n              console.log('设置可用角色:', enabledRoles);\r\n              setAvailableRoles(enabledRoles);\r\n              \r\n              // 如果当前选择的角色不在可用列表中，则选择第一个可用角色\r\n              if (!enabledRoles.includes(selectedRole)) {\r\n                const firstRole = enabledRoles[0];\r\n                console.log('当前选择的角色不可用，切换到:', firstRole);\r\n                setSelectedRole(firstRole);\r\n                setIsStudent(firstRole === 'student');\r\n                form.setFieldsValue({ role: firstRole });\r\n              }\r\n            } else {\r\n              console.log('没有可用角色!');\r\n              // 如果没有可用角色，显示警告\r\n              Modal.warning({\r\n                title: '系统通知',\r\n                content: '系统当前未开放任何角色注册，请联系管理员',\r\n                onOk: () => navigate('/login')\r\n              });\r\n            }\r\n          } else {\r\n            console.log('未获取到高级注册设置或角色配置为空');\r\n            // 回退到基本设置\r\n            const basicRoles = [];\r\n            if (settings.allow_student_registration) basicRoles.push('student');\r\n            if (settings.allow_teacher_registration) basicRoles.push('teacher');\r\n            \r\n            console.log('基于基本设置的可用角色:', basicRoles);\r\n            if (basicRoles.length > 0) {\r\n              setAvailableRoles(basicRoles);\r\n              \r\n              // 如果当前选择的角色不在可用列表中，则选择第一个可用角色\r\n              if (!basicRoles.includes(selectedRole)) {\r\n                const firstRole = basicRoles[0];\r\n                setSelectedRole(firstRole);\r\n                setIsStudent(firstRole === 'student');\r\n                form.setFieldsValue({ role: firstRole });\r\n              }\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 检查是否所有注册功能都已关闭\r\n        if (!settings.allow_student_registration && !settings.allow_teacher_registration) {\r\n          console.log('所有注册功能已关闭，显示提示并重定向');\r\n          Modal.warning({\r\n            title: '系统通知',\r\n            content: '系统当前已关闭所有注册功能，请联系管理员创建账号',\r\n            onOk: () => navigate('/login')\r\n          });\r\n          return;\r\n        }\r\n        \r\n        console.log('注册设置处理完成，当前isStudent状态:', isStudent);\r\n        console.log('当前选择的角色:', selectedRole);\r\n        console.log('表单值:', form.getFieldsValue());\r\n        console.log('最终可用角色列表:', availableRoles);\r\n        \r\n      } catch (error) {\r\n        console.error('获取注册设置失败:', error);\r\n        // 出错时使用默认设置\r\n        setAvailableRoles(['student', 'teacher']);\r\n      } finally {\r\n        setSettingsLoading(false);\r\n        console.log('设置加载完成，settingsLoading设置为false');\r\n        console.log('最终注册设置状态:', registrationSettings);\r\n        console.log('最终isStudent状态:', isStudent);\r\n        console.log('最终选择的角色:', selectedRole);\r\n      }\r\n    };\r\n    \r\n    fetchRegistrationSettings();\r\n  }, [navigate, form]);\r\n\r\n  // 表单初始化\r\n  useEffect(() => {\r\n    if (!settingsLoading && availableRoles.length > 0) {\r\n      const initialValues = getInitialValues();\r\n      console.log('设置表单初始值:', initialValues);\r\n      form.setFieldsValue(initialValues);\r\n      \r\n      // 设置初始角色状态\r\n      setSelectedRole(initialValues.role);\r\n      setIsStudent(['student', 'parent'].includes(initialValues.role));\r\n    }\r\n  }, [settingsLoading, availableRoles, form]);\r\n\r\n  useEffect(() => {\r\n    // 页面加载时获取省份列表\r\n    const fetchProvinces = async () => {\r\n      console.log('正在获取省份列表...');\r\n      \r\n      try {\r\n        const regionsData = await getRegions();\r\n        console.log('获取到省份列表:', regionsData);\r\n        if (regionsData.provinces && regionsData.provinces.length > 0) {\r\n          setProvinces(regionsData.provinces);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取省份列表失败:', error);\r\n      }\r\n    };\r\n\r\n    // 使用setTimeout延迟执行，确保页面已经完全加载\r\n    const timer = setTimeout(() => {\r\n      fetchProvinces();\r\n    }, 200);\r\n    \r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n  \r\n  // 当选择省份时，获取城市列表\r\n  useEffect(() => {\r\n    if (!selectedProvince) {\r\n      setCities([]);\r\n      setSelectedCity('');\r\n      return;\r\n    }\r\n    \r\n    const fetchCities = async () => {\r\n      console.log(`正在获取${selectedProvince}的城市列表...`);\r\n      \r\n      try {\r\n        const regionsData = await getRegions({ province: selectedProvince });\r\n        console.log('获取到城市列表:', regionsData);\r\n        if (regionsData.cities && regionsData.cities.length > 0) {\r\n          setCities(regionsData.cities);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取城市列表失败:', error);\r\n      }\r\n    };\r\n    \r\n    fetchCities();\r\n  }, [selectedProvince]);\r\n  \r\n  // 当选择城市时，获取区县列表\r\n  useEffect(() => {\r\n    if (!selectedCity) {\r\n      setDistricts([]);\r\n      setSelectedDistrict('');\r\n      return;\r\n    }\r\n    \r\n    const fetchDistricts = async () => {\r\n      console.log(`正在获取${selectedProvince}${selectedCity}的区县列表...`);\r\n      \r\n      try {\r\n        const regionsData = await getRegions({ \r\n          province: selectedProvince,\r\n          city: selectedCity \r\n        });\r\n        console.log('获取到区县列表:', regionsData);\r\n        if (regionsData.districts && regionsData.districts.length > 0) {\r\n          setDistricts(regionsData.districts);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取区县列表失败:', error);\r\n      }\r\n    };\r\n    \r\n    fetchDistricts();\r\n  }, [selectedProvince, selectedCity]);\r\n  \r\n  // 当选择区县时，获取该区县的学校列表\r\n  useEffect(() => {\r\n    if (!selectedDistrict) {\r\n      setSchools([]);\r\n      return;\r\n    }\r\n    \r\n    const fetchSchools = async () => {\r\n      console.log(`正在获取${selectedProvince}${selectedCity}${selectedDistrict}的学校列表...`);\r\n      \r\n      try {\r\n        const schoolsList = await getSchools({\r\n          province: selectedProvince,\r\n          city: selectedCity,\r\n          district: selectedDistrict\r\n        });\r\n        console.log('获取到学校列表:', schoolsList.length, '所学校');\r\n        setSchools(schoolsList);\r\n      } catch (error) {\r\n        console.error('获取学校列表失败:', error);\r\n        message.error('获取学校列表失败，请稍后重试');\r\n      }\r\n    };\r\n    \r\n    fetchSchools();\r\n  }, [selectedProvince, selectedCity, selectedDistrict]);\r\n\r\n  // 当选择学校时，获取该学校的班级列表（所有角色都需要）\r\n  useEffect(() => {\r\n    const schoolId = form.getFieldValue('school_id');\r\n    if (!schoolId) {\r\n      setClasses([]);\r\n      return;\r\n    }\r\n    \r\n    const fetchClasses = async () => {\r\n      console.log(`正在获取学校ID:${schoolId}的班级列表...`);\r\n      \r\n      try {\r\n        // 确保使用正确的API端点和参数\r\n        const classesList = await getPublicClasses({\r\n          school_id: schoolId\r\n        });\r\n        console.log('获取到班级列表:', classesList);\r\n        \r\n        if (Array.isArray(classesList) && classesList.length > 0) {\r\n          setClasses(classesList);\r\n        } else {\r\n          console.warn('班级列表为空或格式不正确:', classesList);\r\n          setClasses([]);\r\n          message.warning('该学校暂无班级数据');\r\n        }\r\n      } catch (error) {\r\n        console.error('获取班级列表失败:', error);\r\n        message.error('获取班级列表失败，请稍后重试');\r\n        setClasses([]);\r\n      }\r\n    };\r\n    \r\n    fetchClasses();\r\n  }, [form, isStudent]);\r\n\r\n  // 不在初始加载时获取科目列表，等选择学校后再获取\r\n  // useEffect(() => {\r\n  //   const fetchSubjects = async () => {\r\n  //     try {\r\n  //       const subjectsList = await getPublicSubjects();\r\n  //       console.log('获取到科目列表:', subjectsList);\r\n  //       setSubjects(subjectsList);\r\n  //     } catch (error) {\r\n  //       console.error('获取科目列表失败:', error);\r\n  //     }\r\n  //   };\r\n  //\r\n  //   fetchSubjects();\r\n  // }, []);\r\n\r\n  // 添加家长绑定状态\r\n  const [parentBindingComplete, setParentBindingComplete] = useState(false);\r\n  const [boundStudent, setBoundStudent] = useState(null);\r\n\r\n  // 处理家长绑定完成的回调\r\n  const handleBindingComplete = (student, result) => {\r\n    setParentBindingComplete(true);\r\n    setBoundStudent(student);\r\n    \r\n    // 更新表单中的学校和班级信息\r\n    if (student) {\r\n      form.setFieldsValue({\r\n        school_id: student.school_id,\r\n        school_name: student.school_name,\r\n        class_id: student.class_id\r\n      });\r\n    }\r\n    \r\n    message.success('学生绑定成功，请继续完成注册');\r\n  };\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      // 防止重复提交\r\n      if (loading) {\r\n        return;\r\n      }\r\n      \r\n      setLoading(true);\r\n      console.log('开始处理注册表单提交...');\r\n      \r\n      // 确认密码在前端验证，不发送到服务器\r\n      const { confirm, province, city, district, ...userData } = values;\r\n      \r\n      // 如果是家长注册，检查是否选择了学生和关系\r\n      if (userData.role === 'parent') {\r\n        if (!parentRegistrationRef.current?.isValid()) {\r\n          message.error('请先选择学生并设置关系');\r\n          setLoading(false);\r\n          return;\r\n        }\r\n\r\n        // 获取学生绑定信息\r\n        const studentInfo = parentRegistrationRef.current?.getSelectedStudentInfo();\r\n        if (studentInfo) {\r\n          userData.student_id = studentInfo.student_id;\r\n          userData.relationship = studentInfo.relationship;\r\n          userData.is_primary = studentInfo.is_primary;\r\n        }\r\n      }\r\n      \r\n      // 确保学校名称正确传递\r\n      if (!userData.school_name) {\r\n        throw new Error('请选择或输入所属学校');\r\n      }\r\n      \r\n      // 确保学校ID正确传递\r\n      if (!customSchool && !userData.school_id) {\r\n        throw new Error('请选择有效的学校');\r\n      }\r\n      \r\n      // 根据角色类型检查必填字段\r\n      const role = userData.role || (userData.is_teacher ? 'teacher' : 'student');\r\n      const studentRoles = ['student', 'parent'];\r\n      const isStudentRole = studentRoles.includes(role);\r\n      \r\n      // 如果是学生角色，确保班级ID正确传递\r\n      if (isStudentRole && !userData.class_id) {\r\n        throw new Error('请选择班级');\r\n      }\r\n\r\n      // 记录班级信息用于调试\r\n      if (userData.class_id) {\r\n        console.log('班级ID信息:', {\r\n          class_id: userData.class_id,\r\n          isArray: Array.isArray(userData.class_id),\r\n          role: selectedRole\r\n        });\r\n      }\r\n      \r\n      // 如果是教师角色，确保科目正确传递\r\n      const teacherRoles = ['teacher', 'class_teacher', 'subject_leader'];\r\n      if (teacherRoles.includes(role) && !userData.subject) {\r\n        const roleDisplay = roleDisplayNames[role] || role;\r\n        throw new Error(`请选择${roleDisplay}教学科目`);\r\n      }\r\n      \r\n      // 添加地区信息到用户数据\r\n      userData.province = selectedProvince;\r\n      userData.city = selectedCity;\r\n      userData.district = selectedDistrict;\r\n      \r\n      // 添加角色信息\r\n      userData.role_name = role; // 高级注册API使用role_name而不是role\r\n      \r\n      // 兼容旧版API，确保is_teacher字段正确\r\n      if (!userData.hasOwnProperty('is_teacher')) {\r\n        userData.is_teacher = role === 'teacher';\r\n      }\r\n      \r\n      console.log('准备提交注册数据:', userData);\r\n      \r\n      try {\r\n        // 使用高级注册API\r\n        const result = await advancedRegister(userData);\r\n        console.log('高级注册API返回结果:', result);\r\n        \r\n        if (result) {\r\n          // 获取角色配置，检查是否需要审核\r\n          const roleConfig = advancedSettings?.roles?.[role] || {};\r\n          const requiresApproval = roleConfig.requires_approval || false;\r\n          \r\n          if (requiresApproval) {\r\n            // 如果需要审核，显示不同的成功消息\r\n            Modal.success({\r\n              title: '注册申请已提交',\r\n              content: '您的注册申请已提交，需要管理员审核后才能使用账号。审核通过后，您将收到通知。',\r\n              onOk: () => {\r\n                // 清除localStorage中的token和user信息，确保不会自动登录\r\n                localStorage.removeItem('token');\r\n                localStorage.removeItem('user');\r\n                // 跳转到登录页\r\n                window.location.href = '/login';\r\n              }\r\n            });\r\n          } else {\r\n            // 如果不需要审核，显示普通成功消息\r\n            message.success('注册成功！3秒后将跳转到登录页面...');\r\n            // 设置标记，告诉登录页这是从注册页来的\r\n            sessionStorage.setItem('from_register', 'true');\r\n            // 清除localStorage中的token和user信息，确保不会自动登录\r\n            localStorage.removeItem('token');\r\n            localStorage.removeItem('user');\r\n            // 延迟导航，确保用户能看到成功消息\r\n            setTimeout(() => {\r\n              // 使用window.location.href直接跳转，避免React Router的重定向逻辑\r\n              window.location.href = '/login';\r\n            }, 3000); // 增加到3秒，让用户有足够时间看到成功消息\r\n          }\r\n        } else {\r\n          throw new Error('注册失败，未收到服务器响应');\r\n        }\r\n      } catch (apiError) {\r\n        console.error('注册API调用失败:', apiError);\r\n        if (apiError.detail) {\r\n          message.error(apiError.detail);\r\n        } else {\r\n          message.error(apiError.message || '注册失败，请稍后重试');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('注册过程中发生错误:', error);\r\n      message.error(error.message || '注册失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 计算表单初始值\r\n  const getInitialValues = () => {\r\n    // 如果有可用角色，选择第一个可用角色\r\n    if (availableRoles.length > 0) {\r\n      const firstRole = availableRoles[0];\r\n      return { \r\n        role: firstRole,\r\n        is_teacher: firstRole === 'teacher' // 兼容旧版API\r\n      };\r\n    }\r\n\r\n    // 默认情况\r\n    return { role: 'student', is_teacher: false };\r\n  };\r\n\r\n  // 判断是否应该显示表单\r\n  const shouldShowForm = () => {\r\n    return registrationSettings.allow_student_registration || registrationSettings.allow_teacher_registration;\r\n  };\r\n\r\n  // 处理角色变更\r\n  const handleRoleChange = (value) => {\r\n    console.log('角色变更:', value);\r\n    console.log('高级设置:', advancedSettings);\r\n    \r\n    // 更新选中的角色\r\n    setSelectedRole(value);\r\n    \r\n    // 学生相关角色\r\n    const studentRoles = ['student', 'parent'];\r\n    const isStudentRole = studentRoles.includes(value);\r\n    setIsStudent(isStudentRole);\r\n    \r\n    // 获取角色的字段配置\r\n    const roleConfig = advancedSettings?.roles?.[value]?.fields || {};\r\n    console.log(`角色 ${value} 的字段配置:`, roleConfig);\r\n    \r\n    // 清除与角色相关的字段\r\n    const formValues = { role: value };\r\n    \r\n    // 设置is_teacher字段（兼容旧版API）\r\n    formValues.is_teacher = (value === 'teacher');\r\n    \r\n    // 根据角色配置清除或保留字段\r\n    if (!roleConfig.class?.required) {\r\n      // 如果班级不是必填，则清除班级字段\r\n      formValues.class_id = undefined;\r\n    }\r\n    \r\n    if (roleConfig.subject?.hidden) {\r\n      // 如果科目需要隐藏，则清除科目字段\r\n      formValues.subject = undefined;\r\n    }\r\n    \r\n    // 更新表单值\r\n    console.log('更新表单值:', formValues);\r\n    form.setFieldsValue(formValues);\r\n    \r\n    // 如果角色变更，可能需要重新加载相关数据\r\n    if (isStudentRole) {\r\n      // 学生角色可能需要加载班级数据\r\n      const schoolId = form.getFieldValue('school_id');\r\n      if (schoolId) {\r\n        console.log('重新加载班级数据, 学校ID:', schoolId);\r\n        getPublicClasses({ school_id: schoolId })\r\n          .then(classesList => {\r\n            setClasses(classesList || []);\r\n          })\r\n          .catch(error => {\r\n            console.error('加载班级数据失败:', error);\r\n            setClasses([]);\r\n          });\r\n      }\r\n    } else {\r\n      // 教师角色需要科目数据，但只有选择学校后才加载\r\n      if (!roleConfig.subject?.hidden) {\r\n        const currentSchoolId = form.getFieldValue('school_id');\r\n        if (currentSchoolId) {\r\n          console.log('重新加载科目数据, 学校ID:', currentSchoolId);\r\n          getPublicSubjects(currentSchoolId)\r\n            .then(subjectsList => {\r\n              setSubjects(subjectsList || []);\r\n            })\r\n            .catch(error => {\r\n              console.error('加载科目数据失败:', error);\r\n              setSubjects([]);\r\n            });\r\n        } else {\r\n          console.log('未选择学校，清空科目数据');\r\n          setSubjects([]);\r\n        }\r\n      }\r\n    }\r\n  };\r\n  \r\n  // 处理省份选择变化\r\n  const handleProvinceChange = (value) => {\r\n    setSelectedProvince(value);\r\n    form.setFieldsValue({ city: undefined, district: undefined, school_name: undefined, school_id: undefined, class_id: undefined });\r\n    // 清空学校选择状态\r\n    setSelectedSchoolId(null);\r\n  };\r\n\r\n  // 处理城市选择变化\r\n  const handleCityChange = (value) => {\r\n    setSelectedCity(value);\r\n    form.setFieldsValue({ district: undefined, school_name: undefined, school_id: undefined, class_id: undefined });\r\n    // 清空学校选择状态\r\n    setSelectedSchoolId(null);\r\n  };\r\n\r\n  // 处理区县选择变化\r\n  const handleDistrictChange = (value) => {\r\n    setSelectedDistrict(value);\r\n    form.setFieldsValue({ school_name: undefined, school_id: undefined, class_id: undefined });\r\n    // 清空学校选择状态\r\n    setSelectedSchoolId(null);\r\n  };\r\n\r\n  // 处理学校选择变化\r\n  const handleSchoolChange = (value, option) => {\r\n    console.log('选择学校:', value, option);\r\n    \r\n    // 获取学校ID\r\n    let schoolId = null;\r\n    \r\n    // 如果option是对象且有key属性\r\n    if (option && option.key) {\r\n      schoolId = option.key;\r\n      console.log('从option.key获取学校ID:', schoolId);\r\n    } \r\n    // 如果是Ant Design 4.x版本，可能是数组形式\r\n    else if (Array.isArray(option) && option.length > 0 && option[0].key) {\r\n      schoolId = option[0].key;\r\n      console.log('从option数组获取学校ID:', schoolId);\r\n    }\r\n    // 如果上述方法都失败，尝试从学校列表中查找\r\n    else {\r\n      const selectedSchool = schools.find(s => s.school_name === value);\r\n      if (selectedSchool) {\r\n        schoolId = selectedSchool.id;\r\n        console.log('通过学校名称查找学校ID:', value, schoolId);\r\n      }\r\n    }\r\n    \r\n    if (!schoolId) {\r\n      console.error('无法获取学校ID');\r\n      message.error('选择学校失败，请重新选择');\r\n      return;\r\n    }\r\n    \r\n    console.log('设置学校ID:', schoolId);\r\n    form.setFieldsValue({ school_id: schoolId, class_id: undefined });\r\n\r\n    // 更新选中的学校ID状态\r\n    setSelectedSchoolId(schoolId);\r\n    \r\n    // 手动触发班级列表加载（所有角色都需要）\r\n    if (schoolId) {\r\n      console.log('手动触发班级列表加载, 学校ID:', schoolId, '角色:', selectedRole);\r\n      getPublicClasses({ school_id: schoolId })\r\n        .then(classesList => {\r\n          console.log('手动加载班级列表成功:', classesList);\r\n          if (Array.isArray(classesList) && classesList.length > 0) {\r\n            setClasses(classesList);\r\n          } else {\r\n            console.warn('手动加载的班级列表为空:', classesList);\r\n            setClasses([]);\r\n            // 只对需要班级的角色显示警告\r\n            if (isStudent || selectedRole === 'parent') {\r\n              message.warning('该学校暂无班级数据');\r\n            }\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error('手动加载班级列表失败:', error);\r\n          setClasses([]);\r\n          // 只对需要班级的角色显示错误\r\n          if (isStudent || selectedRole === 'parent') {\r\n            message.error('获取班级列表失败，请稍后重试');\r\n          }\r\n        });\r\n    }\r\n\r\n    // 如果是教师角色，加载该学校的科目列表\r\n    if (!isStudent && selectedRole !== 'parent') {\r\n      console.log('教师角色，加载学校科目列表, 学校ID:', schoolId);\r\n      getPublicSubjects(schoolId)\r\n        .then(subjectsList => {\r\n          console.log('加载学校科目列表成功:', subjectsList);\r\n          setSubjects(subjectsList || []);\r\n        })\r\n        .catch(error => {\r\n          console.error('加载学校科目列表失败:', error);\r\n          setSubjects([]);\r\n        });\r\n    }\r\n  };\r\n\r\n  // 如果全局注册开关关闭，显示告示页面\r\n  if (globalRegistrationDisabled) {\r\n    return (\r\n      <div style={{\r\n        height: '100vh',\r\n        display: 'flex',\r\n        justifyContent: 'center',\r\n        alignItems: 'center',\r\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n        fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif'\r\n      }}>\r\n        <Card\r\n          style={{\r\n            width: 500,\r\n            padding: '40px',\r\n            textAlign: 'center',\r\n            borderRadius: '12px',\r\n            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',\r\n            border: 'none'\r\n          }}\r\n          bordered={false}\r\n        >\r\n          <div style={{ marginBottom: 30 }}>\r\n            <Title level={2} style={{ color: '#667eea', marginBottom: 8 }}>\r\n              智教云端智能辅导平台\r\n            </Title>\r\n            <Title level={4} style={{ color: '#8b5cf6', marginBottom: 0 }}>\r\n              系统维护通知\r\n            </Title>\r\n          </div>\r\n\r\n          <div style={{\r\n            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\r\n            padding: '30px',\r\n            borderRadius: '8px',\r\n            marginBottom: 30,\r\n            color: 'white'\r\n          }}>\r\n            <p style={{\r\n              fontSize: '18px',\r\n              lineHeight: '1.6',\r\n              margin: 0,\r\n              fontWeight: '500'\r\n            }}>\r\n              由于系统维护，暂停开放注册\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{\r\n            background: '#f8fafc',\r\n            padding: '20px',\r\n            borderRadius: '8px',\r\n            marginBottom: 30\r\n          }}>\r\n            <p style={{\r\n              fontSize: '16px',\r\n              color: '#64748b',\r\n              lineHeight: '1.6',\r\n              margin: 0\r\n            }}>\r\n              具体开放时间请联系：\r\n            </p>\r\n            <div style={{ marginTop: 15 }}>\r\n              <p style={{\r\n                fontSize: '16px',\r\n                color: '#1e293b',\r\n                margin: '8px 0',\r\n                fontWeight: '600'\r\n              }}>\r\n                微信：<EMAIL>\r\n              </p>\r\n              <p style={{\r\n                fontSize: '16px',\r\n                color: '#1e293b',\r\n                margin: '8px 0',\r\n                fontWeight: '600'\r\n              }}>\r\n                QQ：1965457418\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div style={{ textAlign: 'center' }}>\r\n            <Button\r\n              type=\"primary\"\r\n              size=\"large\"\r\n              onClick={() => navigate('/login')}\r\n              style={{\r\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n                border: 'none',\r\n                borderRadius: '6px',\r\n                padding: '0 30px',\r\n                height: '44px',\r\n                fontSize: '16px'\r\n              }}\r\n            >\r\n              返回登录\r\n            </Button>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div style={{\r\n      height: '100vh',\r\n      display: 'flex',\r\n      justifyContent: 'center',\r\n      alignItems: 'center',\r\n      background: 'linear-gradient(to right, #1890ff, #52c41a)'\r\n    }}>\r\n      <Card\r\n        style={{\r\n          width: 500,\r\n          maxWidth: '90vw',\r\n          padding: '16px',\r\n          maxHeight: '95vh',\r\n          overflowY: 'auto'\r\n        }}\r\n        bordered={false}\r\n        className=\"register-card\"\r\n      >\r\n        <style jsx>{`\r\n          .register-card .ant-form-item {\r\n            margin-bottom: 16px;\r\n          }\r\n          .register-card .ant-divider {\r\n            margin: 12px 0 16px 0;\r\n          }\r\n          .register-card .ant-divider-inner-text {\r\n            font-size: 14px;\r\n          }\r\n        `}</style>\r\n        <div style={{ textAlign: 'center', marginBottom: 16 }}>\r\n          <Title level={3} style={{ color: '#1890ff', marginBottom: 8 }}>智教云端智能辅导平台</Title>\r\n          <Title level={5} style={{ marginBottom: 0 }}>创建新账户</Title>\r\n        </div>\r\n        \r\n        <Spin spinning={loading || settingsLoading}>\r\n          {shouldShowForm() ? (\r\n            <Form\r\n              name=\"register_form\"\r\n              initialValues={getInitialValues()}\r\n              onFinish={onFinish}\r\n              layout=\"vertical\"\r\n              form={form}\r\n              style={{ marginBottom: 0 }}\r\n            >\r\n              {/* 基本信息 */}\r\n              <Divider orientation=\"left\" style={{ margin: '12px 0 16px 0', fontSize: '14px' }}>基本信息</Divider>\r\n\r\n              <Row gutter={16}>\r\n                <Col span={12}>\r\n                  <Form.Item\r\n                    name=\"username\"\r\n                    rules={[\r\n                      { required: true, message: '请输入用户名!' },\r\n                      { min: 4, message: '用户名至少4个字符' }\r\n                    ]}\r\n                  >\r\n                    <Input\r\n                      prefix={<UserOutlined className=\"site-form-item-icon\" />}\r\n                      placeholder=\"用户名\"\r\n                    />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={12}>\r\n                  <Form.Item\r\n                    name=\"full_name\"\r\n                    rules={[{ required: true, message: '请输入姓名!' }]}\r\n                  >\r\n                    <Input\r\n                      prefix={<UserOutlined className=\"site-form-item-icon\" />}\r\n                      placeholder=\"姓名\"\r\n                    />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row gutter={16}>\r\n                <Col span={12}>\r\n                  <Form.Item\r\n                    name=\"email\"\r\n                    rules={[\r\n                      { required: true, message: '请输入邮箱!' },\r\n                      { type: 'email', message: '请输入有效的邮箱地址!' }\r\n                    ]}\r\n                  >\r\n                    <Input\r\n                      prefix={<MailOutlined className=\"site-form-item-icon\" />}\r\n                      placeholder=\"邮箱\"\r\n                    />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={12}>\r\n                  <Form.Item\r\n                    name=\"phone\"\r\n                    rules={[\r\n                      { required: true, message: '请输入手机号码!' },\r\n                      { pattern: /^1[3-9]\\d{9}$/, message: '请输入有效的手机号码!' }\r\n                    ]}\r\n                  >\r\n                    <Input\r\n                      prefix={<PhoneOutlined className=\"site-form-item-icon\" />}\r\n                      placeholder=\"手机号码\"\r\n                    />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row gutter={16}>\r\n                <Col span={12}>\r\n                  <Form.Item\r\n                    name=\"password\"\r\n                    rules={[\r\n                      { required: true, message: '请输入密码!' },\r\n                      { min: 6, message: '密码至少6个字符' }\r\n                    ]}\r\n                  >\r\n                    <Input\r\n                      prefix={<LockOutlined className=\"site-form-item-icon\" />}\r\n                      type=\"password\"\r\n                      placeholder=\"密码\"\r\n                    />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={12}>\r\n                  <Form.Item\r\n                    name=\"confirm\"\r\n                    dependencies={['password']}\r\n                    rules={[\r\n                      { required: true, message: '请确认密码!' },\r\n                      ({ getFieldValue }) => ({\r\n                        validator(_, value) {\r\n                          if (!value || getFieldValue('password') === value) {\r\n                            return Promise.resolve();\r\n                          }\r\n                          return Promise.reject(new Error('两次输入的密码不一致!'));\r\n                        },\r\n                      }),\r\n                    ]}\r\n                  >\r\n                    <Input\r\n                      prefix={<LockOutlined className=\"site-form-item-icon\" />}\r\n                      type=\"password\"\r\n                      placeholder=\"确认密码\"\r\n                    />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              \r\n              {/* 身份选择，显示所有可用角色 */}\r\n              {availableRoles.length === 0 ? (\r\n                <Alert\r\n                  message=\"注册功能暂时关闭\"\r\n                  description=\"系统当前未开放任何角色注册，请联系管理员\"\r\n                  type=\"warning\"\r\n                  showIcon\r\n                />\r\n              ) : availableRoles.length > 1 ? (\r\n                <Form.Item name=\"role\" label=\"请选择身份\" rules={[{ required: true, message: '请选择身份!' }]}>\r\n                  <Select \r\n                    placeholder=\"请选择身份\" \r\n                    onChange={handleRoleChange}\r\n                    style={{ width: '100%' }}\r\n                  >\r\n                    {availableRoles.map(role => (\r\n                      <Option key={role} value={role}>\r\n                        {roleDisplayNames[role] || role}\r\n                      </Option>\r\n                    ))}\r\n                  </Select>\r\n                </Form.Item>\r\n              ) : (\r\n                /* 当只有一种角色可用时，显示当前选择的角色 */\r\n                <Form.Item label=\"注册身份\">\r\n                  <Input\r\n                    value={availableRoles.length > 0 ? roleDisplayNames[availableRoles[0]] || availableRoles[0] : '无可用角色'}\r\n                    disabled\r\n                    prefix={<UserOutlined />}\r\n                  />\r\n                  {/* 隐藏的表单项，用于提交正确的值 */}\r\n                  <Form.Item name=\"role\" hidden initialValue={availableRoles.length > 0 ? availableRoles[0] : ''}>\r\n                    <Input />\r\n                  </Form.Item>\r\n                  {/* 兼容旧版API的is_teacher字段 */}\r\n                  <Form.Item name=\"is_teacher\" hidden initialValue={availableRoles.length > 0 && availableRoles[0] === 'teacher'}>\r\n                    <Input />\r\n                  </Form.Item>\r\n                </Form.Item>\r\n              )}\r\n              \r\n              {/* 地区信息 - 学生和教师都需要 */}\r\n              <Divider orientation=\"left\" style={{ margin: '12px 0 16px 0', fontSize: '14px' }}>地区信息</Divider>\r\n\r\n              {/* 省市区级联选择 - 三列布局 */}\r\n              <Row gutter={16}>\r\n                <Col span={8}>\r\n                  <Form.Item\r\n                    name=\"province\"\r\n                    rules={[{ required: true, message: '请选择省份!' }]}\r\n                  >\r\n                    <Select\r\n                      placeholder=\"请选择省份\"\r\n                      onChange={handleProvinceChange}\r\n                      allowClear\r\n                    >\r\n                      {provinces.map(province => (\r\n                        <Option key={province} value={province}>{province}</Option>\r\n                      ))}\r\n                    </Select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item\r\n                    name=\"city\"\r\n                    rules={[{ required: true, message: '请选择城市!' }]}\r\n                  >\r\n                    <Select\r\n                      placeholder=\"请选择城市\"\r\n                      onChange={handleCityChange}\r\n                      disabled={!selectedProvince}\r\n                      allowClear\r\n                    >\r\n                      {cities.map(city => (\r\n                        <Option key={city} value={city}>{city}</Option>\r\n                      ))}\r\n                    </Select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item\r\n                    name=\"district\"\r\n                    rules={[{ required: true, message: '请选择区县!' }]}\r\n                  >\r\n                    <Select\r\n                      placeholder=\"请选择区县\"\r\n                      onChange={handleDistrictChange}\r\n                      disabled={!selectedCity}\r\n                      allowClear\r\n                    >\r\n                      {districts.map(district => (\r\n                        <Option key={district} value={district}>{district}</Option>\r\n                      ))}\r\n                    </Select>\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n\r\n              {/* 学生专属字段 - 学校和班级 */}\r\n              {isStudent && (\r\n                <>\r\n\r\n                  <Divider orientation=\"left\" style={{ margin: '12px 0 16px 0', fontSize: '14px' }}>学校信息</Divider>\r\n\r\n                  <Row gutter={16}>\r\n                    <Col span={16}>\r\n                      <Form.Item\r\n                        name=\"school_name\"\r\n                        rules={[{ required: true, message: '请选择或输入所属学校!' }]}\r\n                      >\r\n                        {customSchool ? (\r\n                          <Input\r\n                            prefix={<UserOutlined className=\"site-form-item-icon\" />}\r\n                            placeholder=\"请输入学校名称\"\r\n                          />\r\n                        ) : (\r\n                          <Select\r\n                            placeholder=\"请选择学校\"\r\n                            onChange={handleSchoolChange}\r\n                            disabled={!selectedDistrict}\r\n                            allowClear\r\n                            showSearch\r\n                            filterOption={(input, option) =>\r\n                              option.children && typeof option.children === 'string' ?\r\n                              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                            }\r\n                          >\r\n                            {schools.map(school => (\r\n                              <Option key={school.id} value={school.school_name}>{school.school_name}</Option>\r\n                            ))}\r\n                          </Select>\r\n                        )}\r\n                      </Form.Item>\r\n                    </Col>\r\n                    <Col span={8}>\r\n                      <Form.Item\r\n                        name=\"class_id\"\r\n                        rules={[{ required: true, message: '请选择班级!' }]}\r\n                      >\r\n                        <Select\r\n                          placeholder=\"请选择班级\"\r\n                          disabled={!selectedSchoolId || customSchool}\r\n                          allowClear\r\n                        >\r\n                          {classes.map(cls => (\r\n                            <Option key={cls.id} value={cls.id}>{cls.name}</Option>\r\n                          ))}\r\n                        </Select>\r\n                      </Form.Item>\r\n                    </Col>\r\n                  </Row>\r\n\r\n                  {!customSchool && (\r\n                    <div style={{ marginTop: -8, marginBottom: 8 }}>\r\n                      <Button\r\n                        type=\"link\"\r\n                        size=\"small\"\r\n                        style={{ padding: 0 }}\r\n                        onClick={() => setShowSchoolApplicationModal(true)}\r\n                      >\r\n                        没有找到您的学校？点击申请添加\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* 隐藏的学校ID字段 */}\r\n                  <Form.Item name=\"school_id\" hidden>\r\n                    <Input />\r\n                  </Form.Item>\r\n                </>\r\n              )}\r\n              \r\n              {/* 教师专属字段 - 科目选择 */}\r\n              {!isStudent && (\r\n                <>\r\n                  <Divider orientation=\"left\" style={{ margin: '12px 0 16px 0', fontSize: '14px' }}>教师信息</Divider>\r\n\r\n                  <Row gutter={16}>\r\n                    <Col span={16}>\r\n                      <Form.Item\r\n                        name=\"school_name\"\r\n                        rules={[{ required: true, message: '请选择或输入所属学校!' }]}\r\n                      >\r\n                        {customSchool ? (\r\n                          <Input\r\n                            prefix={<UserOutlined className=\"site-form-item-icon\" />}\r\n                            placeholder=\"请输入学校名称\"\r\n                          />\r\n                        ) : (\r\n                          <Select\r\n                            placeholder=\"请选择学校\"\r\n                            onChange={handleSchoolChange}\r\n                            disabled={!selectedDistrict}\r\n                            allowClear\r\n                            showSearch\r\n                            filterOption={(input, option) =>\r\n                              option.children && typeof option.children === 'string' ?\r\n                              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0 : false\r\n                            }\r\n                          >\r\n                            {schools.map(school => (\r\n                              <Option key={school.id} value={school.school_name}>{school.school_name}</Option>\r\n                            ))}\r\n                          </Select>\r\n                        )}\r\n                      </Form.Item>\r\n                    </Col>\r\n                    <Col span={8}>\r\n                      <Form.Item\r\n                        name=\"subject\"\r\n                        rules={[{ required: true, message: '请选择任教科目!' }]}\r\n                      >\r\n                        <Select\r\n                          placeholder=\"请选择任教科目\"\r\n                          allowClear\r\n                          disabled={!selectedSchoolId}\r\n                        >\r\n                          {subjects.map(subject => (\r\n                            <Option key={subject.id} value={subject.name}>{subject.name}</Option>\r\n                          ))}\r\n                        </Select>\r\n                      </Form.Item>\r\n                    </Col>\r\n                  </Row>\r\n\r\n                  <div style={{ marginTop: -8, marginBottom: 8 }}>\r\n                    {!customSchool ? (\r\n                      <Button\r\n                        type=\"link\"\r\n                        size=\"small\"\r\n                        onClick={() => setCustomSchool(true)}\r\n                        style={{ padding: 0 }}\r\n                      >\r\n                        没有找到您的学校？点击手动输入\r\n                      </Button>\r\n                    ) : (\r\n                      <Button\r\n                        type=\"link\"\r\n                        size=\"small\"\r\n                        onClick={() => setCustomSchool(false)}\r\n                        style={{ padding: 0 }}\r\n                      >\r\n                        返回选择已有学校\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* 隐藏的学校ID字段 */}\r\n                  <Form.Item name=\"school_id\" hidden>\r\n                    <Input />\r\n                  </Form.Item>\r\n                  \r\n                  {/* 班级选择 - 对于需要选择班级的教师角色 */}\r\n                  {advancedSettings?.roles?.[selectedRole]?.fields?.class?.required && (\r\n                    <Form.Item\r\n                      name=\"class_id\"\r\n                      label={selectedRole === 'class_teacher' ? '管理班级' : '任教班级'}\r\n                      rules={[{ required: true, message: '请选择班级!' }]}\r\n                      extra={\r\n                        selectedRole === 'class_teacher' && advancedSettings?.roles?.[selectedRole]?.fields?.class?.max > 1\r\n                          ? '⚠️ 班主任提示：如果选择多个班级，第一个选择的班级将作为您的主要管理班级'\r\n                          : selectedRole === 'class_teacher'\r\n                          ? '💡 您将成为该班级的班主任，负责班级的日常管理工作'\r\n                          : advancedSettings?.roles?.[selectedRole]?.fields?.class?.max > 1\r\n                          ? '可选择多个任教班级'\r\n                          : undefined\r\n                      }\r\n                    >\r\n                      <Select\r\n                        placeholder={selectedRole === 'class_teacher' ? '请选择管理班级' : '请选择任教班级'}\r\n                        disabled={!selectedSchoolId || customSchool}\r\n                        allowClear\r\n                        mode={advancedSettings?.roles?.[selectedRole]?.fields?.class?.max > 1 ? \"multiple\" : undefined}\r\n                        maxTagCount={advancedSettings?.roles?.[selectedRole]?.fields?.class?.max || 1}\r\n                      >\r\n                        {classes.map(cls => (\r\n                          <Option key={cls.id} value={cls.id}>{cls.name}</Option>\r\n                        ))}\r\n                      </Select>\r\n                    </Form.Item>\r\n                  )}\r\n                </>\r\n              )}\r\n              \r\n              {/* 家长专属字段 - 学生绑定 */}\r\n              {selectedRole === 'parent' && (\r\n                <Form.Item\r\n                  label=\"学生绑定\"\r\n                  name=\"student_binding\"\r\n                  rules={[{ required: true, message: '请完成学生绑定' }]}\r\n                >\r\n                  <div>\r\n                    {parentBindingComplete ? (\r\n                      <Alert\r\n                        message=\"绑定成功\"\r\n                        description={`已成功绑定学生: ${boundStudent?.full_name || '未知'}, 学校: ${boundStudent?.school_name || '未知'}, 班级: ${boundStudent?.class_name || '未知'}`}\r\n                        type=\"success\"\r\n                        showIcon\r\n                      />\r\n                    ) : (\r\n                      <ParentRegistration\r\n                        ref={parentRegistrationRef}\r\n                        onBindingComplete={handleBindingComplete}\r\n                        form={form}\r\n                        initialValues={form.getFieldsValue()}\r\n                        compact={true}\r\n                      />\r\n                    )}\r\n                  </div>\r\n                </Form.Item>\r\n              )}\r\n              \r\n              <Form.Item>\r\n                <Button \r\n                  type=\"primary\" \r\n                  htmlType=\"submit\" \r\n                  className=\"login-form-button\"\r\n                  loading={loading}\r\n                >\r\n                  注册\r\n                </Button>\r\n              </Form.Item>\r\n            </Form>\r\n          ) : (\r\n            !settingsLoading && (\r\n              <Alert\r\n                message=\"注册功能已关闭\"\r\n                description=\"系统当前已关闭所有注册功能，请联系管理员创建账号\"\r\n                type=\"error\"\r\n                showIcon\r\n              />\r\n            )\r\n          )}\r\n          \r\n          <div style={{ textAlign: 'center', marginTop: 16 }}>\r\n            已有账号? <a href=\"/login\">立即登录</a>\r\n          </div>\r\n        </Spin>\r\n      </Card>\r\n\r\n      {/* 学校申请表单模态框 */}\r\n      <Modal\r\n        title=\"申请添加新学校\"\r\n        visible={showSchoolApplicationModal}\r\n        onCancel={() => setShowSchoolApplicationModal(false)}\r\n        footer={null}\r\n        width={800}\r\n        destroyOnClose\r\n      >\r\n        <SchoolApplicationForm \r\n          onSubmitSuccess={() => {\r\n            setShowSchoolApplicationModal(false);\r\n            message.success('学校申请已提交，请等待审核通过后再进行注册');\r\n          }} \r\n        />\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StandaloneRegister; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3H,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AACrI,SAASC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,+BAA+B,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,cAAc;AACnM,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,kBAAkB,MAAM,sBAAsB,CAAC,CAAC;AACvD,OAAOC,qBAAqB,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAM;EAAEC;AAAM,CAAC,GAAGlC,UAAU;AAC5B,MAAM;EAAEmC;AAAO,CAAC,GAAG/B,MAAM;;AAEzB;AACA,MAAMgC,gBAAgB,GAAG;EACvB,SAAS,EAAE,IAAI;EACf,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,eAAe,EAAE,KAAK;EACtB,gBAAgB,EAAE,MAAM;EACxB,mBAAmB,EAAE,MAAM;EAC3B,gBAAgB,EAAE,KAAK;EACvB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EAC/BC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;EACtC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0E,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4E,MAAM,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsF,OAAO,EAAEC,UAAU,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwF,QAAQ,EAAEC,WAAW,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0F,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,SAAS,CAAC;EAC3D,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgG,IAAI,CAAC,GAAG7F,IAAI,CAAC8F,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGhE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpG,QAAQ,CAAC;IAC/DqG,0BAA0B,EAAE,IAAI;IAChCC,0BAA0B,EAAE;EAC9B,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyG,eAAe,EAAEC,kBAAkB,CAAC,GAAG1G,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC2G,cAAc,EAAEC,iBAAiB,CAAC,GAAG5G,QAAQ,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;;EAE5E;EACA,MAAM6G,qBAAqB,GAAG3G,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAM,CAAC4G,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACgH,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;;EAEnF;EACAC,SAAS,CAAC,MAAM;IACdiE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD+C,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;IAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlH,SAAS,CAAC,MAAM;IACd,MAAMmH,yBAAyB,GAAG,MAAAA,CAAA,KAAY;MAC5C,IAAI;QACFlD,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1BuC,kBAAkB,CAAC,IAAI,CAAC;QACxBxC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,MAAMkD,QAAQ,GAAG,MAAMxF,uBAAuB,CAAC,CAAC;QAChDqC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkD,QAAQ,CAAC;QACjCnD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkD,QAAQ,CAAChB,0BAA0B,CAAC;QAC3DnC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkD,QAAQ,CAACf,0BAA0B,CAAC;QAC3DpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkD,QAAQ,CAACC,2BAA2B,CAAC;QAC9DlB,uBAAuB,CAACiB,QAAQ,CAAC;;QAEjC;QACA,IAAIA,QAAQ,CAACC,2BAA2B,KAAK,KAAK,EAAE;UAClDpD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;UAC/B8C,6BAA6B,CAAC,IAAI,CAAC;UACnCP,kBAAkB,CAAC,KAAK,CAAC;UACzB;QACF;;QAEA;QACAxC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,MAAMoD,WAAW,GAAG,MAAMzF,+BAA+B,CAAC,CAAC;QAC3DoC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEoD,WAAW,CAAC;QACtCf,mBAAmB,CAACe,WAAW,CAAC;;QAEhC;QACArD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,MAAMqD,kBAAkB,GAAG,MAAMzF,iBAAiB,CAAC,CAAC;QACpDmC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqD,kBAAkB,CAAC;QAE7C,IAAIA,kBAAkB,IAAIC,KAAK,CAACC,OAAO,CAACF,kBAAkB,CAACG,KAAK,CAAC,IAAIH,kBAAkB,CAACG,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;UACxG;UACA,MAAMC,YAAY,GAAGL,kBAAkB,CAACG,KAAK,CAACG,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;UACpE9D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE0D,YAAY,CAAC;;UAEpC;UACAA,YAAY,CAACI,OAAO,CAACC,QAAQ,IAAI;YAC/B,MAAMC,UAAU,GAAGX,kBAAkB,CAACG,KAAK,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACL,IAAI,KAAKE,QAAQ,CAAC;YAC1EhE,OAAO,CAACC,GAAG,CAAC,MAAM+D,QAAQ,MAAM,EAAEC,UAAU,CAAC;UAC/C,CAAC,CAAC;UAEFvB,iBAAiB,CAACiB,YAAY,CAAC;;UAE/B;UACA,IAAI,CAACA,YAAY,CAACS,QAAQ,CAAC1C,YAAY,CAAC,EAAE;YACxC,MAAM2C,SAAS,GAAGV,YAAY,CAAC,CAAC,CAAC;YACjC3D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoE,SAAS,CAAC;YACzC1C,eAAe,CAAC0C,SAAS,CAAC;YAC1B5C,YAAY,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC2C,QAAQ,CAACC,SAAS,CAAC,CAAC;YACvDvC,IAAI,CAACwC,cAAc,CAAC;cAClBT,IAAI,EAAEQ,SAAS;cACfE,UAAU,EAAEF,SAAS,KAAK;YAC5B,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA,IAAIhB,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;YACpCzD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;YAC/B,MAAM0D,YAAY,GAAGa,MAAM,CAACC,IAAI,CAACpB,WAAW,CAACI,KAAK,CAAC,CAACiB,MAAM,CACxDb,IAAI,IAAIR,WAAW,CAACI,KAAK,CAACI,IAAI,CAAC,CAACc,OAClC,CAAC;YACD3E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE0D,YAAY,CAAC;YAC5C3D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoD,WAAW,CAACI,KAAK,CAAC;;YAEzC;YACA,IAAIE,YAAY,CAACD,MAAM,GAAG,CAAC,EAAE;cAC3B1D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE0D,YAAY,CAAC;cACpCjB,iBAAiB,CAACiB,YAAY,CAAC;;cAE/B;cACA,IAAI,CAACA,YAAY,CAACS,QAAQ,CAAC1C,YAAY,CAAC,EAAE;gBACxC,MAAM2C,SAAS,GAAGV,YAAY,CAAC,CAAC,CAAC;gBACjC3D,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoE,SAAS,CAAC;gBACzC1C,eAAe,CAAC0C,SAAS,CAAC;gBAC1B5C,YAAY,CAAC4C,SAAS,KAAK,SAAS,CAAC;gBACrCvC,IAAI,CAACwC,cAAc,CAAC;kBAAET,IAAI,EAAEQ;gBAAU,CAAC,CAAC;cAC1C;YACF,CAAC,MAAM;cACLrE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;cACtB;cACAvD,KAAK,CAACkI,OAAO,CAAC;gBACZC,KAAK,EAAE,MAAM;gBACbC,OAAO,EAAE,sBAAsB;gBAC/BC,IAAI,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,QAAQ;cAC/B,CAAC,CAAC;YACJ;UACF,CAAC,MAAM;YACLhC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;YAChC;YACA,MAAM+E,UAAU,GAAG,EAAE;YACrB,IAAI7B,QAAQ,CAAChB,0BAA0B,EAAE6C,UAAU,CAACC,IAAI,CAAC,SAAS,CAAC;YACnE,IAAI9B,QAAQ,CAACf,0BAA0B,EAAE4C,UAAU,CAACC,IAAI,CAAC,SAAS,CAAC;YAEnEjF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE+E,UAAU,CAAC;YACvC,IAAIA,UAAU,CAACtB,MAAM,GAAG,CAAC,EAAE;cACzBhB,iBAAiB,CAACsC,UAAU,CAAC;;cAE7B;cACA,IAAI,CAACA,UAAU,CAACZ,QAAQ,CAAC1C,YAAY,CAAC,EAAE;gBACtC,MAAM2C,SAAS,GAAGW,UAAU,CAAC,CAAC,CAAC;gBAC/BrD,eAAe,CAAC0C,SAAS,CAAC;gBAC1B5C,YAAY,CAAC4C,SAAS,KAAK,SAAS,CAAC;gBACrCvC,IAAI,CAACwC,cAAc,CAAC;kBAAET,IAAI,EAAEQ;gBAAU,CAAC,CAAC;cAC1C;YACF;UACF;QACF;;QAEA;QACA,IAAI,CAAClB,QAAQ,CAAChB,0BAA0B,IAAI,CAACgB,QAAQ,CAACf,0BAA0B,EAAE;UAChFpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;UACjCvD,KAAK,CAACkI,OAAO,CAAC;YACZC,KAAK,EAAE,MAAM;YACbC,OAAO,EAAE,0BAA0B;YACnCC,IAAI,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,QAAQ;UAC/B,CAAC,CAAC;UACF;QACF;QAEAhC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuB,SAAS,CAAC;QACjDxB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,YAAY,CAAC;QACrC1B,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE6B,IAAI,CAACoD,cAAc,CAAC,CAAC,CAAC;QAC1ClF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwC,cAAc,CAAC;MAE1C,CAAC,CAAC,OAAO0C,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACAzC,iBAAiB,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;MAC3C,CAAC,SAAS;QACRF,kBAAkB,CAAC,KAAK,CAAC;QACzBxC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgC,oBAAoB,CAAC;QAC9CjC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuB,SAAS,CAAC;QACxCxB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyB,YAAY,CAAC;MACvC;IACF,CAAC;IAEDwB,yBAAyB,CAAC,CAAC;EAC7B,CAAC,EAAE,CAAClB,QAAQ,EAAEF,IAAI,CAAC,CAAC;;EAEpB;EACA/F,SAAS,CAAC,MAAM;IACd,IAAI,CAACwG,eAAe,IAAIE,cAAc,CAACiB,MAAM,GAAG,CAAC,EAAE;MACjD,MAAM0B,aAAa,GAAGC,gBAAgB,CAAC,CAAC;MACxCrF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmF,aAAa,CAAC;MACtCtD,IAAI,CAACwC,cAAc,CAACc,aAAa,CAAC;;MAElC;MACAzD,eAAe,CAACyD,aAAa,CAACvB,IAAI,CAAC;MACnCpC,YAAY,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC2C,QAAQ,CAACgB,aAAa,CAACvB,IAAI,CAAC,CAAC;IAClE;EACF,CAAC,EAAE,CAACtB,eAAe,EAAEE,cAAc,EAAEX,IAAI,CAAC,CAAC;EAE3C/F,SAAS,CAAC,MAAM;IACd;IACA,MAAMuJ,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjCtF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAE1B,IAAI;QACF,MAAMsF,WAAW,GAAG,MAAM/H,UAAU,CAAC,CAAC;QACtCwC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsF,WAAW,CAAC;QACpC,IAAIA,WAAW,CAAC/E,SAAS,IAAI+E,WAAW,CAAC/E,SAAS,CAACkD,MAAM,GAAG,CAAC,EAAE;UAC7DjD,YAAY,CAAC8E,WAAW,CAAC/E,SAAS,CAAC;QACrC;MACF,CAAC,CAAC,OAAO2E,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;;IAED;IACA,MAAMK,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BH,cAAc,CAAC,CAAC;IAClB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMI,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzJ,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+E,gBAAgB,EAAE;MACrBH,SAAS,CAAC,EAAE,CAAC;MACbM,eAAe,CAAC,EAAE,CAAC;MACnB;IACF;IAEA,MAAM0E,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B3F,OAAO,CAACC,GAAG,CAAC,OAAOa,gBAAgB,UAAU,CAAC;MAE9C,IAAI;QACF,MAAMyE,WAAW,GAAG,MAAM/H,UAAU,CAAC;UAAEoI,QAAQ,EAAE9E;QAAiB,CAAC,CAAC;QACpEd,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsF,WAAW,CAAC;QACpC,IAAIA,WAAW,CAAC7E,MAAM,IAAI6E,WAAW,CAAC7E,MAAM,CAACgD,MAAM,GAAG,CAAC,EAAE;UACvD/C,SAAS,CAAC4E,WAAW,CAAC7E,MAAM,CAAC;QAC/B;MACF,CAAC,CAAC,OAAOyE,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IAEDQ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC7E,gBAAgB,CAAC,CAAC;;EAEtB;EACA/E,SAAS,CAAC,MAAM;IACd,IAAI,CAACiF,YAAY,EAAE;MACjBH,YAAY,CAAC,EAAE,CAAC;MAChBM,mBAAmB,CAAC,EAAE,CAAC;MACvB;IACF;IAEA,MAAM0E,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC7F,OAAO,CAACC,GAAG,CAAC,OAAOa,gBAAgB,GAAGE,YAAY,UAAU,CAAC;MAE7D,IAAI;QACF,MAAMuE,WAAW,GAAG,MAAM/H,UAAU,CAAC;UACnCoI,QAAQ,EAAE9E,gBAAgB;UAC1BgF,IAAI,EAAE9E;QACR,CAAC,CAAC;QACFhB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsF,WAAW,CAAC;QACpC,IAAIA,WAAW,CAAC3E,SAAS,IAAI2E,WAAW,CAAC3E,SAAS,CAAC8C,MAAM,GAAG,CAAC,EAAE;UAC7D7C,YAAY,CAAC0E,WAAW,CAAC3E,SAAS,CAAC;QACrC;MACF,CAAC,CAAC,OAAOuE,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IAEDU,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC/E,gBAAgB,EAAEE,YAAY,CAAC,CAAC;;EAEpC;EACAjF,SAAS,CAAC,MAAM;IACd,IAAI,CAACmF,gBAAgB,EAAE;MACrBb,UAAU,CAAC,EAAE,CAAC;MACd;IACF;IAEA,MAAM0F,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B/F,OAAO,CAACC,GAAG,CAAC,OAAOa,gBAAgB,GAAGE,YAAY,GAAGE,gBAAgB,UAAU,CAAC;MAEhF,IAAI;QACF,MAAM8E,WAAW,GAAG,MAAMzI,UAAU,CAAC;UACnCqI,QAAQ,EAAE9E,gBAAgB;UAC1BgF,IAAI,EAAE9E,YAAY;UAClBiF,QAAQ,EAAE/E;QACZ,CAAC,CAAC;QACFlB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+F,WAAW,CAACtC,MAAM,EAAE,KAAK,CAAC;QAClDrD,UAAU,CAAC2F,WAAW,CAAC;MACzB,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC7I,OAAO,CAAC6I,KAAK,CAAC,gBAAgB,CAAC;MACjC;IACF,CAAC;IAEDY,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACjF,gBAAgB,EAAEE,YAAY,EAAEE,gBAAgB,CAAC,CAAC;;EAEtD;EACAnF,SAAS,CAAC,MAAM;IACd,MAAMmK,QAAQ,GAAGpE,IAAI,CAACqE,aAAa,CAAC,WAAW,CAAC;IAChD,IAAI,CAACD,QAAQ,EAAE;MACb7E,UAAU,CAAC,EAAE,CAAC;MACd;IACF;IAEA,MAAM+E,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BpG,OAAO,CAACC,GAAG,CAAC,YAAYiG,QAAQ,UAAU,CAAC;MAE3C,IAAI;QACF;QACA,MAAMG,WAAW,GAAG,MAAM5I,gBAAgB,CAAC;UACzC6I,SAAS,EAAEJ;QACb,CAAC,CAAC;QACFlG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoG,WAAW,CAAC;QAEpC,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,WAAW,CAAC,IAAIA,WAAW,CAAC3C,MAAM,GAAG,CAAC,EAAE;UACxDrC,UAAU,CAACgF,WAAW,CAAC;QACzB,CAAC,MAAM;UACLrG,OAAO,CAACuG,IAAI,CAAC,eAAe,EAAEF,WAAW,CAAC;UAC1ChF,UAAU,CAAC,EAAE,CAAC;UACd/E,OAAO,CAACsI,OAAO,CAAC,WAAW,CAAC;QAC9B;MACF,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC7I,OAAO,CAAC6I,KAAK,CAAC,gBAAgB,CAAC;QAC/B9D,UAAU,CAAC,EAAE,CAAC;MAChB;IACF,CAAC;IAED+E,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACtE,IAAI,EAAEN,SAAS,CAAC,CAAC;;EAErB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAM,CAACgF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3K,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC4K,YAAY,EAAEC,eAAe,CAAC,GAAG7K,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM8K,qBAAqB,GAAGA,CAACC,OAAO,EAAEC,MAAM,KAAK;IACjDL,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,eAAe,CAACE,OAAO,CAAC;;IAExB;IACA,IAAIA,OAAO,EAAE;MACX/E,IAAI,CAACwC,cAAc,CAAC;QAClBgC,SAAS,EAAEO,OAAO,CAACP,SAAS;QAC5BS,WAAW,EAAEF,OAAO,CAACE,WAAW;QAChCC,QAAQ,EAAEH,OAAO,CAACG;MACpB,CAAC,CAAC;IACJ;IAEA1K,OAAO,CAAC2K,OAAO,CAAC,gBAAgB,CAAC;EACnC,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF;MACA,IAAIjH,OAAO,EAAE;QACX;MACF;MAEAC,UAAU,CAAC,IAAI,CAAC;MAChBH,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;MAE5B;MACA,MAAM;QAAEmH,OAAO;QAAExB,QAAQ;QAAEE,IAAI;QAAEG,QAAQ;QAAE,GAAGoB;MAAS,CAAC,GAAGF,MAAM;;MAEjE;MACA,IAAIE,QAAQ,CAACxD,IAAI,KAAK,QAAQ,EAAE;QAAA,IAAAyD,qBAAA,EAAAC,sBAAA;QAC9B,IAAI,GAAAD,qBAAA,GAAC3E,qBAAqB,CAAC6E,OAAO,cAAAF,qBAAA,eAA7BA,qBAAA,CAA+BG,OAAO,CAAC,CAAC,GAAE;UAC7CnL,OAAO,CAAC6I,KAAK,CAAC,aAAa,CAAC;UAC5BhF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMuH,WAAW,IAAAH,sBAAA,GAAG5E,qBAAqB,CAAC6E,OAAO,cAAAD,sBAAA,uBAA7BA,sBAAA,CAA+BI,sBAAsB,CAAC,CAAC;QAC3E,IAAID,WAAW,EAAE;UACfL,QAAQ,CAACO,UAAU,GAAGF,WAAW,CAACE,UAAU;UAC5CP,QAAQ,CAACQ,YAAY,GAAGH,WAAW,CAACG,YAAY;UAChDR,QAAQ,CAACS,UAAU,GAAGJ,WAAW,CAACI,UAAU;QAC9C;MACF;;MAEA;MACA,IAAI,CAACT,QAAQ,CAACN,WAAW,EAAE;QACzB,MAAM,IAAIgB,KAAK,CAAC,YAAY,CAAC;MAC/B;;MAEA;MACA,IAAI,CAACzH,YAAY,IAAI,CAAC+G,QAAQ,CAACf,SAAS,EAAE;QACxC,MAAM,IAAIyB,KAAK,CAAC,UAAU,CAAC;MAC7B;;MAEA;MACA,MAAMlE,IAAI,GAAGwD,QAAQ,CAACxD,IAAI,KAAKwD,QAAQ,CAAC9C,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;MAC3E,MAAMyD,YAAY,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC;MAC1C,MAAMC,aAAa,GAAGD,YAAY,CAAC5D,QAAQ,CAACP,IAAI,CAAC;;MAEjD;MACA,IAAIoE,aAAa,IAAI,CAACZ,QAAQ,CAACL,QAAQ,EAAE;QACvC,MAAM,IAAIe,KAAK,CAAC,OAAO,CAAC;MAC1B;;MAEA;MACA,IAAIV,QAAQ,CAACL,QAAQ,EAAE;QACrBhH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrB+G,QAAQ,EAAEK,QAAQ,CAACL,QAAQ;UAC3BxD,OAAO,EAAED,KAAK,CAACC,OAAO,CAAC6D,QAAQ,CAACL,QAAQ,CAAC;UACzCnD,IAAI,EAAEnC;QACR,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMwG,YAAY,GAAG,CAAC,SAAS,EAAE,eAAe,EAAE,gBAAgB,CAAC;MACnE,IAAIA,YAAY,CAAC9D,QAAQ,CAACP,IAAI,CAAC,IAAI,CAACwD,QAAQ,CAACc,OAAO,EAAE;QACpD,MAAMC,WAAW,GAAG3J,gBAAgB,CAACoF,IAAI,CAAC,IAAIA,IAAI;QAClD,MAAM,IAAIkE,KAAK,CAAC,MAAMK,WAAW,MAAM,CAAC;MAC1C;;MAEA;MACAf,QAAQ,CAACzB,QAAQ,GAAG9E,gBAAgB;MACpCuG,QAAQ,CAACvB,IAAI,GAAG9E,YAAY;MAC5BqG,QAAQ,CAACpB,QAAQ,GAAG/E,gBAAgB;;MAEpC;MACAmG,QAAQ,CAACgB,SAAS,GAAGxE,IAAI,CAAC,CAAC;;MAE3B;MACA,IAAI,CAACwD,QAAQ,CAACiB,cAAc,CAAC,YAAY,CAAC,EAAE;QAC1CjB,QAAQ,CAAC9C,UAAU,GAAGV,IAAI,KAAK,SAAS;MAC1C;MAEA7D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoH,QAAQ,CAAC;MAElC,IAAI;QACF;QACA,MAAMP,MAAM,GAAG,MAAMhJ,gBAAgB,CAACuJ,QAAQ,CAAC;QAC/CrH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE6G,MAAM,CAAC;QAEnC,IAAIA,MAAM,EAAE;UAAA,IAAAyB,qBAAA;UACV;UACA,MAAMtE,UAAU,GAAG,CAAA5B,gBAAgB,aAAhBA,gBAAgB,wBAAAkG,qBAAA,GAAhBlG,gBAAgB,CAAEoB,KAAK,cAAA8E,qBAAA,uBAAvBA,qBAAA,CAA0B1E,IAAI,CAAC,KAAI,CAAC,CAAC;UACxD,MAAM2E,gBAAgB,GAAGvE,UAAU,CAACwE,iBAAiB,IAAI,KAAK;UAE9D,IAAID,gBAAgB,EAAE;YACpB;YACA9L,KAAK,CAACuK,OAAO,CAAC;cACZpC,KAAK,EAAE,SAAS;cAChBC,OAAO,EAAE,wCAAwC;cACjDC,IAAI,EAAEA,CAAA,KAAM;gBACV;gBACA/B,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;gBAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;gBAC/B;gBACAyF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;cACjC;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAtM,OAAO,CAAC2K,OAAO,CAAC,qBAAqB,CAAC;YACtC;YACA4B,cAAc,CAACC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;YAC/C;YACA9F,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;YAChCD,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;YAC/B;YACAwC,UAAU,CAAC,MAAM;cACf;cACAiD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;YACjC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;UACZ;QACF,CAAC,MAAM;UACL,MAAM,IAAIb,KAAK,CAAC,eAAe,CAAC;QAClC;MACF,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QACjB/I,OAAO,CAACmF,KAAK,CAAC,YAAY,EAAE4D,QAAQ,CAAC;QACrC,IAAIA,QAAQ,CAACC,MAAM,EAAE;UACnB1M,OAAO,CAAC6I,KAAK,CAAC4D,QAAQ,CAACC,MAAM,CAAC;QAChC,CAAC,MAAM;UACL1M,OAAO,CAAC6I,KAAK,CAAC4D,QAAQ,CAACzM,OAAO,IAAI,YAAY,CAAC;QACjD;MACF;IACF,CAAC,CAAC,OAAO6I,KAAK,EAAE;MACdnF,OAAO,CAACmF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC7I,OAAO,CAAC6I,KAAK,CAACA,KAAK,CAAC7I,OAAO,IAAI,YAAY,CAAC;IAC9C,CAAC,SAAS;MACR6D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAI5C,cAAc,CAACiB,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAMW,SAAS,GAAG5B,cAAc,CAAC,CAAC,CAAC;MACnC,OAAO;QACLoB,IAAI,EAAEQ,SAAS;QACfE,UAAU,EAAEF,SAAS,KAAK,SAAS,CAAC;MACtC,CAAC;IACH;;IAEA;IACA,OAAO;MAAER,IAAI,EAAE,SAAS;MAAEU,UAAU,EAAE;IAAM,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM0E,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOhH,oBAAoB,CAACE,0BAA0B,IAAIF,oBAAoB,CAACG,0BAA0B;EAC3G,CAAC;;EAED;EACA,MAAM8G,gBAAgB,GAAIC,KAAK,IAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,mBAAA;IAClCvJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEkJ,KAAK,CAAC;IAC3BnJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEoC,gBAAgB,CAAC;;IAEtC;IACAV,eAAe,CAACwH,KAAK,CAAC;;IAEtB;IACA,MAAMnB,YAAY,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC;IAC1C,MAAMC,aAAa,GAAGD,YAAY,CAAC5D,QAAQ,CAAC+E,KAAK,CAAC;IAClD1H,YAAY,CAACwG,aAAa,CAAC;;IAE3B;IACA,MAAMhE,UAAU,GAAG,CAAA5B,gBAAgB,aAAhBA,gBAAgB,wBAAA+G,sBAAA,GAAhB/G,gBAAgB,CAAEoB,KAAK,cAAA2F,sBAAA,wBAAAC,sBAAA,GAAvBD,sBAAA,CAA0BD,KAAK,CAAC,cAAAE,sBAAA,uBAAhCA,sBAAA,CAAkCG,MAAM,KAAI,CAAC,CAAC;IACjExJ,OAAO,CAACC,GAAG,CAAC,MAAMkJ,KAAK,SAAS,EAAElF,UAAU,CAAC;;IAE7C;IACA,MAAMwF,UAAU,GAAG;MAAE5F,IAAI,EAAEsF;IAAM,CAAC;;IAElC;IACAM,UAAU,CAAClF,UAAU,GAAI4E,KAAK,KAAK,SAAU;;IAE7C;IACA,IAAI,GAAAG,iBAAA,GAACrF,UAAU,CAACyF,KAAK,cAAAJ,iBAAA,eAAhBA,iBAAA,CAAkBK,QAAQ,GAAE;MAC/B;MACAF,UAAU,CAACzC,QAAQ,GAAG4C,SAAS;IACjC;IAEA,KAAAL,mBAAA,GAAItF,UAAU,CAACkE,OAAO,cAAAoB,mBAAA,eAAlBA,mBAAA,CAAoBM,MAAM,EAAE;MAC9B;MACAJ,UAAU,CAACtB,OAAO,GAAGyB,SAAS;IAChC;;IAEA;IACA5J,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEwJ,UAAU,CAAC;IACjC3H,IAAI,CAACwC,cAAc,CAACmF,UAAU,CAAC;;IAE/B;IACA,IAAIxB,aAAa,EAAE;MACjB;MACA,MAAM/B,QAAQ,GAAGpE,IAAI,CAACqE,aAAa,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACZlG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEiG,QAAQ,CAAC;QACxCzI,gBAAgB,CAAC;UAAE6I,SAAS,EAAEJ;QAAS,CAAC,CAAC,CACtC4D,IAAI,CAACzD,WAAW,IAAI;UACnBhF,UAAU,CAACgF,WAAW,IAAI,EAAE,CAAC;QAC/B,CAAC,CAAC,CACD0D,KAAK,CAAC5E,KAAK,IAAI;UACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACjC9D,UAAU,CAAC,EAAE,CAAC;QAChB,CAAC,CAAC;MACN;IACF,CAAC,MAAM;MAAA,IAAA2I,oBAAA;MACL;MACA,IAAI,GAAAA,oBAAA,GAAC/F,UAAU,CAACkE,OAAO,cAAA6B,oBAAA,eAAlBA,oBAAA,CAAoBH,MAAM,GAAE;QAC/B,MAAMI,eAAe,GAAGnI,IAAI,CAACqE,aAAa,CAAC,WAAW,CAAC;QACvD,IAAI8D,eAAe,EAAE;UACnBjK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgK,eAAe,CAAC;UAC/CvM,iBAAiB,CAACuM,eAAe,CAAC,CAC/BH,IAAI,CAACI,YAAY,IAAI;YACpB3I,WAAW,CAAC2I,YAAY,IAAI,EAAE,CAAC;UACjC,CAAC,CAAC,CACDH,KAAK,CAAC5E,KAAK,IAAI;YACdnF,OAAO,CAACmF,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;YACjC5D,WAAW,CAAC,EAAE,CAAC;UACjB,CAAC,CAAC;QACN,CAAC,MAAM;UACLvB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC3BsB,WAAW,CAAC,EAAE,CAAC;QACjB;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAM4I,oBAAoB,GAAIhB,KAAK,IAAK;IACtCpI,mBAAmB,CAACoI,KAAK,CAAC;IAC1BrH,IAAI,CAACwC,cAAc,CAAC;MAAEwB,IAAI,EAAE8D,SAAS;MAAE3D,QAAQ,EAAE2D,SAAS;MAAE7C,WAAW,EAAE6C,SAAS;MAAEtD,SAAS,EAAEsD,SAAS;MAAE5C,QAAQ,EAAE4C;IAAU,CAAC,CAAC;IAChI;IACA/H,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMuI,gBAAgB,GAAIjB,KAAK,IAAK;IAClClI,eAAe,CAACkI,KAAK,CAAC;IACtBrH,IAAI,CAACwC,cAAc,CAAC;MAAE2B,QAAQ,EAAE2D,SAAS;MAAE7C,WAAW,EAAE6C,SAAS;MAAEtD,SAAS,EAAEsD,SAAS;MAAE5C,QAAQ,EAAE4C;IAAU,CAAC,CAAC;IAC/G;IACA/H,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMwI,oBAAoB,GAAIlB,KAAK,IAAK;IACtChI,mBAAmB,CAACgI,KAAK,CAAC;IAC1BrH,IAAI,CAACwC,cAAc,CAAC;MAAEyC,WAAW,EAAE6C,SAAS;MAAEtD,SAAS,EAAEsD,SAAS;MAAE5C,QAAQ,EAAE4C;IAAU,CAAC,CAAC;IAC1F;IACA/H,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyI,kBAAkB,GAAGA,CAACnB,KAAK,EAAEoB,MAAM,KAAK;IAC5CvK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEkJ,KAAK,EAAEoB,MAAM,CAAC;;IAEnC;IACA,IAAIrE,QAAQ,GAAG,IAAI;;IAEnB;IACA,IAAIqE,MAAM,IAAIA,MAAM,CAACC,GAAG,EAAE;MACxBtE,QAAQ,GAAGqE,MAAM,CAACC,GAAG;MACrBxK,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiG,QAAQ,CAAC;IAC7C;IACA;IAAA,KACK,IAAI3C,KAAK,CAACC,OAAO,CAAC+G,MAAM,CAAC,IAAIA,MAAM,CAAC7G,MAAM,GAAG,CAAC,IAAI6G,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG,EAAE;MACpEtE,QAAQ,GAAGqE,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG;MACxBxK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiG,QAAQ,CAAC;IAC3C;IACA;IAAA,KACK;MACH,MAAMuE,cAAc,GAAGrK,OAAO,CAAC8D,IAAI,CAACwG,CAAC,IAAIA,CAAC,CAAC3D,WAAW,KAAKoC,KAAK,CAAC;MACjE,IAAIsB,cAAc,EAAE;QAClBvE,QAAQ,GAAGuE,cAAc,CAACE,EAAE;QAC5B3K,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkJ,KAAK,EAAEjD,QAAQ,CAAC;MAC/C;IACF;IAEA,IAAI,CAACA,QAAQ,EAAE;MACblG,OAAO,CAACmF,KAAK,CAAC,UAAU,CAAC;MACzB7I,OAAO,CAAC6I,KAAK,CAAC,cAAc,CAAC;MAC7B;IACF;IAEAnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiG,QAAQ,CAAC;IAChCpE,IAAI,CAACwC,cAAc,CAAC;MAAEgC,SAAS,EAAEJ,QAAQ;MAAEc,QAAQ,EAAE4C;IAAU,CAAC,CAAC;;IAEjE;IACA/H,mBAAmB,CAACqE,QAAQ,CAAC;;IAE7B;IACA,IAAIA,QAAQ,EAAE;MACZlG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEiG,QAAQ,EAAE,KAAK,EAAExE,YAAY,CAAC;MAC/DjE,gBAAgB,CAAC;QAAE6I,SAAS,EAAEJ;MAAS,CAAC,CAAC,CACtC4D,IAAI,CAACzD,WAAW,IAAI;QACnBrG,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoG,WAAW,CAAC;QACvC,IAAI9C,KAAK,CAACC,OAAO,CAAC6C,WAAW,CAAC,IAAIA,WAAW,CAAC3C,MAAM,GAAG,CAAC,EAAE;UACxDrC,UAAU,CAACgF,WAAW,CAAC;QACzB,CAAC,MAAM;UACLrG,OAAO,CAACuG,IAAI,CAAC,cAAc,EAAEF,WAAW,CAAC;UACzChF,UAAU,CAAC,EAAE,CAAC;UACd;UACA,IAAIG,SAAS,IAAIE,YAAY,KAAK,QAAQ,EAAE;YAC1CpF,OAAO,CAACsI,OAAO,CAAC,WAAW,CAAC;UAC9B;QACF;MACF,CAAC,CAAC,CACDmF,KAAK,CAAC5E,KAAK,IAAI;QACdnF,OAAO,CAACmF,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC9D,UAAU,CAAC,EAAE,CAAC;QACd;QACA,IAAIG,SAAS,IAAIE,YAAY,KAAK,QAAQ,EAAE;UAC1CpF,OAAO,CAAC6I,KAAK,CAAC,gBAAgB,CAAC;QACjC;MACF,CAAC,CAAC;IACN;;IAEA;IACA,IAAI,CAAC3D,SAAS,IAAIE,YAAY,KAAK,QAAQ,EAAE;MAC3C1B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEiG,QAAQ,CAAC;MAC7CxI,iBAAiB,CAACwI,QAAQ,CAAC,CACxB4D,IAAI,CAACI,YAAY,IAAI;QACpBlK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiK,YAAY,CAAC;QACxC3I,WAAW,CAAC2I,YAAY,IAAI,EAAE,CAAC;MACjC,CAAC,CAAC,CACDH,KAAK,CAAC5E,KAAK,IAAI;QACdnF,OAAO,CAACmF,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC5D,WAAW,CAAC,EAAE,CAAC;MACjB,CAAC,CAAC;IACN;EACF,CAAC;;EAED;EACA,IAAIuB,0BAA0B,EAAE;IAC9B,oBACE1E,OAAA;MAAKwM,KAAK,EAAE;QACVC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,mDAAmD;QAC/DC,UAAU,EAAE;MACd,CAAE;MAAAC,QAAA,eACA/M,OAAA,CAAChC,IAAI;QACHwO,KAAK,EAAE;UACLQ,KAAK,EAAE,GAAG;UACVC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,QAAQ;UACnBC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,+BAA+B;UAC1CC,MAAM,EAAE;QACV,CAAE;QACFC,QAAQ,EAAE,KAAM;QAAAP,QAAA,gBAEhB/M,OAAA;UAAKwM,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAG,CAAE;UAAAR,QAAA,gBAC/B/M,OAAA,CAACG,KAAK;YAACqN,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEiB,KAAK,EAAE,SAAS;cAAEF,YAAY,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAE/D;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7N,OAAA,CAACG,KAAK;YAACqN,KAAK,EAAE,CAAE;YAAChB,KAAK,EAAE;cAAEiB,KAAK,EAAE,SAAS;cAAEF,YAAY,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAE/D;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7N,OAAA;UAAKwM,KAAK,EAAE;YACVK,UAAU,EAAE,mDAAmD;YAC/DI,OAAO,EAAE,MAAM;YACfE,YAAY,EAAE,KAAK;YACnBI,YAAY,EAAE,EAAE;YAChBE,KAAK,EAAE;UACT,CAAE;UAAAV,QAAA,eACA/M,OAAA;YAAGwM,KAAK,EAAE;cACRsB,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBC,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE;YACd,CAAE;YAAAlB,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7N,OAAA;UAAKwM,KAAK,EAAE;YACVK,UAAU,EAAE,SAAS;YACrBI,OAAO,EAAE,MAAM;YACfE,YAAY,EAAE,KAAK;YACnBI,YAAY,EAAE;UAChB,CAAE;UAAAR,QAAA,gBACA/M,OAAA;YAAGwM,KAAK,EAAE;cACRsB,QAAQ,EAAE,MAAM;cAChBL,KAAK,EAAE,SAAS;cAChBM,UAAU,EAAE,KAAK;cACjBC,MAAM,EAAE;YACV,CAAE;YAAAjB,QAAA,EAAC;UAEH;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ7N,OAAA;YAAKwM,KAAK,EAAE;cAAE0B,SAAS,EAAE;YAAG,CAAE;YAAAnB,QAAA,gBAC5B/M,OAAA;cAAGwM,KAAK,EAAE;gBACRsB,QAAQ,EAAE,MAAM;gBAChBL,KAAK,EAAE,SAAS;gBAChBO,MAAM,EAAE,OAAO;gBACfC,UAAU,EAAE;cACd,CAAE;cAAAlB,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJ7N,OAAA;cAAGwM,KAAK,EAAE;gBACRsB,QAAQ,EAAE,MAAM;gBAChBL,KAAK,EAAE,SAAS;gBAChBO,MAAM,EAAE,OAAO;gBACfC,UAAU,EAAE;cACd,CAAE;cAAAlB,QAAA,EAAC;YAEH;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7N,OAAA;UAAKwM,KAAK,EAAE;YAAEU,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,eAClC/M,OAAA,CAACjC,MAAM;YACLoQ,IAAI,EAAC,SAAS;YACdC,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEA,CAAA,KAAMzK,QAAQ,CAAC,QAAQ,CAAE;YAClC4I,KAAK,EAAE;cACLK,UAAU,EAAE,mDAAmD;cAC/DQ,MAAM,EAAE,MAAM;cACdF,YAAY,EAAE,KAAK;cACnBF,OAAO,EAAE,QAAQ;cACjBR,MAAM,EAAE,MAAM;cACdqB,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAEV;EAEA,oBACE7N,OAAA;IAAKwM,KAAK,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAE;IAAAE,QAAA,gBACA/M,OAAA,CAAChC,IAAI;MACHwO,KAAK,EAAE;QACLQ,KAAK,EAAE,GAAG;QACVsB,QAAQ,EAAE,MAAM;QAChBrB,OAAO,EAAE,MAAM;QACfsB,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACb,CAAE;MACFlB,QAAQ,EAAE,KAAM;MAChBmB,SAAS,EAAC,eAAe;MAAA1B,QAAA,gBAEzB/M,OAAA;QAAO0O,GAAG;QAAA3B,QAAA,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACV7N,OAAA;QAAKwM,KAAK,EAAE;UAAEU,SAAS,EAAE,QAAQ;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAR,QAAA,gBACpD/M,OAAA,CAACG,KAAK;UAACqN,KAAK,EAAE,CAAE;UAAChB,KAAK,EAAE;YAAEiB,KAAK,EAAE,SAAS;YAAEF,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAAU;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjF7N,OAAA,CAACG,KAAK;UAACqN,KAAK,EAAE,CAAE;UAAChB,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAEN7N,OAAA,CAAC7B,IAAI;QAACwQ,QAAQ,EAAE7M,OAAO,IAAIqC,eAAgB;QAAA4I,QAAA,GACxClC,cAAc,CAAC,CAAC,gBACf7K,OAAA,CAACnC,IAAI;UACH6H,IAAI,EAAC,eAAe;UACpBsB,aAAa,EAAEC,gBAAgB,CAAC,CAAE;UAClC6B,QAAQ,EAAEA,QAAS;UACnB8F,MAAM,EAAC,UAAU;UACjBlL,IAAI,EAAEA,IAAK;UACX8I,KAAK,EAAE;YAAEe,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,gBAG3B/M,OAAA,CAACtB,OAAO;YAACmQ,WAAW,EAAC,MAAM;YAACrC,KAAK,EAAE;cAAEwB,MAAM,EAAE,eAAe;cAAEF,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAAC;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAEhG7N,OAAA,CAACxB,GAAG;YAACsQ,MAAM,EAAE,EAAG;YAAA/B,QAAA,gBACd/M,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,UAAU;gBACfuJ,KAAK,EAAE,CACL;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAU,CAAC,EACtC;kBAAEgR,GAAG,EAAE,CAAC;kBAAEhR,OAAO,EAAE;gBAAY,CAAC,CAChC;gBAAA6O,QAAA,eAEF/M,OAAA,CAAClC,KAAK;kBACJqR,MAAM,eAAEnP,OAAA,CAACrB,YAAY;oBAAC8P,SAAS,EAAC;kBAAqB;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDuB,WAAW,EAAC;gBAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,WAAW;gBAChBuJ,KAAK,EAAE,CAAC;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA6O,QAAA,eAE/C/M,OAAA,CAAClC,KAAK;kBACJqR,MAAM,eAAEnP,OAAA,CAACrB,YAAY;oBAAC8P,SAAS,EAAC;kBAAqB;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDuB,WAAW,EAAC;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7N,OAAA,CAACxB,GAAG;YAACsQ,MAAM,EAAE,EAAG;YAAA/B,QAAA,gBACd/M,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,OAAO;gBACZuJ,KAAK,EAAE,CACL;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,EACrC;kBAAEiQ,IAAI,EAAE,OAAO;kBAAEjQ,OAAO,EAAE;gBAAc,CAAC,CACzC;gBAAA6O,QAAA,eAEF/M,OAAA,CAAClC,KAAK;kBACJqR,MAAM,eAAEnP,OAAA,CAACnB,YAAY;oBAAC4P,SAAS,EAAC;kBAAqB;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDuB,WAAW,EAAC;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,OAAO;gBACZuJ,KAAK,EAAE,CACL;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAW,CAAC,EACvC;kBAAEmR,OAAO,EAAE,eAAe;kBAAEnR,OAAO,EAAE;gBAAc,CAAC,CACpD;gBAAA6O,QAAA,eAEF/M,OAAA,CAAClC,KAAK;kBACJqR,MAAM,eAAEnP,OAAA,CAAClB,aAAa;oBAAC2P,SAAS,EAAC;kBAAqB;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC1DuB,WAAW,EAAC;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7N,OAAA,CAACxB,GAAG;YAACsQ,MAAM,EAAE,EAAG;YAAA/B,QAAA,gBACd/M,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,UAAU;gBACfuJ,KAAK,EAAE,CACL;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,EACrC;kBAAEgR,GAAG,EAAE,CAAC;kBAAEhR,OAAO,EAAE;gBAAW,CAAC,CAC/B;gBAAA6O,QAAA,eAEF/M,OAAA,CAAClC,KAAK;kBACJqR,MAAM,eAAEnP,OAAA,CAACpB,YAAY;oBAAC6P,SAAS,EAAC;kBAAqB;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDM,IAAI,EAAC,UAAU;kBACfiB,WAAW,EAAC;gBAAI;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,SAAS;gBACd4J,YAAY,EAAE,CAAC,UAAU,CAAE;gBAC3BL,KAAK,EAAE,CACL;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,EACrC,CAAC;kBAAE6J;gBAAc,CAAC,MAAM;kBACtBwH,SAASA,CAACC,CAAC,EAAEzE,KAAK,EAAE;oBAClB,IAAI,CAACA,KAAK,IAAIhD,aAAa,CAAC,UAAU,CAAC,KAAKgD,KAAK,EAAE;sBACjD,OAAO0E,OAAO,CAACC,OAAO,CAAC,CAAC;oBAC1B;oBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIhG,KAAK,CAAC,aAAa,CAAC,CAAC;kBACjD;gBACF,CAAC,CAAC,CACF;gBAAAoD,QAAA,eAEF/M,OAAA,CAAClC,KAAK;kBACJqR,MAAM,eAAEnP,OAAA,CAACpB,YAAY;oBAAC6P,SAAS,EAAC;kBAAqB;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzDM,IAAI,EAAC,UAAU;kBACfiB,WAAW,EAAC;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxJ,cAAc,CAACiB,MAAM,KAAK,CAAC,gBAC1BtF,OAAA,CAACzB,KAAK;YACJL,OAAO,EAAC,kDAAU;YAClB0R,WAAW,EAAC,0HAAsB;YAClCzB,IAAI,EAAC,SAAS;YACd0B,QAAQ;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,GACAxJ,cAAc,CAACiB,MAAM,GAAG,CAAC,gBAC3BtF,OAAA,CAACnC,IAAI,CAACmR,IAAI;YAACtJ,IAAI,EAAC,MAAM;YAACoK,KAAK,EAAC,gCAAO;YAACb,KAAK,EAAE,CAAC;cAAE1D,QAAQ,EAAE,IAAI;cAAErN,OAAO,EAAE;YAAS,CAAC,CAAE;YAAA6O,QAAA,eAClF/M,OAAA,CAAC3B,MAAM;cACL+Q,WAAW,EAAC,gCAAO;cACnBW,QAAQ,EAAEjF,gBAAiB;cAC3B0B,KAAK,EAAE;gBAAEQ,KAAK,EAAE;cAAO,CAAE;cAAAD,QAAA,EAExB1I,cAAc,CAACmB,GAAG,CAACC,IAAI,iBACtBzF,OAAA,CAACI,MAAM;gBAAY2K,KAAK,EAAEtF,IAAK;gBAAAsH,QAAA,EAC5B1M,gBAAgB,CAACoF,IAAI,CAAC,IAAIA;cAAI,GADpBA,IAAI;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAET,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;UAAA;UAEZ;UACA7N,OAAA,CAACnC,IAAI,CAACmR,IAAI;YAACc,KAAK,EAAC,0BAAM;YAAA/C,QAAA,gBACrB/M,OAAA,CAAClC,KAAK;cACJiN,KAAK,EAAE1G,cAAc,CAACiB,MAAM,GAAG,CAAC,GAAGjF,gBAAgB,CAACgE,cAAc,CAAC,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,GAAG,OAAQ;cACtG2L,QAAQ;cACRb,MAAM,eAAEnP,OAAA,CAACrB,YAAY;gBAAA+O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eAEF7N,OAAA,CAACnC,IAAI,CAACmR,IAAI;cAACtJ,IAAI,EAAC,MAAM;cAAC+F,MAAM;cAACwE,YAAY,EAAE5L,cAAc,CAACiB,MAAM,GAAG,CAAC,GAAGjB,cAAc,CAAC,CAAC,CAAC,GAAG,EAAG;cAAA0I,QAAA,eAC7F/M,OAAA,CAAClC,KAAK;gBAAA4P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZ7N,OAAA,CAACnC,IAAI,CAACmR,IAAI;cAACtJ,IAAI,EAAC,YAAY;cAAC+F,MAAM;cAACwE,YAAY,EAAE5L,cAAc,CAACiB,MAAM,GAAG,CAAC,IAAIjB,cAAc,CAAC,CAAC,CAAC,KAAK,SAAU;cAAA0I,QAAA,eAC7G/M,OAAA,CAAClC,KAAK;gBAAA4P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACZ,eAGD7N,OAAA,CAACtB,OAAO;YAACmQ,WAAW,EAAC,MAAM;YAACrC,KAAK,EAAE;cAAEwB,MAAM,EAAE,eAAe;cAAEF,QAAQ,EAAE;YAAO,CAAE;YAAAf,QAAA,EAAC;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAGhG7N,OAAA,CAACxB,GAAG;YAACsQ,MAAM,EAAE,EAAG;YAAA/B,QAAA,gBACd/M,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,CAAE;cAAAhC,QAAA,eACX/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,UAAU;gBACfuJ,KAAK,EAAE,CAAC;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA6O,QAAA,eAE/C/M,OAAA,CAAC3B,MAAM;kBACL+Q,WAAW,EAAC,gCAAO;kBACnBW,QAAQ,EAAEhE,oBAAqB;kBAC/BmE,UAAU;kBAAAnD,QAAA,EAET3K,SAAS,CAACoD,GAAG,CAACgC,QAAQ,iBACrBxH,OAAA,CAACI,MAAM;oBAAgB2K,KAAK,EAAEvD,QAAS;oBAAAuF,QAAA,EAAEvF;kBAAQ,GAApCA,QAAQ;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,CAAE;cAAAhC,QAAA,eACX/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,MAAM;gBACXuJ,KAAK,EAAE,CAAC;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA6O,QAAA,eAE/C/M,OAAA,CAAC3B,MAAM;kBACL+Q,WAAW,EAAC,gCAAO;kBACnBW,QAAQ,EAAE/D,gBAAiB;kBAC3BgE,QAAQ,EAAE,CAACtN,gBAAiB;kBAC5BwN,UAAU;kBAAAnD,QAAA,EAETzK,MAAM,CAACkD,GAAG,CAACkC,IAAI,iBACd1H,OAAA,CAACI,MAAM;oBAAY2K,KAAK,EAAErD,IAAK;oBAAAqF,QAAA,EAAErF;kBAAI,GAAxBA,IAAI;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;cAACsQ,IAAI,EAAE,CAAE;cAAAhC,QAAA,eACX/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;gBACRtJ,IAAI,EAAC,UAAU;gBACfuJ,KAAK,EAAE,CAAC;kBAAE1D,QAAQ,EAAE,IAAI;kBAAErN,OAAO,EAAE;gBAAS,CAAC,CAAE;gBAAA6O,QAAA,eAE/C/M,OAAA,CAAC3B,MAAM;kBACL+Q,WAAW,EAAC,gCAAO;kBACnBW,QAAQ,EAAE9D,oBAAqB;kBAC/B+D,QAAQ,EAAE,CAACpN,YAAa;kBACxBsN,UAAU;kBAAAnD,QAAA,EAETvK,SAAS,CAACgD,GAAG,CAACqC,QAAQ,iBACrB7H,OAAA,CAACI,MAAM;oBAAgB2K,KAAK,EAAElD,QAAS;oBAAAkF,QAAA,EAAElF;kBAAQ,GAApCA,QAAQ;oBAAA6F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLzK,SAAS,iBACRpD,OAAA,CAAAE,SAAA;YAAA6M,QAAA,gBAEE/M,OAAA,CAACtB,OAAO;cAACmQ,WAAW,EAAC,MAAM;cAACrC,KAAK,EAAE;gBAAEwB,MAAM,EAAE,eAAe;gBAAEF,QAAQ,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEhG7N,OAAA,CAACxB,GAAG;cAACsQ,MAAM,EAAE,EAAG;cAAA/B,QAAA,gBACd/M,OAAA,CAACvB,GAAG;gBAACsQ,IAAI,EAAE,EAAG;gBAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;kBACRtJ,IAAI,EAAC,aAAa;kBAClBuJ,KAAK,EAAE,CAAC;oBAAE1D,QAAQ,EAAE,IAAI;oBAAErN,OAAO,EAAE;kBAAc,CAAC,CAAE;kBAAA6O,QAAA,EAEnD7K,YAAY,gBACXlC,OAAA,CAAClC,KAAK;oBACJqR,MAAM,eAAEnP,OAAA,CAACrB,YAAY;sBAAC8P,SAAS,EAAC;oBAAqB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzDuB,WAAW,EAAC;kBAAS;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,gBAEF7N,OAAA,CAAC3B,MAAM;oBACL+Q,WAAW,EAAC,gCAAO;oBACnBW,QAAQ,EAAE7D,kBAAmB;oBAC7B8D,QAAQ,EAAE,CAAClN,gBAAiB;oBAC5BoN,UAAU;oBACVC,UAAU;oBACVC,YAAY,EAAEA,CAACC,KAAK,EAAElE,MAAM,KAC1BA,MAAM,CAACY,QAAQ,IAAI,OAAOZ,MAAM,CAACY,QAAQ,KAAK,QAAQ,GACtDZ,MAAM,CAACY,QAAQ,CAACuD,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;oBAAAvD,QAAA,EAEA/K,OAAO,CAACwD,GAAG,CAACgL,MAAM,iBACjBxQ,OAAA,CAACI,MAAM;sBAAiB2K,KAAK,EAAEyF,MAAM,CAAC7H,WAAY;sBAAAoE,QAAA,EAAEyD,MAAM,CAAC7H;oBAAW,GAAzD6H,MAAM,CAACjE,EAAE;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAyD,CAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;gBAACsQ,IAAI,EAAE,CAAE;gBAAAhC,QAAA,eACX/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;kBACRtJ,IAAI,EAAC,UAAU;kBACfuJ,KAAK,EAAE,CAAC;oBAAE1D,QAAQ,EAAE,IAAI;oBAAErN,OAAO,EAAE;kBAAS,CAAC,CAAE;kBAAA6O,QAAA,eAE/C/M,OAAA,CAAC3B,MAAM;oBACL+Q,WAAW,EAAC,gCAAO;oBACnBY,QAAQ,EAAE,CAACxM,gBAAgB,IAAItB,YAAa;oBAC5CgO,UAAU;oBAAAnD,QAAA,EAET/J,OAAO,CAACwC,GAAG,CAACiL,GAAG,iBACdzQ,OAAA,CAACI,MAAM;sBAAc2K,KAAK,EAAE0F,GAAG,CAAClE,EAAG;sBAAAQ,QAAA,EAAE0D,GAAG,CAAC/K;oBAAI,GAAhC+K,GAAG,CAAClE,EAAE;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAmC,CACvD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL,CAAC3L,YAAY,iBACZlC,OAAA;cAAKwM,KAAK,EAAE;gBAAE0B,SAAS,EAAE,CAAC,CAAC;gBAAEX,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eAC7C/M,OAAA,CAACjC,MAAM;gBACLoQ,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,OAAO;gBACZ5B,KAAK,EAAE;kBAAES,OAAO,EAAE;gBAAE,CAAE;gBACtBoB,OAAO,EAAEA,CAAA,KAAM5J,6BAA6B,CAAC,IAAI,CAAE;gBAAAsI,QAAA,EACpD;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAGD7N,OAAA,CAACnC,IAAI,CAACmR,IAAI;cAACtJ,IAAI,EAAC,WAAW;cAAC+F,MAAM;cAAAsB,QAAA,eAChC/M,OAAA,CAAClC,KAAK;gBAAA4P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACZ,CACH,EAGA,CAACzK,SAAS,iBACTpD,OAAA,CAAAE,SAAA;YAAA6M,QAAA,gBACE/M,OAAA,CAACtB,OAAO;cAACmQ,WAAW,EAAC,MAAM;cAACrC,KAAK,EAAE;gBAAEwB,MAAM,EAAE,eAAe;gBAAEF,QAAQ,EAAE;cAAO,CAAE;cAAAf,QAAA,EAAC;YAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAEhG7N,OAAA,CAACxB,GAAG;cAACsQ,MAAM,EAAE,EAAG;cAAA/B,QAAA,gBACd/M,OAAA,CAACvB,GAAG;gBAACsQ,IAAI,EAAE,EAAG;gBAAAhC,QAAA,eACZ/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;kBACRtJ,IAAI,EAAC,aAAa;kBAClBuJ,KAAK,EAAE,CAAC;oBAAE1D,QAAQ,EAAE,IAAI;oBAAErN,OAAO,EAAE;kBAAc,CAAC,CAAE;kBAAA6O,QAAA,EAEnD7K,YAAY,gBACXlC,OAAA,CAAClC,KAAK;oBACJqR,MAAM,eAAEnP,OAAA,CAACrB,YAAY;sBAAC8P,SAAS,EAAC;oBAAqB;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzDuB,WAAW,EAAC;kBAAS;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,gBAEF7N,OAAA,CAAC3B,MAAM;oBACL+Q,WAAW,EAAC,gCAAO;oBACnBW,QAAQ,EAAE7D,kBAAmB;oBAC7B8D,QAAQ,EAAE,CAAClN,gBAAiB;oBAC5BoN,UAAU;oBACVC,UAAU;oBACVC,YAAY,EAAEA,CAACC,KAAK,EAAElE,MAAM,KAC1BA,MAAM,CAACY,QAAQ,IAAI,OAAOZ,MAAM,CAACY,QAAQ,KAAK,QAAQ,GACtDZ,MAAM,CAACY,QAAQ,CAACuD,WAAW,CAAC,CAAC,CAACC,OAAO,CAACF,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KACnE;oBAAAvD,QAAA,EAEA/K,OAAO,CAACwD,GAAG,CAACgL,MAAM,iBACjBxQ,OAAA,CAACI,MAAM;sBAAiB2K,KAAK,EAAEyF,MAAM,CAAC7H,WAAY;sBAAAoE,QAAA,EAAEyD,MAAM,CAAC7H;oBAAW,GAAzD6H,MAAM,CAACjE,EAAE;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAyD,CAChF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBACT;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACN7N,OAAA,CAACvB,GAAG;gBAACsQ,IAAI,EAAE,CAAE;gBAAAhC,QAAA,eACX/M,OAAA,CAACnC,IAAI,CAACmR,IAAI;kBACRtJ,IAAI,EAAC,SAAS;kBACduJ,KAAK,EAAE,CAAC;oBAAE1D,QAAQ,EAAE,IAAI;oBAAErN,OAAO,EAAE;kBAAW,CAAC,CAAE;kBAAA6O,QAAA,eAEjD/M,OAAA,CAAC3B,MAAM;oBACL+Q,WAAW,EAAC,4CAAS;oBACrBc,UAAU;oBACVF,QAAQ,EAAE,CAACxM,gBAAiB;oBAAAuJ,QAAA,EAE3B7J,QAAQ,CAACsC,GAAG,CAACuE,OAAO,iBACnB/J,OAAA,CAACI,MAAM;sBAAkB2K,KAAK,EAAEhB,OAAO,CAACrE,IAAK;sBAAAqH,QAAA,EAAEhD,OAAO,CAACrE;oBAAI,GAA9CqE,OAAO,CAACwC,EAAE;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA6C,CACrE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7N,OAAA;cAAKwM,KAAK,EAAE;gBAAE0B,SAAS,EAAE,CAAC,CAAC;gBAAEX,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,EAC5C,CAAC7K,YAAY,gBACZlC,OAAA,CAACjC,MAAM;gBACLoQ,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,OAAO;gBACZC,OAAO,EAAEA,CAAA,KAAMlM,eAAe,CAAC,IAAI,CAAE;gBACrCqK,KAAK,EAAE;kBAAES,OAAO,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EACvB;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAET7N,OAAA,CAACjC,MAAM;gBACLoQ,IAAI,EAAC,MAAM;gBACXC,IAAI,EAAC,OAAO;gBACZC,OAAO,EAAEA,CAAA,KAAMlM,eAAe,CAAC,KAAK,CAAE;gBACtCqK,KAAK,EAAE;kBAAES,OAAO,EAAE;gBAAE,CAAE;gBAAAF,QAAA,EACvB;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YACT;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN7N,OAAA,CAACnC,IAAI,CAACmR,IAAI;cAACtJ,IAAI,EAAC,WAAW;cAAC+F,MAAM;cAAAsB,QAAA,eAChC/M,OAAA,CAAClC,KAAK;gBAAA4P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGX,CAAA5J,gBAAgB,aAAhBA,gBAAgB,wBAAAzD,sBAAA,GAAhByD,gBAAgB,CAAEoB,KAAK,cAAA7E,sBAAA,wBAAAC,sBAAA,GAAvBD,sBAAA,CAA0B8C,YAAY,CAAC,cAAA7C,sBAAA,wBAAAC,sBAAA,GAAvCD,sBAAA,CAAyC2K,MAAM,cAAA1K,sBAAA,wBAAAC,sBAAA,GAA/CD,sBAAA,CAAiD4K,KAAK,cAAA3K,sBAAA,uBAAtDA,sBAAA,CAAwD4K,QAAQ,kBAC/DvL,OAAA,CAACnC,IAAI,CAACmR,IAAI;cACRtJ,IAAI,EAAC,UAAU;cACfoK,KAAK,EAAExM,YAAY,KAAK,eAAe,GAAG,MAAM,GAAG,MAAO;cAC1D2L,KAAK,EAAE,CAAC;gBAAE1D,QAAQ,EAAE,IAAI;gBAAErN,OAAO,EAAE;cAAS,CAAC,CAAE;cAC/CwS,KAAK,EACHpN,YAAY,KAAK,eAAe,IAAI,CAAAW,gBAAgB,aAAhBA,gBAAgB,wBAAArD,sBAAA,GAAhBqD,gBAAgB,CAAEoB,KAAK,cAAAzE,sBAAA,wBAAAC,sBAAA,GAAvBD,sBAAA,CAA0B0C,YAAY,CAAC,cAAAzC,sBAAA,wBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCuK,MAAM,cAAAtK,sBAAA,wBAAAC,sBAAA,GAA/CD,sBAAA,CAAiDwK,KAAK,cAAAvK,sBAAA,uBAAtDA,sBAAA,CAAwD4P,GAAG,IAAG,CAAC,GAC/F,uCAAuC,GACvCrN,YAAY,KAAK,eAAe,GAChC,4BAA4B,GAC5B,CAAAW,gBAAgB,aAAhBA,gBAAgB,wBAAAjD,uBAAA,GAAhBiD,gBAAgB,CAAEoB,KAAK,cAAArE,uBAAA,wBAAAC,uBAAA,GAAvBD,uBAAA,CAA0BsC,YAAY,CAAC,cAAArC,uBAAA,wBAAAC,uBAAA,GAAvCD,uBAAA,CAAyCmK,MAAM,cAAAlK,uBAAA,wBAAAC,uBAAA,GAA/CD,uBAAA,CAAiDoK,KAAK,cAAAnK,uBAAA,uBAAtDA,uBAAA,CAAwDwP,GAAG,IAAG,CAAC,GAC/D,WAAW,GACXnF,SACL;cAAAuB,QAAA,eAED/M,OAAA,CAAC3B,MAAM;gBACL+Q,WAAW,EAAE9L,YAAY,KAAK,eAAe,GAAG,SAAS,GAAG,SAAU;gBACtE0M,QAAQ,EAAE,CAACxM,gBAAgB,IAAItB,YAAa;gBAC5CgO,UAAU;gBACVU,IAAI,EAAE,CAAA3M,gBAAgB,aAAhBA,gBAAgB,wBAAA7C,uBAAA,GAAhB6C,gBAAgB,CAAEoB,KAAK,cAAAjE,uBAAA,wBAAAC,uBAAA,GAAvBD,uBAAA,CAA0BkC,YAAY,CAAC,cAAAjC,uBAAA,wBAAAC,uBAAA,GAAvCD,uBAAA,CAAyC+J,MAAM,cAAA9J,uBAAA,wBAAAC,uBAAA,GAA/CD,uBAAA,CAAiDgK,KAAK,cAAA/J,uBAAA,uBAAtDA,uBAAA,CAAwDoP,GAAG,IAAG,CAAC,GAAG,UAAU,GAAGnF,SAAU;gBAC/FqF,WAAW,EAAE,CAAA5M,gBAAgB,aAAhBA,gBAAgB,wBAAAzC,uBAAA,GAAhByC,gBAAgB,CAAEoB,KAAK,cAAA7D,uBAAA,wBAAAC,uBAAA,GAAvBD,uBAAA,CAA0B8B,YAAY,CAAC,cAAA7B,uBAAA,wBAAAC,uBAAA,GAAvCD,uBAAA,CAAyC2J,MAAM,cAAA1J,uBAAA,wBAAAC,uBAAA,GAA/CD,uBAAA,CAAiD4J,KAAK,cAAA3J,uBAAA,uBAAtDA,uBAAA,CAAwDgP,GAAG,KAAI,CAAE;gBAAA5D,QAAA,EAE7E/J,OAAO,CAACwC,GAAG,CAACiL,GAAG,iBACdzQ,OAAA,CAACI,MAAM;kBAAc2K,KAAK,EAAE0F,GAAG,CAAClE,EAAG;kBAAAQ,QAAA,EAAE0D,GAAG,CAAC/K;gBAAI,GAAhC+K,GAAG,CAAClE,EAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACZ;UAAA,eACD,CACH,EAGAvK,YAAY,KAAK,QAAQ,iBACxBtD,OAAA,CAACnC,IAAI,CAACmR,IAAI;YACRc,KAAK,EAAC,0BAAM;YACZpK,IAAI,EAAC,iBAAiB;YACtBuJ,KAAK,EAAE,CAAC;cAAE1D,QAAQ,EAAE,IAAI;cAAErN,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA6O,QAAA,eAEhD/M,OAAA;cAAA+M,QAAA,EACG3E,qBAAqB,gBACpBpI,OAAA,CAACzB,KAAK;gBACJL,OAAO,EAAC,0BAAM;gBACd0R,WAAW,EAAE,YAAY,CAAAtH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwI,SAAS,KAAI,IAAI,SAAS,CAAAxI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEK,WAAW,KAAI,IAAI,SAAS,CAAAL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyI,UAAU,KAAI,IAAI,EAAG;gBAC9I5C,IAAI,EAAC,SAAS;gBACd0B,QAAQ;cAAA;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,gBAEF7N,OAAA,CAACH,kBAAkB;gBACjBmR,GAAG,EAAEzM,qBAAsB;gBAC3B0M,iBAAiB,EAAEzI,qBAAsB;gBACzC9E,IAAI,EAAEA,IAAK;gBACXsD,aAAa,EAAEtD,IAAI,CAACoD,cAAc,CAAC,CAAE;gBACrCoK,OAAO,EAAE;cAAK;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CACZ,eAED7N,OAAA,CAACnC,IAAI,CAACmR,IAAI;YAAAjC,QAAA,eACR/M,OAAA,CAACjC,MAAM;cACLoQ,IAAI,EAAC,SAAS;cACdgD,QAAQ,EAAC,QAAQ;cACjB1C,SAAS,EAAC,mBAAmB;cAC7B3M,OAAO,EAAEA,OAAQ;cAAAiL,QAAA,EAClB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,GAEP,CAAC1J,eAAe,iBACdnE,OAAA,CAACzB,KAAK;UACJL,OAAO,EAAC,4CAAS;UACjB0R,WAAW,EAAC,kJAA0B;UACtCzB,IAAI,EAAC,OAAO;UACZ0B,QAAQ;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAEJ,eAED7N,OAAA;UAAKwM,KAAK,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAEgB,SAAS,EAAE;UAAG,CAAE;UAAAnB,QAAA,GAAC,4BAC5C,eAAA/M,OAAA;YAAGwK,IAAI,EAAC,QAAQ;YAAAuC,QAAA,EAAC;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP7N,OAAA,CAAC1B,KAAK;MACJmI,KAAK,EAAC,4CAAS;MACf2K,OAAO,EAAE5M,0BAA2B;MACpC6M,QAAQ,EAAEA,CAAA,KAAM5M,6BAA6B,CAAC,KAAK,CAAE;MACrD6M,MAAM,EAAE,IAAK;MACbtE,KAAK,EAAE,GAAI;MACXuE,cAAc;MAAAxE,QAAA,eAEd/M,OAAA,CAACF,qBAAqB;QACpB0R,eAAe,EAAEA,CAAA,KAAM;UACrB/M,6BAA6B,CAAC,KAAK,CAAC;UACpCvG,OAAO,CAAC2K,OAAO,CAAC,uBAAuB,CAAC;QAC1C;MAAE;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtN,EAAA,CAryCID,kBAAkB;EAAA,QAgBPzC,IAAI,CAAC8F,OAAO,EACV/D,WAAW;AAAA;AAAA6R,EAAA,GAjBxBnR,kBAAkB;AAuyCxB,eAAeA,kBAAkB;AAAC,IAAAmR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}