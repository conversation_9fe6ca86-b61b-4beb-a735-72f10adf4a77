#!/usr/bin/env python3
"""
测试新的作业上传功能
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.file_manager import FileManager

def test_file_manager():
    """测试FileManager的基本功能"""
    print("🧪 测试FileManager基本功能...")
    
    # 创建临时目录用于测试
    with tempfile.TemporaryDirectory() as temp_dir:
        file_manager = FileManager(base_upload_dir=temp_dir)
        
        # 测试目录创建
        print("✅ 基础目录创建成功")
        
        # 测试文件路径生成
        file_info = file_manager.generate_homework_file_path(
            school_id=1,
            grade_class_code="701",
            subject="math",
            assignment_id=123,
            assignment_name="期中考试",
            user_id=12345,
            homework_id=67890,
            page_number=1,
            file_extension=".jpg",
            is_annotated=False
        )
        
        print(f"📁 生成的文件路径信息:")
        print(f"   文件名: {file_info['filename']}")
        print(f"   完整路径: {file_info['full_path']}")
        print(f"   URL路径: {file_info['url_path']}")
        print(f"   相对路径: {file_info['relative_path']}")
        
        # 测试文件保存
        test_content = b"test image content"
        success = file_manager.save_file(test_content, file_info)
        
        if success:
            print("✅ 文件保存成功")
            
            # 验证文件是否存在
            if os.path.exists(file_info['full_path']):
                print("✅ 文件确实存在于磁盘上")
                
                # 验证文件内容
                with open(file_info['full_path'], 'rb') as f:
                    saved_content = f.read()
                    if saved_content == test_content:
                        print("✅ 文件内容正确")
                    else:
                        print("❌ 文件内容不匹配")
            else:
                print("❌ 文件不存在于磁盘上")
        else:
            print("❌ 文件保存失败")
        
        # 测试批注文件路径生成
        annotated_info = file_manager.generate_homework_file_path(
            school_id=1,
            grade_class_code="701",
            subject="math",
            assignment_id=123,
            assignment_name="期中考试",
            user_id=12345,
            homework_id=67890,
            page_number=1,
            file_extension=".jpg",
            is_annotated=True
        )
        
        print(f"📝 批注文件路径信息:")
        print(f"   文件名: {annotated_info['filename']}")
        print(f"   URL路径: {annotated_info['url_path']}")
        
        # 验证目录结构
        expected_dirs = [
            "schools/school_001/grade_701/subjects/math/assignments/assignment_123_期中考试/original",
            "schools/school_001/grade_701/subjects/math/assignments/assignment_123_期中考试/annotated"
        ]
        
        for expected_dir in expected_dirs:
            full_dir_path = os.path.join(temp_dir, expected_dir)
            if os.path.exists(full_dir_path):
                print(f"✅ 目录存在: {expected_dir}")
            else:
                print(f"❌ 目录不存在: {expected_dir}")

def test_file_naming():
    """测试文件命名规范"""
    print("\n🧪 测试文件命名规范...")
    
    file_manager = FileManager()
    
    # 测试作业文件名生成
    filename = file_manager.generate_homework_filename(
        user_id=12345,
        homework_id=67890,
        page_number=1,
        file_extension=".jpg"
    )
    
    print(f"📝 生成的作业文件名: {filename}")
    
    # 验证文件名格式
    if filename.startswith("user_12345_") and "_hw67890_p1.jpg" in filename:
        print("✅ 作业文件名格式正确")
    else:
        print("❌ 作业文件名格式不正确")
    
    # 测试批注文件名生成
    annotated_filename = file_manager.generate_annotated_filename(filename)
    print(f"📝 生成的批注文件名: {annotated_filename}")
    
    if "_annotated.jpg" in annotated_filename:
        print("✅ 批注文件名格式正确")
    else:
        print("❌ 批注文件名格式不正确")

def test_file_validation():
    """测试文件验证功能"""
    print("\n🧪 测试文件验证功能...")
    
    file_manager = FileManager()
    
    # 测试有效文件
    valid, msg = file_manager.validate_homework_file(1024 * 1024, ".jpg")  # 1MB JPG
    if valid:
        print("✅ 有效文件验证通过")
    else:
        print(f"❌ 有效文件验证失败: {msg}")
    
    # 测试无效格式
    valid, msg = file_manager.validate_homework_file(1024 * 1024, ".txt")  # 1MB TXT
    if not valid:
        print(f"✅ 无效格式正确拒绝: {msg}")
    else:
        print("❌ 无效格式未被拒绝")
    
    # 测试文件过大
    valid, msg = file_manager.validate_homework_file(20 * 1024 * 1024, ".jpg")  # 20MB JPG
    if not valid:
        print(f"✅ 过大文件正确拒绝: {msg}")
    else:
        print("❌ 过大文件未被拒绝")

if __name__ == "__main__":
    print("🚀 开始测试新的作业上传功能...")
    
    try:
        test_file_manager()
        test_file_naming()
        test_file_validation()
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
