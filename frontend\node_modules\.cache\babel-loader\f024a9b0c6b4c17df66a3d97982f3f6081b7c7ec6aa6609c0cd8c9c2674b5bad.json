{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Size ================================\nconst genSizeDividerStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      '&-horizontal': {\n        [`&${componentCls}`]: {\n          '&-sm': {\n            marginBlock: token.marginXS\n          },\n          '&-md': {\n            marginBlock: token.margin\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Shared ==============================\nconst genSharedDividerStyle = token => {\n  const {\n    componentCls,\n    sizePaddingEdgeHorizontal,\n    colorSplit,\n    lineWidth,\n    textPaddingInline,\n    orientationMargin,\n    verticalMarginInline\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      borderBlockStart: `${unit(lineWidth)} solid ${colorSplit}`,\n      // vertical\n      '&-vertical': {\n        position: 'relative',\n        top: '-0.06em',\n        display: 'inline-block',\n        height: '0.9em',\n        marginInline: verticalMarginInline,\n        marginBlock: 0,\n        verticalAlign: 'middle',\n        borderTop: 0,\n        borderInlineStart: `${unit(lineWidth)} solid ${colorSplit}`\n      },\n      '&-horizontal': {\n        display: 'flex',\n        clear: 'both',\n        width: '100%',\n        minWidth: '100%',\n        // Fix https://github.com/ant-design/ant-design/issues/10914\n        margin: `${unit(token.marginLG)} 0`\n      },\n      [`&-horizontal${componentCls}-with-text`]: {\n        display: 'flex',\n        alignItems: 'center',\n        margin: `${unit(token.dividerHorizontalWithTextGutterMargin)} 0`,\n        color: token.colorTextHeading,\n        fontWeight: 500,\n        fontSize: token.fontSizeLG,\n        whiteSpace: 'nowrap',\n        textAlign: 'center',\n        borderBlockStart: `0 ${colorSplit}`,\n        '&::before, &::after': {\n          position: 'relative',\n          width: '50%',\n          borderBlockStart: `${unit(lineWidth)} solid transparent`,\n          // Chrome not accept `inherit` in `border-top`\n          borderBlockStartColor: 'inherit',\n          borderBlockEnd: 0,\n          transform: 'translateY(50%)',\n          content: \"''\"\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-start`]: {\n        '&::before': {\n          width: `calc(${orientationMargin} * 100%)`\n        },\n        '&::after': {\n          width: `calc(100% - ${orientationMargin} * 100%)`\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-end`]: {\n        '&::before': {\n          width: `calc(100% - ${orientationMargin} * 100%)`\n        },\n        '&::after': {\n          width: `calc(${orientationMargin} * 100%)`\n        }\n      },\n      [`${componentCls}-inner-text`]: {\n        display: 'inline-block',\n        paddingBlock: 0,\n        paddingInline: textPaddingInline\n      },\n      '&-dashed': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dashed',\n        borderWidth: `${unit(lineWidth)} 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dashed`]: {\n        '&::before, &::after': {\n          borderStyle: 'dashed none none'\n        }\n      },\n      [`&-vertical${componentCls}-dashed`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      '&-dotted': {\n        background: 'none',\n        borderColor: colorSplit,\n        borderStyle: 'dotted',\n        borderWidth: `${unit(lineWidth)} 0 0`\n      },\n      [`&-horizontal${componentCls}-with-text${componentCls}-dotted`]: {\n        '&::before, &::after': {\n          borderStyle: 'dotted none none'\n        }\n      },\n      [`&-vertical${componentCls}-dotted`]: {\n        borderInlineStartWidth: lineWidth,\n        borderInlineEnd: 0,\n        borderBlockStart: 0,\n        borderBlockEnd: 0\n      },\n      [`&-plain${componentCls}-with-text`]: {\n        color: token.colorText,\n        fontWeight: 'normal',\n        fontSize: token.fontSize\n      },\n      [`&-horizontal${componentCls}-with-text-start${componentCls}-no-default-orientation-margin-start`]: {\n        '&::before': {\n          width: 0\n        },\n        '&::after': {\n          width: '100%'\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineStart: sizePaddingEdgeHorizontal\n        }\n      },\n      [`&-horizontal${componentCls}-with-text-end${componentCls}-no-default-orientation-margin-end`]: {\n        '&::before': {\n          width: '100%'\n        },\n        '&::after': {\n          width: 0\n        },\n        [`${componentCls}-inner-text`]: {\n          paddingInlineEnd: sizePaddingEdgeHorizontal\n        }\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  textPaddingInline: '1em',\n  orientationMargin: 0.05,\n  verticalMarginInline: token.marginXS\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Divider', token => {\n  const dividerToken = mergeToken(token, {\n    dividerHorizontalWithTextGutterMargin: token.margin,\n    sizePaddingEdgeHorizontal: 0\n  });\n  return [genSharedDividerStyle(dividerToken), genSizeDividerStyle(dividerToken)];\n}, prepareComponentToken, {\n  unitless: {\n    orientationMargin: true\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}