{"ast": null, "code": "import { getImageUrl } from '../../utils/imageUrl';\nexport const processImageUrls = images => {\n  if (!images || images.length === 0) {\n    return [];\n  }\n  return images.map(image => ({\n    ...image,\n    image_path: getImageUrl(image.image_path)\n  }));\n};", "map": {"version": 3, "names": ["getImageUrl", "processImageUrls", "images", "length", "map", "image", "image_path"], "sources": ["D:/pythonproject/checkingsys/frontend/src/components/student/utils.js"], "sourcesContent": ["import { getImageUrl } from '../../utils/imageUrl';\r\n\r\nexport const processImageUrls = (images) => {\r\n  if (!images || images.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  return images.map(image => ({\r\n    ...image,\r\n    image_path: getImageUrl(image.image_path)\r\n  }));\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,sBAAsB;AAElD,OAAO,MAAMC,gBAAgB,GAAIC,MAAM,IAAK;EAC1C,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;IAClC,OAAO,EAAE;EACX;EAEA,OAAOD,MAAM,CAACE,GAAG,CAACC,KAAK,KAAK;IAC1B,GAAGA,KAAK;IACRC,UAAU,EAAEN,WAAW,CAACK,KAAK,CAACC,UAAU;EAC1C,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}