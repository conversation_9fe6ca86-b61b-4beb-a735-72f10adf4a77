{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst getRTLStyle = ({\n  componentCls,\n  menuArrowOffset,\n  calc\n}) => ({\n  [`${componentCls}-rtl`]: {\n    direction: 'rtl'\n  },\n  [`${componentCls}-submenu-rtl`]: {\n    transformOrigin: '100% 0'\n  },\n  // Vertical Arrow\n  [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n    [`${componentCls}-submenu-arrow`]: {\n      '&::before': {\n        transform: `rotate(-45deg) translateY(${unit(calc(menuArrowOffset).mul(-1).equal())})`\n      },\n      '&::after': {\n        transform: `rotate(45deg) translateY(${unit(menuArrowOffset)})`\n      }\n    }\n  }\n});\nexport default getRTLStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}