{"ast": null, "code": "var has = Object.prototype.hasOwnProperty;\nfunction find(iter, tar, key) {\n  for (key of iter.keys()) {\n    if (dequal(key, tar)) return key;\n  }\n}\nexport function dequal(foo, bar) {\n  var ctor, len, tmp;\n  if (foo === bar) return true;\n  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {\n    if (ctor === Date) return foo.getTime() === bar.getTime();\n    if (ctor === RegExp) return foo.toString() === bar.toString();\n    if (ctor === Array) {\n      if ((len = foo.length) === bar.length) {\n        while (len-- && dequal(foo[len], bar[len]));\n      }\n      return len === -1;\n    }\n    if (ctor === Set) {\n      if (foo.size !== bar.size) {\n        return false;\n      }\n      for (len of foo) {\n        tmp = len;\n        if (tmp && typeof tmp === 'object') {\n          tmp = find(bar, tmp);\n          if (!tmp) return false;\n        }\n        if (!bar.has(tmp)) return false;\n      }\n      return true;\n    }\n    if (ctor === Map) {\n      if (foo.size !== bar.size) {\n        return false;\n      }\n      for (len of foo) {\n        tmp = len[0];\n        if (tmp && typeof tmp === 'object') {\n          tmp = find(bar, tmp);\n          if (!tmp) return false;\n        }\n        if (!dequal(len[1], bar.get(tmp))) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (ctor === ArrayBuffer) {\n      foo = new Uint8Array(foo);\n      bar = new Uint8Array(bar);\n    } else if (ctor === DataView) {\n      if ((len = foo.byteLength) === bar.byteLength) {\n        while (len-- && foo.getInt8(len) === bar.getInt8(len));\n      }\n      return len === -1;\n    }\n    if (ArrayBuffer.isView(foo)) {\n      if ((len = foo.byteLength) === bar.byteLength) {\n        while (len-- && foo[len] === bar[len]);\n      }\n      return len === -1;\n    }\n    if (!ctor || typeof foo === 'object') {\n      len = 0;\n      for (ctor in foo) {\n        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n      }\n      return Object.keys(bar).length === len;\n    }\n  }\n  return foo !== foo && bar !== bar;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}