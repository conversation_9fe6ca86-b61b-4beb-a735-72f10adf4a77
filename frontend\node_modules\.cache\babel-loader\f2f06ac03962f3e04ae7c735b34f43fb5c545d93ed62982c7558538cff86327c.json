{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nimport { isImageValid } from \"../util\";\nexport default function useStatus(_ref) {\n  var src = _ref.src,\n    isCustomPlaceholder = _ref.isCustomPlaceholder,\n    fallback = _ref.fallback;\n  var _useState = useState(isCustomPlaceholder ? 'loading' : 'normal'),\n    _useState2 = _slicedToArray(_useState, 2),\n    status = _useState2[0],\n    setStatus = _useState2[1];\n  var isLoaded = useRef(false);\n  var isError = status === 'error';\n\n  // https://github.com/react-component/image/pull/187\n  useEffect(function () {\n    var isCurrentSrc = true;\n    isImageValid(src).then(function (isValid) {\n      // https://github.com/ant-design/ant-design/issues/44948\n      // If src changes, the previous setStatus should not be triggered\n      if (!isValid && isCurrentSrc) {\n        setStatus('error');\n      }\n    });\n    return function () {\n      isCurrentSrc = false;\n    };\n  }, [src]);\n  useEffect(function () {\n    if (isCustomPlaceholder && !isLoaded.current) {\n      setStatus('loading');\n    } else if (isError) {\n      setStatus('normal');\n    }\n  }, [src]);\n  var onLoad = function onLoad() {\n    setStatus('normal');\n  };\n  var getImgRef = function getImgRef(img) {\n    isLoaded.current = false;\n    if (status === 'loading' && img !== null && img !== void 0 && img.complete && (img.naturalWidth || img.naturalHeight)) {\n      isLoaded.current = true;\n      onLoad();\n    }\n  };\n  var srcAndOnload = isError && fallback ? {\n    src: fallback\n  } : {\n    onLoad: onLoad,\n    src: src\n  };\n  return [getImgRef, srcAndOnload, status];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}