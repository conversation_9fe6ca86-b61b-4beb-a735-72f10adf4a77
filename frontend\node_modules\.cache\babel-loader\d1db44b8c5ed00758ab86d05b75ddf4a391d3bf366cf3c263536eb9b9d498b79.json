{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nimport { getFixedBottom, getFixedTop, getTargetRect } from './utils';\nconst TRIGGER_EVENTS = ['resize', 'scroll', 'touchstart', 'touchmove', 'touchend', 'pageshow', 'load'];\nfunction getDefaultTarget() {\n  return typeof window !== 'undefined' ? window : null;\n}\nconst AFFIX_STATUS_NONE = 0;\nconst AFFIX_STATUS_PREPARE = 1;\nconst Affix = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a;\n  const {\n      style,\n      offsetTop,\n      offsetBottom,\n      prefixCls,\n      className,\n      rootClassName,\n      children,\n      target,\n      onChange,\n      onTestUpdatePosition\n    } = props,\n    restProps = __rest(props, [\"style\", \"offsetTop\", \"offsetBottom\", \"prefixCls\", \"className\", \"rootClassName\", \"children\", \"target\", \"onChange\", \"onTestUpdatePosition\"]);\n  const {\n    getPrefixCls,\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const affixPrefixCls = getPrefixCls('affix', prefixCls);\n  const [lastAffix, setLastAffix] = React.useState(false);\n  const [affixStyle, setAffixStyle] = React.useState();\n  const [placeholderStyle, setPlaceholderStyle] = React.useState();\n  const status = React.useRef(AFFIX_STATUS_NONE);\n  const prevTarget = React.useRef(null);\n  const prevListener = React.useRef(null);\n  const placeholderNodeRef = React.useRef(null);\n  const fixedNodeRef = React.useRef(null);\n  const timer = React.useRef(null);\n  const targetFunc = (_a = target !== null && target !== void 0 ? target : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultTarget;\n  const internalOffsetTop = offsetBottom === undefined && offsetTop === undefined ? 0 : offsetTop;\n  // =================== Measure ===================\n  const measure = () => {\n    if (status.current !== AFFIX_STATUS_PREPARE || !fixedNodeRef.current || !placeholderNodeRef.current || !targetFunc) {\n      return;\n    }\n    const targetNode = targetFunc();\n    if (targetNode) {\n      const newState = {\n        status: AFFIX_STATUS_NONE\n      };\n      const placeholderRect = getTargetRect(placeholderNodeRef.current);\n      if (placeholderRect.top === 0 && placeholderRect.left === 0 && placeholderRect.width === 0 && placeholderRect.height === 0) {\n        return;\n      }\n      const targetRect = getTargetRect(targetNode);\n      const fixedTop = getFixedTop(placeholderRect, targetRect, internalOffsetTop);\n      const fixedBottom = getFixedBottom(placeholderRect, targetRect, offsetBottom);\n      if (fixedTop !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          top: fixedTop,\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n      } else if (fixedBottom !== undefined) {\n        newState.affixStyle = {\n          position: 'fixed',\n          bottom: fixedBottom,\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n        newState.placeholderStyle = {\n          width: placeholderRect.width,\n          height: placeholderRect.height\n        };\n      }\n      newState.lastAffix = !!newState.affixStyle;\n      if (lastAffix !== newState.lastAffix) {\n        onChange === null || onChange === void 0 ? void 0 : onChange(newState.lastAffix);\n      }\n      status.current = newState.status;\n      setAffixStyle(newState.affixStyle);\n      setPlaceholderStyle(newState.placeholderStyle);\n      setLastAffix(newState.lastAffix);\n    }\n  };\n  const prepareMeasure = () => {\n    status.current = AFFIX_STATUS_PREPARE;\n    measure();\n    if (process.env.NODE_ENV === 'test') {\n      onTestUpdatePosition === null || onTestUpdatePosition === void 0 ? void 0 : onTestUpdatePosition();\n    }\n  };\n  const updatePosition = throttleByAnimationFrame(() => {\n    prepareMeasure();\n  });\n  const lazyUpdatePosition = throttleByAnimationFrame(() => {\n    // Check position change before measure to make Safari smooth\n    if (targetFunc && affixStyle) {\n      const targetNode = targetFunc();\n      if (targetNode && placeholderNodeRef.current) {\n        const targetRect = getTargetRect(targetNode);\n        const placeholderRect = getTargetRect(placeholderNodeRef.current);\n        const fixedTop = getFixedTop(placeholderRect, targetRect, internalOffsetTop);\n        const fixedBottom = getFixedBottom(placeholderRect, targetRect, offsetBottom);\n        if (fixedTop !== undefined && affixStyle.top === fixedTop || fixedBottom !== undefined && affixStyle.bottom === fixedBottom) {\n          return;\n        }\n      }\n    }\n    // Directly call prepare measure since it's already throttled.\n    prepareMeasure();\n  });\n  const addListeners = () => {\n    const listenerTarget = targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc();\n    if (!listenerTarget) {\n      return;\n    }\n    TRIGGER_EVENTS.forEach(eventName => {\n      var _a;\n      if (prevListener.current) {\n        (_a = prevTarget.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, prevListener.current);\n      }\n      listenerTarget === null || listenerTarget === void 0 ? void 0 : listenerTarget.addEventListener(eventName, lazyUpdatePosition);\n    });\n    prevTarget.current = listenerTarget;\n    prevListener.current = lazyUpdatePosition;\n  };\n  const removeListeners = () => {\n    if (timer.current) {\n      clearTimeout(timer.current);\n      timer.current = null;\n    }\n    const newTarget = targetFunc === null || targetFunc === void 0 ? void 0 : targetFunc();\n    TRIGGER_EVENTS.forEach(eventName => {\n      var _a;\n      newTarget === null || newTarget === void 0 ? void 0 : newTarget.removeEventListener(eventName, lazyUpdatePosition);\n      if (prevListener.current) {\n        (_a = prevTarget.current) === null || _a === void 0 ? void 0 : _a.removeEventListener(eventName, prevListener.current);\n      }\n    });\n    updatePosition.cancel();\n    lazyUpdatePosition.cancel();\n  };\n  React.useImperativeHandle(ref, () => ({\n    updatePosition\n  }));\n  // mount & unmount\n  React.useEffect(() => {\n    // [Legacy] Wait for parent component ref has its value.\n    // We should use target as directly element instead of function which makes element check hard.\n    timer.current = setTimeout(addListeners);\n    return () => removeListeners();\n  }, []);\n  React.useEffect(() => {\n    addListeners();\n    return () => removeListeners();\n  }, [target, affixStyle, lastAffix, offsetTop, offsetBottom]);\n  React.useEffect(() => {\n    updatePosition();\n  }, [target, offsetTop, offsetBottom]);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(affixPrefixCls);\n  const rootCls = classNames(rootClassName, hashId, affixPrefixCls, cssVarCls);\n  const mergedCls = classNames({\n    [rootCls]: affixStyle\n  });\n  return wrapCSSVar(/*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: updatePosition\n  }, /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    style: style,\n    className: className,\n    ref: placeholderNodeRef\n  }, restProps), affixStyle && /*#__PURE__*/React.createElement(\"div\", {\n    style: placeholderStyle,\n    \"aria-hidden\": \"true\"\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: mergedCls,\n    ref: fixedNodeRef,\n    style: affixStyle\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: updatePosition\n  }, children)))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Affix.displayName = 'Affix';\n}\nexport default Affix;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}