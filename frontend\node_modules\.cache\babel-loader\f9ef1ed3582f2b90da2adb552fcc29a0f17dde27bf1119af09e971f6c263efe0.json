{"ast": null, "code": "'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\nvar isArray = function isArray(arr) {\n  if (typeof Array.isArray === 'function') {\n    return Array.isArray(arr);\n  }\n  return toStr.call(arr) === '[object Array]';\n};\nvar isPlainObject = function isPlainObject(obj) {\n  if (!obj || toStr.call(obj) !== '[object Object]') {\n    return false;\n  }\n  var hasOwnConstructor = hasOwn.call(obj, 'constructor');\n  var hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n  // Not own constructor property must be Object\n  if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n    return false;\n  }\n\n  // Own properties are enumerated firstly, so to speed up,\n  // if last one is own, then all properties are own.\n  var key;\n  for (key in obj) {/**/}\n  return typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n  if (defineProperty && options.name === '__proto__') {\n    defineProperty(target, options.name, {\n      enumerable: true,\n      configurable: true,\n      value: options.newValue,\n      writable: true\n    });\n  } else {\n    target[options.name] = options.newValue;\n  }\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n  if (name === '__proto__') {\n    if (!hasOwn.call(obj, name)) {\n      return void 0;\n    } else if (gOPD) {\n      // In early versions of node, obj['__proto__'] is buggy when obj has\n      // __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n      return gOPD(obj, name).value;\n    }\n  }\n  return obj[name];\n};\nmodule.exports = function extend() {\n  var options, name, src, copy, copyIsArray, clone;\n  var target = arguments[0];\n  var i = 1;\n  var length = arguments.length;\n  var deep = false;\n\n  // Handle a deep copy situation\n  if (typeof target === 'boolean') {\n    deep = target;\n    target = arguments[1] || {};\n    // skip the boolean and the target\n    i = 2;\n  }\n  if (target == null || typeof target !== 'object' && typeof target !== 'function') {\n    target = {};\n  }\n  for (; i < length; ++i) {\n    options = arguments[i];\n    // Only deal with non-null/undefined values\n    if (options != null) {\n      // Extend the base object\n      for (name in options) {\n        src = getProperty(target, name);\n        copy = getProperty(options, name);\n\n        // Prevent never-ending loop\n        if (target !== copy) {\n          // Recurse if we're merging plain objects or arrays\n          if (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n            if (copyIsArray) {\n              copyIsArray = false;\n              clone = src && isArray(src) ? src : [];\n            } else {\n              clone = src && isPlainObject(src) ? src : {};\n            }\n\n            // Never move original objects, clone them\n            setProperty(target, {\n              name: name,\n              newValue: extend(deep, clone, copy)\n            });\n\n            // Don't bring in undefined values\n          } else if (typeof copy !== 'undefined') {\n            setProperty(target, {\n              name: name,\n              newValue: copy\n            });\n          }\n        }\n      }\n    }\n  }\n\n  // Return the modified object\n  return target;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}