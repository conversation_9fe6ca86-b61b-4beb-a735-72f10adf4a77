{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { forwardRef, useContext, useImperativeHandle } from 'react';\nimport CalendarOutlined from \"@ant-design/icons/es/icons/CalendarOutlined\";\nimport ClockCircleOutlined from \"@ant-design/icons/es/icons/ClockCircleOutlined\";\nimport cls from 'classnames';\nimport RCPicker from 'rc-picker';\nimport ContextIsolator from '../../_util/ContextIsolator';\nimport { useZIndex } from '../../_util/hooks/useZIndex';\nimport { getMergedStatus, getStatusClassNames } from '../../_util/statusUtils';\nimport { devUseWarning } from '../../_util/warning';\nimport { ConfigContext } from '../../config-provider';\nimport DisabledContext from '../../config-provider/DisabledContext';\nimport useCSSVarCls from '../../config-provider/hooks/useCSSVarCls';\nimport useSize from '../../config-provider/hooks/useSize';\nimport { FormItemInputContext } from '../../form/context';\nimport useVariant from '../../form/hooks/useVariants';\nimport { useLocale } from '../../locale';\nimport { useCompactItemContext } from '../../space/Compact';\nimport enUS from '../locale/en_US';\nimport useStyle from '../style';\nimport { getPlaceholder, useIcons } from '../util';\nimport { MONTH, MONTHPICKER, QUARTER, QUARTERPICKER, TIME, TIMEPICKER, WEEK, WEEKPICKER, YEAR, YEARPICKER } from './constant';\nimport useComponents from './useComponents';\nimport useMergedPickerSemantic from '../hooks/useMergedPickerSemantic';\nconst generatePicker = generateConfig => {\n  const getPicker = (picker, displayName) => {\n    const consumerName = displayName === TIMEPICKER ? 'timePicker' : 'datePicker';\n    const Picker = /*#__PURE__*/forwardRef((props, ref) => {\n      var _a;\n      const {\n          prefixCls: customizePrefixCls,\n          getPopupContainer: customizeGetPopupContainer,\n          components,\n          style,\n          className,\n          rootClassName,\n          size: customizeSize,\n          bordered,\n          placement,\n          placeholder,\n          popupStyle,\n          popupClassName,\n          dropdownClassName,\n          disabled: customDisabled,\n          status: customStatus,\n          variant: customVariant,\n          onCalendarChange,\n          styles,\n          classNames\n        } = props,\n        restProps = __rest(props, [\"prefixCls\", \"getPopupContainer\", \"components\", \"style\", \"className\", \"rootClassName\", \"size\", \"bordered\", \"placement\", \"placeholder\", \"popupStyle\", \"popupClassName\", \"dropdownClassName\", \"disabled\", \"status\", \"variant\", \"onCalendarChange\", \"styles\", \"classNames\"]);\n      const {\n        getPrefixCls,\n        direction,\n        getPopupContainer,\n        // Consume different styles according to different names\n        [consumerName]: consumerStyle\n      } = useContext(ConfigContext);\n      const prefixCls = getPrefixCls('picker', customizePrefixCls);\n      const {\n        compactSize,\n        compactItemClassnames\n      } = useCompactItemContext(prefixCls, direction);\n      const innerRef = React.useRef(null);\n      const [variant, enableVariantCls] = useVariant('datePicker', customVariant, bordered);\n      const rootCls = useCSSVarCls(prefixCls);\n      const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n      useImperativeHandle(ref, () => innerRef.current);\n      const additionalProps = {\n        showToday: true\n      };\n      const mergedPicker = picker || props.picker;\n      const rootPrefixCls = getPrefixCls();\n      // ==================== Legacy =====================\n      const {\n        onSelect,\n        multiple\n      } = restProps;\n      const hasLegacyOnSelect = onSelect && picker === 'time' && !multiple;\n      const onInternalCalendarChange = (date, dateStr, info) => {\n        onCalendarChange === null || onCalendarChange === void 0 ? void 0 : onCalendarChange(date, dateStr, info);\n        if (hasLegacyOnSelect) {\n          onSelect(date);\n        }\n      };\n      // =================== Warning =====================\n      if (process.env.NODE_ENV !== 'production') {\n        const warning = devUseWarning(displayName || 'DatePicker');\n        process.env.NODE_ENV !== \"production\" ? warning(picker !== 'quarter', 'deprecated', `DatePicker.${displayName} is legacy usage. Please use DatePicker[picker='${picker}'] directly.`) : void 0;\n        // ==================== Deprecated =====================\n        const deprecatedProps = {\n          dropdownClassName: 'classNames.popup.root',\n          popupClassName: 'classNames.popup.root',\n          popupStyle: 'styles.popup.root',\n          bordered: 'variant',\n          onSelect: 'onCalendarChange'\n        };\n        Object.entries(deprecatedProps).forEach(([oldProp, newProp]) => {\n          warning.deprecated(!(oldProp in props), oldProp, newProp);\n        });\n      }\n      const [mergedClassNames, mergedStyles] = useMergedPickerSemantic(consumerName, classNames, styles, popupClassName || dropdownClassName, popupStyle);\n      // ===================== Icon =====================\n      const [mergedAllowClear, removeIcon] = useIcons(props, prefixCls);\n      // ================== components ==================\n      const mergedComponents = useComponents(components);\n      // ===================== Size =====================\n      const mergedSize = useSize(ctx => {\n        var _a;\n        return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : compactSize) !== null && _a !== void 0 ? _a : ctx;\n      });\n      // ===================== Disabled =====================\n      const disabled = React.useContext(DisabledContext);\n      const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n      // ===================== FormItemInput =====================\n      const formItemContext = useContext(FormItemInputContext);\n      const {\n        hasFeedback,\n        status: contextStatus,\n        feedbackIcon\n      } = formItemContext;\n      const suffixNode = /*#__PURE__*/React.createElement(React.Fragment, null, mergedPicker === 'time' ? /*#__PURE__*/React.createElement(ClockCircleOutlined, null) : /*#__PURE__*/React.createElement(CalendarOutlined, null), hasFeedback && feedbackIcon);\n      const [contextLocale] = useLocale('DatePicker', enUS);\n      const locale = Object.assign(Object.assign({}, contextLocale), props.locale);\n      // ============================ zIndex ============================\n      const [zIndex] = useZIndex('DatePicker', (_a = mergedStyles.popup.root) === null || _a === void 0 ? void 0 : _a.zIndex);\n      return wrapCSSVar(/*#__PURE__*/React.createElement(ContextIsolator, {\n        space: true\n      }, /*#__PURE__*/React.createElement(RCPicker, Object.assign({\n        ref: innerRef,\n        placeholder: getPlaceholder(locale, mergedPicker, placeholder),\n        suffixIcon: suffixNode,\n        placement: placement,\n        prevIcon: /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-prev-icon`\n        }),\n        nextIcon: /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-next-icon`\n        }),\n        superPrevIcon: /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-super-prev-icon`\n        }),\n        superNextIcon: /*#__PURE__*/React.createElement(\"span\", {\n          className: `${prefixCls}-super-next-icon`\n        }),\n        transitionName: `${rootPrefixCls}-slide-up`,\n        picker: picker,\n        onCalendarChange: onInternalCalendarChange\n      }, additionalProps, restProps, {\n        locale: locale.lang,\n        className: cls({\n          [`${prefixCls}-${mergedSize}`]: mergedSize,\n          [`${prefixCls}-${variant}`]: enableVariantCls\n        }, getStatusClassNames(prefixCls, getMergedStatus(contextStatus, customStatus), hasFeedback), hashId, compactItemClassnames, consumerStyle === null || consumerStyle === void 0 ? void 0 : consumerStyle.className, className, cssVarCls, rootCls, rootClassName, mergedClassNames.root),\n        style: Object.assign(Object.assign(Object.assign({}, consumerStyle === null || consumerStyle === void 0 ? void 0 : consumerStyle.style), style), mergedStyles.root),\n        prefixCls: prefixCls,\n        getPopupContainer: customizeGetPopupContainer || getPopupContainer,\n        generateConfig: generateConfig,\n        components: mergedComponents,\n        direction: direction,\n        disabled: mergedDisabled,\n        classNames: {\n          popup: cls(hashId, cssVarCls, rootCls, rootClassName, mergedClassNames.popup.root)\n        },\n        styles: {\n          popup: Object.assign(Object.assign({}, mergedStyles.popup.root), {\n            zIndex\n          })\n        },\n        allowClear: mergedAllowClear,\n        removeIcon: removeIcon\n      }))));\n    });\n    if (process.env.NODE_ENV !== 'production' && displayName) {\n      Picker.displayName = displayName;\n    }\n    return Picker;\n  };\n  const DatePicker = getPicker();\n  const WeekPicker = getPicker(WEEK, WEEKPICKER);\n  const MonthPicker = getPicker(MONTH, MONTHPICKER);\n  const YearPicker = getPicker(YEAR, YEARPICKER);\n  const QuarterPicker = getPicker(QUARTER, QUARTERPICKER);\n  const TimePicker = getPicker(TIME, TIMEPICKER);\n  return {\n    DatePicker,\n    WeekPicker,\n    MonthPicker,\n    YearPicker,\n    TimePicker,\n    QuarterPicker\n  };\n};\nexport default generatePicker;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}