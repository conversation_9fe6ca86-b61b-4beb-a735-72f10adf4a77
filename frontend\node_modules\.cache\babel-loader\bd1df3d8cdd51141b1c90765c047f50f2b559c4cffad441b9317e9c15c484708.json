{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getColorAlpha } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const alphaValue = value || internalValue;\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getColorAlpha(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}