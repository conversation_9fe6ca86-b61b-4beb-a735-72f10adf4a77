{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { getStyle as getCheckboxStyle } from '../../checkbox/style';\nimport { textEllipsis } from '../../style';\nconst getColumnsStyle = token => {\n  const {\n    prefixCls,\n    componentCls\n  } = token;\n  const cascaderMenuItemCls = `${componentCls}-menu-item`;\n  const iconCls = `\n  &${cascaderMenuItemCls}-expand ${cascaderMenuItemCls}-expand-icon,\n  ${cascaderMenuItemCls}-loading-icon\n`;\n  return [\n  // ==================== Checkbox ====================\n  getCheckboxStyle(`${prefixCls}-checkbox`, token), {\n    [componentCls]: {\n      // ================== Checkbox ==================\n      '&-checkbox': {\n        top: 0,\n        marginInlineEnd: token.paddingXS,\n        pointerEvents: 'unset'\n      },\n      // ==================== Menu ====================\n      // >>> Menus\n      '&-menus': {\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'flex-start',\n        [`&${componentCls}-menu-empty`]: {\n          [`${componentCls}-menu`]: {\n            width: '100%',\n            height: 'auto',\n            [cascaderMenuItemCls]: {\n              color: token.colorTextDisabled\n            }\n          }\n        }\n      },\n      // >>> Menu\n      '&-menu': {\n        flexGrow: 1,\n        flexShrink: 0,\n        minWidth: token.controlItemWidth,\n        height: token.dropdownHeight,\n        margin: 0,\n        padding: token.menuPadding,\n        overflow: 'auto',\n        verticalAlign: 'top',\n        listStyle: 'none',\n        '-ms-overflow-style': '-ms-autohiding-scrollbar',\n        // https://github.com/ant-design/ant-design/issues/11857\n        '&:not(:last-child)': {\n          borderInlineEnd: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        },\n        '&-item': Object.assign(Object.assign({}, textEllipsis), {\n          display: 'flex',\n          flexWrap: 'nowrap',\n          alignItems: 'center',\n          padding: token.optionPadding,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: `all ${token.motionDurationMid}`,\n          borderRadius: token.borderRadiusSM,\n          '&:hover': {\n            background: token.controlItemBgHover\n          },\n          '&-disabled': {\n            color: token.colorTextDisabled,\n            cursor: 'not-allowed',\n            '&:hover': {\n              background: 'transparent'\n            },\n            [iconCls]: {\n              color: token.colorTextDisabled\n            }\n          },\n          [`&-active:not(${cascaderMenuItemCls}-disabled)`]: {\n            '&, &:hover': {\n              color: token.optionSelectedColor,\n              fontWeight: token.optionSelectedFontWeight,\n              backgroundColor: token.optionSelectedBg\n            }\n          },\n          '&-content': {\n            flex: 'auto'\n          },\n          [iconCls]: {\n            marginInlineStart: token.paddingXXS,\n            color: token.colorIcon,\n            fontSize: token.fontSizeIcon\n          },\n          '&-keyword': {\n            color: token.colorHighlight\n          }\n        })\n      }\n    }\n  }];\n};\nexport default getColumnsStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}