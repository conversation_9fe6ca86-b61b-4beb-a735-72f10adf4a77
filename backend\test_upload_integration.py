#!/usr/bin/env python3
"""
测试新的作业上传功能集成
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.file_manager import FileManager

def test_upload_integration():
    """测试上传功能集成"""
    print("🧪 测试作业上传功能集成...")
    
    file_manager = FileManager()
    
    # 模拟作业上传参数
    school_id = 1
    grade_class_code = "701"
    subject = "math"
    assignment_id = 123
    assignment_name = "期中考试"
    user_id = 12345
    homework_id = 67890
    
    # 测试多页作业上传
    for page_number in range(1, 4):  # 3页作业
        print(f"\n📄 测试第 {page_number} 页上传...")
        
        # 生成文件路径
        file_info = file_manager.generate_homework_file_path(
            school_id=school_id,
            grade_class_code=grade_class_code,
            subject=subject,
            assignment_id=assignment_id,
            assignment_name=assignment_name,
            user_id=user_id,
            homework_id=homework_id,
            page_number=page_number,
            file_extension=".jpg",
            is_annotated=False
        )
        
        print(f"   生成的文件路径: {file_info['url_path']}")
        print(f"   完整路径: {file_info['full_path']}")
        
        # 模拟图片内容
        test_content = f"test homework page {page_number} content".encode()
        
        # 保存文件
        success = file_manager.save_file(test_content, file_info)
        
        if success:
            print(f"   ✅ 第 {page_number} 页保存成功")
            
            # 验证文件是否存在
            if os.path.exists(file_info['full_path']):
                print(f"   ✅ 文件确实存在于磁盘上")
                
                # 验证文件内容
                with open(file_info['full_path'], 'rb') as f:
                    saved_content = f.read()
                    if saved_content == test_content:
                        print(f"   ✅ 文件内容正确")
                    else:
                        print(f"   ❌ 文件内容不匹配")
            else:
                print(f"   ❌ 文件不存在于磁盘上")
        else:
            print(f"   ❌ 第 {page_number} 页保存失败")
    
    # 测试批注文件生成
    print(f"\n📝 测试批注文件生成...")
    
    annotated_info = file_manager.generate_homework_file_path(
        school_id=school_id,
        grade_class_code=grade_class_code,
        subject=subject,
        assignment_id=assignment_id,
        assignment_name=assignment_name,
        user_id=user_id,
        homework_id=homework_id,
        page_number=1,
        file_extension=".jpg",
        is_annotated=True
    )
    
    print(f"   批注文件路径: {annotated_info['url_path']}")
    
    # 模拟批注内容
    annotated_content = b"test annotated content"
    
    # 保存批注文件
    success = file_manager.save_file(annotated_content, annotated_info)
    
    if success:
        print(f"   ✅ 批注文件保存成功")
    else:
        print(f"   ❌ 批注文件保存失败")
    
    # 检查目录结构
    print(f"\n📁 检查生成的目录结构...")
    
    expected_dirs = [
        f"uploads/schools/school_{school_id:03d}/grade_{grade_class_code}/subjects/{subject}/assignments/assignment_{assignment_id}_{assignment_name}/original",
        f"uploads/schools/school_{school_id:03d}/grade_{grade_class_code}/subjects/{subject}/assignments/assignment_{assignment_id}_{assignment_name}/annotated"
    ]
    
    for expected_dir in expected_dirs:
        if os.path.exists(expected_dir):
            print(f"   ✅ 目录存在: {expected_dir}")
            
            # 列出目录中的文件
            files = os.listdir(expected_dir)
            if files:
                print(f"      文件: {', '.join(files)}")
            else:
                print(f"      目录为空")
        else:
            print(f"   ❌ 目录不存在: {expected_dir}")

def test_url_path_format():
    """测试URL路径格式"""
    print(f"\n🔗 测试URL路径格式...")
    
    file_manager = FileManager()
    
    file_info = file_manager.generate_homework_file_path(
        school_id=1,
        grade_class_code="701",
        subject="math",
        assignment_id=123,
        assignment_name="期中考试",
        user_id=12345,
        homework_id=67890,
        page_number=1,
        file_extension=".jpg",
        is_annotated=False
    )
    
    url_path = file_info['url_path']
    print(f"   生成的URL路径: {url_path}")
    
    # 验证URL路径格式
    expected_parts = [
        "/uploads/schools/school_001/grade_701/subjects/math/assignments/assignment_123_期中考试/original/",
        "user_12345_",
        "_hw67890_p1.jpg"
    ]
    
    all_parts_found = True
    for part in expected_parts:
        if part not in url_path:
            print(f"   ❌ URL路径中缺少部分: {part}")
            all_parts_found = False
    
    if all_parts_found:
        print(f"   ✅ URL路径格式正确")
    else:
        print(f"   ❌ URL路径格式不正确")

if __name__ == "__main__":
    print("🚀 开始测试作业上传功能集成...")
    
    try:
        test_upload_integration()
        test_url_path_format()
        
        print("\n🎉 所有集成测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
