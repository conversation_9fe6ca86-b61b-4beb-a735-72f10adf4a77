{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\utils\\\\roleUtils.js\";\nimport React from 'react';\nimport { Tag, Space } from 'antd';\n\n/**\n * 专业角色颜色体系设计\n * 基于学校角色权限分析与设计方案的12种角色颜色体系\n *\n * 颜色层次体系：\n * - 红色系：管理员角色，突出最高权限\n * - 紫色系：高级管理角色，体现领导地位\n * - 蓝色系：中级管理角色，专业管理职能\n * - 绿色系：教学角色，统一教学身份\n * - 其他颜色：基础用户角色\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const getRoleColor = roleName => {\n  const roleColorMap = {\n    // 管理员角色 - 红色系（最高权限）\n    '超级管理员': 'red',\n    // 系统最高管理者，可管理所有学校\n    '学校管理员': 'orange',\n    // 学校技术管理员，权限限制在学校内\n\n    // 高级管理角色 - 紫色系（学校领导）\n    '校长': 'purple',\n    // 学校最高领导\n    '副校长': 'purple',\n    // 协助校长管理学校（统一为紫色）\n\n    // 中级管理角色 - 蓝色系（部门管理）\n    '教务处主任': 'blue',\n    // 负责教学管理和教务安排\n    '年级组长': 'cyan',\n    // 管理特定年级的教学和学生\n    '教研组长': 'geekblue',\n    // 管理特定学科的教学和研究\n    '备课组长': 'lime',\n    // 管理特定学科特定年级的备课\n\n    // 教学角色 - 绿色系（统一教学身份）\n    '班主任': 'green',\n    // 管理特定班级的学生和教学\n    '教师': 'green',\n    // 进行教学工作，管理自己的课程和作业\n\n    // 基础用户角色 - 其他颜色\n    '学生': 'default',\n    // 管理个人学习数据\n    '家长': 'gold',\n    // 查看关联学生的学习情况\n\n    // 兼容旧系统的角色名称\n    '管理员': 'orange' // 映射为学校管理员颜色\n  };\n  return roleColorMap[roleName] || 'default';\n};\n\n// 获取用户角色标签组件\nexport const getUserRoleTags = user => {\n  if (!user) return null;\n  const roles = [];\n\n  // 主要角色（优先级最高）\n  if (user.role) {\n    const color = getRoleColor(user.role);\n    roles.push(/*#__PURE__*/_jsxDEV(Tag, {\n      color: color,\n      children: user.role\n    }, \"main-role\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 16\n    }, this));\n  } else if (user.is_admin && user.is_teacher) {\n    roles.push(/*#__PURE__*/_jsxDEV(Tag, {\n      color: \"red\",\n      children: \"\\u8D85\\u7EA7\\u7BA1\\u7406\\u5458\"\n    }, \"admin\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 16\n    }, this)); // 既是管理员又是教师，权限最高\n  } else if (user.is_admin) {\n    roles.push(/*#__PURE__*/_jsxDEV(Tag, {\n      color: \"orange\",\n      children: \"\\u5B66\\u6821\\u7BA1\\u7406\\u5458\"\n    }, \"admin\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 16\n    }, this)); // 只是管理员，权限限制在学校内\n  }\n\n  // 附加角色 - 教师角色\n  if (user.is_teacher) {\n    // 如果主角色不是教师相关角色，则添加教师标签\n    const teacherRoles = ['班主任', '教师', '教研组长', '备课组长'];\n    const isMainRoleTeacher = user.role && teacherRoles.some(role => user.role.includes(role));\n    if (!isMainRoleTeacher) {\n      roles.push(/*#__PURE__*/_jsxDEV(Tag, {\n        color: \"green\",\n        children: \"\\u6559\\u5E08\"\n      }, \"teacher\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 18\n      }, this));\n    }\n  }\n\n  // 如果用户有额外的角色信息（从user_roles表获取）\n  if (user.roles && Array.isArray(user.roles)) {\n    user.roles.forEach((role, index) => {\n      // 避免重复显示已经在主角色中显示的角色\n      const isDuplicate = user.role === role.name || user.is_admin && role.name.includes('管理员') || user.is_teacher && role.name === '教师';\n      if (!isDuplicate) {\n        const color = getRoleColor(role.name);\n        roles.push(/*#__PURE__*/_jsxDEV(Tag, {\n          color: color,\n          children: role.name\n        }, `additional-role-${index}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 20\n        }, this));\n      }\n    });\n  }\n\n  // 如果没有任何角色，默认为学生\n  if (roles.length === 0) {\n    roles.push(/*#__PURE__*/_jsxDEV(Tag, {\n      color: \"default\",\n      children: \"\\u5B66\\u751F\"\n    }, \"student\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 16\n    }, this));\n  }\n  return /*#__PURE__*/_jsxDEV(Space, {\n    children: roles\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 10\n  }, this);\n};\n\n// 获取简单的角色文本（用于头像下方显示）\nexport const getSimpleRoleText = user => {\n  if (!user) return '未知';\n  if (user.role) {\n    return user.role;\n  }\n  if (user.is_admin && user.is_teacher) {\n    return '超级管理员'; // 既是管理员又是教师，权限最高\n  }\n  if (user.is_admin) {\n    return '学校管理员'; // 只是管理员，权限限制在学校内\n  }\n  if (user.is_teacher) {\n    return '教师';\n  }\n  return '学生';\n};\n\n// 获取用户的所有角色名称数组\nexport const getUserRoleNames = user => {\n  if (!user) return [];\n  const roleNames = [];\n\n  // 主要角色\n  if (user.role) {\n    roleNames.push(user.role);\n  } else if (user.is_admin && user.is_teacher) {\n    roleNames.push('超级管理员'); // 既是管理员又是教师，权限最高\n  } else if (user.is_admin) {\n    roleNames.push('学校管理员'); // 只是管理员，权限限制在学校内\n  }\n\n  // 教师角色\n  if (user.is_teacher) {\n    const teacherRoles = ['班主任', '教师', '教研组长', '备课组长'];\n    const isMainRoleTeacher = user.role && teacherRoles.some(role => user.role.includes(role));\n    if (!isMainRoleTeacher) {\n      roleNames.push('教师');\n    }\n  }\n\n  // 额外角色\n  if (user.roles && Array.isArray(user.roles)) {\n    user.roles.forEach(role => {\n      if (!roleNames.includes(role.name)) {\n        roleNames.push(role.name);\n      }\n    });\n  }\n\n  // 如果没有任何角色，默认为学生\n  if (roleNames.length === 0) {\n    roleNames.push('学生');\n  }\n  return roleNames;\n};\n\n/**\n * 角色级别映射\n * 用于权限检查和角色比较\n */\nexport const getRoleLevel = roleName => {\n  const roleLevelMap = {\n    '超级管理员': 100,\n    '校长': 90,\n    '副校长': 85,\n    '学校管理员': 80,\n    '教务处主任': 75,\n    '年级组长': 60,\n    '教研组长': 55,\n    '备课组长': 50,\n    '班主任': 40,\n    '教师': 30,\n    '家长': 20,\n    '学生': 10,\n    // 兼容旧系统\n    '管理员': 80\n  };\n  return roleLevelMap[roleName] || 0;\n};\n\n/**\n * 检查用户是否有特定权限\n * @param {Object} user - 用户对象\n * @param {string} permissionCode - 权限代码\n * @param {string} resource - 资源类型（可选）\n * @returns {boolean}\n */\nexport const hasPermission = (user, permissionCode, resource = null) => {\n  if (!user) return false;\n\n  // 超级管理员拥有所有权限\n  if (isSuperAdmin(user)) return true;\n\n  // 检查用户权限列表\n  if (user.permissions && Array.isArray(user.permissions)) {\n    return user.permissions.includes(permissionCode);\n  }\n\n  // 基于角色的简单权限检查\n  const userLevel = getRoleLevel(user.role || getSimpleRoleText(user));\n\n  // 系统级权限需要管理角色（级别40以上）\n  if (permissionCode.startsWith('system_') && userLevel >= 40) {\n    return true;\n  }\n\n  // 教师级权限需要教师角色（级别30以上）\n  if (['homework_management', 'error_training', 'stats_report', 'homework_analysis'].includes(permissionCode) && userLevel >= 30) {\n    return true;\n  }\n  return false;\n};\n\n/**\n * 检查用户角色级别\n * @param {Object} user - 用户对象\n * @param {number} minLevel - 最低要求级别\n * @returns {boolean}\n */\nexport const hasRoleLevel = (user, minLevel) => {\n  if (!user) return false;\n  const userLevel = getRoleLevel(user.role || getSimpleRoleText(user));\n  return userLevel >= minLevel;\n};\n\n/**\n * 检查是否为超级管理员\n * @param {Object} user - 用户对象\n * @returns {boolean}\n */\nexport const isSuperAdmin = user => {\n  return user && (user.role === '超级管理员' || user.role_code === 'super_admin' || user.is_admin && user.is_teacher);\n};\n\n/**\n * 检查是否为学校管理员\n * @param {Object} user - 用户对象\n * @returns {boolean}\n */\nexport const isSchoolAdmin = user => {\n  return user && (user.role === '学校管理员' || user.role_code === 'school_admin' || user.is_admin && !user.is_teacher);\n};\n\n/**\n * 检查是否为教师\n * @param {Object} user - 用户对象\n * @returns {boolean}\n */\nexport const isTeacher = user => {\n  return user && (user.is_teacher || ['教师', '班主任', '备课组长', '教研组长', '年级组长', '教务处主任'].includes(user.role));\n};\n\n/**\n * 角色显示组件\n * @param {Object} props - 组件属性\n * @param {Object} props.user - 用户对象\n * @param {boolean} props.showMultiple - 是否显示多重角色\n * @param {string} props.size - 标签大小\n * @returns {JSX.Element}\n */\nexport const RoleDisplay = ({\n  user,\n  showMultiple = true,\n  size = 'default'\n}) => {\n  if (!showMultiple) {\n    // 简单显示模式\n    const roleText = getSimpleRoleText(user);\n    const color = getRoleColor(roleText);\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: color,\n      size: size,\n      children: roleText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 多重角色显示模式\n  const roleTags = getUserRoleTags(user);\n  return /*#__PURE__*/_jsxDEV(Space, {\n    size: \"small\",\n    wrap: true,\n    children: roleTags\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n};\n\n/**\n * 权限保护组件\n * @param {Object} props - 组件属性\n * @param {string} props.permission - 权限代码\n * @param {string} props.resource - 资源类型\n * @param {Object} props.user - 用户对象\n * @param {JSX.Element} props.children - 子组件\n * @returns {JSX.Element|null}\n */\n_c = RoleDisplay;\nexport const PermissionGuard = ({\n  permission,\n  resource,\n  children,\n  user\n}) => {\n  const hasPermissionResult = hasPermission(user, permission, resource);\n  if (!hasPermissionResult) return null;\n  return children;\n};\n_c2 = PermissionGuard;\nvar _c, _c2;\n$RefreshReg$(_c, \"RoleDisplay\");\n$RefreshReg$(_c2, \"PermissionGuard\");", "map": {"version": 3, "names": ["React", "Tag", "Space", "jsxDEV", "_jsxDEV", "getRoleColor", "<PERSON><PERSON><PERSON>", "roleColorMap", "getUserRoleTags", "user", "roles", "role", "color", "push", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "is_admin", "is_teacher", "teacher<PERSON><PERSON><PERSON>", "isMain<PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "includes", "Array", "isArray", "for<PERSON>ach", "index", "isDuplicate", "name", "length", "getSimpleRoleText", "getUserRoleNames", "roleNames", "getRoleLevel", "roleLevelMap", "hasPermission", "permissionCode", "resource", "isSuperAdmin", "permissions", "userLevel", "startsWith", "hasRoleLevel", "minLevel", "role_code", "isSchoolAdmin", "<PERSON><PERSON><PERSON>er", "RoleDisplay", "showMultiple", "size", "roleText", "roleTags", "wrap", "_c", "PermissionGuard", "permission", "hasPermissionResult", "_c2", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/utils/roleUtils.js"], "sourcesContent": ["import React from 'react';\nimport { Tag, Space } from 'antd';\n\n/**\n * 专业角色颜色体系设计\n * 基于学校角色权限分析与设计方案的12种角色颜色体系\n *\n * 颜色层次体系：\n * - 红色系：管理员角色，突出最高权限\n * - 紫色系：高级管理角色，体现领导地位\n * - 蓝色系：中级管理角色，专业管理职能\n * - 绿色系：教学角色，统一教学身份\n * - 其他颜色：基础用户角色\n */\nexport const getRoleColor = (roleName) => {\n  const roleColorMap = {\n    // 管理员角色 - 红色系（最高权限）\n    '超级管理员': 'red',        // 系统最高管理者，可管理所有学校\n    '学校管理员': 'orange',     // 学校技术管理员，权限限制在学校内\n\n    // 高级管理角色 - 紫色系（学校领导）\n    '校长': 'purple',           // 学校最高领导\n    '副校长': 'purple',         // 协助校长管理学校（统一为紫色）\n\n    // 中级管理角色 - 蓝色系（部门管理）\n    '教务处主任': 'blue',       // 负责教学管理和教务安排\n    '年级组长': 'cyan',         // 管理特定年级的教学和学生\n    '教研组长': 'geekblue',     // 管理特定学科的教学和研究\n    '备课组长': 'lime',         // 管理特定学科特定年级的备课\n\n    // 教学角色 - 绿色系（统一教学身份）\n    '班主任': 'green',          // 管理特定班级的学生和教学\n    '教师': 'green',            // 进行教学工作，管理自己的课程和作业\n\n    // 基础用户角色 - 其他颜色\n    '学生': 'default',          // 管理个人学习数据\n    '家长': 'gold',             // 查看关联学生的学习情况\n\n    // 兼容旧系统的角色名称\n    '管理员': 'orange'          // 映射为学校管理员颜色\n  };\n\n  return roleColorMap[roleName] || 'default';\n};\n\n// 获取用户角色标签组件\nexport const getUserRoleTags = (user) => {\n  if (!user) return null;\n  \n  const roles = [];\n\n  // 主要角色（优先级最高）\n  if (user.role) {\n    const color = getRoleColor(user.role);\n    roles.push(<Tag color={color} key=\"main-role\">{user.role}</Tag>);\n  } else if (user.is_admin && user.is_teacher) {\n    roles.push(<Tag color=\"red\" key=\"admin\">超级管理员</Tag>);  // 既是管理员又是教师，权限最高\n  } else if (user.is_admin) {\n    roles.push(<Tag color=\"orange\" key=\"admin\">学校管理员</Tag>);  // 只是管理员，权限限制在学校内\n  }\n\n  // 附加角色 - 教师角色\n  if (user.is_teacher) {\n    // 如果主角色不是教师相关角色，则添加教师标签\n    const teacherRoles = ['班主任', '教师', '教研组长', '备课组长'];\n    const isMainRoleTeacher = user.role && teacherRoles.some(role => user.role.includes(role));\n    \n    if (!isMainRoleTeacher) {\n      roles.push(<Tag color=\"green\" key=\"teacher\">教师</Tag>);\n    }\n  }\n\n  // 如果用户有额外的角色信息（从user_roles表获取）\n  if (user.roles && Array.isArray(user.roles)) {\n    user.roles.forEach((role, index) => {\n      // 避免重复显示已经在主角色中显示的角色\n      const isDuplicate = user.role === role.name || \n                         (user.is_admin && role.name.includes('管理员')) ||\n                         (user.is_teacher && role.name === '教师');\n      \n      if (!isDuplicate) {\n        const color = getRoleColor(role.name);\n        roles.push(<Tag color={color} key={`additional-role-${index}`}>{role.name}</Tag>);\n      }\n    });\n  }\n\n  // 如果没有任何角色，默认为学生\n  if (roles.length === 0) {\n    roles.push(<Tag color=\"default\" key=\"student\">学生</Tag>);\n  }\n\n  return <Space>{roles}</Space>;\n};\n\n// 获取简单的角色文本（用于头像下方显示）\nexport const getSimpleRoleText = (user) => {\n  if (!user) return '未知';\n\n  if (user.role) {\n    return user.role;\n  }\n  if (user.is_admin && user.is_teacher) {\n    return '超级管理员';  // 既是管理员又是教师，权限最高\n  }\n  if (user.is_admin) {\n    return '学校管理员';  // 只是管理员，权限限制在学校内\n  }\n  if (user.is_teacher) {\n    return '教师';\n  }\n  return '学生';\n};\n\n// 获取用户的所有角色名称数组\nexport const getUserRoleNames = (user) => {\n  if (!user) return [];\n  \n  const roleNames = [];\n  \n  // 主要角色\n  if (user.role) {\n    roleNames.push(user.role);\n  } else if (user.is_admin && user.is_teacher) {\n    roleNames.push('超级管理员');  // 既是管理员又是教师，权限最高\n  } else if (user.is_admin) {\n    roleNames.push('学校管理员');  // 只是管理员，权限限制在学校内\n  }\n  \n  // 教师角色\n  if (user.is_teacher) {\n    const teacherRoles = ['班主任', '教师', '教研组长', '备课组长'];\n    const isMainRoleTeacher = user.role && teacherRoles.some(role => user.role.includes(role));\n    \n    if (!isMainRoleTeacher) {\n      roleNames.push('教师');\n    }\n  }\n  \n  // 额外角色\n  if (user.roles && Array.isArray(user.roles)) {\n    user.roles.forEach(role => {\n      if (!roleNames.includes(role.name)) {\n        roleNames.push(role.name);\n      }\n    });\n  }\n  \n  // 如果没有任何角色，默认为学生\n  if (roleNames.length === 0) {\n    roleNames.push('学生');\n  }\n  \n  return roleNames;\n};\n\n/**\n * 角色级别映射\n * 用于权限检查和角色比较\n */\nexport const getRoleLevel = (roleName) => {\n  const roleLevelMap = {\n    '超级管理员': 100,\n    '校长': 90,\n    '副校长': 85,\n    '学校管理员': 80,\n    '教务处主任': 75,\n    '年级组长': 60,\n    '教研组长': 55,\n    '备课组长': 50,\n    '班主任': 40,\n    '教师': 30,\n    '家长': 20,\n    '学生': 10,\n    // 兼容旧系统\n    '管理员': 80\n  };\n\n  return roleLevelMap[roleName] || 0;\n};\n\n/**\n * 检查用户是否有特定权限\n * @param {Object} user - 用户对象\n * @param {string} permissionCode - 权限代码\n * @param {string} resource - 资源类型（可选）\n * @returns {boolean}\n */\nexport const hasPermission = (user, permissionCode, resource = null) => {\n  if (!user) return false;\n\n  // 超级管理员拥有所有权限\n  if (isSuperAdmin(user)) return true;\n\n  // 检查用户权限列表\n  if (user.permissions && Array.isArray(user.permissions)) {\n    return user.permissions.includes(permissionCode);\n  }\n\n  // 基于角色的简单权限检查\n  const userLevel = getRoleLevel(user.role || getSimpleRoleText(user));\n\n  // 系统级权限需要管理角色（级别40以上）\n  if (permissionCode.startsWith('system_') && userLevel >= 40) {\n    return true;\n  }\n\n  // 教师级权限需要教师角色（级别30以上）\n  if (['homework_management', 'error_training', 'stats_report', 'homework_analysis'].includes(permissionCode) && userLevel >= 30) {\n    return true;\n  }\n\n  return false;\n};\n\n/**\n * 检查用户角色级别\n * @param {Object} user - 用户对象\n * @param {number} minLevel - 最低要求级别\n * @returns {boolean}\n */\nexport const hasRoleLevel = (user, minLevel) => {\n  if (!user) return false;\n\n  const userLevel = getRoleLevel(user.role || getSimpleRoleText(user));\n  return userLevel >= minLevel;\n};\n\n/**\n * 检查是否为超级管理员\n * @param {Object} user - 用户对象\n * @returns {boolean}\n */\nexport const isSuperAdmin = (user) => {\n  return user && (\n    user.role === '超级管理员' ||\n    user.role_code === 'super_admin' ||\n    (user.is_admin && user.is_teacher)\n  );\n};\n\n/**\n * 检查是否为学校管理员\n * @param {Object} user - 用户对象\n * @returns {boolean}\n */\nexport const isSchoolAdmin = (user) => {\n  return user && (\n    user.role === '学校管理员' ||\n    user.role_code === 'school_admin' ||\n    (user.is_admin && !user.is_teacher)\n  );\n};\n\n/**\n * 检查是否为教师\n * @param {Object} user - 用户对象\n * @returns {boolean}\n */\nexport const isTeacher = (user) => {\n  return user && (\n    user.is_teacher ||\n    ['教师', '班主任', '备课组长', '教研组长', '年级组长', '教务处主任'].includes(user.role)\n  );\n};\n\n/**\n * 角色显示组件\n * @param {Object} props - 组件属性\n * @param {Object} props.user - 用户对象\n * @param {boolean} props.showMultiple - 是否显示多重角色\n * @param {string} props.size - 标签大小\n * @returns {JSX.Element}\n */\nexport const RoleDisplay = ({ user, showMultiple = true, size = 'default' }) => {\n  if (!showMultiple) {\n    // 简单显示模式\n    const roleText = getSimpleRoleText(user);\n    const color = getRoleColor(roleText);\n    return <Tag color={color} size={size}>{roleText}</Tag>;\n  }\n\n  // 多重角色显示模式\n  const roleTags = getUserRoleTags(user);\n  return (\n    <Space size=\"small\" wrap>\n      {roleTags}\n    </Space>\n  );\n};\n\n/**\n * 权限保护组件\n * @param {Object} props - 组件属性\n * @param {string} props.permission - 权限代码\n * @param {string} props.resource - 资源类型\n * @param {Object} props.user - 用户对象\n * @param {JSX.Element} props.children - 子组件\n * @returns {JSX.Element|null}\n */\nexport const PermissionGuard = ({ permission, resource, children, user }) => {\n  const hasPermissionResult = hasPermission(user, permission, resource);\n\n  if (!hasPermissionResult) return null;\n  return children;\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,KAAK,QAAQ,MAAM;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,OAAO,MAAMC,YAAY,GAAIC,QAAQ,IAAK;EACxC,MAAMC,YAAY,GAAG;IACnB;IACA,OAAO,EAAE,KAAK;IAAS;IACvB,OAAO,EAAE,QAAQ;IAAM;;IAEvB;IACA,IAAI,EAAE,QAAQ;IAAY;IAC1B,KAAK,EAAE,QAAQ;IAAU;;IAEzB;IACA,OAAO,EAAE,MAAM;IAAQ;IACvB,MAAM,EAAE,MAAM;IAAU;IACxB,MAAM,EAAE,UAAU;IAAM;IACxB,MAAM,EAAE,MAAM;IAAU;;IAExB;IACA,KAAK,EAAE,OAAO;IAAW;IACzB,IAAI,EAAE,OAAO;IAAa;;IAE1B;IACA,IAAI,EAAE,SAAS;IAAW;IAC1B,IAAI,EAAE,MAAM;IAAc;;IAE1B;IACA,KAAK,EAAE,QAAQ,CAAU;EAC3B,CAAC;EAED,OAAOA,YAAY,CAACD,QAAQ,CAAC,IAAI,SAAS;AAC5C,CAAC;;AAED;AACA,OAAO,MAAME,eAAe,GAAIC,IAAI,IAAK;EACvC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;EAEtB,MAAMC,KAAK,GAAG,EAAE;;EAEhB;EACA,IAAID,IAAI,CAACE,IAAI,EAAE;IACb,MAAMC,KAAK,GAAGP,YAAY,CAACI,IAAI,CAACE,IAAI,CAAC;IACrCD,KAAK,CAACG,IAAI,cAACT,OAAA,CAACH,GAAG;MAACW,KAAK,EAAEA,KAAM;MAAAE,QAAA,EAAkBL,IAAI,CAACE;IAAI,GAAtB,WAAW;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB,CAAC,CAAC;EAClE,CAAC,MAAM,IAAIT,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACW,UAAU,EAAE;IAC3CV,KAAK,CAACG,IAAI,cAACT,OAAA,CAACH,GAAG;MAACW,KAAK,EAAC,KAAK;MAAAE,QAAA,EAAa;IAAK,GAAb,OAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,CAAC,CAAC,CAAE;EACzD,CAAC,MAAM,IAAIT,IAAI,CAACU,QAAQ,EAAE;IACxBT,KAAK,CAACG,IAAI,cAACT,OAAA,CAACH,GAAG;MAACW,KAAK,EAAC,QAAQ;MAAAE,QAAA,EAAa;IAAK,GAAb,OAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,CAAC,CAAC,CAAE;EAC5D;;EAEA;EACA,IAAIT,IAAI,CAACW,UAAU,EAAE;IACnB;IACA,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;IAClD,MAAMC,iBAAiB,GAAGb,IAAI,CAACE,IAAI,IAAIU,YAAY,CAACE,IAAI,CAACZ,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACa,QAAQ,CAACb,IAAI,CAAC,CAAC;IAE1F,IAAI,CAACW,iBAAiB,EAAE;MACtBZ,KAAK,CAACG,IAAI,cAACT,OAAA,CAACH,GAAG;QAACW,KAAK,EAAC,OAAO;QAAAE,QAAA,EAAe;MAAE,GAAZ,SAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,CAAC;IACvD;EACF;;EAEA;EACA,IAAIT,IAAI,CAACC,KAAK,IAAIe,KAAK,CAACC,OAAO,CAACjB,IAAI,CAACC,KAAK,CAAC,EAAE;IAC3CD,IAAI,CAACC,KAAK,CAACiB,OAAO,CAAC,CAAChB,IAAI,EAAEiB,KAAK,KAAK;MAClC;MACA,MAAMC,WAAW,GAAGpB,IAAI,CAACE,IAAI,KAAKA,IAAI,CAACmB,IAAI,IACvBrB,IAAI,CAACU,QAAQ,IAAIR,IAAI,CAACmB,IAAI,CAACN,QAAQ,CAAC,KAAK,CAAE,IAC3Cf,IAAI,CAACW,UAAU,IAAIT,IAAI,CAACmB,IAAI,KAAK,IAAK;MAE1D,IAAI,CAACD,WAAW,EAAE;QAChB,MAAMjB,KAAK,GAAGP,YAAY,CAACM,IAAI,CAACmB,IAAI,CAAC;QACrCpB,KAAK,CAACG,IAAI,cAACT,OAAA,CAACH,GAAG;UAACW,KAAK,EAAEA,KAAM;UAAAE,QAAA,EAAmCH,IAAI,CAACmB;QAAI,GAAtC,mBAAmBF,KAAK,EAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,CAAC;MACnF;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIR,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAE;IACtBrB,KAAK,CAACG,IAAI,cAACT,OAAA,CAACH,GAAG;MAACW,KAAK,EAAC,SAAS;MAAAE,QAAA,EAAe;IAAE,GAAZ,SAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,CAAC;EACzD;EAEA,oBAAOd,OAAA,CAACF,KAAK;IAAAY,QAAA,EAAEJ;EAAK;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAQ,CAAC;AAC/B,CAAC;;AAED;AACA,OAAO,MAAMc,iBAAiB,GAAIvB,IAAI,IAAK;EACzC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;EAEtB,IAAIA,IAAI,CAACE,IAAI,EAAE;IACb,OAAOF,IAAI,CAACE,IAAI;EAClB;EACA,IAAIF,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACW,UAAU,EAAE;IACpC,OAAO,OAAO,CAAC,CAAE;EACnB;EACA,IAAIX,IAAI,CAACU,QAAQ,EAAE;IACjB,OAAO,OAAO,CAAC,CAAE;EACnB;EACA,IAAIV,IAAI,CAACW,UAAU,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA,OAAO,MAAMa,gBAAgB,GAAIxB,IAAI,IAAK;EACxC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EAEpB,MAAMyB,SAAS,GAAG,EAAE;;EAEpB;EACA,IAAIzB,IAAI,CAACE,IAAI,EAAE;IACbuB,SAAS,CAACrB,IAAI,CAACJ,IAAI,CAACE,IAAI,CAAC;EAC3B,CAAC,MAAM,IAAIF,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACW,UAAU,EAAE;IAC3Cc,SAAS,CAACrB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE;EAC5B,CAAC,MAAM,IAAIJ,IAAI,CAACU,QAAQ,EAAE;IACxBe,SAAS,CAACrB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAE;EAC5B;;EAEA;EACA,IAAIJ,IAAI,CAACW,UAAU,EAAE;IACnB,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;IAClD,MAAMC,iBAAiB,GAAGb,IAAI,CAACE,IAAI,IAAIU,YAAY,CAACE,IAAI,CAACZ,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACa,QAAQ,CAACb,IAAI,CAAC,CAAC;IAE1F,IAAI,CAACW,iBAAiB,EAAE;MACtBY,SAAS,CAACrB,IAAI,CAAC,IAAI,CAAC;IACtB;EACF;;EAEA;EACA,IAAIJ,IAAI,CAACC,KAAK,IAAIe,KAAK,CAACC,OAAO,CAACjB,IAAI,CAACC,KAAK,CAAC,EAAE;IAC3CD,IAAI,CAACC,KAAK,CAACiB,OAAO,CAAChB,IAAI,IAAI;MACzB,IAAI,CAACuB,SAAS,CAACV,QAAQ,CAACb,IAAI,CAACmB,IAAI,CAAC,EAAE;QAClCI,SAAS,CAACrB,IAAI,CAACF,IAAI,CAACmB,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAII,SAAS,CAACH,MAAM,KAAK,CAAC,EAAE;IAC1BG,SAAS,CAACrB,IAAI,CAAC,IAAI,CAAC;EACtB;EAEA,OAAOqB,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAI7B,QAAQ,IAAK;EACxC,MAAM8B,YAAY,GAAG;IACnB,OAAO,EAAE,GAAG;IACZ,IAAI,EAAE,EAAE;IACR,KAAK,EAAE,EAAE;IACT,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,EAAE;IACX,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,KAAK,EAAE,EAAE;IACT,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,EAAE;IACR,IAAI,EAAE,EAAE;IACR;IACA,KAAK,EAAE;EACT,CAAC;EAED,OAAOA,YAAY,CAAC9B,QAAQ,CAAC,IAAI,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,aAAa,GAAGA,CAAC5B,IAAI,EAAE6B,cAAc,EAAEC,QAAQ,GAAG,IAAI,KAAK;EACtE,IAAI,CAAC9B,IAAI,EAAE,OAAO,KAAK;;EAEvB;EACA,IAAI+B,YAAY,CAAC/B,IAAI,CAAC,EAAE,OAAO,IAAI;;EAEnC;EACA,IAAIA,IAAI,CAACgC,WAAW,IAAIhB,KAAK,CAACC,OAAO,CAACjB,IAAI,CAACgC,WAAW,CAAC,EAAE;IACvD,OAAOhC,IAAI,CAACgC,WAAW,CAACjB,QAAQ,CAACc,cAAc,CAAC;EAClD;;EAEA;EACA,MAAMI,SAAS,GAAGP,YAAY,CAAC1B,IAAI,CAACE,IAAI,IAAIqB,iBAAiB,CAACvB,IAAI,CAAC,CAAC;;EAEpE;EACA,IAAI6B,cAAc,CAACK,UAAU,CAAC,SAAS,CAAC,IAAID,SAAS,IAAI,EAAE,EAAE;IAC3D,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAClB,QAAQ,CAACc,cAAc,CAAC,IAAII,SAAS,IAAI,EAAE,EAAE;IAC9H,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,YAAY,GAAGA,CAACnC,IAAI,EAAEoC,QAAQ,KAAK;EAC9C,IAAI,CAACpC,IAAI,EAAE,OAAO,KAAK;EAEvB,MAAMiC,SAAS,GAAGP,YAAY,CAAC1B,IAAI,CAACE,IAAI,IAAIqB,iBAAiB,CAACvB,IAAI,CAAC,CAAC;EACpE,OAAOiC,SAAS,IAAIG,QAAQ;AAC9B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAML,YAAY,GAAI/B,IAAI,IAAK;EACpC,OAAOA,IAAI,KACTA,IAAI,CAACE,IAAI,KAAK,OAAO,IACrBF,IAAI,CAACqC,SAAS,KAAK,aAAa,IAC/BrC,IAAI,CAACU,QAAQ,IAAIV,IAAI,CAACW,UAAW,CACnC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2B,aAAa,GAAItC,IAAI,IAAK;EACrC,OAAOA,IAAI,KACTA,IAAI,CAACE,IAAI,KAAK,OAAO,IACrBF,IAAI,CAACqC,SAAS,KAAK,cAAc,IAChCrC,IAAI,CAACU,QAAQ,IAAI,CAACV,IAAI,CAACW,UAAW,CACpC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,SAAS,GAAIvC,IAAI,IAAK;EACjC,OAAOA,IAAI,KACTA,IAAI,CAACW,UAAU,IACf,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAACI,QAAQ,CAACf,IAAI,CAACE,IAAI,CAAC,CACnE;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsC,WAAW,GAAGA,CAAC;EAAExC,IAAI;EAAEyC,YAAY,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAU,CAAC,KAAK;EAC9E,IAAI,CAACD,YAAY,EAAE;IACjB;IACA,MAAME,QAAQ,GAAGpB,iBAAiB,CAACvB,IAAI,CAAC;IACxC,MAAMG,KAAK,GAAGP,YAAY,CAAC+C,QAAQ,CAAC;IACpC,oBAAOhD,OAAA,CAACH,GAAG;MAACW,KAAK,EAAEA,KAAM;MAACuC,IAAI,EAAEA,IAAK;MAAArC,QAAA,EAAEsC;IAAQ;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACxD;;EAEA;EACA,MAAMmC,QAAQ,GAAG7C,eAAe,CAACC,IAAI,CAAC;EACtC,oBACEL,OAAA,CAACF,KAAK;IAACiD,IAAI,EAAC,OAAO;IAACG,IAAI;IAAAxC,QAAA,EACrBuC;EAAQ;IAAAtC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARAqC,EAAA,GAjBaN,WAAW;AA0BxB,OAAO,MAAMO,eAAe,GAAGA,CAAC;EAAEC,UAAU;EAAElB,QAAQ;EAAEzB,QAAQ;EAAEL;AAAK,CAAC,KAAK;EAC3E,MAAMiD,mBAAmB,GAAGrB,aAAa,CAAC5B,IAAI,EAAEgD,UAAU,EAAElB,QAAQ,CAAC;EAErE,IAAI,CAACmB,mBAAmB,EAAE,OAAO,IAAI;EACrC,OAAO5C,QAAQ;AACjB,CAAC;AAAC6C,GAAA,GALWH,eAAe;AAAA,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}