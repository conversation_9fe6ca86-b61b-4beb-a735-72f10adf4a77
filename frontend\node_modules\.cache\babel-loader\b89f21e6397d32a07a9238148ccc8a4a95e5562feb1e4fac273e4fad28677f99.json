{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { removeCSS, updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { useContext } from 'react';\nimport StyleContext, { ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { isClientSide, toStyleStr } from \"../util\";\nimport { transformToken } from \"../util/css-variables\";\nimport useGlobalCache from \"./useGlobalCache\";\nimport { uniqueHash } from \"./useStyleRegister\";\nexport var CSS_VAR_PREFIX = 'cssVar';\nvar useCSSVarRegister = function useCSSVarRegister(config, fn) {\n  var key = config.key,\n    prefix = config.prefix,\n    unitless = config.unitless,\n    ignore = config.ignore,\n    token = config.token,\n    _config$scope = config.scope,\n    scope = _config$scope === void 0 ? '' : _config$scope;\n  var _useContext = useContext(StyleContext),\n    instanceId = _useContext.cache.instanceId,\n    container = _useContext.container;\n  var tokenKey = token._tokenKey;\n  var stylePath = [].concat(_toConsumableArray(config.path), [key, scope, tokenKey]);\n  var cache = useGlobalCache(CSS_VAR_PREFIX, stylePath, function () {\n    var originToken = fn();\n    var _transformToken = transformToken(originToken, key, {\n        prefix: prefix,\n        unitless: unitless,\n        ignore: ignore,\n        scope: scope\n      }),\n      _transformToken2 = _slicedToArray(_transformToken, 2),\n      mergedToken = _transformToken2[0],\n      cssVarsStr = _transformToken2[1];\n    var styleId = uniqueHash(stylePath, cssVarsStr);\n    return [mergedToken, cssVarsStr, styleId, key];\n  }, function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 3),\n      styleId = _ref2[2];\n    if (isClientSide) {\n      removeCSS(styleId, {\n        mark: ATTR_MARK\n      });\n    }\n  }, function (_ref3) {\n    var _ref4 = _slicedToArray(_ref3, 3),\n      cssVarsStr = _ref4[1],\n      styleId = _ref4[2];\n    if (!cssVarsStr) {\n      return;\n    }\n    var style = updateCSS(cssVarsStr, styleId, {\n      mark: ATTR_MARK,\n      prepend: 'queue',\n      attachTo: container,\n      priority: -999\n    });\n    style[CSS_IN_JS_INSTANCE] = instanceId;\n\n    // Used for `useCacheToken` to remove on batch when token removed\n    style.setAttribute(ATTR_TOKEN, key);\n  });\n  return cache;\n};\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 4),\n    styleStr = _cache[1],\n    styleId = _cache[2],\n    cssVarKey = _cache[3];\n  var _ref5 = options || {},\n    plain = _ref5.plain;\n  if (!styleStr) {\n    return null;\n  }\n  var order = -999;\n\n  // ====================== Style ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n  var styleText = toStyleStr(styleStr, cssVarKey, styleId, sharedAttrs, plain);\n  return [order, styleId, styleText];\n};\nexport default useCSSVarRegister;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}