{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\HomeworkUpload.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Form, Input, Upload, Button, Select, Typography, message, Card, Row, Col, Divider, Spin, Result, Radio, Alert, Table, Space } from 'antd';\nimport { UploadOutlined, PlusOutlined, DeleteOutlined, CheckCircleOutlined, FolderOpenOutlined, FileImageOutlined, LoadingOutlined } from '@ant-design/icons';\nimport { batchUploadHomework, getClasses, getUsers, getHomeworkAssignment, getHomeworkAssignments, getClass } from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst HomeworkUpload = ({\n  user\n}) => {\n  _s();\n  var _location$state3;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n\n  // 批量上传的状态\n  const [studentUploads, setStudentUploads] = useState([{\n    id: 1,\n    studentId: null,\n    files: []\n  }]);\n\n  // 添加班级和学生数据状态\n  const [classes, setClasses] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [dataLoading, setDataLoading] = useState(false);\n\n  // 添加作业任务列表状态\n  const [assignments, setAssignments] = useState([]);\n  const [loadingAssignments, setLoadingAssignments] = useState(false);\n\n  // 添加上传模式状态\n  const [uploadMode, setUploadMode] = useState('manual'); // 'manual'(手动) 或 'directory'(目录)\n\n  // 目录上传相关状态\n  const [directoryFiles, setDirectoryFiles] = useState([]);\n  const [matchedStudents, setMatchedStudents] = useState([]);\n  const [processingFiles, setProcessingFiles] = useState(false);\n  const directoryInputRef = useRef(null);\n\n  // 添加当前选择的班级学生列表状态\n  const [classStudents, setClassStudents] = useState([]);\n\n  // 添加作业任务ID状态\n  const [assignmentId, setAssignmentId] = useState(null);\n\n  // 当选择班级时筛选学生列表\n  const handleClassChange = async classId => {\n    if (!classId) {\n      setClassStudents([]);\n      return;\n    }\n    try {\n      // 使用getClass API获取班级详情，包括学生列表\n      console.log(`开始获取班级 ${classId} 详情...`);\n      const classDetails = await getClass(classId);\n      console.log(`班级 ${classId} 详情:`, classDetails);\n      if (classDetails && classDetails.students && Array.isArray(classDetails.students)) {\n        console.log(`班级 ${classId} 的学生:`, classDetails.students);\n        console.log(`学生数量: ${classDetails.students.length}`);\n\n        // 检查学生数据结构\n        if (classDetails.students.length > 0) {\n          console.log('第一个学生数据示例:', classDetails.students[0]);\n        }\n        setClassStudents(classDetails.students);\n\n        // 清除已选择的学生ID（如果不在当前班级中）\n        setStudentUploads(prevUploads => prevUploads.map(item => {\n          const studentInClass = classDetails.students.some(s => s.id === item.studentId);\n          return studentInClass ? item : {\n            ...item,\n            studentId: null\n          };\n        }));\n      } else {\n        console.warn(`班级 ${classId} 没有学生数据`);\n        console.warn('classDetails结构:', classDetails);\n        setClassStudents([]);\n        message.warning('该班级暂无学生，请先添加学生到班级');\n      }\n    } catch (error) {\n      console.error(`获取班级 ${classId} 学生列表失败:`, error);\n      message.error('获取班级学生列表失败，请重试');\n      setClassStudents([]);\n    }\n  };\n\n  // 定义fetchAssignmentDetails函数 - 确保在handleClassChange之后定义\n  const fetchAssignmentDetails = async id => {\n    try {\n      const assignmentDetails = await getHomeworkAssignment(id);\n      console.log(\"获取到的作业任务详情:\", assignmentDetails);\n\n      // 检查作业任务状态，如果已结束则显示提示并返回上一页\n      if (assignmentDetails && assignmentDetails.status === 'finished') {\n        message.error('该作业任务已结束，不能再上传作业');\n        navigate('/homework');\n        return;\n      }\n\n      // 如果作业任务有关联班级，则自动设置班级信息\n      if (assignmentDetails && assignmentDetails.class_id) {\n        console.log(\"设置班级ID:\", assignmentDetails.class_id);\n        form.setFieldsValue({\n          class_id: assignmentDetails.class_id\n        });\n\n        // 在表单标题中设置作业标题（不包括班级名称）\n        if (assignmentDetails.title) {\n          form.setFieldsValue({\n            title: assignmentDetails.title\n          });\n        }\n\n        // 立即加载班级学生列表\n        console.log(\"自动加载班级学生列表\");\n        handleClassChange(assignmentDetails.class_id);\n      }\n    } catch (error) {\n      console.error(\"获取作业任务详情失败:\", error);\n    }\n  };\n\n  // 获取班级、学生和作业任务数据\n  useEffect(() => {\n    const fetchData = async () => {\n      if (user && user.is_teacher) {\n        setDataLoading(true);\n        try {\n          var _location$state;\n          // 获取班级数据\n          console.log('获取班级数据');\n          const classesData = await getClasses();\n          console.log('班级数据:', classesData);\n          setClasses(classesData || []);\n\n          // 获取学生数据\n          console.log('获取学生数据');\n          const usersResponse = await getUsers();\n          console.log('用户数据:', usersResponse);\n\n          // 处理不同的响应格式\n          let studentUsers = [];\n          if (usersResponse) {\n            if (Array.isArray(usersResponse)) {\n              // 处理数组格式响应\n              studentUsers = usersResponse.filter(u => !u.is_teacher);\n            } else if (usersResponse.items && Array.isArray(usersResponse.items)) {\n              // 处理对象格式响应，包含items数组\n              studentUsers = usersResponse.items.filter(u => !u.is_teacher);\n            } else if (usersResponse.users && Array.isArray(usersResponse.users)) {\n              // 处理对象格式响应，包含users数组\n              studentUsers = usersResponse.users.filter(u => !u.is_teacher);\n            }\n          }\n          console.log('过滤后的学生数据:', studentUsers);\n          setStudents(studentUsers || []);\n\n          // 获取作业任务列表\n          setLoadingAssignments(true);\n          const assignmentsResponse = await getHomeworkAssignments();\n          console.log('获取到的作业任务列表:', assignmentsResponse);\n          // 确保我们有一个有效的数组\n          let assignmentsArray = [];\n          if (assignmentsResponse && assignmentsResponse.items) {\n            assignmentsArray = assignmentsResponse.items || [];\n            setAssignments(assignmentsArray);\n          } else if (Array.isArray(assignmentsResponse)) {\n            assignmentsArray = assignmentsResponse;\n            setAssignments(assignmentsArray);\n          } else {\n            setAssignments([]);\n          }\n\n          // 默认选择最新的作业任务（列表中的第一个）\n          if (assignmentsArray.length > 0 && !((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.assignmentId)) {\n            const latestAssignment = assignmentsArray[0];\n            console.log('默认选择最新作业任务:', latestAssignment.title);\n            form.setFieldsValue({\n              assignment_id: latestAssignment.id,\n              title: latestAssignment.title\n            });\n\n            // 如果最新作业任务有班级信息，也自动设置班级\n            if (latestAssignment.class_id) {\n              form.setFieldsValue({\n                class_id: latestAssignment.class_id\n              });\n\n              // 更新班级学生列表\n              handleClassChange(latestAssignment.class_id);\n            }\n          }\n          setLoadingAssignments(false);\n        } catch (error) {\n          console.error('获取数据失败:', error);\n          message.error('获取数据失败，请检查网络连接');\n          setLoadingAssignments(false);\n        } finally {\n          setDataLoading(false);\n        }\n      }\n    };\n    fetchData();\n  }, [user]);\n\n  // 初始化表单和获取作业任务信息\n  useEffect(() => {\n    var _location$state2;\n    if ((_location$state2 = location.state) !== null && _location$state2 !== void 0 && _location$state2.assignmentId) {\n      console.log('从路由参数获取到作业任务ID:', location.state.assignmentId);\n      setAssignmentId(location.state.assignmentId);\n      form.setFieldsValue({\n        assignment_id: location.state.assignmentId\n      });\n      fetchAssignmentDetails(location.state.assignmentId);\n    } else {\n      console.log('未从路由参数获取到作业任务ID');\n    }\n  }, [(_location$state3 = location.state) === null || _location$state3 === void 0 ? void 0 : _location$state3.assignmentId, form]);\n\n  // 检查是否有预设的作业任务ID\n  useEffect(() => {\n    console.log(\"HomeworkUpload - location.state:\", location.state);\n    if (location.state) {\n      if (location.state.assignmentId) {\n        console.log(\"设置作业任务ID:\", location.state.assignmentId);\n        form.setFieldsValue({\n          assignment_id: location.state.assignmentId\n        });\n\n        // 获取作业任务详情以获取班级信息\n        fetchAssignmentDetails(location.state.assignmentId);\n      }\n    }\n  }, [location.state, form]);\n\n  // 当选择作业任务时自动设置标题和班级\n  const handleAssignmentChange = value => {\n    const selectedAssignment = assignments.find(a => a.id === value);\n    if (selectedAssignment) {\n      form.setFieldsValue({\n        title: selectedAssignment.title\n      });\n\n      // 如果作业任务有班级信息，也自动设置班级\n      if (selectedAssignment.class_id) {\n        form.setFieldsValue({\n          class_id: selectedAssignment.class_id\n        });\n\n        // 更新班级学生列表\n        handleClassChange(selectedAssignment.class_id);\n      }\n    }\n  };\n\n  // 处理教师批量上传\n  const handleTeacherBatchUpload = async values => {\n    try {\n      setLoading(true);\n\n      // 检查学生ID和文件\n      const validStudentUploads = studentUploads.filter(item => item.studentId && item.files.length > 0);\n      if (validStudentUploads.length === 0) {\n        message.error('请至少选择一个学生并上传对应的作业文件');\n        setLoading(false);\n        return;\n      }\n\n      // 构建表单数据\n      const formData = new FormData();\n\n      // 使用选择的作业任务作为标题\n      if (values.assignment_id) {\n        const selectedAssignment = assignments.find(a => a.id === values.assignment_id);\n        if (selectedAssignment) {\n          formData.append('title', selectedAssignment.title);\n        } else {\n          formData.append('title', '未命名作业');\n        }\n      } else {\n        formData.append('title', '未命名作业');\n      }\n      if (values.description) {\n        formData.append('description', values.description);\n      }\n      if (values.assignment_id) {\n        formData.append('assignment_id', values.assignment_id);\n      }\n\n      // 确保班级ID被正确添加到表单\n      if (values.class_id) {\n        formData.append('class_id', values.class_id);\n        console.log('添加班级ID到表单:', values.class_id);\n      }\n\n      // 构建学生数据\n      const studentData = validStudentUploads.map(item => {\n        // 获取学生信息\n        const student = students.find(s => s.id === item.studentId);\n        const studentName = student ? student.full_name || student.username : '未知学生';\n        return {\n          student_id: item.studentId,\n          student_name: studentName,\n          images: item.files.map(file => file.originFileObj.name)\n        };\n      });\n      console.log('批量上传学生数据:', studentData);\n      formData.append('student_data', JSON.stringify(studentData));\n\n      // 批量上传文件\n      let fileCount = 0;\n      validStudentUploads.forEach(item => {\n        item.files.forEach(file => {\n          if (file.originFileObj) {\n            // 确保文件名唯一，避免同名文件覆盖\n            const fileName = file.originFileObj.name;\n            const uniqueFileName = `${item.studentId}_${Date.now()}_${fileName}`;\n\n            // 创建一个新的File对象，重命名文件\n            const renamedFile = new File([file.originFileObj], uniqueFileName, {\n              type: file.originFileObj.type\n            });\n            formData.append('files', renamedFile);\n            fileCount++;\n\n            // 更新studentData中的文件名\n            const studentIndex = studentData.findIndex(s => s.student_id === item.studentId);\n            if (studentIndex !== -1) {\n              const imageIndex = studentData[studentIndex].images.findIndex(img => img === fileName);\n              if (imageIndex !== -1) {\n                studentData[studentIndex].images[imageIndex] = uniqueFileName;\n              }\n            }\n          }\n        });\n      });\n\n      // 更新formData中的studentData\n      formData.delete('student_data');\n      formData.append('student_data', JSON.stringify(studentData));\n      console.log(`准备上传 ${fileCount} 个文件，${validStudentUploads.length} 个学生的作业`);\n\n      // 提交批量作业\n      const response = await batchUploadHomework(formData);\n      console.log('批量上传作业响应:', response);\n\n      // 设置提交成功状态\n      setSubmitSuccess(true);\n      message.success('批量上传作业成功！');\n\n      // 显示上传结果详情\n      if (response && response.homeworks) {\n        console.log('上传的作业详情:', response.homeworks);\n      }\n\n      // 延迟跳转，确保消息显示完成\n      setTimeout(() => {\n        // 刷新作业列表页面，添加强制刷新参数和时间戳\n        navigate('/homework/pending-review', {\n          state: {\n            refresh: true,\n            forceRefresh: true,\n            timestamp: new Date().getTime()\n          }\n        });\n      }, 2000);\n    } catch (error) {\n      console.error('批量上传失败:', error);\n      message.error(`批量上传失败: ${error.message || '请检查网络连接或文件格式'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 处理目录选择\n  const handleDirectorySelect = async e => {\n    try {\n      setProcessingFiles(true);\n      const files = e.target.files;\n      if (!files || files.length === 0) {\n        setProcessingFiles(false);\n        return;\n      }\n      console.log('选择的文件数量:', files.length);\n\n      // 检查是否已选择班级\n      const selectedClassId = form.getFieldValue('class_id');\n      if (!selectedClassId) {\n        message.warning('请先选择班级，再上传文件夹');\n        setProcessingFiles(false);\n        return;\n      }\n\n      // 如果班级学生列表为空，尝试重新获取班级学生\n      if (classStudents.length === 0) {\n        try {\n          const classDetails = await getClass(selectedClassId);\n          if (classDetails && classDetails.students && Array.isArray(classDetails.students)) {\n            setClassStudents(classDetails.students);\n          } else {\n            message.error('所选班级没有学生信息，请先添加学生到班级');\n            setProcessingFiles(false);\n            return;\n          }\n        } catch (error) {\n          console.error('获取班级学生失败:', error);\n          message.error('获取班级学生失败，请重试');\n          setProcessingFiles(false);\n          return;\n        }\n      }\n\n      // 收集文件信息\n      const fileList = Array.from(files).map(file => ({\n        file,\n        name: file.name,\n        path: file.webkitRelativePath || file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: file.lastModified,\n        matched: false,\n        studentId: null,\n        studentName: null\n      }));\n      setDirectoryFiles(fileList);\n\n      // 尝试匹配学生\n      const matched = [];\n      const unmatched = [];\n\n      // 使用班级学生列表\n      const matchStudents = classStudents;\n      if (matchStudents.length === 0) {\n        message.warning('所选班级没有学生信息，请先添加学生到班级');\n        setProcessingFiles(false);\n        return;\n      }\n      fileList.forEach(fileInfo => {\n        // 从文件名中提取学生名字（去掉扩展名）\n        const fileName = fileInfo.name;\n        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\n\n        // 尝试匹配学生，使用更严格的匹配逻辑\n        let matchedStudent = null;\n        let bestMatchScore = 0;\n\n        // 首先尝试完全匹配\n        matchStudents.forEach(student => {\n          const fullName = student.full_name || '';\n          const username = student.username || '';\n\n          // 检查文件名是否完全包含学生姓名或用户名\n          if (nameWithoutExt === fullName || nameWithoutExt === username) {\n            matchedStudent = student;\n            bestMatchScore = 100; // 完全匹配赋予最高分\n          }\n          // 检查文件名是否包含学生姓名或用户名\n          else if (nameWithoutExt.includes(fullName) && fullName.length > 1) {\n            const score = fullName.length;\n            if (score > bestMatchScore) {\n              matchedStudent = student;\n              bestMatchScore = score;\n            }\n          } else if (nameWithoutExt.includes(username) && username.length > 1) {\n            const score = username.length;\n            if (score > bestMatchScore) {\n              matchedStudent = student;\n              bestMatchScore = score;\n            }\n          }\n        });\n\n        // 如果找到匹配的学生\n        if (matchedStudent && bestMatchScore > 1) {\n          fileInfo.matched = true;\n          fileInfo.studentId = matchedStudent.id;\n          fileInfo.studentName = matchedStudent.full_name || matchedStudent.username;\n          matched.push({\n            ...fileInfo,\n            student: matchedStudent,\n            matchScore: bestMatchScore\n          });\n        } else {\n          unmatched.push(fileInfo);\n        }\n      });\n      setMatchedStudents(matched);\n      setDirectoryFiles([...matched, ...unmatched]);\n      if (matched.length === 0) {\n        message.warning('未能匹配任何学生，请手动为文件指定学生');\n      } else {\n        message.success(`成功匹配 ${matched.length}/${fileList.length} 个文件与学生${unmatched.length > 0 ? '，剩余' + unmatched.length + '个文件需要手动指定学生' : ''}`);\n      }\n    } catch (error) {\n      console.error('处理目录文件失败:', error);\n      message.error('处理目录文件失败: ' + (error.message || '未知错误'));\n    } finally {\n      setProcessingFiles(false);\n    }\n  };\n\n  // 手动更新文件与学生的匹配关系\n  const updateFileStudentMatch = (fileIndex, studentId) => {\n    const updatedFiles = [...directoryFiles];\n    const file = updatedFiles[fileIndex];\n    if (file) {\n      // 使用班级学生列表而不是所有学生\n      const student = classStudents.find(s => s.id === studentId);\n      file.studentId = studentId;\n      file.matched = !!student;\n      file.studentName = student ? student.full_name || student.username : null;\n      setDirectoryFiles(updatedFiles);\n\n      // 更新匹配的学生列表\n      const updatedMatched = updatedFiles.filter(f => f.matched).map(f => ({\n        ...f,\n        student: classStudents.find(s => s.id === f.studentId)\n      }));\n      setMatchedStudents(updatedMatched);\n    }\n  };\n\n  // 处理目录批量上传\n  const handleDirectoryBatchUpload = async values => {\n    try {\n      setLoading(true);\n\n      // 检查匹配的文件\n      if (matchedStudents.length === 0) {\n        message.error('没有匹配的学生文件，请先选择目录并确保文件名包含学生姓名');\n        setLoading(false);\n        return;\n      }\n\n      // 构建表单数据\n      const formData = new FormData();\n\n      // 使用选择的作业任务作为标题\n      if (values.assignment_id) {\n        const selectedAssignment = assignments.find(a => a.id === values.assignment_id);\n        if (selectedAssignment) {\n          formData.append('title', selectedAssignment.title);\n        } else {\n          formData.append('title', '未命名作业');\n        }\n      } else {\n        formData.append('title', '未命名作业');\n      }\n      if (values.description) {\n        formData.append('description', values.description);\n      }\n      if (values.assignment_id) {\n        formData.append('assignment_id', values.assignment_id);\n      }\n\n      // 确保班级ID被正确添加到表单\n      if (values.class_id) {\n        formData.append('class_id', values.class_id);\n        console.log('添加班级ID到表单:', values.class_id);\n      }\n\n      // 构建学生数据\n      const studentData = matchedStudents.reduce((acc, fileInfo) => {\n        const studentId = fileInfo.studentId;\n        if (!studentId) return acc;\n\n        // 检查学生是否已存在于累加器中\n        let studentEntry = acc.find(entry => entry.student_id === studentId);\n        if (!studentEntry) {\n          // 如果学生不存在，创建新条目\n          const student = classStudents.find(s => s.id === studentId);\n          const studentName = student ? student.full_name || student.username : '未知学生';\n          studentEntry = {\n            student_id: studentId,\n            student_name: studentName,\n            images: []\n          };\n          acc.push(studentEntry);\n        }\n\n        // 添加文件名到学生的images数组\n        const uniqueFileName = `${studentId}_${Date.now()}_${fileInfo.name}`;\n        studentEntry.images.push(uniqueFileName);\n\n        // 创建一个新的File对象，重命名文件\n        const renamedFile = new File([fileInfo.file], uniqueFileName, {\n          type: fileInfo.file.type\n        });\n\n        // 添加文件到formData\n        formData.append('files', renamedFile);\n        return acc;\n      }, []);\n      console.log('目录批量上传学生数据:', studentData);\n      formData.append('student_data', JSON.stringify(studentData));\n      console.log(`准备上传 ${matchedStudents.length} 个文件，${studentData.length} 个学生的作业`);\n\n      // 提交批量作业\n      const response = await batchUploadHomework(formData);\n      console.log('批量上传作业响应:', response);\n\n      // 设置提交成功状态\n      setSubmitSuccess(true);\n      message.success('批量上传作业成功！');\n\n      // 显示上传结果详情\n      if (response && response.homeworks) {\n        console.log('上传的作业详情:', response.homeworks);\n      }\n\n      // 延迟跳转，确保消息显示完成\n      setTimeout(() => {\n        // 刷新作业列表页面，添加强制刷新参数和时间戳\n        navigate('/homework/pending-review', {\n          state: {\n            refresh: true,\n            forceRefresh: true,\n            timestamp: new Date().getTime()\n          }\n        });\n      }, 2000);\n    } catch (error) {\n      console.error('目录批量上传失败:', error);\n      message.error(`目录批量上传失败: ${error.message || '请检查网络连接或文件格式'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 表单提交处理\n  const onFinish = values => {\n    if (uploadMode === 'directory') {\n      handleDirectoryBatchUpload(values);\n    } else {\n      handleTeacherBatchUpload(values);\n    }\n  };\n\n  // 作业配置常量\n  const HOMEWORK_CONFIG = {\n    MAX_PAGES: 4,\n    MIN_PAGES: 1,\n    SUPPORTED_FORMATS: ['.jpg', '.jpeg', '.png', '.bmp', '.webp'],\n    MAX_FILE_SIZE_MB: 10,\n    MAX_TOTAL_SIZE_MB: 40\n  };\n\n  // 科目特定的页面标签\n  const PAGE_LABELS = {\n    'math': ['第1页', '第2页', '第3页', '第4页'],\n    'english': ['听力部分', '阅读部分', '写作部分', '第4页'],\n    'physics': ['实验步骤', '实验数据', '实验结果', '实验总结'],\n    'chemistry': ['实验过程', '化学方程式', '实验现象', '实验结论'],\n    'biology': ['观察记录', '实验步骤', '实验结果', '分析总结'],\n    'chinese': ['第1页', '第2页', '第3页', '第4页'],\n    'history': ['第1页', '第2页', '第3页', '第4页'],\n    'geography': ['第1页', '第2页', '第3页', '第4页']\n  };\n\n  // 获取科目特定的页面标签\n  const getPageLabels = (subject = 'math') => {\n    return PAGE_LABELS[subject] || PAGE_LABELS['math'];\n  };\n\n  // 验证单个文件\n  const validateSingleFile = file => {\n    // 检查文件格式\n    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();\n    if (!HOMEWORK_CONFIG.SUPPORTED_FORMATS.includes(fileExtension)) {\n      message.error(`不支持的文件格式: ${fileExtension}，请使用 ${HOMEWORK_CONFIG.SUPPORTED_FORMATS.join(', ')} 格式`);\n      return false;\n    }\n\n    // 检查文件大小\n    const fileSizeMB = file.size / 1024 / 1024;\n    if (fileSizeMB > HOMEWORK_CONFIG.MAX_FILE_SIZE_MB) {\n      message.error(`文件大小超过限制: ${fileSizeMB.toFixed(1)}MB > ${HOMEWORK_CONFIG.MAX_FILE_SIZE_MB}MB`);\n      return false;\n    }\n    return true;\n  };\n\n  // 验证文件列表\n  const validateFileList = (fileList, studentId = null) => {\n    if (!fileList || fileList.length === 0) {\n      return {\n        isValid: false,\n        message: '请至少上传1页作业'\n      };\n    }\n\n    // 检查页数限制\n    if (fileList.length > HOMEWORK_CONFIG.MAX_PAGES) {\n      return {\n        isValid: false,\n        message: `作业页数超过限制: ${fileList.length} > ${HOMEWORK_CONFIG.MAX_PAGES}页`\n      };\n    }\n    if (fileList.length < HOMEWORK_CONFIG.MIN_PAGES) {\n      return {\n        isValid: false,\n        message: `作业页数不足: ${fileList.length} < ${HOMEWORK_CONFIG.MIN_PAGES}页`\n      };\n    }\n\n    // 检查总文件大小\n    const totalSize = fileList.reduce((sum, file) => {\n      const fileObj = file.originFileObj || file;\n      return sum + (fileObj.size || 0);\n    }, 0);\n    const totalSizeMB = totalSize / 1024 / 1024;\n    if (totalSizeMB > HOMEWORK_CONFIG.MAX_TOTAL_SIZE_MB) {\n      return {\n        isValid: false,\n        message: `总文件大小超过限制: ${totalSizeMB.toFixed(1)}MB > ${HOMEWORK_CONFIG.MAX_TOTAL_SIZE_MB}MB`\n      };\n    }\n    return {\n      isValid: true,\n      message: ''\n    };\n  };\n\n  // 处理文件上传前的验证\n  const beforeUpload = (file, fileList) => {\n    if (!validateSingleFile(file)) {\n      return Upload.LIST_IGNORE;\n    }\n\n    // 检查是否超过页数限制\n    if (fileList && fileList.length >= HOMEWORK_CONFIG.MAX_PAGES) {\n      message.error(`最多只能上传 ${HOMEWORK_CONFIG.MAX_PAGES} 页作业`);\n      return Upload.LIST_IGNORE;\n    }\n    return false; // 阻止自动上传\n  };\n\n  // 添加学生上传项（教师批量模式）\n  const addStudentUpload = () => {\n    const newId = studentUploads.length > 0 ? Math.max(...studentUploads.map(item => item.id)) + 1 : 1;\n    setStudentUploads([...studentUploads, {\n      id: newId,\n      studentId: null,\n      files: []\n    }]);\n  };\n\n  // 移除学生上传项（教师批量模式）\n  const removeStudentUpload = id => {\n    if (studentUploads.length <= 1) {\n      message.warning('至少需要保留一个学生');\n      return;\n    }\n    setStudentUploads(studentUploads.filter(item => item.id !== id));\n  };\n\n  // 更新学生ID（教师批量模式）\n  const updateStudentId = (id, studentId) => {\n    setStudentUploads(studentUploads.map(item => item.id === id ? {\n      ...item,\n      studentId\n    } : item));\n  };\n\n  // 更新学生文件（教师批量模式）\n  const updateStudentFiles = (id, newFiles) => {\n    // 验证文件列表\n    const validation = validateFileList(newFiles, id);\n\n    // 更新文件列表\n    setStudentUploads(studentUploads.map(item => item.id === id ? {\n      ...item,\n      files: newFiles\n    } : item));\n\n    // 如果验证失败，显示警告（但不阻止更新，让用户可以继续调整）\n    if (!validation.isValid && newFiles.length > 0) {\n      message.warning(`学生 ${id}: ${validation.message}`);\n    }\n  };\n\n  // 格式化文件大小\n  const formatFileSize = bytes => {\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  // 获取文件状态标签\n  const getFileStatusTag = (fileList, subject = 'math') => {\n    if (!fileList || fileList.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u672A\\u4E0A\\u4F20\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 14\n      }, this);\n    }\n    const validation = validateFileList(fileList);\n    const pageLabels = getPageLabels(subject);\n    return /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: [\"\\u5DF2\\u4E0A\\u4F20 \", fileList.length, \"/\", HOMEWORK_CONFIG.MAX_PAGES, \" \\u9875\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this), validation.isValid ? /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a',\n            marginLeft: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Text, {\n          type: \"warning\",\n          style: {\n            marginLeft: 8\n          },\n          children: [\"(\", validation.message, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: fileList.map((file, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginRight: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: pageLabels[index] || `第${index + 1}页`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 851,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        style: {\n          fontSize: '12px'\n        },\n        children: [\"\\u603B\\u5927\\u5C0F: \", formatFileSize(fileList.reduce((sum, file) => sum + (file.size || 0), 0))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染学生上传项\n  const renderStudentUploadItem = (item, index) => {\n    // 使用班级学生列表而不是所有学生\n    const studentList = classStudents.length > 0 ? classStudents : students;\n    const studentInfo = studentList.find(s => s.id === item.studentId);\n    const studentName = studentInfo ? studentInfo.full_name || studentInfo.username : `学生ID: ${item.studentId}`;\n\n    // 获取当前选择的班级ID\n    const selectedClassId = form.getFieldValue('class_id');\n    const hasClassSelected = !!selectedClassId;\n    console.log(`渲染学生上传项 ${index}:`, {\n      selectedClassId,\n      hasClassSelected,\n      classStudentsLength: classStudents.length,\n      studentListLength: studentList.length,\n      studentId: item.studentId,\n      studentInfo: studentInfo\n    });\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-upload-item\",\n      style: {\n        marginBottom: 16,\n        padding: 16,\n        border: '1px solid #eee',\n        borderRadius: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u5B66\\u751F\",\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              value: item.studentId,\n              onChange: value => updateStudentId(item.id, value),\n              style: {\n                width: '100%'\n              },\n              placeholder: hasClassSelected ? \"选择学生\" : \"请先选择班级\",\n              disabled: !hasClassSelected,\n              children: studentList.map(student => /*#__PURE__*/_jsxDEV(Option, {\n                value: student.id,\n                children: student.full_name || student.username\n              }, student.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 15\n            }, this), hasClassSelected && studentList.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: 'orange',\n                marginTop: 4\n              },\n              children: \"\\u8BE5\\u73ED\\u7EA7\\u6682\\u65E0\\u5B66\\u751F\\uFF0C\\u8BF7\\u5148\\u6DFB\\u52A0\\u5B66\\u751F\\u5230\\u73ED\\u7EA7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 12,\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"\\u4F5C\\u4E1A\\u6587\\u4EF6\",\n            children: /*#__PURE__*/_jsxDEV(Upload, {\n              fileList: item.files,\n              onChange: ({\n                fileList\n              }) => updateStudentFiles(item.id, fileList),\n              beforeUpload: beforeUpload,\n              multiple: true,\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 31\n                }, this),\n                children: \"\\u9009\\u62E9\\u6587\\u4EF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 6,\n          style: {\n            textAlign: 'right'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"danger\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 21\n            }, this),\n            onClick: () => removeStudentUpload(item.id),\n            children: \"\\u79FB\\u9664\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 12,\n          padding: 12,\n          background: '#f9f9f9',\n          borderRadius: 6\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: getFileStatusTag(item.files)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 938,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: item.files.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  fontSize: '12px',\n                  color: '#666'\n                },\n                children: \"\\u6587\\u4EF6\\u5217\\u8868:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 4\n                },\n                children: item.files.map((file, fileIndex) => {\n                  const pageLabels = getPageLabels();\n                  const pageLabel = pageLabels[fileIndex] || `第${fileIndex + 1}页`;\n                  const fileName = file.name || file.originFileObj && file.originFileObj.name || `文件 ${fileIndex + 1}`;\n                  const fileSize = file.size || file.originFileObj && file.originFileObj.size || 0;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      color: '#666',\n                      marginBottom: '2px',\n                      display: 'flex',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: [pageLabel, \":\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 963,\n                        columnNumber: 29\n                      }, this), \" \", fileName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 962,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: formatFileSize(fileSize)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 965,\n                      columnNumber: 27\n                    }, this)]\n                  }, fileIndex, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 25\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 937,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 936,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 8\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          type: \"secondary\",\n          children: [\"\\u5B66\\u751F: \", /*#__PURE__*/_jsxDEV(Typography.Text, {\n            strong: true,\n            children: studentName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 17\n          }, this), studentInfo && studentInfo.email && ` (${studentInfo.email})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 9\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 886,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染目录上传匹配表格\n  const renderDirectoryMatchTable = () => {\n    const columns = [{\n      title: '文件名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(FileImageOutlined, {\n          style: {\n            marginRight: 8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 13\n        }, this), text, /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#999'\n          },\n          children: [(record.size / 1024).toFixed(2), \" KB\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 11\n      }, this)\n    }, {\n      title: '匹配状态',\n      dataIndex: 'matched',\n      key: 'matched',\n      render: matched => matched ? /*#__PURE__*/_jsxDEV(Text, {\n        type: \"success\",\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 34\n        }, this), \" \\u5DF2\\u5339\\u914D\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1009,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Text, {\n        type: \"danger\",\n        children: \"\\u672A\\u5339\\u914D\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1010,\n        columnNumber: 13\n      }, this)\n    }, {\n      title: '学生',\n      dataIndex: 'studentName',\n      key: 'studentName',\n      render: (text, record, index) => /*#__PURE__*/_jsxDEV(Select, {\n        value: record.studentId,\n        onChange: value => updateFileStudentMatch(index, value),\n        style: {\n          width: '100%'\n        },\n        placeholder: \"\\u9009\\u62E9\\u5B66\\u751F\",\n        children: classStudents.map(student => /*#__PURE__*/_jsxDEV(Option, {\n          value: student.id,\n          children: student.full_name || student.username\n        }, student.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 11\n      }, this)\n    }];\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6587\\u4EF6\\u4E0E\\u5B66\\u751F\\u5339\\u914D\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u7CFB\\u7EDF\\u5DF2\\u6839\\u636E\\u6587\\u4EF6\\u540D\\u5C1D\\u8BD5\\u5339\\u914D\\u5B66\\u751F\\u3002\\u5982\\u679C\\u5339\\u914D\\u4E0D\\u6B63\\u786E\\uFF0C\\u8BF7\\u624B\\u52A8\\u9009\\u62E9\\u6B63\\u786E\\u7684\\u5B66\\u751F\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u5DF2\\u6210\\u529F\\u5339\\u914D \", matchedStudents.length, \"/\", directoryFiles.length, \" \\u4E2A\\u6587\\u4EF6\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1039,\n          columnNumber: 13\n        }, this),\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1036,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        dataSource: directoryFiles,\n        columns: columns,\n        rowKey: (record, index) => index,\n        size: \"small\",\n        pagination: {\n          pageSize: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1049,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1035,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"homework-batch-upload-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 3,\n      children: \"\\u6279\\u91CF\\u4E0A\\u4F20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1062,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading || dataLoading,\n      children: submitSuccess ? /*#__PURE__*/_jsxDEV(Result, {\n        status: \"success\",\n        title: \"\\u4F5C\\u4E1A\\u63D0\\u4EA4\\u6210\\u529F\\uFF01\",\n        subTitle: \"\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5904\\u7406\\u60A8\\u7684\\u4F5C\\u4E1A\\uFF0C\\u8BF7\\u7A0D\\u540E\\u67E5\\u770B\\u7ED3\\u679C\\u3002\",\n        extra: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: () => navigate('/homework'),\n          children: \"\\u8FD4\\u56DE\\u4F5C\\u4E1A\\u5217\\u8868\"\n        }, \"list\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setSubmitSuccess(false);\n            setStudentUploads([{\n              id: 1,\n              studentId: null,\n              files: []\n            }]);\n            setDirectoryFiles([]);\n            setMatchedStudents([]);\n            form.resetFields();\n          },\n          children: \"\\u7EE7\\u7EED\\u63D0\\u4EA4\"\n        }, \"again\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 15\n        }, this)]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: onFinish,\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\uD83D\\uDCDA \\u4F5C\\u4E1A\\u4E0A\\u4F20\\u89C4\\u8303\\u8BF4\\u660E\",\n          description: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\uD83D\\uDCC4 \\u6587\\u4EF6\\u8981\\u6C42\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    style: {\n                      marginTop: 4,\n                      marginBottom: 8\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"\\u652F\\u6301\\u683C\\u5F0F\\uFF1A\", HOMEWORK_CONFIG.SUPPORTED_FORMATS.join(', ')]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1108,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"\\u9875\\u6570\\u9650\\u5236\\uFF1A\", HOMEWORK_CONFIG.MIN_PAGES, \"-\", HOMEWORK_CONFIG.MAX_PAGES, \"\\u9875\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1109,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"\\u5355\\u4E2A\\u6587\\u4EF6\\uFF1A\\u4E0D\\u8D85\\u8FC7\", HOMEWORK_CONFIG.MAX_FILE_SIZE_MB, \"MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1110,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"\\u603B\\u5927\\u5C0F\\uFF1A\\u4E0D\\u8D85\\u8FC7\", HOMEWORK_CONFIG.MAX_TOTAL_SIZE_MB, \"MB\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1111,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1107,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: 8\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\uD83D\\uDCDD \\u9875\\u9762\\u6807\\u7B7E\\u793A\\u4F8B\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                    style: {\n                      marginTop: 4,\n                      marginBottom: 8\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: \"\\u6570\\u5B66\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1119,\n                        columnNumber: 31\n                      }, this), \"\\u7B2C1\\u9875\\u3001\\u7B2C2\\u9875\\u3001\\u7B2C3\\u9875\\u3001\\u7B2C4\\u9875\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: \"\\u82F1\\u8BED\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1120,\n                        columnNumber: 31\n                      }, this), \"\\u542C\\u529B\\u90E8\\u5206\\u3001\\u9605\\u8BFB\\u90E8\\u5206\\u3001\\u5199\\u4F5C\\u90E8\\u5206\\u3001\\u7B2C4\\u9875\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: \"\\u7269\\u7406\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1121,\n                        columnNumber: 31\n                      }, this), \"\\u5B9E\\u9A8C\\u6B65\\u9AA4\\u3001\\u5B9E\\u9A8C\\u6570\\u636E\\u3001\\u5B9E\\u9A8C\\u7ED3\\u679C\\u3001\\u5B9E\\u9A8C\\u603B\\u7ED3\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1121,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [/*#__PURE__*/_jsxDEV(Text, {\n                        type: \"secondary\",\n                        children: \"\\u5316\\u5B66\\uFF1A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1122,\n                        columnNumber: 31\n                      }, this), \"\\u5B9E\\u9A8C\\u8FC7\\u7A0B\\u3001\\u5316\\u5B66\\u65B9\\u7A0B\\u5F0F\\u3001\\u5B9E\\u9A8C\\u73B0\\u8C61\\u3001\\u5B9E\\u9A8C\\u7ED3\\u8BBA\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1122,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                padding: 8,\n                background: '#f0f8ff',\n                borderRadius: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: \"\\uD83D\\uDCA1 \\u63D0\\u793A\\uFF1A\\u7CFB\\u7EDF\\u4F1A\\u6839\\u636E\\u79D1\\u76EE\\u81EA\\u52A8\\u4E3A\\u6BCF\\u9875\\u4F5C\\u4E1A\\u5206\\u914D\\u5408\\u9002\\u7684\\u6807\\u7B7E\\uFF0C\\u8BF7\\u6309\\u987A\\u5E8F\\u4E0A\\u4F20\\u4F5C\\u4E1A\\u9875\\u9762\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1128,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 17\n          }, this),\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1099,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"assignment_id\",\n            label: \"\\u4F5C\\u4E1A\\u6807\\u9898\",\n            rules: [{\n              required: true,\n              message: '请选择作业标题'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u4F5C\\u4E1A\\u6807\\u9898\",\n              loading: loadingAssignments,\n              onChange: handleAssignmentChange,\n              children: assignments.map(assignment => /*#__PURE__*/_jsxDEV(Option, {\n                value: assignment.id,\n                children: assignment.title\n              }, assignment.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1145,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1140,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"class_id\",\n            label: \"\\u73ED\\u7EA7\",\n            rules: [{\n              required: true,\n              message: '请选择班级'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u73ED\\u7EA7\",\n              onChange: handleClassChange,\n              children: classes.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls.id,\n                children: cls.name\n              }, cls.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1168,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"description\",\n            label: \"\\u63CF\\u8FF0\",\n            children: /*#__PURE__*/_jsxDEV(TextArea, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F5C\\u4E1A\\u63CF\\u8FF0\\uFF08\\u53EF\\u9009\\uFF09\",\n              rows: 4\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1173,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1139,\n          columnNumber: 13\n        }, this), user.is_teacher && /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4E0A\\u4F20\\u6A21\\u5F0F\",\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: uploadMode,\n            onChange: e => setUploadMode(e.target.value),\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Radio.Button, {\n              value: \"manual\",\n              children: \"\\u624B\\u52A8\\u9009\\u62E9\\u5B66\\u751F\\u548C\\u6587\\u4EF6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Radio.Button, {\n              value: \"directory\",\n              children: \"\\u6587\\u4EF6\\u5939\\u76EE\\u5F55\\u6279\\u91CF\\u4E0A\\u4F20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1187,\n            columnNumber: 17\n          }, this), uploadMode === 'directory' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"directory-upload-info\",\n            children: /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u6587\\u4EF6\\u5939\\u76EE\\u5F55\\u6279\\u91CF\\u4E0A\\u4F20\\u8BF4\\u660E\",\n              description: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: \"1. \\u8BF7\\u5148\\u9009\\u62E9\\u73ED\\u7EA7\\uFF0C\\u518D\\u9009\\u62E9\\u5305\\u542B\\u5B66\\u751F\\u4F5C\\u4E1A\\u56FE\\u7247\\u7684\\u6587\\u4EF6\\u5939\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1202,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: \"2. \\u6587\\u4EF6\\u540D\\u9700\\u5305\\u542B\\u5B66\\u751F\\u59D3\\u540D\\u6216\\u7528\\u6237\\u540D\\uFF0C\\u7CFB\\u7EDF\\u5C06\\u81EA\\u52A8\\u5339\\u914D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1205,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: \"3. \\u652F\\u6301\\u7684\\u6587\\u4EF6\\u683C\\u5F0F\\uFF1AJPG\\u3001PNG\\u3001PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1208,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: \"4. \\u6BCF\\u4E2A\\u6587\\u4EF6\\u5927\\u5C0F\\u4E0D\\u8D85\\u8FC710MB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 25\n              }, this),\n              type: \"info\",\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1198,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1197,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 15\n        }, this), !user.is_teacher ? /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\uD83D\\uDCE4 \\u4E0A\\u4F20\\u4F5C\\u4E1A\\u56FE\\u7247\",\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u4F5C\\u4E1A\\u56FE\\u7247\", /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  marginLeft: 8,\n                  fontSize: '12px'\n                },\n                children: [\"(\", HOMEWORK_CONFIG.MIN_PAGES, \"-\", HOMEWORK_CONFIG.MAX_PAGES, \"\\u9875\\uFF0C\\u652F\\u6301\", HOMEWORK_CONFIG.SUPPORTED_FORMATS.join('、'), \"\\u683C\\u5F0F)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1229,\n              columnNumber: 21\n            }, this),\n            required: true,\n            tooltip: `上传作业图片，支持${HOMEWORK_CONFIG.SUPPORTED_FORMATS.join('、')}格式，每页不超过${HOMEWORK_CONFIG.MAX_FILE_SIZE_MB}MB`,\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              listType: \"picture-card\",\n              fileList: [],\n              beforeUpload: beforeUpload,\n              onChange: () => {},\n              multiple: true,\n              accept: HOMEWORK_CONFIG.SUPPORTED_FORMATS.join(','),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1248,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: 8\n                  },\n                  children: \"\\u4E0A\\u4F20\\u56FE\\u7247\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1249,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1239,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Alert, {\n                message: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"\\uD83D\\uDCCB \\u8BF7\\u6309\\u9875\\u9762\\u987A\\u5E8F\\u4E0A\\u4F20\\uFF1A\", getPageLabels().slice(0, HOMEWORK_CONFIG.MAX_PAGES).join(' → ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1256,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1255,\n                  columnNumber: 25\n                }, this),\n                type: \"info\",\n                showIcon: false,\n                style: {\n                  fontSize: '12px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1252,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1226,\n          columnNumber: 15\n        }, this) : uploadMode === 'directory' ? /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6587\\u4EF6\\u5939\\u76EE\\u5F55\\u6279\\u91CF\\u4E0A\\u4F20\",\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"directory-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              ref: directoryInputRef,\n              type: \"file\",\n              webkitdirectory: \"true\",\n              directory: \"true\",\n              multiple: true,\n              style: {\n                display: 'none'\n              },\n              onChange: handleDirectorySelect\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1271,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginBottom: 20\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                icon: /*#__PURE__*/_jsxDEV(FolderOpenOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1284,\n                  columnNumber: 29\n                }, this),\n                size: \"large\",\n                onClick: () => {\n                  const selectedClassId = form.getFieldValue('class_id');\n                  if (!selectedClassId) {\n                    message.warning('请先选择班级，再上传文件夹');\n                    return;\n                  }\n                  directoryInputRef.current && directoryInputRef.current.click();\n                },\n                disabled: processingFiles || classStudents.length === 0,\n                children: [processingFiles ? /*#__PURE__*/_jsxDEV(LoadingOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 42\n                }, this) : null, \"\\u9009\\u62E9\\u6587\\u4EF6\\u5939\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 8\n                },\n                children: classStudents.length === 0 ? /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"warning\",\n                  children: \"\\u8BF7\\u5148\\u9009\\u62E9\\u73ED\\u7EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u5DF2\\u9009\\u62E9 \", directoryFiles.length, \" \\u4E2A\\u6587\\u4EF6\\uFF0C \\u6210\\u529F\\u5339\\u914D \", matchedStudents.length, \" \\u4E2A\\u5B66\\u751F\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1303,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1299,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1281,\n              columnNumber: 19\n            }, this), directoryFiles.length > 0 && renderDirectoryMatchTable()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1270,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1269,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"batch-upload-section\",\n          children: [/*#__PURE__*/_jsxDEV(Divider, {\n            orientation: \"left\",\n            children: \"\\u6279\\u91CF\\u4E0A\\u4F20\\u5B66\\u751F\\u4F5C\\u4E1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1316,\n            columnNumber: 17\n          }, this), studentUploads.map((item, index) => renderStudentUploadItem(item, index)), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"dashed\",\n            onClick: addStudentUpload,\n            style: {\n              width: '100%',\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1327,\n              columnNumber: 19\n            }, this), \" \\u6DFB\\u52A0\\u5B66\\u751F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1322,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1315,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            disabled: uploadMode === 'directory' && matchedStudents.length === 0,\n            children: user.is_teacher ? '批量提交' : '提交作业'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            style: {\n              marginLeft: 8\n            },\n            onClick: () => navigate('/homework'),\n            children: \"\\u53D6\\u6D88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1341,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1332,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1093,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1064,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1061,\n    columnNumber: 5\n  }, this);\n};\n_s(HomeworkUpload, \"s+78yRdV4mo6lWL71cyxOC1Nm50=\", false, function () {\n  return [useNavigate, useLocation, Form.useForm];\n});\n_c = HomeworkUpload;\nexport default HomeworkUpload;\nvar _c;\n$RefreshReg$(_c, \"HomeworkUpload\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useNavigate", "useLocation", "Form", "Input", "Upload", "<PERSON><PERSON>", "Select", "Typography", "message", "Card", "Row", "Col", "Divider", "Spin", "Result", "Radio", "<PERSON><PERSON>", "Table", "Space", "UploadOutlined", "PlusOutlined", "DeleteOutlined", "CheckCircleOutlined", "FolderOpenOutlined", "FileImageOutlined", "LoadingOutlined", "batchUploadHomework", "getClasses", "getUsers", "getHomeworkAssignment", "getHomeworkAssignments", "getClass", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "Option", "TextArea", "HomeworkUpload", "user", "_s", "_location$state3", "navigate", "location", "form", "useForm", "loading", "setLoading", "submitSuccess", "setSubmitSuccess", "studentUploads", "setStudentUploads", "id", "studentId", "files", "classes", "setClasses", "students", "setStudents", "dataLoading", "setDataLoading", "assignments", "setAssignments", "loadingAssignments", "setLoadingAssignments", "uploadMode", "setUploadMode", "directoryFiles", "setDirectoryFiles", "matchedStudents", "setMatchedStudents", "processingFiles", "setProcessingFiles", "directoryInputRef", "classStudents", "setClassStudents", "assignmentId", "setAssignmentId", "handleClassChange", "classId", "console", "log", "classDetails", "Array", "isArray", "length", "prevUploads", "map", "item", "studentInClass", "some", "s", "warn", "warning", "error", "fetchAssignmentDetails", "assignmentDetails", "status", "class_id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "fetchData", "is_teacher", "_location$state", "classesData", "usersResponse", "studentUsers", "filter", "u", "items", "users", "assignmentsResponse", "assignmentsArray", "state", "latestAssignment", "assignment_id", "_location$state2", "handleAssignmentChange", "value", "selectedAssignment", "find", "a", "handleTeacherBatchUpload", "values", "validStudentUploads", "formData", "FormData", "append", "description", "studentData", "student", "studentName", "full_name", "username", "student_id", "student_name", "images", "file", "originFileObj", "name", "JSON", "stringify", "fileCount", "for<PERSON>ach", "fileName", "uniqueFileName", "Date", "now", "renamedFile", "File", "type", "studentIndex", "findIndex", "imageIndex", "img", "delete", "response", "success", "homeworks", "setTimeout", "refresh", "forceRefresh", "timestamp", "getTime", "handleDirectorySelect", "e", "target", "selectedClassId", "getFieldValue", "fileList", "from", "path", "webkitRelativePath", "size", "lastModified", "matched", "unmatched", "matchStudents", "fileInfo", "nameWithoutExt", "substring", "lastIndexOf", "matchedStudent", "bestMatchScore", "fullName", "includes", "score", "push", "matchScore", "updateFileStudentMatch", "fileIndex", "updatedFiles", "updatedMatched", "f", "handleDirectoryBatchUpload", "reduce", "acc", "studentEntry", "entry", "onFinish", "HOMEWORK_CONFIG", "MAX_PAGES", "MIN_PAGES", "SUPPORTED_FORMATS", "MAX_FILE_SIZE_MB", "MAX_TOTAL_SIZE_MB", "PAGE_LABELS", "getPageLabels", "subject", "validateSingleFile", "fileExtension", "split", "pop", "toLowerCase", "join", "fileSizeMB", "toFixed", "validateFileList", "<PERSON><PERSON><PERSON><PERSON>", "totalSize", "sum", "fileObj", "totalSizeMB", "beforeUpload", "LIST_IGNORE", "addStudentUpload", "newId", "Math", "max", "removeStudentUpload", "updateStudentId", "updateStudentFiles", "newFiles", "validation", "formatFileSize", "bytes", "k", "sizes", "i", "floor", "parseFloat", "pow", "getFileStatusTag", "children", "_jsxFileName", "lineNumber", "columnNumber", "pageLabe<PERSON>", "direction", "strong", "style", "color", "marginLeft", "index", "marginRight", "fontSize", "renderStudentUploadItem", "studentList", "studentInfo", "hasClassSelected", "classStudentsLength", "studentList<PERSON><PERSON><PERSON>", "className", "marginBottom", "padding", "border", "borderRadius", "gutter", "align", "span", "<PERSON><PERSON>", "label", "onChange", "width", "placeholder", "disabled", "marginTop", "multiple", "icon", "textAlign", "onClick", "background", "pageLabel", "fileSize", "display", "justifyContent", "email", "renderDirectoryMatchTable", "columns", "dataIndex", "key", "render", "text", "record", "showIcon", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "level", "spinning", "subTitle", "extra", "resetFields", "layout", "rules", "required", "assignment", "cls", "rows", "Group", "tooltip", "listType", "accept", "slice", "ref", "webkitdirectory", "directory", "current", "click", "orientation", "htmlType", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/HomeworkUpload.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport {\r\n  Form, Input, Upload, Button, Select, Typography, message,\r\n  Card, Row, Col, Divider, Spin, Result, Radio, Alert, Table, Space\r\n} from 'antd';\r\nimport { \r\n  UploadOutlined, PlusOutlined, DeleteOutlined, CheckCircleOutlined,\r\n  FolderOpenOutlined, FileImageOutlined, LoadingOutlined\r\n} from '@ant-design/icons';\r\nimport { batchUploadHomework, getClasses, getUsers, getHomeworkAssignment, getHomeworkAssignments, getClass } from '../utils/api';\r\n\r\nconst { Title, Text, Paragraph } = Typography;\r\nconst { Option } = Select;\r\nconst { TextArea } = Input;\r\n\r\nconst HomeworkUpload = ({ user }) => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [form] = Form.useForm();\r\n  const [loading, setLoading] = useState(false);\r\n  const [submitSuccess, setSubmitSuccess] = useState(false);\r\n  \r\n  // 批量上传的状态\r\n  const [studentUploads, setStudentUploads] = useState([\r\n    { id: 1, studentId: null, files: [] }\r\n  ]);\r\n  \r\n  // 添加班级和学生数据状态\r\n  const [classes, setClasses] = useState([]);\r\n  const [students, setStudents] = useState([]);\r\n  const [dataLoading, setDataLoading] = useState(false);\r\n  \r\n  // 添加作业任务列表状态\r\n  const [assignments, setAssignments] = useState([]);\r\n  const [loadingAssignments, setLoadingAssignments] = useState(false);\r\n  \r\n  // 添加上传模式状态\r\n  const [uploadMode, setUploadMode] = useState('manual'); // 'manual'(手动) 或 'directory'(目录)\r\n  \r\n  // 目录上传相关状态\r\n  const [directoryFiles, setDirectoryFiles] = useState([]);\r\n  const [matchedStudents, setMatchedStudents] = useState([]);\r\n  const [processingFiles, setProcessingFiles] = useState(false);\r\n  const directoryInputRef = useRef(null);\r\n  \r\n  // 添加当前选择的班级学生列表状态\r\n  const [classStudents, setClassStudents] = useState([]);\r\n  \r\n  // 添加作业任务ID状态\r\n  const [assignmentId, setAssignmentId] = useState(null);\r\n  \r\n  // 当选择班级时筛选学生列表\r\n  const handleClassChange = async (classId) => {\r\n    if (!classId) {\r\n      setClassStudents([]);\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      // 使用getClass API获取班级详情，包括学生列表\r\n      console.log(`开始获取班级 ${classId} 详情...`);\r\n      const classDetails = await getClass(classId);\r\n      console.log(`班级 ${classId} 详情:`, classDetails);\r\n      \r\n      if (classDetails && classDetails.students && Array.isArray(classDetails.students)) {\r\n        console.log(`班级 ${classId} 的学生:`, classDetails.students);\r\n        console.log(`学生数量: ${classDetails.students.length}`);\r\n        \r\n        // 检查学生数据结构\r\n        if (classDetails.students.length > 0) {\r\n          console.log('第一个学生数据示例:', classDetails.students[0]);\r\n        }\r\n        \r\n        setClassStudents(classDetails.students);\r\n        \r\n        // 清除已选择的学生ID（如果不在当前班级中）\r\n        setStudentUploads(prevUploads => \r\n          prevUploads.map(item => {\r\n            const studentInClass = classDetails.students.some(s => s.id === item.studentId);\r\n            return studentInClass ? item : { ...item, studentId: null };\r\n          })\r\n        );\r\n      } else {\r\n        console.warn(`班级 ${classId} 没有学生数据`);\r\n        console.warn('classDetails结构:', classDetails);\r\n        setClassStudents([]);\r\n        message.warning('该班级暂无学生，请先添加学生到班级');\r\n      }\r\n    } catch (error) {\r\n      console.error(`获取班级 ${classId} 学生列表失败:`, error);\r\n      message.error('获取班级学生列表失败，请重试');\r\n      setClassStudents([]);\r\n    }\r\n  };\r\n  \r\n  // 定义fetchAssignmentDetails函数 - 确保在handleClassChange之后定义\r\n  const fetchAssignmentDetails = async (id) => {\r\n    try {\r\n      const assignmentDetails = await getHomeworkAssignment(id);\r\n      console.log(\"获取到的作业任务详情:\", assignmentDetails);\r\n      \r\n      // 检查作业任务状态，如果已结束则显示提示并返回上一页\r\n      if (assignmentDetails && assignmentDetails.status === 'finished') {\r\n        message.error('该作业任务已结束，不能再上传作业');\r\n        navigate('/homework');\r\n        return;\r\n      }\r\n      \r\n      // 如果作业任务有关联班级，则自动设置班级信息\r\n      if (assignmentDetails && assignmentDetails.class_id) {\r\n        console.log(\"设置班级ID:\", assignmentDetails.class_id);\r\n        form.setFieldsValue({\r\n          class_id: assignmentDetails.class_id\r\n        });\r\n        \r\n        // 在表单标题中设置作业标题（不包括班级名称）\r\n        if (assignmentDetails.title) {\r\n          form.setFieldsValue({\r\n            title: assignmentDetails.title\r\n          });\r\n        }\r\n        \r\n        // 立即加载班级学生列表\r\n        console.log(\"自动加载班级学生列表\");\r\n        handleClassChange(assignmentDetails.class_id);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"获取作业任务详情失败:\", error);\r\n    }\r\n  };\r\n  \r\n  // 获取班级、学生和作业任务数据\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (user && user.is_teacher) {\r\n        setDataLoading(true);\r\n        try {\r\n          // 获取班级数据\r\n          console.log('获取班级数据');\r\n          const classesData = await getClasses();\r\n          console.log('班级数据:', classesData);\r\n          setClasses(classesData || []);\r\n          \r\n          // 获取学生数据\r\n          console.log('获取学生数据');\r\n          const usersResponse = await getUsers();\r\n          console.log('用户数据:', usersResponse);\r\n          \r\n          // 处理不同的响应格式\r\n          let studentUsers = [];\r\n          if (usersResponse) {\r\n            if (Array.isArray(usersResponse)) {\r\n              // 处理数组格式响应\r\n              studentUsers = usersResponse.filter(u => !u.is_teacher);\r\n            } else if (usersResponse.items && Array.isArray(usersResponse.items)) {\r\n              // 处理对象格式响应，包含items数组\r\n              studentUsers = usersResponse.items.filter(u => !u.is_teacher);\r\n            } else if (usersResponse.users && Array.isArray(usersResponse.users)) {\r\n              // 处理对象格式响应，包含users数组\r\n              studentUsers = usersResponse.users.filter(u => !u.is_teacher);\r\n            }\r\n          }\r\n          \r\n          console.log('过滤后的学生数据:', studentUsers);\r\n          setStudents(studentUsers || []);\r\n          \r\n          // 获取作业任务列表\r\n          setLoadingAssignments(true);\r\n          const assignmentsResponse = await getHomeworkAssignments();\r\n          console.log('获取到的作业任务列表:', assignmentsResponse);\r\n          // 确保我们有一个有效的数组\r\n          let assignmentsArray = [];\r\n          if (assignmentsResponse && assignmentsResponse.items) {\r\n            assignmentsArray = assignmentsResponse.items || [];\r\n            setAssignments(assignmentsArray);\r\n          } else if (Array.isArray(assignmentsResponse)) {\r\n            assignmentsArray = assignmentsResponse;\r\n            setAssignments(assignmentsArray);\r\n          } else {\r\n            setAssignments([]);\r\n          }\r\n          \r\n          // 默认选择最新的作业任务（列表中的第一个）\r\n          if (assignmentsArray.length > 0 && !location.state?.assignmentId) {\r\n            const latestAssignment = assignmentsArray[0];\r\n            console.log('默认选择最新作业任务:', latestAssignment.title);\r\n            form.setFieldsValue({\r\n              assignment_id: latestAssignment.id,\r\n              title: latestAssignment.title\r\n            });\r\n            \r\n            // 如果最新作业任务有班级信息，也自动设置班级\r\n            if (latestAssignment.class_id) {\r\n              form.setFieldsValue({\r\n                class_id: latestAssignment.class_id\r\n              });\r\n              \r\n              // 更新班级学生列表\r\n              handleClassChange(latestAssignment.class_id);\r\n            }\r\n          }\r\n          \r\n          setLoadingAssignments(false);\r\n        } catch (error) {\r\n          console.error('获取数据失败:', error);\r\n          message.error('获取数据失败，请检查网络连接');\r\n          setLoadingAssignments(false);\r\n        } finally {\r\n          setDataLoading(false);\r\n        }\r\n      }\r\n    };\r\n    \r\n    fetchData();\r\n  }, [user]);\r\n  \r\n  // 初始化表单和获取作业任务信息\r\n  useEffect(() => {\r\n    if (location.state?.assignmentId) {\r\n      console.log('从路由参数获取到作业任务ID:', location.state.assignmentId);\r\n      setAssignmentId(location.state.assignmentId);\r\n      form.setFieldsValue({\r\n        assignment_id: location.state.assignmentId\r\n      });\r\n      fetchAssignmentDetails(location.state.assignmentId);\r\n    } else {\r\n      console.log('未从路由参数获取到作业任务ID');\r\n    }\r\n  }, [location.state?.assignmentId, form]); \r\n  \r\n  // 检查是否有预设的作业任务ID\r\n  useEffect(() => {\r\n    console.log(\"HomeworkUpload - location.state:\", location.state);\r\n    if (location.state) {\r\n      if (location.state.assignmentId) {\r\n        console.log(\"设置作业任务ID:\", location.state.assignmentId);\r\n        form.setFieldsValue({\r\n          assignment_id: location.state.assignmentId\r\n        });\r\n        \r\n        // 获取作业任务详情以获取班级信息\r\n        fetchAssignmentDetails(location.state.assignmentId);\r\n      }\r\n    }\r\n  }, [location.state, form]);\r\n  \r\n  // 当选择作业任务时自动设置标题和班级\r\n  const handleAssignmentChange = (value) => {\r\n    const selectedAssignment = assignments.find(a => a.id === value);\r\n    if (selectedAssignment) {\r\n      form.setFieldsValue({\r\n        title: selectedAssignment.title\r\n      });\r\n      \r\n      // 如果作业任务有班级信息，也自动设置班级\r\n      if (selectedAssignment.class_id) {\r\n        form.setFieldsValue({\r\n          class_id: selectedAssignment.class_id\r\n        });\r\n        \r\n        // 更新班级学生列表\r\n        handleClassChange(selectedAssignment.class_id);\r\n      }\r\n    }\r\n  };\r\n  \r\n  // 处理教师批量上传\r\n  const handleTeacherBatchUpload = async (values) => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // 检查学生ID和文件\r\n      const validStudentUploads = studentUploads.filter(\r\n        item => item.studentId && item.files.length > 0\r\n      );\r\n      \r\n      if (validStudentUploads.length === 0) {\r\n        message.error('请至少选择一个学生并上传对应的作业文件');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 构建表单数据\r\n      const formData = new FormData();\r\n      \r\n      // 使用选择的作业任务作为标题\r\n      if (values.assignment_id) {\r\n        const selectedAssignment = assignments.find(a => a.id === values.assignment_id);\r\n        if (selectedAssignment) {\r\n          formData.append('title', selectedAssignment.title);\r\n        } else {\r\n          formData.append('title', '未命名作业');\r\n        }\r\n      } else {\r\n        formData.append('title', '未命名作业');\r\n      }\r\n      \r\n      if (values.description) {\r\n        formData.append('description', values.description);\r\n      }\r\n      \r\n      if (values.assignment_id) {\r\n        formData.append('assignment_id', values.assignment_id);\r\n      }\r\n      \r\n      // 确保班级ID被正确添加到表单\r\n      if (values.class_id) {\r\n        formData.append('class_id', values.class_id);\r\n        console.log('添加班级ID到表单:', values.class_id);\r\n      }\r\n      \r\n      // 构建学生数据\r\n      const studentData = validStudentUploads.map(item => {\r\n        // 获取学生信息\r\n        const student = students.find(s => s.id === item.studentId);\r\n        const studentName = student ? (student.full_name || student.username) : '未知学生';\r\n        \r\n        return {\r\n          student_id: item.studentId,\r\n          student_name: studentName,\r\n          images: item.files.map(file => file.originFileObj.name)\r\n        };\r\n      });\r\n      \r\n      console.log('批量上传学生数据:', studentData);\r\n      formData.append('student_data', JSON.stringify(studentData));\r\n      \r\n      // 批量上传文件\r\n      let fileCount = 0;\r\n      validStudentUploads.forEach(item => {\r\n        item.files.forEach(file => {\r\n          if (file.originFileObj) {\r\n            // 确保文件名唯一，避免同名文件覆盖\r\n            const fileName = file.originFileObj.name;\r\n            const uniqueFileName = `${item.studentId}_${Date.now()}_${fileName}`;\r\n            \r\n            // 创建一个新的File对象，重命名文件\r\n            const renamedFile = new File(\r\n              [file.originFileObj], \r\n              uniqueFileName, \r\n              { type: file.originFileObj.type }\r\n            );\r\n            \r\n            formData.append('files', renamedFile);\r\n            fileCount++;\r\n            \r\n            // 更新studentData中的文件名\r\n            const studentIndex = studentData.findIndex(s => s.student_id === item.studentId);\r\n            if (studentIndex !== -1) {\r\n              const imageIndex = studentData[studentIndex].images.findIndex(img => img === fileName);\r\n              if (imageIndex !== -1) {\r\n                studentData[studentIndex].images[imageIndex] = uniqueFileName;\r\n              }\r\n            }\r\n          }\r\n        });\r\n      });\r\n      \r\n      // 更新formData中的studentData\r\n      formData.delete('student_data');\r\n      formData.append('student_data', JSON.stringify(studentData));\r\n      \r\n      console.log(`准备上传 ${fileCount} 个文件，${validStudentUploads.length} 个学生的作业`);\r\n      \r\n      // 提交批量作业\r\n      const response = await batchUploadHomework(formData);\r\n      console.log('批量上传作业响应:', response);\r\n      \r\n      // 设置提交成功状态\r\n      setSubmitSuccess(true);\r\n      message.success('批量上传作业成功！');\r\n      \r\n      // 显示上传结果详情\r\n      if (response && response.homeworks) {\r\n        console.log('上传的作业详情:', response.homeworks);\r\n      }\r\n      \r\n      // 延迟跳转，确保消息显示完成\r\n      setTimeout(() => {\r\n        // 刷新作业列表页面，添加强制刷新参数和时间戳\r\n        navigate('/homework/pending-review', { \r\n          state: { \r\n            refresh: true, \r\n            forceRefresh: true,\r\n            timestamp: new Date().getTime() \r\n          }\r\n        });\r\n      }, 2000);\r\n    } catch (error) {\r\n      console.error('批量上传失败:', error);\r\n      message.error(`批量上传失败: ${error.message || '请检查网络连接或文件格式'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 处理目录选择\r\n  const handleDirectorySelect = async (e) => {\r\n    try {\r\n      setProcessingFiles(true);\r\n      const files = e.target.files;\r\n      if (!files || files.length === 0) {\r\n        setProcessingFiles(false);\r\n        return;\r\n      }\r\n      \r\n      console.log('选择的文件数量:', files.length);\r\n      \r\n      // 检查是否已选择班级\r\n      const selectedClassId = form.getFieldValue('class_id');\r\n      if (!selectedClassId) {\r\n        message.warning('请先选择班级，再上传文件夹');\r\n        setProcessingFiles(false);\r\n        return;\r\n      }\r\n      \r\n      // 如果班级学生列表为空，尝试重新获取班级学生\r\n      if (classStudents.length === 0) {\r\n        try {\r\n          const classDetails = await getClass(selectedClassId);\r\n          if (classDetails && classDetails.students && Array.isArray(classDetails.students)) {\r\n            setClassStudents(classDetails.students);\r\n          } else {\r\n            message.error('所选班级没有学生信息，请先添加学生到班级');\r\n            setProcessingFiles(false);\r\n            return;\r\n          }\r\n        } catch (error) {\r\n          console.error('获取班级学生失败:', error);\r\n          message.error('获取班级学生失败，请重试');\r\n          setProcessingFiles(false);\r\n          return;\r\n        }\r\n      }\r\n      \r\n      // 收集文件信息\r\n      const fileList = Array.from(files).map(file => ({\r\n        file,\r\n        name: file.name,\r\n        path: file.webkitRelativePath || file.name,\r\n        size: file.size,\r\n        type: file.type,\r\n        lastModified: file.lastModified,\r\n        matched: false,\r\n        studentId: null,\r\n        studentName: null\r\n      }));\r\n      \r\n      setDirectoryFiles(fileList);\r\n      \r\n      // 尝试匹配学生\r\n      const matched = [];\r\n      const unmatched = [];\r\n      \r\n      // 使用班级学生列表\r\n      const matchStudents = classStudents;\r\n      \r\n      if (matchStudents.length === 0) {\r\n        message.warning('所选班级没有学生信息，请先添加学生到班级');\r\n        setProcessingFiles(false);\r\n        return;\r\n      }\r\n      \r\n      fileList.forEach(fileInfo => {\r\n        // 从文件名中提取学生名字（去掉扩展名）\r\n        const fileName = fileInfo.name;\r\n        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;\r\n        \r\n        // 尝试匹配学生，使用更严格的匹配逻辑\r\n        let matchedStudent = null;\r\n        let bestMatchScore = 0;\r\n        \r\n        // 首先尝试完全匹配\r\n        matchStudents.forEach(student => {\r\n          const fullName = student.full_name || '';\r\n          const username = student.username || '';\r\n          \r\n          // 检查文件名是否完全包含学生姓名或用户名\r\n          if (nameWithoutExt === fullName || nameWithoutExt === username) {\r\n            matchedStudent = student;\r\n            bestMatchScore = 100; // 完全匹配赋予最高分\r\n          }\r\n          // 检查文件名是否包含学生姓名或用户名\r\n          else if (nameWithoutExt.includes(fullName) && fullName.length > 1) {\r\n            const score = fullName.length;\r\n            if (score > bestMatchScore) {\r\n              matchedStudent = student;\r\n              bestMatchScore = score;\r\n            }\r\n          }\r\n          else if (nameWithoutExt.includes(username) && username.length > 1) {\r\n            const score = username.length;\r\n            if (score > bestMatchScore) {\r\n              matchedStudent = student;\r\n              bestMatchScore = score;\r\n            }\r\n          }\r\n        });\r\n        \r\n        // 如果找到匹配的学生\r\n        if (matchedStudent && bestMatchScore > 1) {\r\n          fileInfo.matched = true;\r\n          fileInfo.studentId = matchedStudent.id;\r\n          fileInfo.studentName = matchedStudent.full_name || matchedStudent.username;\r\n          matched.push({\r\n            ...fileInfo,\r\n            student: matchedStudent,\r\n            matchScore: bestMatchScore\r\n          });\r\n        } else {\r\n          unmatched.push(fileInfo);\r\n        }\r\n      });\r\n      \r\n      setMatchedStudents(matched);\r\n      setDirectoryFiles([...matched, ...unmatched]);\r\n      \r\n      if (matched.length === 0) {\r\n        message.warning('未能匹配任何学生，请手动为文件指定学生');\r\n      } else {\r\n        message.success(`成功匹配 ${matched.length}/${fileList.length} 个文件与学生${unmatched.length > 0 ? '，剩余' + unmatched.length + '个文件需要手动指定学生' : ''}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('处理目录文件失败:', error);\r\n      message.error('处理目录文件失败: ' + (error.message || '未知错误'));\r\n    } finally {\r\n      setProcessingFiles(false);\r\n    }\r\n  };\r\n  \r\n  // 手动更新文件与学生的匹配关系\r\n  const updateFileStudentMatch = (fileIndex, studentId) => {\r\n    const updatedFiles = [...directoryFiles];\r\n    const file = updatedFiles[fileIndex];\r\n    \r\n    if (file) {\r\n      // 使用班级学生列表而不是所有学生\r\n      const student = classStudents.find(s => s.id === studentId);\r\n      file.studentId = studentId;\r\n      file.matched = !!student;\r\n      file.studentName = student ? (student.full_name || student.username) : null;\r\n      \r\n      setDirectoryFiles(updatedFiles);\r\n      \r\n      // 更新匹配的学生列表\r\n      const updatedMatched = updatedFiles.filter(f => f.matched).map(f => ({\r\n        ...f,\r\n        student: classStudents.find(s => s.id === f.studentId)\r\n      }));\r\n      \r\n      setMatchedStudents(updatedMatched);\r\n    }\r\n  };\r\n  \r\n  // 处理目录批量上传\r\n  const handleDirectoryBatchUpload = async (values) => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // 检查匹配的文件\r\n      if (matchedStudents.length === 0) {\r\n        message.error('没有匹配的学生文件，请先选择目录并确保文件名包含学生姓名');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      \r\n      // 构建表单数据\r\n      const formData = new FormData();\r\n      \r\n      // 使用选择的作业任务作为标题\r\n      if (values.assignment_id) {\r\n        const selectedAssignment = assignments.find(a => a.id === values.assignment_id);\r\n        if (selectedAssignment) {\r\n          formData.append('title', selectedAssignment.title);\r\n        } else {\r\n          formData.append('title', '未命名作业');\r\n        }\r\n      } else {\r\n        formData.append('title', '未命名作业');\r\n      }\r\n      \r\n      if (values.description) {\r\n        formData.append('description', values.description);\r\n      }\r\n      \r\n      if (values.assignment_id) {\r\n        formData.append('assignment_id', values.assignment_id);\r\n      }\r\n      \r\n      // 确保班级ID被正确添加到表单\r\n      if (values.class_id) {\r\n        formData.append('class_id', values.class_id);\r\n        console.log('添加班级ID到表单:', values.class_id);\r\n      }\r\n      \r\n      // 构建学生数据\r\n      const studentData = matchedStudents.reduce((acc, fileInfo) => {\r\n        const studentId = fileInfo.studentId;\r\n        if (!studentId) return acc;\r\n        \r\n        // 检查学生是否已存在于累加器中\r\n        let studentEntry = acc.find(entry => entry.student_id === studentId);\r\n        \r\n        if (!studentEntry) {\r\n          // 如果学生不存在，创建新条目\r\n          const student = classStudents.find(s => s.id === studentId);\r\n          const studentName = student ? (student.full_name || student.username) : '未知学生';\r\n          \r\n          studentEntry = {\r\n            student_id: studentId,\r\n            student_name: studentName,\r\n            images: []\r\n          };\r\n          acc.push(studentEntry);\r\n        }\r\n        \r\n        // 添加文件名到学生的images数组\r\n        const uniqueFileName = `${studentId}_${Date.now()}_${fileInfo.name}`;\r\n        studentEntry.images.push(uniqueFileName);\r\n        \r\n        // 创建一个新的File对象，重命名文件\r\n        const renamedFile = new File(\r\n          [fileInfo.file], \r\n          uniqueFileName, \r\n          { type: fileInfo.file.type }\r\n        );\r\n        \r\n        // 添加文件到formData\r\n        formData.append('files', renamedFile);\r\n        \r\n        return acc;\r\n      }, []);\r\n      \r\n      console.log('目录批量上传学生数据:', studentData);\r\n      formData.append('student_data', JSON.stringify(studentData));\r\n      \r\n      console.log(`准备上传 ${matchedStudents.length} 个文件，${studentData.length} 个学生的作业`);\r\n      \r\n      // 提交批量作业\r\n      const response = await batchUploadHomework(formData);\r\n      console.log('批量上传作业响应:', response);\r\n      \r\n      // 设置提交成功状态\r\n      setSubmitSuccess(true);\r\n      message.success('批量上传作业成功！');\r\n      \r\n      // 显示上传结果详情\r\n      if (response && response.homeworks) {\r\n        console.log('上传的作业详情:', response.homeworks);\r\n      }\r\n      \r\n      // 延迟跳转，确保消息显示完成\r\n      setTimeout(() => {\r\n        // 刷新作业列表页面，添加强制刷新参数和时间戳\r\n        navigate('/homework/pending-review', { \r\n          state: { \r\n            refresh: true, \r\n            forceRefresh: true,\r\n            timestamp: new Date().getTime() \r\n          }\r\n        });\r\n      }, 2000);\r\n    } catch (error) {\r\n      console.error('目录批量上传失败:', error);\r\n      message.error(`目录批量上传失败: ${error.message || '请检查网络连接或文件格式'}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  // 表单提交处理\r\n  const onFinish = (values) => {\r\n    if (uploadMode === 'directory') {\r\n      handleDirectoryBatchUpload(values);\r\n    } else {\r\n      handleTeacherBatchUpload(values);\r\n    }\r\n  };\r\n  \r\n  // 作业配置常量\r\n  const HOMEWORK_CONFIG = {\r\n    MAX_PAGES: 4,\r\n    MIN_PAGES: 1,\r\n    SUPPORTED_FORMATS: ['.jpg', '.jpeg', '.png', '.bmp', '.webp'],\r\n    MAX_FILE_SIZE_MB: 10,\r\n    MAX_TOTAL_SIZE_MB: 40\r\n  };\r\n\r\n  // 科目特定的页面标签\r\n  const PAGE_LABELS = {\r\n    'math': ['第1页', '第2页', '第3页', '第4页'],\r\n    'english': ['听力部分', '阅读部分', '写作部分', '第4页'],\r\n    'physics': ['实验步骤', '实验数据', '实验结果', '实验总结'],\r\n    'chemistry': ['实验过程', '化学方程式', '实验现象', '实验结论'],\r\n    'biology': ['观察记录', '实验步骤', '实验结果', '分析总结'],\r\n    'chinese': ['第1页', '第2页', '第3页', '第4页'],\r\n    'history': ['第1页', '第2页', '第3页', '第4页'],\r\n    'geography': ['第1页', '第2页', '第3页', '第4页']\r\n  };\r\n\r\n  // 获取科目特定的页面标签\r\n  const getPageLabels = (subject = 'math') => {\r\n    return PAGE_LABELS[subject] || PAGE_LABELS['math'];\r\n  };\r\n\r\n  // 验证单个文件\r\n  const validateSingleFile = (file) => {\r\n    // 检查文件格式\r\n    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();\r\n    if (!HOMEWORK_CONFIG.SUPPORTED_FORMATS.includes(fileExtension)) {\r\n      message.error(`不支持的文件格式: ${fileExtension}，请使用 ${HOMEWORK_CONFIG.SUPPORTED_FORMATS.join(', ')} 格式`);\r\n      return false;\r\n    }\r\n\r\n    // 检查文件大小\r\n    const fileSizeMB = file.size / 1024 / 1024;\r\n    if (fileSizeMB > HOMEWORK_CONFIG.MAX_FILE_SIZE_MB) {\r\n      message.error(`文件大小超过限制: ${fileSizeMB.toFixed(1)}MB > ${HOMEWORK_CONFIG.MAX_FILE_SIZE_MB}MB`);\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  // 验证文件列表\r\n  const validateFileList = (fileList, studentId = null) => {\r\n    if (!fileList || fileList.length === 0) {\r\n      return { isValid: false, message: '请至少上传1页作业' };\r\n    }\r\n\r\n    // 检查页数限制\r\n    if (fileList.length > HOMEWORK_CONFIG.MAX_PAGES) {\r\n      return {\r\n        isValid: false,\r\n        message: `作业页数超过限制: ${fileList.length} > ${HOMEWORK_CONFIG.MAX_PAGES}页`\r\n      };\r\n    }\r\n\r\n    if (fileList.length < HOMEWORK_CONFIG.MIN_PAGES) {\r\n      return {\r\n        isValid: false,\r\n        message: `作业页数不足: ${fileList.length} < ${HOMEWORK_CONFIG.MIN_PAGES}页`\r\n      };\r\n    }\r\n\r\n    // 检查总文件大小\r\n    const totalSize = fileList.reduce((sum, file) => {\r\n      const fileObj = file.originFileObj || file;\r\n      return sum + (fileObj.size || 0);\r\n    }, 0);\r\n    const totalSizeMB = totalSize / 1024 / 1024;\r\n\r\n    if (totalSizeMB > HOMEWORK_CONFIG.MAX_TOTAL_SIZE_MB) {\r\n      return {\r\n        isValid: false,\r\n        message: `总文件大小超过限制: ${totalSizeMB.toFixed(1)}MB > ${HOMEWORK_CONFIG.MAX_TOTAL_SIZE_MB}MB`\r\n      };\r\n    }\r\n\r\n    return { isValid: true, message: '' };\r\n  };\r\n\r\n  // 处理文件上传前的验证\r\n  const beforeUpload = (file, fileList) => {\r\n    if (!validateSingleFile(file)) {\r\n      return Upload.LIST_IGNORE;\r\n    }\r\n\r\n    // 检查是否超过页数限制\r\n    if (fileList && fileList.length >= HOMEWORK_CONFIG.MAX_PAGES) {\r\n      message.error(`最多只能上传 ${HOMEWORK_CONFIG.MAX_PAGES} 页作业`);\r\n      return Upload.LIST_IGNORE;\r\n    }\r\n\r\n    return false; // 阻止自动上传\r\n  };\r\n  \r\n  // 添加学生上传项（教师批量模式）\r\n  const addStudentUpload = () => {\r\n    const newId = studentUploads.length > 0 \r\n      ? Math.max(...studentUploads.map(item => item.id)) + 1 \r\n      : 1;\r\n    \r\n    setStudentUploads([...studentUploads, { id: newId, studentId: null, files: [] }]);\r\n  };\r\n  \r\n  // 移除学生上传项（教师批量模式）\r\n  const removeStudentUpload = (id) => {\r\n    if (studentUploads.length <= 1) {\r\n      message.warning('至少需要保留一个学生');\r\n      return;\r\n    }\r\n    setStudentUploads(studentUploads.filter(item => item.id !== id));\r\n  };\r\n  \r\n  // 更新学生ID（教师批量模式）\r\n  const updateStudentId = (id, studentId) => {\r\n    setStudentUploads(\r\n      studentUploads.map(item => \r\n        item.id === id ? { ...item, studentId } : item\r\n      )\r\n    );\r\n  };\r\n  \r\n  // 更新学生文件（教师批量模式）\r\n  const updateStudentFiles = (id, newFiles) => {\r\n    // 验证文件列表\r\n    const validation = validateFileList(newFiles, id);\r\n\r\n    // 更新文件列表\r\n    setStudentUploads(\r\n      studentUploads.map(item =>\r\n        item.id === id ? { ...item, files: newFiles } : item\r\n      )\r\n    );\r\n\r\n    // 如果验证失败，显示警告（但不阻止更新，让用户可以继续调整）\r\n    if (!validation.isValid && newFiles.length > 0) {\r\n      message.warning(`学生 ${id}: ${validation.message}`);\r\n    }\r\n  };\r\n\r\n  // 格式化文件大小\r\n  const formatFileSize = (bytes) => {\r\n    if (bytes === 0) return '0 B';\r\n    const k = 1024;\r\n    const sizes = ['B', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  // 获取文件状态标签\r\n  const getFileStatusTag = (fileList, subject = 'math') => {\r\n    if (!fileList || fileList.length === 0) {\r\n      return <Text type=\"secondary\">未上传</Text>;\r\n    }\r\n\r\n    const validation = validateFileList(fileList);\r\n    const pageLabels = getPageLabels(subject);\r\n\r\n    return (\r\n      <Space direction=\"vertical\" size=\"small\">\r\n        <div>\r\n          <Text strong>已上传 {fileList.length}/{HOMEWORK_CONFIG.MAX_PAGES} 页</Text>\r\n          {validation.isValid ?\r\n            <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: 8 }} /> :\r\n            <Text type=\"warning\" style={{ marginLeft: 8 }}>({validation.message})</Text>\r\n          }\r\n        </div>\r\n        <div>\r\n          {fileList.map((file, index) => (\r\n            <span key={index} style={{ marginRight: 8 }}>\r\n              <Text type=\"secondary\">{pageLabels[index] || `第${index + 1}页`}</Text>\r\n            </span>\r\n          ))}\r\n        </div>\r\n        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n          总大小: {formatFileSize(fileList.reduce((sum, file) => sum + (file.size || 0), 0))}\r\n        </Text>\r\n      </Space>\r\n    );\r\n  };\r\n\r\n  // 渲染学生上传项\r\n  const renderStudentUploadItem = (item, index) => {\r\n    // 使用班级学生列表而不是所有学生\r\n    const studentList = classStudents.length > 0 ? classStudents : students;\r\n    const studentInfo = studentList.find(s => s.id === item.studentId);\r\n    const studentName = studentInfo ? (studentInfo.full_name || studentInfo.username) : `学生ID: ${item.studentId}`;\r\n    \r\n    // 获取当前选择的班级ID\r\n    const selectedClassId = form.getFieldValue('class_id');\r\n    const hasClassSelected = !!selectedClassId;\r\n    \r\n    console.log(`渲染学生上传项 ${index}:`, {\r\n      selectedClassId,\r\n      hasClassSelected,\r\n      classStudentsLength: classStudents.length,\r\n      studentListLength: studentList.length,\r\n      studentId: item.studentId,\r\n      studentInfo: studentInfo\r\n    });\r\n    \r\n    return (\r\n      <div key={index} className=\"student-upload-item\" style={{ marginBottom: 16, padding: 16, border: '1px solid #eee', borderRadius: 4 }}>\r\n        <Row gutter={16} align=\"middle\">\r\n          <Col span={6}>\r\n            <Form.Item label=\"学生\">\r\n              <Select\r\n                value={item.studentId}\r\n                onChange={value => updateStudentId(item.id, value)}\r\n                style={{ width: '100%' }}\r\n                placeholder={hasClassSelected ? \"选择学生\" : \"请先选择班级\"}\r\n                disabled={!hasClassSelected}\r\n              >\r\n                {studentList.map(student => (\r\n                  <Option key={student.id} value={student.id}>\r\n                    {student.full_name || student.username}\r\n                  </Option>\r\n                ))}\r\n              </Select>\r\n              {hasClassSelected && studentList.length === 0 && (\r\n                <div style={{ color: 'orange', marginTop: 4 }}>\r\n                  该班级暂无学生，请先添加学生到班级\r\n                </div>\r\n              )}\r\n            </Form.Item>\r\n          </Col>\r\n          \r\n          <Col span={12}>\r\n            <Form.Item label=\"作业文件\">\r\n              <Upload\r\n                fileList={item.files}\r\n                onChange={({ fileList }) => updateStudentFiles(item.id, fileList)}\r\n                beforeUpload={beforeUpload}\r\n                multiple\r\n              >\r\n                <Button icon={<UploadOutlined />}>选择文件</Button>\r\n              </Upload>\r\n            </Form.Item>\r\n          </Col>\r\n          \r\n          <Col span={6} style={{ textAlign: 'right' }}>\r\n            <Button \r\n              type=\"danger\" \r\n              icon={<DeleteOutlined />}\r\n              onClick={() => removeStudentUpload(item.id)}\r\n            >\r\n              移除\r\n            </Button>\r\n          </Col>\r\n        </Row>\r\n        \r\n        {/* 显示文件状态信息 */}\r\n        <div style={{ marginTop: 12, padding: 12, background: '#f9f9f9', borderRadius: 6 }}>\r\n          <Row gutter={16}>\r\n            <Col span={12}>\r\n              <div style={{ marginBottom: 8 }}>\r\n                {getFileStatusTag(item.files)}\r\n              </div>\r\n            </Col>\r\n            <Col span={12}>\r\n              {item.files.length > 0 && (\r\n                <div>\r\n                  <Text strong style={{ fontSize: '12px', color: '#666' }}>文件列表:</Text>\r\n                  <div style={{ marginTop: 4 }}>\r\n                    {item.files.map((file, fileIndex) => {\r\n                      const pageLabels = getPageLabels();\r\n                      const pageLabel = pageLabels[fileIndex] || `第${fileIndex + 1}页`;\r\n                      const fileName = file.name || (file.originFileObj && file.originFileObj.name) || `文件 ${fileIndex + 1}`;\r\n                      const fileSize = file.size || (file.originFileObj && file.originFileObj.size) || 0;\r\n\r\n                      return (\r\n                        <div key={fileIndex} style={{\r\n                          fontSize: '12px',\r\n                          color: '#666',\r\n                          marginBottom: '2px',\r\n                          display: 'flex',\r\n                          justifyContent: 'space-between'\r\n                        }}>\r\n                          <span>\r\n                            <Text type=\"secondary\">{pageLabel}:</Text> {fileName}\r\n                          </span>\r\n                          <Text type=\"secondary\">{formatFileSize(fileSize)}</Text>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </Col>\r\n          </Row>\r\n        </div>\r\n        \r\n        <div style={{ marginTop: 8 }}>\r\n          <Typography.Text type=\"secondary\">\r\n            学生: <Typography.Text strong>{studentName}</Typography.Text>\r\n            {studentInfo && studentInfo.email && ` (${studentInfo.email})`}\r\n          </Typography.Text>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 渲染目录上传匹配表格\r\n  const renderDirectoryMatchTable = () => {\r\n    const columns = [\r\n      {\r\n        title: '文件名',\r\n        dataIndex: 'name',\r\n        key: 'name',\r\n        render: (text, record) => (\r\n          <div>\r\n            <FileImageOutlined style={{ marginRight: 8 }} />\r\n            {text}\r\n            <div style={{ fontSize: '12px', color: '#999' }}>\r\n              {(record.size / 1024).toFixed(2)} KB\r\n            </div>\r\n          </div>\r\n        )\r\n      },\r\n      {\r\n        title: '匹配状态',\r\n        dataIndex: 'matched',\r\n        key: 'matched',\r\n        render: (matched) => (\r\n          matched ? \r\n            <Text type=\"success\"><CheckCircleOutlined /> 已匹配</Text> : \r\n            <Text type=\"danger\">未匹配</Text>\r\n        )\r\n      },\r\n      {\r\n        title: '学生',\r\n        dataIndex: 'studentName',\r\n        key: 'studentName',\r\n        render: (text, record, index) => (\r\n          <Select\r\n            value={record.studentId}\r\n            onChange={(value) => updateFileStudentMatch(index, value)}\r\n            style={{ width: '100%' }}\r\n            placeholder=\"选择学生\"\r\n          >\r\n            {classStudents.map(student => (\r\n              <Option key={student.id} value={student.id}>\r\n                {student.full_name || student.username}\r\n              </Option>\r\n            ))}\r\n          </Select>\r\n        )\r\n      }\r\n    ];\r\n    \r\n    return (\r\n      <div>\r\n        <Alert \r\n          message=\"文件与学生匹配\"\r\n          description={\r\n            <div>\r\n              <p>系统已根据文件名尝试匹配学生。如果匹配不正确，请手动选择正确的学生。</p>\r\n              <p>已成功匹配 {matchedStudents.length}/{directoryFiles.length} 个文件</p>\r\n            </div>\r\n          }\r\n          type=\"info\"\r\n          showIcon\r\n          style={{ marginBottom: 16 }}\r\n        />\r\n        \r\n        <Table \r\n          dataSource={directoryFiles}\r\n          columns={columns}\r\n          rowKey={(record, index) => index}\r\n          size=\"small\"\r\n          pagination={{ pageSize: 10 }}\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"homework-batch-upload-container\">\r\n      <Title level={3}>批量上传</Title>\r\n      \r\n      <Spin spinning={loading || dataLoading}>\r\n        {submitSuccess ? (\r\n          <Result\r\n            status=\"success\"\r\n            title=\"作业提交成功！\"\r\n            subTitle=\"系统将自动处理您的作业，请稍后查看结果。\"\r\n            extra={[\r\n              <Button \r\n                type=\"primary\" \r\n                key=\"list\" \r\n                onClick={() => navigate('/homework')}\r\n              >\r\n                返回作业列表\r\n              </Button>,\r\n              <Button \r\n                key=\"again\" \r\n                onClick={() => {\r\n                  setSubmitSuccess(false);\r\n                  setStudentUploads([{ id: 1, studentId: null, files: [] }]);\r\n                  setDirectoryFiles([]);\r\n                  setMatchedStudents([]);\r\n                  form.resetFields();\r\n                }}\r\n              >\r\n                继续提交\r\n              </Button>\r\n            ]}\r\n          />\r\n        ) : (\r\n          <Form\r\n            form={form}\r\n            layout=\"vertical\"\r\n            onFinish={onFinish}\r\n          >\r\n            {/* 作业上传说明 */}\r\n            <Alert\r\n              message=\"📚 作业上传规范说明\"\r\n              description={\r\n                <div>\r\n                  <Row gutter={16}>\r\n                    <Col span={12}>\r\n                      <div style={{ marginBottom: 8 }}>\r\n                        <Text strong>📄 文件要求：</Text>\r\n                        <ul style={{ marginTop: 4, marginBottom: 8 }}>\r\n                          <li>支持格式：{HOMEWORK_CONFIG.SUPPORTED_FORMATS.join(', ')}</li>\r\n                          <li>页数限制：{HOMEWORK_CONFIG.MIN_PAGES}-{HOMEWORK_CONFIG.MAX_PAGES}页</li>\r\n                          <li>单个文件：不超过{HOMEWORK_CONFIG.MAX_FILE_SIZE_MB}MB</li>\r\n                          <li>总大小：不超过{HOMEWORK_CONFIG.MAX_TOTAL_SIZE_MB}MB</li>\r\n                        </ul>\r\n                      </div>\r\n                    </Col>\r\n                    <Col span={12}>\r\n                      <div style={{ marginBottom: 8 }}>\r\n                        <Text strong>📝 页面标签示例：</Text>\r\n                        <ul style={{ marginTop: 4, marginBottom: 8 }}>\r\n                          <li><Text type=\"secondary\">数学：</Text>第1页、第2页、第3页、第4页</li>\r\n                          <li><Text type=\"secondary\">英语：</Text>听力部分、阅读部分、写作部分、第4页</li>\r\n                          <li><Text type=\"secondary\">物理：</Text>实验步骤、实验数据、实验结果、实验总结</li>\r\n                          <li><Text type=\"secondary\">化学：</Text>实验过程、化学方程式、实验现象、实验结论</li>\r\n                        </ul>\r\n                      </div>\r\n                    </Col>\r\n                  </Row>\r\n                  <div style={{ marginTop: 8, padding: 8, background: '#f0f8ff', borderRadius: 4 }}>\r\n                    <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n                      💡 提示：系统会根据科目自动为每页作业分配合适的标签，请按顺序上传作业页面\r\n                    </Text>\r\n                  </div>\r\n                </div>\r\n              }\r\n              type=\"info\"\r\n              showIcon\r\n              style={{ marginBottom: 16 }}\r\n            />\r\n\r\n            <Card title=\"基本信息\" className=\"mb-4\">\r\n              <Form.Item\r\n                name=\"assignment_id\"\r\n                label=\"作业标题\"\r\n                rules={[{ required: true, message: '请选择作业标题' }]}\r\n              >\r\n                <Select \r\n                  placeholder=\"请选择作业标题\" \r\n                  loading={loadingAssignments}\r\n                  onChange={handleAssignmentChange}\r\n                >\r\n                  {assignments.map(assignment => (\r\n                    <Option key={assignment.id} value={assignment.id}>\r\n                      {assignment.title}\r\n                    </Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n              \r\n              <Form.Item\r\n                name=\"class_id\"\r\n                label=\"班级\"\r\n                rules={[{ required: true, message: '请选择班级' }]}\r\n              >\r\n                <Select \r\n                  placeholder=\"请选择班级\" \r\n                  onChange={handleClassChange}\r\n                >\r\n                  {classes.map(cls => (\r\n                    <Option key={cls.id} value={cls.id}>{cls.name}</Option>\r\n                  ))}\r\n                </Select>\r\n              </Form.Item>\r\n              \r\n              <Form.Item\r\n                name=\"description\"\r\n                label=\"描述\"\r\n              >\r\n                <TextArea \r\n                  placeholder=\"请输入作业描述（可选）\"\r\n                  rows={4}\r\n                />\r\n              </Form.Item>\r\n            </Card>\r\n            \r\n            {/* 上传模式选择 */}\r\n            {user.is_teacher && (\r\n              <Card title=\"上传模式\" className=\"mb-4\">\r\n                <Radio.Group \r\n                  value={uploadMode} \r\n                  onChange={e => setUploadMode(e.target.value)}\r\n                  style={{ marginBottom: 16 }}\r\n                >\r\n                  <Radio.Button value=\"manual\">手动选择学生和文件</Radio.Button>\r\n                  <Radio.Button value=\"directory\">文件夹目录批量上传</Radio.Button>\r\n                </Radio.Group>\r\n                \r\n                {uploadMode === 'directory' && (\r\n                  <div className=\"directory-upload-info\">\r\n                    <Alert\r\n                      message=\"文件夹目录批量上传说明\"\r\n                      description={\r\n                        <div>\r\n                          <Paragraph>\r\n                            1. 请先选择班级，再选择包含学生作业图片的文件夹\r\n                          </Paragraph>\r\n                          <Paragraph>\r\n                            2. 文件名需包含学生姓名或用户名，系统将自动匹配\r\n                          </Paragraph>\r\n                          <Paragraph>\r\n                            3. 支持的文件格式：JPG、PNG、PDF\r\n                          </Paragraph>\r\n                          <Paragraph>\r\n                            4. 每个文件大小不超过10MB\r\n                          </Paragraph>\r\n                        </div>\r\n                      }\r\n                      type=\"info\"\r\n                      showIcon\r\n                    />\r\n                  </div>\r\n                )}\r\n              </Card>\r\n            )}\r\n            \r\n            {/* 根据用户角色和模式显示不同的上传部分 */}\r\n            {!user.is_teacher ? (\r\n              <Card title=\"📤 上传作业图片\" className=\"mb-4\">\r\n                <Form.Item\r\n                  label={\r\n                    <span>\r\n                      作业图片\r\n                      <Text type=\"secondary\" style={{ marginLeft: 8, fontSize: '12px' }}>\r\n                        ({HOMEWORK_CONFIG.MIN_PAGES}-{HOMEWORK_CONFIG.MAX_PAGES}页，支持{HOMEWORK_CONFIG.SUPPORTED_FORMATS.join('、')}格式)\r\n                      </Text>\r\n                    </span>\r\n                  }\r\n                  required\r\n                  tooltip={`上传作业图片，支持${HOMEWORK_CONFIG.SUPPORTED_FORMATS.join('、')}格式，每页不超过${HOMEWORK_CONFIG.MAX_FILE_SIZE_MB}MB`}\r\n                >\r\n                  <Upload\r\n                    listType=\"picture-card\"\r\n                    fileList={[]}\r\n                    beforeUpload={beforeUpload}\r\n                    onChange={() => {}}\r\n                    multiple\r\n                    accept={HOMEWORK_CONFIG.SUPPORTED_FORMATS.join(',')}\r\n                  >\r\n                    <div>\r\n                      <PlusOutlined />\r\n                      <div style={{ marginTop: 8 }}>上传图片</div>\r\n                    </div>\r\n                  </Upload>\r\n                  <div style={{ marginTop: 8 }}>\r\n                    <Alert\r\n                      message={\r\n                        <div>\r\n                          <Text type=\"secondary\">\r\n                            📋 请按页面顺序上传：{getPageLabels().slice(0, HOMEWORK_CONFIG.MAX_PAGES).join(' → ')}\r\n                          </Text>\r\n                        </div>\r\n                      }\r\n                      type=\"info\"\r\n                      showIcon={false}\r\n                      style={{ fontSize: '12px' }}\r\n                    />\r\n                  </div>\r\n                </Form.Item>\r\n              </Card>\r\n            ) : uploadMode === 'directory' ? (\r\n              <Card title=\"文件夹目录批量上传\" className=\"mb-4\">\r\n                <div className=\"directory-upload-section\">\r\n                  <input\r\n                    ref={directoryInputRef}\r\n                    type=\"file\"\r\n                    webkitdirectory=\"true\"\r\n                    directory=\"true\"\r\n                    multiple\r\n                    style={{ display: 'none' }}\r\n                    onChange={handleDirectorySelect}\r\n                  />\r\n                  \r\n                  <div style={{ textAlign: 'center', marginBottom: 20 }}>\r\n                    <Button \r\n                      type=\"primary\" \r\n                      icon={<FolderOpenOutlined />}\r\n                      size=\"large\"\r\n                      onClick={() => {\r\n                        const selectedClassId = form.getFieldValue('class_id');\r\n                        if (!selectedClassId) {\r\n                          message.warning('请先选择班级，再上传文件夹');\r\n                          return;\r\n                        }\r\n                        directoryInputRef.current && directoryInputRef.current.click();\r\n                      }}\r\n                      disabled={processingFiles || classStudents.length === 0}\r\n                    >\r\n                      {processingFiles ? <LoadingOutlined /> : null}\r\n                      选择文件夹\r\n                    </Button>\r\n                    <div style={{ marginTop: 8 }}>\r\n                      {classStudents.length === 0 ? (\r\n                        <Text type=\"warning\">请先选择班级</Text>\r\n                      ) : (\r\n                        <Text type=\"secondary\">\r\n                          已选择 {directoryFiles.length} 个文件，\r\n                          成功匹配 {matchedStudents.length} 个学生\r\n                        </Text>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {directoryFiles.length > 0 && renderDirectoryMatchTable()}\r\n                </div>\r\n              </Card>\r\n            ) : (\r\n              <div className=\"batch-upload-section\">\r\n                <Divider orientation=\"left\">批量上传学生作业</Divider>\r\n                \r\n                {studentUploads.map((item, index) => \r\n                  renderStudentUploadItem(item, index)\r\n                )}\r\n                \r\n                <Button \r\n                  type=\"dashed\" \r\n                  onClick={addStudentUpload} \r\n                  style={{ width: '100%', marginBottom: 16 }}\r\n                >\r\n                  <PlusOutlined /> 添加学生\r\n                </Button>\r\n              </div>\r\n            )}\r\n            \r\n            <Form.Item>\r\n              <Button \r\n                type=\"primary\" \r\n                htmlType=\"submit\" \r\n                loading={loading}\r\n                disabled={uploadMode === 'directory' && matchedStudents.length === 0}\r\n              >\r\n                {user.is_teacher ? '批量提交' : '提交作业'}\r\n              </Button>\r\n              <Button \r\n                style={{ marginLeft: 8 }}\r\n                onClick={() => navigate('/homework')}\r\n              >\r\n                取消\r\n              </Button>\r\n            </Form.Item>\r\n          </Form>\r\n        )}\r\n      </Spin>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomeworkUpload; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EACxDC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAC5D,MAAM;AACb,SACEC,cAAc,EAAEC,YAAY,EAAEC,cAAc,EAAEC,mBAAmB,EACjEC,kBAAkB,EAAEC,iBAAiB,EAAEC,eAAe,QACjD,mBAAmB;AAC1B,SAASC,mBAAmB,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElI,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG7B,UAAU;AAC7C,MAAM;EAAE8B;AAAO,CAAC,GAAG/B,MAAM;AACzB,MAAM;EAAEgC;AAAS,CAAC,GAAGnC,KAAK;AAE1B,MAAMoC,cAAc,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACnC,MAAMC,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4C,IAAI,CAAC,GAAG3C,IAAI,CAAC4C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,CACnD;IAAEyD,EAAE,EAAE,CAAC;IAAEC,SAAS,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAG,CAAC,CACtC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;;EAExD;EACA,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM8E,iBAAiB,GAAG5E,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACiF,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMmF,iBAAiB,GAAG,MAAOC,OAAO,IAAK;IAC3C,IAAI,CAACA,OAAO,EAAE;MACZJ,gBAAgB,CAAC,EAAE,CAAC;MACpB;IACF;IAEA,IAAI;MACF;MACAK,OAAO,CAACC,GAAG,CAAC,UAAUF,OAAO,QAAQ,CAAC;MACtC,MAAMG,YAAY,GAAG,MAAMpD,QAAQ,CAACiD,OAAO,CAAC;MAC5CC,OAAO,CAACC,GAAG,CAAC,MAAMF,OAAO,MAAM,EAAEG,YAAY,CAAC;MAE9C,IAAIA,YAAY,IAAIA,YAAY,CAACzB,QAAQ,IAAI0B,KAAK,CAACC,OAAO,CAACF,YAAY,CAACzB,QAAQ,CAAC,EAAE;QACjFuB,OAAO,CAACC,GAAG,CAAC,MAAMF,OAAO,OAAO,EAAEG,YAAY,CAACzB,QAAQ,CAAC;QACxDuB,OAAO,CAACC,GAAG,CAAC,SAASC,YAAY,CAACzB,QAAQ,CAAC4B,MAAM,EAAE,CAAC;;QAEpD;QACA,IAAIH,YAAY,CAACzB,QAAQ,CAAC4B,MAAM,GAAG,CAAC,EAAE;UACpCL,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEC,YAAY,CAACzB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrD;QAEAkB,gBAAgB,CAACO,YAAY,CAACzB,QAAQ,CAAC;;QAEvC;QACAN,iBAAiB,CAACmC,WAAW,IAC3BA,WAAW,CAACC,GAAG,CAACC,IAAI,IAAI;UACtB,MAAMC,cAAc,GAAGP,YAAY,CAACzB,QAAQ,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKoC,IAAI,CAACnC,SAAS,CAAC;UAC/E,OAAOoC,cAAc,GAAGD,IAAI,GAAG;YAAE,GAAGA,IAAI;YAAEnC,SAAS,EAAE;UAAK,CAAC;QAC7D,CAAC,CACH,CAAC;MACH,CAAC,MAAM;QACL2B,OAAO,CAACY,IAAI,CAAC,MAAMb,OAAO,SAAS,CAAC;QACpCC,OAAO,CAACY,IAAI,CAAC,iBAAiB,EAAEV,YAAY,CAAC;QAC7CP,gBAAgB,CAAC,EAAE,CAAC;QACpBpE,OAAO,CAACsF,OAAO,CAAC,mBAAmB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,QAAQf,OAAO,UAAU,EAAEe,KAAK,CAAC;MAC/CvF,OAAO,CAACuF,KAAK,CAAC,gBAAgB,CAAC;MAC/BnB,gBAAgB,CAAC,EAAE,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMoB,sBAAsB,GAAG,MAAO3C,EAAE,IAAK;IAC3C,IAAI;MACF,MAAM4C,iBAAiB,GAAG,MAAMpE,qBAAqB,CAACwB,EAAE,CAAC;MACzD4B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEe,iBAAiB,CAAC;;MAE7C;MACA,IAAIA,iBAAiB,IAAIA,iBAAiB,CAACC,MAAM,KAAK,UAAU,EAAE;QAChE1F,OAAO,CAACuF,KAAK,CAAC,kBAAkB,CAAC;QACjCpD,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF;;MAEA;MACA,IAAIsD,iBAAiB,IAAIA,iBAAiB,CAACE,QAAQ,EAAE;QACnDlB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEe,iBAAiB,CAACE,QAAQ,CAAC;QAClDtD,IAAI,CAACuD,cAAc,CAAC;UAClBD,QAAQ,EAAEF,iBAAiB,CAACE;QAC9B,CAAC,CAAC;;QAEF;QACA,IAAIF,iBAAiB,CAACI,KAAK,EAAE;UAC3BxD,IAAI,CAACuD,cAAc,CAAC;YAClBC,KAAK,EAAEJ,iBAAiB,CAACI;UAC3B,CAAC,CAAC;QACJ;;QAEA;QACApB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QACzBH,iBAAiB,CAACkB,iBAAiB,CAACE,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACAlG,SAAS,CAAC,MAAM;IACd,MAAMyG,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI9D,IAAI,IAAIA,IAAI,CAAC+D,UAAU,EAAE;QAC3B1C,cAAc,CAAC,IAAI,CAAC;QACpB,IAAI;UAAA,IAAA2C,eAAA;UACF;UACAvB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrB,MAAMuB,WAAW,GAAG,MAAM9E,UAAU,CAAC,CAAC;UACtCsD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEuB,WAAW,CAAC;UACjChD,UAAU,CAACgD,WAAW,IAAI,EAAE,CAAC;;UAE7B;UACAxB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrB,MAAMwB,aAAa,GAAG,MAAM9E,QAAQ,CAAC,CAAC;UACtCqD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEwB,aAAa,CAAC;;UAEnC;UACA,IAAIC,YAAY,GAAG,EAAE;UACrB,IAAID,aAAa,EAAE;YACjB,IAAItB,KAAK,CAACC,OAAO,CAACqB,aAAa,CAAC,EAAE;cAChC;cACAC,YAAY,GAAGD,aAAa,CAACE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACN,UAAU,CAAC;YACzD,CAAC,MAAM,IAAIG,aAAa,CAACI,KAAK,IAAI1B,KAAK,CAACC,OAAO,CAACqB,aAAa,CAACI,KAAK,CAAC,EAAE;cACpE;cACAH,YAAY,GAAGD,aAAa,CAACI,KAAK,CAACF,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACN,UAAU,CAAC;YAC/D,CAAC,MAAM,IAAIG,aAAa,CAACK,KAAK,IAAI3B,KAAK,CAACC,OAAO,CAACqB,aAAa,CAACK,KAAK,CAAC,EAAE;cACpE;cACAJ,YAAY,GAAGD,aAAa,CAACK,KAAK,CAACH,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACN,UAAU,CAAC;YAC/D;UACF;UAEAtB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEyB,YAAY,CAAC;UACtChD,WAAW,CAACgD,YAAY,IAAI,EAAE,CAAC;;UAE/B;UACA1C,qBAAqB,CAAC,IAAI,CAAC;UAC3B,MAAM+C,mBAAmB,GAAG,MAAMlF,sBAAsB,CAAC,CAAC;UAC1DmD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE8B,mBAAmB,CAAC;UAC/C;UACA,IAAIC,gBAAgB,GAAG,EAAE;UACzB,IAAID,mBAAmB,IAAIA,mBAAmB,CAACF,KAAK,EAAE;YACpDG,gBAAgB,GAAGD,mBAAmB,CAACF,KAAK,IAAI,EAAE;YAClD/C,cAAc,CAACkD,gBAAgB,CAAC;UAClC,CAAC,MAAM,IAAI7B,KAAK,CAACC,OAAO,CAAC2B,mBAAmB,CAAC,EAAE;YAC7CC,gBAAgB,GAAGD,mBAAmB;YACtCjD,cAAc,CAACkD,gBAAgB,CAAC;UAClC,CAAC,MAAM;YACLlD,cAAc,CAAC,EAAE,CAAC;UACpB;;UAEA;UACA,IAAIkD,gBAAgB,CAAC3B,MAAM,GAAG,CAAC,IAAI,GAAAkB,eAAA,GAAC5D,QAAQ,CAACsE,KAAK,cAAAV,eAAA,eAAdA,eAAA,CAAgB3B,YAAY,GAAE;YAChE,MAAMsC,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;YAC5ChC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiC,gBAAgB,CAACd,KAAK,CAAC;YAClDxD,IAAI,CAACuD,cAAc,CAAC;cAClBgB,aAAa,EAAED,gBAAgB,CAAC9D,EAAE;cAClCgD,KAAK,EAAEc,gBAAgB,CAACd;YAC1B,CAAC,CAAC;;YAEF;YACA,IAAIc,gBAAgB,CAAChB,QAAQ,EAAE;cAC7BtD,IAAI,CAACuD,cAAc,CAAC;gBAClBD,QAAQ,EAAEgB,gBAAgB,CAAChB;cAC7B,CAAC,CAAC;;cAEF;cACApB,iBAAiB,CAACoC,gBAAgB,CAAChB,QAAQ,CAAC;YAC9C;UACF;UAEAlC,qBAAqB,CAAC,KAAK,CAAC;QAC9B,CAAC,CAAC,OAAO8B,KAAK,EAAE;UACdd,OAAO,CAACc,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BvF,OAAO,CAACuF,KAAK,CAAC,gBAAgB,CAAC;UAC/B9B,qBAAqB,CAAC,KAAK,CAAC;QAC9B,CAAC,SAAS;UACRJ,cAAc,CAAC,KAAK,CAAC;QACvB;MACF;IACF,CAAC;IAEDyC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC9D,IAAI,CAAC,CAAC;;EAEV;EACA3C,SAAS,CAAC,MAAM;IAAA,IAAAwH,gBAAA;IACd,KAAAA,gBAAA,GAAIzE,QAAQ,CAACsE,KAAK,cAAAG,gBAAA,eAAdA,gBAAA,CAAgBxC,YAAY,EAAE;MAChCI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEtC,QAAQ,CAACsE,KAAK,CAACrC,YAAY,CAAC;MAC3DC,eAAe,CAAClC,QAAQ,CAACsE,KAAK,CAACrC,YAAY,CAAC;MAC5ChC,IAAI,CAACuD,cAAc,CAAC;QAClBgB,aAAa,EAAExE,QAAQ,CAACsE,KAAK,CAACrC;MAChC,CAAC,CAAC;MACFmB,sBAAsB,CAACpD,QAAQ,CAACsE,KAAK,CAACrC,YAAY,CAAC;IACrD,CAAC,MAAM;MACLI,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC;EACF,CAAC,EAAE,EAAAxC,gBAAA,GAACE,QAAQ,CAACsE,KAAK,cAAAxE,gBAAA,uBAAdA,gBAAA,CAAgBmC,YAAY,EAAEhC,IAAI,CAAC,CAAC;;EAExC;EACAhD,SAAS,CAAC,MAAM;IACdoF,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEtC,QAAQ,CAACsE,KAAK,CAAC;IAC/D,IAAItE,QAAQ,CAACsE,KAAK,EAAE;MAClB,IAAItE,QAAQ,CAACsE,KAAK,CAACrC,YAAY,EAAE;QAC/BI,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEtC,QAAQ,CAACsE,KAAK,CAACrC,YAAY,CAAC;QACrDhC,IAAI,CAACuD,cAAc,CAAC;UAClBgB,aAAa,EAAExE,QAAQ,CAACsE,KAAK,CAACrC;QAChC,CAAC,CAAC;;QAEF;QACAmB,sBAAsB,CAACpD,QAAQ,CAACsE,KAAK,CAACrC,YAAY,CAAC;MACrD;IACF;EACF,CAAC,EAAE,CAACjC,QAAQ,CAACsE,KAAK,EAAErE,IAAI,CAAC,CAAC;;EAE1B;EACA,MAAMyE,sBAAsB,GAAIC,KAAK,IAAK;IACxC,MAAMC,kBAAkB,GAAG1D,WAAW,CAAC2D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrE,EAAE,KAAKkE,KAAK,CAAC;IAChE,IAAIC,kBAAkB,EAAE;MACtB3E,IAAI,CAACuD,cAAc,CAAC;QAClBC,KAAK,EAAEmB,kBAAkB,CAACnB;MAC5B,CAAC,CAAC;;MAEF;MACA,IAAImB,kBAAkB,CAACrB,QAAQ,EAAE;QAC/BtD,IAAI,CAACuD,cAAc,CAAC;UAClBD,QAAQ,EAAEqB,kBAAkB,CAACrB;QAC/B,CAAC,CAAC;;QAEF;QACApB,iBAAiB,CAACyC,kBAAkB,CAACrB,QAAQ,CAAC;MAChD;IACF;EACF,CAAC;;EAED;EACA,MAAMwB,wBAAwB,GAAG,MAAOC,MAAM,IAAK;IACjD,IAAI;MACF5E,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM6E,mBAAmB,GAAG1E,cAAc,CAACyD,MAAM,CAC/CnB,IAAI,IAAIA,IAAI,CAACnC,SAAS,IAAImC,IAAI,CAAClC,KAAK,CAAC+B,MAAM,GAAG,CAChD,CAAC;MAED,IAAIuC,mBAAmB,CAACvC,MAAM,KAAK,CAAC,EAAE;QACpC9E,OAAO,CAACuF,KAAK,CAAC,qBAAqB,CAAC;QACpC/C,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM8E,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACA,IAAIH,MAAM,CAACR,aAAa,EAAE;QACxB,MAAMI,kBAAkB,GAAG1D,WAAW,CAAC2D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrE,EAAE,KAAKuE,MAAM,CAACR,aAAa,CAAC;QAC/E,IAAII,kBAAkB,EAAE;UACtBM,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAER,kBAAkB,CAACnB,KAAK,CAAC;QACpD,CAAC,MAAM;UACLyB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC;QACnC;MACF,CAAC,MAAM;QACLF,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC;MACnC;MAEA,IAAIJ,MAAM,CAACK,WAAW,EAAE;QACtBH,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEJ,MAAM,CAACK,WAAW,CAAC;MACpD;MAEA,IAAIL,MAAM,CAACR,aAAa,EAAE;QACxBU,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEJ,MAAM,CAACR,aAAa,CAAC;MACxD;;MAEA;MACA,IAAIQ,MAAM,CAACzB,QAAQ,EAAE;QACnB2B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,MAAM,CAACzB,QAAQ,CAAC;QAC5ClB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0C,MAAM,CAACzB,QAAQ,CAAC;MAC5C;;MAEA;MACA,MAAM+B,WAAW,GAAGL,mBAAmB,CAACrC,GAAG,CAACC,IAAI,IAAI;QAClD;QACA,MAAM0C,OAAO,GAAGzE,QAAQ,CAAC+D,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKoC,IAAI,CAACnC,SAAS,CAAC;QAC3D,MAAM8E,WAAW,GAAGD,OAAO,GAAIA,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACG,QAAQ,GAAI,MAAM;QAE9E,OAAO;UACLC,UAAU,EAAE9C,IAAI,CAACnC,SAAS;UAC1BkF,YAAY,EAAEJ,WAAW;UACzBK,MAAM,EAAEhD,IAAI,CAAClC,KAAK,CAACiC,GAAG,CAACkD,IAAI,IAAIA,IAAI,CAACC,aAAa,CAACC,IAAI;QACxD,CAAC;MACH,CAAC,CAAC;MAEF3D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgD,WAAW,CAAC;MACrCJ,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEa,IAAI,CAACC,SAAS,CAACZ,WAAW,CAAC,CAAC;;MAE5D;MACA,IAAIa,SAAS,GAAG,CAAC;MACjBlB,mBAAmB,CAACmB,OAAO,CAACvD,IAAI,IAAI;QAClCA,IAAI,CAAClC,KAAK,CAACyF,OAAO,CAACN,IAAI,IAAI;UACzB,IAAIA,IAAI,CAACC,aAAa,EAAE;YACtB;YACA,MAAMM,QAAQ,GAAGP,IAAI,CAACC,aAAa,CAACC,IAAI;YACxC,MAAMM,cAAc,GAAG,GAAGzD,IAAI,CAACnC,SAAS,IAAI6F,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIH,QAAQ,EAAE;;YAEpE;YACA,MAAMI,WAAW,GAAG,IAAIC,IAAI,CAC1B,CAACZ,IAAI,CAACC,aAAa,CAAC,EACpBO,cAAc,EACd;cAAEK,IAAI,EAAEb,IAAI,CAACC,aAAa,CAACY;YAAK,CAClC,CAAC;YAEDzB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEqB,WAAW,CAAC;YACrCN,SAAS,EAAE;;YAEX;YACA,MAAMS,YAAY,GAAGtB,WAAW,CAACuB,SAAS,CAAC7D,CAAC,IAAIA,CAAC,CAAC2C,UAAU,KAAK9C,IAAI,CAACnC,SAAS,CAAC;YAChF,IAAIkG,YAAY,KAAK,CAAC,CAAC,EAAE;cACvB,MAAME,UAAU,GAAGxB,WAAW,CAACsB,YAAY,CAAC,CAACf,MAAM,CAACgB,SAAS,CAACE,GAAG,IAAIA,GAAG,KAAKV,QAAQ,CAAC;cACtF,IAAIS,UAAU,KAAK,CAAC,CAAC,EAAE;gBACrBxB,WAAW,CAACsB,YAAY,CAAC,CAACf,MAAM,CAACiB,UAAU,CAAC,GAAGR,cAAc;cAC/D;YACF;UACF;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACApB,QAAQ,CAAC8B,MAAM,CAAC,cAAc,CAAC;MAC/B9B,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEa,IAAI,CAACC,SAAS,CAACZ,WAAW,CAAC,CAAC;MAE5DjD,OAAO,CAACC,GAAG,CAAC,QAAQ6D,SAAS,QAAQlB,mBAAmB,CAACvC,MAAM,SAAS,CAAC;;MAEzE;MACA,MAAMuE,QAAQ,GAAG,MAAMnI,mBAAmB,CAACoG,QAAQ,CAAC;MACpD7C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2E,QAAQ,CAAC;;MAElC;MACA3G,gBAAgB,CAAC,IAAI,CAAC;MACtB1C,OAAO,CAACsJ,OAAO,CAAC,WAAW,CAAC;;MAE5B;MACA,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,EAAE;QAClC9E,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2E,QAAQ,CAACE,SAAS,CAAC;MAC7C;;MAEA;MACAC,UAAU,CAAC,MAAM;QACf;QACArH,QAAQ,CAAC,0BAA0B,EAAE;UACnCuE,KAAK,EAAE;YACL+C,OAAO,EAAE,IAAI;YACbC,YAAY,EAAE,IAAI;YAClBC,SAAS,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACiB,OAAO,CAAC;UAChC;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BvF,OAAO,CAACuF,KAAK,CAAC,WAAWA,KAAK,CAACvF,OAAO,IAAI,cAAc,EAAE,CAAC;IAC7D,CAAC,SAAS;MACRwC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqH,qBAAqB,GAAG,MAAOC,CAAC,IAAK;IACzC,IAAI;MACF7F,kBAAkB,CAAC,IAAI,CAAC;MACxB,MAAMlB,KAAK,GAAG+G,CAAC,CAACC,MAAM,CAAChH,KAAK;MAC5B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAAC+B,MAAM,KAAK,CAAC,EAAE;QAChCb,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACF;MAEAQ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE3B,KAAK,CAAC+B,MAAM,CAAC;;MAErC;MACA,MAAMkF,eAAe,GAAG3H,IAAI,CAAC4H,aAAa,CAAC,UAAU,CAAC;MACtD,IAAI,CAACD,eAAe,EAAE;QACpBhK,OAAO,CAACsF,OAAO,CAAC,eAAe,CAAC;QAChCrB,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACF;;MAEA;MACA,IAAIE,aAAa,CAACW,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI;UACF,MAAMH,YAAY,GAAG,MAAMpD,QAAQ,CAACyI,eAAe,CAAC;UACpD,IAAIrF,YAAY,IAAIA,YAAY,CAACzB,QAAQ,IAAI0B,KAAK,CAACC,OAAO,CAACF,YAAY,CAACzB,QAAQ,CAAC,EAAE;YACjFkB,gBAAgB,CAACO,YAAY,CAACzB,QAAQ,CAAC;UACzC,CAAC,MAAM;YACLlD,OAAO,CAACuF,KAAK,CAAC,sBAAsB,CAAC;YACrCtB,kBAAkB,CAAC,KAAK,CAAC;YACzB;UACF;QACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;UACdd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;UACjCvF,OAAO,CAACuF,KAAK,CAAC,cAAc,CAAC;UAC7BtB,kBAAkB,CAAC,KAAK,CAAC;UACzB;QACF;MACF;;MAEA;MACA,MAAMiG,QAAQ,GAAGtF,KAAK,CAACuF,IAAI,CAACpH,KAAK,CAAC,CAACiC,GAAG,CAACkD,IAAI,KAAK;QAC9CA,IAAI;QACJE,IAAI,EAAEF,IAAI,CAACE,IAAI;QACfgC,IAAI,EAAElC,IAAI,CAACmC,kBAAkB,IAAInC,IAAI,CAACE,IAAI;QAC1CkC,IAAI,EAAEpC,IAAI,CAACoC,IAAI;QACfvB,IAAI,EAAEb,IAAI,CAACa,IAAI;QACfwB,YAAY,EAAErC,IAAI,CAACqC,YAAY;QAC/BC,OAAO,EAAE,KAAK;QACd1H,SAAS,EAAE,IAAI;QACf8E,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;MAEH/D,iBAAiB,CAACqG,QAAQ,CAAC;;MAE3B;MACA,MAAMM,OAAO,GAAG,EAAE;MAClB,MAAMC,SAAS,GAAG,EAAE;;MAEpB;MACA,MAAMC,aAAa,GAAGvG,aAAa;MAEnC,IAAIuG,aAAa,CAAC5F,MAAM,KAAK,CAAC,EAAE;QAC9B9E,OAAO,CAACsF,OAAO,CAAC,sBAAsB,CAAC;QACvCrB,kBAAkB,CAAC,KAAK,CAAC;QACzB;MACF;MAEAiG,QAAQ,CAAC1B,OAAO,CAACmC,QAAQ,IAAI;QAC3B;QACA,MAAMlC,QAAQ,GAAGkC,QAAQ,CAACvC,IAAI;QAC9B,MAAMwC,cAAc,GAAGnC,QAAQ,CAACoC,SAAS,CAAC,CAAC,EAAEpC,QAAQ,CAACqC,WAAW,CAAC,GAAG,CAAC,CAAC,IAAIrC,QAAQ;;QAEnF;QACA,IAAIsC,cAAc,GAAG,IAAI;QACzB,IAAIC,cAAc,GAAG,CAAC;;QAEtB;QACAN,aAAa,CAAClC,OAAO,CAACb,OAAO,IAAI;UAC/B,MAAMsD,QAAQ,GAAGtD,OAAO,CAACE,SAAS,IAAI,EAAE;UACxC,MAAMC,QAAQ,GAAGH,OAAO,CAACG,QAAQ,IAAI,EAAE;;UAEvC;UACA,IAAI8C,cAAc,KAAKK,QAAQ,IAAIL,cAAc,KAAK9C,QAAQ,EAAE;YAC9DiD,cAAc,GAAGpD,OAAO;YACxBqD,cAAc,GAAG,GAAG,CAAC,CAAC;UACxB;UACA;UAAA,KACK,IAAIJ,cAAc,CAACM,QAAQ,CAACD,QAAQ,CAAC,IAAIA,QAAQ,CAACnG,MAAM,GAAG,CAAC,EAAE;YACjE,MAAMqG,KAAK,GAAGF,QAAQ,CAACnG,MAAM;YAC7B,IAAIqG,KAAK,GAAGH,cAAc,EAAE;cAC1BD,cAAc,GAAGpD,OAAO;cACxBqD,cAAc,GAAGG,KAAK;YACxB;UACF,CAAC,MACI,IAAIP,cAAc,CAACM,QAAQ,CAACpD,QAAQ,CAAC,IAAIA,QAAQ,CAAChD,MAAM,GAAG,CAAC,EAAE;YACjE,MAAMqG,KAAK,GAAGrD,QAAQ,CAAChD,MAAM;YAC7B,IAAIqG,KAAK,GAAGH,cAAc,EAAE;cAC1BD,cAAc,GAAGpD,OAAO;cACxBqD,cAAc,GAAGG,KAAK;YACxB;UACF;QACF,CAAC,CAAC;;QAEF;QACA,IAAIJ,cAAc,IAAIC,cAAc,GAAG,CAAC,EAAE;UACxCL,QAAQ,CAACH,OAAO,GAAG,IAAI;UACvBG,QAAQ,CAAC7H,SAAS,GAAGiI,cAAc,CAAClI,EAAE;UACtC8H,QAAQ,CAAC/C,WAAW,GAAGmD,cAAc,CAAClD,SAAS,IAAIkD,cAAc,CAACjD,QAAQ;UAC1E0C,OAAO,CAACY,IAAI,CAAC;YACX,GAAGT,QAAQ;YACXhD,OAAO,EAAEoD,cAAc;YACvBM,UAAU,EAAEL;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACLP,SAAS,CAACW,IAAI,CAACT,QAAQ,CAAC;QAC1B;MACF,CAAC,CAAC;MAEF5G,kBAAkB,CAACyG,OAAO,CAAC;MAC3B3G,iBAAiB,CAAC,CAAC,GAAG2G,OAAO,EAAE,GAAGC,SAAS,CAAC,CAAC;MAE7C,IAAID,OAAO,CAAC1F,MAAM,KAAK,CAAC,EAAE;QACxB9E,OAAO,CAACsF,OAAO,CAAC,qBAAqB,CAAC;MACxC,CAAC,MAAM;QACLtF,OAAO,CAACsJ,OAAO,CAAC,QAAQkB,OAAO,CAAC1F,MAAM,IAAIoF,QAAQ,CAACpF,MAAM,UAAU2F,SAAS,CAAC3F,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG2F,SAAS,CAAC3F,MAAM,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;MAC5I;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCvF,OAAO,CAACuF,KAAK,CAAC,YAAY,IAAIA,KAAK,CAACvF,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRiE,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMqH,sBAAsB,GAAGA,CAACC,SAAS,EAAEzI,SAAS,KAAK;IACvD,MAAM0I,YAAY,GAAG,CAAC,GAAG5H,cAAc,CAAC;IACxC,MAAMsE,IAAI,GAAGsD,YAAY,CAACD,SAAS,CAAC;IAEpC,IAAIrD,IAAI,EAAE;MACR;MACA,MAAMP,OAAO,GAAGxD,aAAa,CAAC8C,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKC,SAAS,CAAC;MAC3DoF,IAAI,CAACpF,SAAS,GAAGA,SAAS;MAC1BoF,IAAI,CAACsC,OAAO,GAAG,CAAC,CAAC7C,OAAO;MACxBO,IAAI,CAACN,WAAW,GAAGD,OAAO,GAAIA,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACG,QAAQ,GAAI,IAAI;MAE3EjE,iBAAiB,CAAC2H,YAAY,CAAC;;MAE/B;MACA,MAAMC,cAAc,GAAGD,YAAY,CAACpF,MAAM,CAACsF,CAAC,IAAIA,CAAC,CAAClB,OAAO,CAAC,CAACxF,GAAG,CAAC0G,CAAC,KAAK;QACnE,GAAGA,CAAC;QACJ/D,OAAO,EAAExD,aAAa,CAAC8C,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAK6I,CAAC,CAAC5I,SAAS;MACvD,CAAC,CAAC,CAAC;MAEHiB,kBAAkB,CAAC0H,cAAc,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAME,0BAA0B,GAAG,MAAOvE,MAAM,IAAK;IACnD,IAAI;MACF5E,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAIsB,eAAe,CAACgB,MAAM,KAAK,CAAC,EAAE;QAChC9E,OAAO,CAACuF,KAAK,CAAC,8BAA8B,CAAC;QAC7C/C,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM8E,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACA,IAAIH,MAAM,CAACR,aAAa,EAAE;QACxB,MAAMI,kBAAkB,GAAG1D,WAAW,CAAC2D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrE,EAAE,KAAKuE,MAAM,CAACR,aAAa,CAAC;QAC/E,IAAII,kBAAkB,EAAE;UACtBM,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAER,kBAAkB,CAACnB,KAAK,CAAC;QACpD,CAAC,MAAM;UACLyB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC;QACnC;MACF,CAAC,MAAM;QACLF,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC;MACnC;MAEA,IAAIJ,MAAM,CAACK,WAAW,EAAE;QACtBH,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAEJ,MAAM,CAACK,WAAW,CAAC;MACpD;MAEA,IAAIL,MAAM,CAACR,aAAa,EAAE;QACxBU,QAAQ,CAACE,MAAM,CAAC,eAAe,EAAEJ,MAAM,CAACR,aAAa,CAAC;MACxD;;MAEA;MACA,IAAIQ,MAAM,CAACzB,QAAQ,EAAE;QACnB2B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,MAAM,CAACzB,QAAQ,CAAC;QAC5ClB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0C,MAAM,CAACzB,QAAQ,CAAC;MAC5C;;MAEA;MACA,MAAM+B,WAAW,GAAG5D,eAAe,CAAC8H,MAAM,CAAC,CAACC,GAAG,EAAElB,QAAQ,KAAK;QAC5D,MAAM7H,SAAS,GAAG6H,QAAQ,CAAC7H,SAAS;QACpC,IAAI,CAACA,SAAS,EAAE,OAAO+I,GAAG;;QAE1B;QACA,IAAIC,YAAY,GAAGD,GAAG,CAAC5E,IAAI,CAAC8E,KAAK,IAAIA,KAAK,CAAChE,UAAU,KAAKjF,SAAS,CAAC;QAEpE,IAAI,CAACgJ,YAAY,EAAE;UACjB;UACA,MAAMnE,OAAO,GAAGxD,aAAa,CAAC8C,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKC,SAAS,CAAC;UAC3D,MAAM8E,WAAW,GAAGD,OAAO,GAAIA,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACG,QAAQ,GAAI,MAAM;UAE9EgE,YAAY,GAAG;YACb/D,UAAU,EAAEjF,SAAS;YACrBkF,YAAY,EAAEJ,WAAW;YACzBK,MAAM,EAAE;UACV,CAAC;UACD4D,GAAG,CAACT,IAAI,CAACU,YAAY,CAAC;QACxB;;QAEA;QACA,MAAMpD,cAAc,GAAG,GAAG5F,SAAS,IAAI6F,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI+B,QAAQ,CAACvC,IAAI,EAAE;QACpE0D,YAAY,CAAC7D,MAAM,CAACmD,IAAI,CAAC1C,cAAc,CAAC;;QAExC;QACA,MAAMG,WAAW,GAAG,IAAIC,IAAI,CAC1B,CAAC6B,QAAQ,CAACzC,IAAI,CAAC,EACfQ,cAAc,EACd;UAAEK,IAAI,EAAE4B,QAAQ,CAACzC,IAAI,CAACa;QAAK,CAC7B,CAAC;;QAED;QACAzB,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEqB,WAAW,CAAC;QAErC,OAAOgD,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;MAENpH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgD,WAAW,CAAC;MACvCJ,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEa,IAAI,CAACC,SAAS,CAACZ,WAAW,CAAC,CAAC;MAE5DjD,OAAO,CAACC,GAAG,CAAC,QAAQZ,eAAe,CAACgB,MAAM,QAAQ4C,WAAW,CAAC5C,MAAM,SAAS,CAAC;;MAE9E;MACA,MAAMuE,QAAQ,GAAG,MAAMnI,mBAAmB,CAACoG,QAAQ,CAAC;MACpD7C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2E,QAAQ,CAAC;;MAElC;MACA3G,gBAAgB,CAAC,IAAI,CAAC;MACtB1C,OAAO,CAACsJ,OAAO,CAAC,WAAW,CAAC;;MAE5B;MACA,IAAID,QAAQ,IAAIA,QAAQ,CAACE,SAAS,EAAE;QAClC9E,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2E,QAAQ,CAACE,SAAS,CAAC;MAC7C;;MAEA;MACAC,UAAU,CAAC,MAAM;QACf;QACArH,QAAQ,CAAC,0BAA0B,EAAE;UACnCuE,KAAK,EAAE;YACL+C,OAAO,EAAE,IAAI;YACbC,YAAY,EAAE,IAAI;YAClBC,SAAS,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACiB,OAAO,CAAC;UAChC;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCvF,OAAO,CAACuF,KAAK,CAAC,aAAaA,KAAK,CAACvF,OAAO,IAAI,cAAc,EAAE,CAAC;IAC/D,CAAC,SAAS;MACRwC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwJ,QAAQ,GAAI5E,MAAM,IAAK;IAC3B,IAAI1D,UAAU,KAAK,WAAW,EAAE;MAC9BiI,0BAA0B,CAACvE,MAAM,CAAC;IACpC,CAAC,MAAM;MACLD,wBAAwB,CAACC,MAAM,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAM6E,eAAe,GAAG;IACtBC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IAC7DC,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE;EACrB,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG;IAClB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;IAC1C,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3C,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC;IAC9C,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAC3C,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACvC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACvC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EAC1C,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,OAAO,GAAG,MAAM,KAAK;IAC1C,OAAOF,WAAW,CAACE,OAAO,CAAC,IAAIF,WAAW,CAAC,MAAM,CAAC;EACpD,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAIxE,IAAI,IAAK;IACnC;IACA,MAAMyE,aAAa,GAAG,GAAG,GAAGzE,IAAI,CAACE,IAAI,CAACwE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACpE,IAAI,CAACb,eAAe,CAACG,iBAAiB,CAAClB,QAAQ,CAACyB,aAAa,CAAC,EAAE;MAC9D3M,OAAO,CAACuF,KAAK,CAAC,aAAaoH,aAAa,QAAQV,eAAe,CAACG,iBAAiB,CAACW,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;MAClG,OAAO,KAAK;IACd;;IAEA;IACA,MAAMC,UAAU,GAAG9E,IAAI,CAACoC,IAAI,GAAG,IAAI,GAAG,IAAI;IAC1C,IAAI0C,UAAU,GAAGf,eAAe,CAACI,gBAAgB,EAAE;MACjDrM,OAAO,CAACuF,KAAK,CAAC,aAAayH,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,QAAQhB,eAAe,CAACI,gBAAgB,IAAI,CAAC;MAC7F,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMa,gBAAgB,GAAGA,CAAChD,QAAQ,EAAEpH,SAAS,GAAG,IAAI,KAAK;IACvD,IAAI,CAACoH,QAAQ,IAAIA,QAAQ,CAACpF,MAAM,KAAK,CAAC,EAAE;MACtC,OAAO;QAAEqI,OAAO,EAAE,KAAK;QAAEnN,OAAO,EAAE;MAAY,CAAC;IACjD;;IAEA;IACA,IAAIkK,QAAQ,CAACpF,MAAM,GAAGmH,eAAe,CAACC,SAAS,EAAE;MAC/C,OAAO;QACLiB,OAAO,EAAE,KAAK;QACdnN,OAAO,EAAE,aAAakK,QAAQ,CAACpF,MAAM,MAAMmH,eAAe,CAACC,SAAS;MACtE,CAAC;IACH;IAEA,IAAIhC,QAAQ,CAACpF,MAAM,GAAGmH,eAAe,CAACE,SAAS,EAAE;MAC/C,OAAO;QACLgB,OAAO,EAAE,KAAK;QACdnN,OAAO,EAAE,WAAWkK,QAAQ,CAACpF,MAAM,MAAMmH,eAAe,CAACE,SAAS;MACpE,CAAC;IACH;;IAEA;IACA,MAAMiB,SAAS,GAAGlD,QAAQ,CAAC0B,MAAM,CAAC,CAACyB,GAAG,EAAEnF,IAAI,KAAK;MAC/C,MAAMoF,OAAO,GAAGpF,IAAI,CAACC,aAAa,IAAID,IAAI;MAC1C,OAAOmF,GAAG,IAAIC,OAAO,CAAChD,IAAI,IAAI,CAAC,CAAC;IAClC,CAAC,EAAE,CAAC,CAAC;IACL,MAAMiD,WAAW,GAAGH,SAAS,GAAG,IAAI,GAAG,IAAI;IAE3C,IAAIG,WAAW,GAAGtB,eAAe,CAACK,iBAAiB,EAAE;MACnD,OAAO;QACLa,OAAO,EAAE,KAAK;QACdnN,OAAO,EAAE,cAAcuN,WAAW,CAACN,OAAO,CAAC,CAAC,CAAC,QAAQhB,eAAe,CAACK,iBAAiB;MACxF,CAAC;IACH;IAEA,OAAO;MAAEa,OAAO,EAAE,IAAI;MAAEnN,OAAO,EAAE;IAAG,CAAC;EACvC,CAAC;;EAED;EACA,MAAMwN,YAAY,GAAGA,CAACtF,IAAI,EAAEgC,QAAQ,KAAK;IACvC,IAAI,CAACwC,kBAAkB,CAACxE,IAAI,CAAC,EAAE;MAC7B,OAAOtI,MAAM,CAAC6N,WAAW;IAC3B;;IAEA;IACA,IAAIvD,QAAQ,IAAIA,QAAQ,CAACpF,MAAM,IAAImH,eAAe,CAACC,SAAS,EAAE;MAC5DlM,OAAO,CAACuF,KAAK,CAAC,UAAU0G,eAAe,CAACC,SAAS,MAAM,CAAC;MACxD,OAAOtM,MAAM,CAAC6N,WAAW;IAC3B;IAEA,OAAO,KAAK,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,KAAK,GAAGhL,cAAc,CAACmC,MAAM,GAAG,CAAC,GACnC8I,IAAI,CAACC,GAAG,CAAC,GAAGlL,cAAc,CAACqC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACpC,EAAE,CAAC,CAAC,GAAG,CAAC,GACpD,CAAC;IAELD,iBAAiB,CAAC,CAAC,GAAGD,cAAc,EAAE;MAAEE,EAAE,EAAE8K,KAAK;MAAE7K,SAAS,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC,CAAC;EACnF,CAAC;;EAED;EACA,MAAM+K,mBAAmB,GAAIjL,EAAE,IAAK;IAClC,IAAIF,cAAc,CAACmC,MAAM,IAAI,CAAC,EAAE;MAC9B9E,OAAO,CAACsF,OAAO,CAAC,YAAY,CAAC;MAC7B;IACF;IACA1C,iBAAiB,CAACD,cAAc,CAACyD,MAAM,CAACnB,IAAI,IAAIA,IAAI,CAACpC,EAAE,KAAKA,EAAE,CAAC,CAAC;EAClE,CAAC;;EAED;EACA,MAAMkL,eAAe,GAAGA,CAAClL,EAAE,EAAEC,SAAS,KAAK;IACzCF,iBAAiB,CACfD,cAAc,CAACqC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACpC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGoC,IAAI;MAAEnC;IAAU,CAAC,GAAGmC,IAC5C,CACF,CAAC;EACH,CAAC;;EAED;EACA,MAAM+I,kBAAkB,GAAGA,CAACnL,EAAE,EAAEoL,QAAQ,KAAK;IAC3C;IACA,MAAMC,UAAU,GAAGhB,gBAAgB,CAACe,QAAQ,EAAEpL,EAAE,CAAC;;IAEjD;IACAD,iBAAiB,CACfD,cAAc,CAACqC,GAAG,CAACC,IAAI,IACrBA,IAAI,CAACpC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGoC,IAAI;MAAElC,KAAK,EAAEkL;IAAS,CAAC,GAAGhJ,IAClD,CACF,CAAC;;IAED;IACA,IAAI,CAACiJ,UAAU,CAACf,OAAO,IAAIc,QAAQ,CAACnJ,MAAM,GAAG,CAAC,EAAE;MAC9C9E,OAAO,CAACsF,OAAO,CAAC,MAAMzC,EAAE,KAAKqL,UAAU,CAAClO,OAAO,EAAE,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMmO,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;IAC7B,MAAMC,CAAC,GAAG,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,MAAMC,CAAC,GAAGX,IAAI,CAACY,KAAK,CAACZ,IAAI,CAAClJ,GAAG,CAAC0J,KAAK,CAAC,GAAGR,IAAI,CAAClJ,GAAG,CAAC2J,CAAC,CAAC,CAAC;IACnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGR,IAAI,CAACc,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEtB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGqB,KAAK,CAACC,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAGA,CAACzE,QAAQ,EAAEuC,OAAO,GAAG,MAAM,KAAK;IACvD,IAAI,CAACvC,QAAQ,IAAIA,QAAQ,CAACpF,MAAM,KAAK,CAAC,EAAE;MACtC,oBAAOrD,OAAA,CAACE,IAAI;QAACoH,IAAI,EAAC,WAAW;QAAA6F,QAAA,EAAC;MAAG;QAAAnG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC1C;IAEA,MAAMb,UAAU,GAAGhB,gBAAgB,CAAChD,QAAQ,CAAC;IAC7C,MAAM8E,UAAU,GAAGxC,aAAa,CAACC,OAAO,CAAC;IAEzC,oBACEhL,OAAA,CAACf,KAAK;MAACuO,SAAS,EAAC,UAAU;MAAC3E,IAAI,EAAC,OAAO;MAAAsE,QAAA,gBACtCnN,OAAA;QAAAmN,QAAA,gBACEnN,OAAA,CAACE,IAAI;UAACuN,MAAM;UAAAN,QAAA,GAAC,qBAAI,EAAC1E,QAAQ,CAACpF,MAAM,EAAC,GAAC,EAACmH,eAAe,CAACC,SAAS,EAAC,SAAE;QAAA;UAAAzD,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtEb,UAAU,CAACf,OAAO,gBACjB1L,OAAA,CAACX,mBAAmB;UAACqO,KAAK,EAAE;YAAEC,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAE;QAAE;UAAA5G,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBACnEtN,OAAA,CAACE,IAAI;UAACoH,IAAI,EAAC,SAAS;UAACoG,KAAK,EAAE;YAAEE,UAAU,EAAE;UAAE,CAAE;UAAAT,QAAA,GAAC,GAAC,EAACV,UAAU,CAAClO,OAAO,EAAC,GAAC;QAAA;UAAAyI,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE3E,CAAC,eACNtN,OAAA;QAAAmN,QAAA,EACG1E,QAAQ,CAAClF,GAAG,CAAC,CAACkD,IAAI,EAAEoH,KAAK,kBACxB7N,OAAA;UAAkB0N,KAAK,EAAE;YAAEI,WAAW,EAAE;UAAE,CAAE;UAAAX,QAAA,eAC1CnN,OAAA,CAACE,IAAI;YAACoH,IAAI,EAAC,WAAW;YAAA6F,QAAA,EAAEI,UAAU,CAACM,KAAK,CAAC,IAAI,IAAIA,KAAK,GAAG,CAAC;UAAG;YAAA7G,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC,GAD5DO,KAAK;UAAA7G,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACP;MAAC;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtN,OAAA,CAACE,IAAI;QAACoH,IAAI,EAAC,WAAW;QAACoG,KAAK,EAAE;UAAEK,QAAQ,EAAE;QAAO,CAAE;QAAAZ,QAAA,GAAC,sBAC7C,EAACT,cAAc,CAACjE,QAAQ,CAAC0B,MAAM,CAAC,CAACyB,GAAG,EAAEnF,IAAI,KAAKmF,GAAG,IAAInF,IAAI,CAACoC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAA;QAAA7B,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC;IAAA;MAAAtG,QAAA,EAAAoG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA,MAAMU,uBAAuB,GAAGA,CAACxK,IAAI,EAAEqK,KAAK,KAAK;IAC/C;IACA,MAAMI,WAAW,GAAGvL,aAAa,CAACW,MAAM,GAAG,CAAC,GAAGX,aAAa,GAAGjB,QAAQ;IACvE,MAAMyM,WAAW,GAAGD,WAAW,CAACzI,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACvC,EAAE,KAAKoC,IAAI,CAACnC,SAAS,CAAC;IAClE,MAAM8E,WAAW,GAAG+H,WAAW,GAAIA,WAAW,CAAC9H,SAAS,IAAI8H,WAAW,CAAC7H,QAAQ,GAAI,SAAS7C,IAAI,CAACnC,SAAS,EAAE;;IAE7G;IACA,MAAMkH,eAAe,GAAG3H,IAAI,CAAC4H,aAAa,CAAC,UAAU,CAAC;IACtD,MAAM2F,gBAAgB,GAAG,CAAC,CAAC5F,eAAe;IAE1CvF,OAAO,CAACC,GAAG,CAAC,WAAW4K,KAAK,GAAG,EAAE;MAC/BtF,eAAe;MACf4F,gBAAgB;MAChBC,mBAAmB,EAAE1L,aAAa,CAACW,MAAM;MACzCgL,iBAAiB,EAAEJ,WAAW,CAAC5K,MAAM;MACrChC,SAAS,EAAEmC,IAAI,CAACnC,SAAS;MACzB6M,WAAW,EAAEA;IACf,CAAC,CAAC;IAEF,oBACElO,OAAA;MAAiBsO,SAAS,EAAC,qBAAqB;MAACZ,KAAK,EAAE;QAAEa,YAAY,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,gBAAgB;QAAEC,YAAY,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBACnInN,OAAA,CAACvB,GAAG;QAACkQ,MAAM,EAAE,EAAG;QAACC,KAAK,EAAC,QAAQ;QAAAzB,QAAA,gBAC7BnN,OAAA,CAACtB,GAAG;UAACmQ,IAAI,EAAE,CAAE;UAAA1B,QAAA,eACXnN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;YAACC,KAAK,EAAC,cAAI;YAAA5B,QAAA,gBACnBnN,OAAA,CAAC3B,MAAM;cACLiH,KAAK,EAAE9B,IAAI,CAACnC,SAAU;cACtB2N,QAAQ,EAAE1J,KAAK,IAAIgH,eAAe,CAAC9I,IAAI,CAACpC,EAAE,EAAEkE,KAAK,CAAE;cACnDoI,KAAK,EAAE;gBAAEuB,KAAK,EAAE;cAAO,CAAE;cACzBC,WAAW,EAAEf,gBAAgB,GAAG,MAAM,GAAG,QAAS;cAClDgB,QAAQ,EAAE,CAAChB,gBAAiB;cAAAhB,QAAA,EAE3Bc,WAAW,CAAC1K,GAAG,CAAC2C,OAAO,iBACtBlG,OAAA,CAACI,MAAM;gBAAkBkF,KAAK,EAAEY,OAAO,CAAC9E,EAAG;gBAAA+L,QAAA,EACxCjH,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACG;cAAQ,GAD3BH,OAAO,CAAC9E,EAAE;gBAAA4F,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRa,gBAAgB,IAAIF,WAAW,CAAC5K,MAAM,KAAK,CAAC,iBAC3CrD,OAAA;cAAK0N,KAAK,EAAE;gBAAEC,KAAK,EAAE,QAAQ;gBAAEyB,SAAS,EAAE;cAAE,CAAE;cAAAjC,QAAA,EAAC;YAE/C;cAAAnG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtN,OAAA,CAACtB,GAAG;UAACmQ,IAAI,EAAE,EAAG;UAAA1B,QAAA,eACZnN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5B,QAAA,eACrBnN,OAAA,CAAC7B,MAAM;cACLsK,QAAQ,EAAEjF,IAAI,CAAClC,KAAM;cACrB0N,QAAQ,EAAEA,CAAC;gBAAEvG;cAAS,CAAC,KAAK8D,kBAAkB,CAAC/I,IAAI,CAACpC,EAAE,EAAEqH,QAAQ,CAAE;cAClEsD,YAAY,EAAEA,YAAa;cAC3BsD,QAAQ;cAAAlC,QAAA,eAERnN,OAAA,CAAC5B,MAAM;gBAACkR,IAAI,eAAEtP,OAAA,CAACd,cAAc;kBAAA8H,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAH,QAAA,EAAC;cAAI;gBAAAnG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENtN,OAAA,CAACtB,GAAG;UAACmQ,IAAI,EAAE,CAAE;UAACnB,KAAK,EAAE;YAAE6B,SAAS,EAAE;UAAQ,CAAE;UAAApC,QAAA,eAC1CnN,OAAA,CAAC5B,MAAM;YACLkJ,IAAI,EAAC,QAAQ;YACbgI,IAAI,eAAEtP,OAAA,CAACZ,cAAc;cAAA4H,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBkC,OAAO,EAAEA,CAAA,KAAMnD,mBAAmB,CAAC7I,IAAI,CAACpC,EAAE,CAAE;YAAA+L,QAAA,EAC7C;UAED;YAAAnG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtN,OAAA;QAAK0N,KAAK,EAAE;UAAE0B,SAAS,EAAE,EAAE;UAAEZ,OAAO,EAAE,EAAE;UAAEiB,UAAU,EAAE,SAAS;UAAEf,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,eACjFnN,OAAA,CAACvB,GAAG;UAACkQ,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACdnN,OAAA,CAACtB,GAAG;YAACmQ,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZnN,OAAA;cAAK0N,KAAK,EAAE;gBAAEa,YAAY,EAAE;cAAE,CAAE;cAAApB,QAAA,EAC7BD,gBAAgB,CAAC1J,IAAI,CAAClC,KAAK;YAAC;cAAA0F,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtN,OAAA,CAACtB,GAAG;YAACmQ,IAAI,EAAE,EAAG;YAAA1B,QAAA,EACX3J,IAAI,CAAClC,KAAK,CAAC+B,MAAM,GAAG,CAAC,iBACpBrD,OAAA;cAAAmN,QAAA,gBACEnN,OAAA,CAACE,IAAI;gBAACuN,MAAM;gBAACC,KAAK,EAAE;kBAAEK,QAAQ,EAAE,MAAM;kBAAEJ,KAAK,EAAE;gBAAO,CAAE;gBAAAR,QAAA,EAAC;cAAK;gBAAAnG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrEtN,OAAA;gBAAK0N,KAAK,EAAE;kBAAE0B,SAAS,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,EAC1B3J,IAAI,CAAClC,KAAK,CAACiC,GAAG,CAAC,CAACkD,IAAI,EAAEqD,SAAS,KAAK;kBACnC,MAAMyD,UAAU,GAAGxC,aAAa,CAAC,CAAC;kBAClC,MAAM2E,SAAS,GAAGnC,UAAU,CAACzD,SAAS,CAAC,IAAI,IAAIA,SAAS,GAAG,CAAC,GAAG;kBAC/D,MAAM9C,QAAQ,GAAGP,IAAI,CAACE,IAAI,IAAKF,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,IAAK,IAAI,MAAMmD,SAAS,GAAG,CAAC,EAAE;kBACtG,MAAM6F,QAAQ,GAAGlJ,IAAI,CAACoC,IAAI,IAAKpC,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACmC,IAAK,IAAI,CAAC;kBAElF,oBACE7I,OAAA;oBAAqB0N,KAAK,EAAE;sBAC1BK,QAAQ,EAAE,MAAM;sBAChBJ,KAAK,EAAE,MAAM;sBACbY,YAAY,EAAE,KAAK;sBACnBqB,OAAO,EAAE,MAAM;sBACfC,cAAc,EAAE;oBAClB,CAAE;oBAAA1C,QAAA,gBACAnN,OAAA;sBAAAmN,QAAA,gBACEnN,OAAA,CAACE,IAAI;wBAACoH,IAAI,EAAC,WAAW;wBAAA6F,QAAA,GAAEuC,SAAS,EAAC,GAAC;sBAAA;wBAAA1I,QAAA,EAAAoG,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,KAAC,EAACtG,QAAQ;oBAAA;sBAAAA,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACPtN,OAAA,CAACE,IAAI;sBAACoH,IAAI,EAAC,WAAW;sBAAA6F,QAAA,EAAET,cAAc,CAACiD,QAAQ;oBAAC;sBAAA3I,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAVhDxD,SAAS;oBAAA9C,QAAA,EAAAoG,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWd,CAAC;gBAEV,CAAC;cAAC;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtN,OAAA;QAAK0N,KAAK,EAAE;UAAE0B,SAAS,EAAE;QAAE,CAAE;QAAAjC,QAAA,eAC3BnN,OAAA,CAAC1B,UAAU,CAAC4B,IAAI;UAACoH,IAAI,EAAC,WAAW;UAAA6F,QAAA,GAAC,gBAC5B,eAAAnN,OAAA,CAAC1B,UAAU,CAAC4B,IAAI;YAACuN,MAAM;YAAAN,QAAA,EAAEhH;UAAW;YAAAa,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CAAC,EAC1DY,WAAW,IAAIA,WAAW,CAAC4B,KAAK,IAAI,KAAK5B,WAAW,CAAC4B,KAAK,GAAG;QAAA;UAAA9I,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA,GA/FEO,KAAK;MAAA7G,QAAA,EAAAoG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAgGV,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMC,OAAO,GAAG,CACd;MACE5L,KAAK,EAAE,KAAK;MACZ6L,SAAS,EAAE,MAAM;MACjBC,GAAG,EAAE,MAAM;MACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBrQ,OAAA;QAAAmN,QAAA,gBACEnN,OAAA,CAACT,iBAAiB;UAACmO,KAAK,EAAE;YAAEI,WAAW,EAAE;UAAE;QAAE;UAAA9G,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC/C8C,IAAI,eACLpQ,OAAA;UAAK0N,KAAK,EAAE;YAAEK,QAAQ,EAAE,MAAM;YAAEJ,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,GAC7C,CAACkD,MAAM,CAACxH,IAAI,GAAG,IAAI,EAAE2C,OAAO,CAAC,CAAC,CAAC,EAAC,KACnC;QAAA;UAAAxE,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAET,CAAC,EACD;MACElJ,KAAK,EAAE,MAAM;MACb6L,SAAS,EAAE,SAAS;MACpBC,GAAG,EAAE,SAAS;MACdC,MAAM,EAAGpH,OAAO,IACdA,OAAO,gBACL/I,OAAA,CAACE,IAAI;QAACoH,IAAI,EAAC,SAAS;QAAA6F,QAAA,gBAACnN,OAAA,CAACX,mBAAmB;UAAA2H,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,uBAAI;MAAA;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBACvDtN,OAAA,CAACE,IAAI;QAACoH,IAAI,EAAC,QAAQ;QAAA6F,QAAA,EAAC;MAAG;QAAAnG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAEnC,CAAC,EACD;MACElJ,KAAK,EAAE,IAAI;MACX6L,SAAS,EAAE,aAAa;MACxBC,GAAG,EAAE,aAAa;MAClBC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,EAAExC,KAAK,kBAC1B7N,OAAA,CAAC3B,MAAM;QACLiH,KAAK,EAAE+K,MAAM,CAAChP,SAAU;QACxB2N,QAAQ,EAAG1J,KAAK,IAAKuE,sBAAsB,CAACgE,KAAK,EAAEvI,KAAK,CAAE;QAC1DoI,KAAK,EAAE;UAAEuB,KAAK,EAAE;QAAO,CAAE;QACzBC,WAAW,EAAC,0BAAM;QAAA/B,QAAA,EAEjBzK,aAAa,CAACa,GAAG,CAAC2C,OAAO,iBACxBlG,OAAA,CAACI,MAAM;UAAkBkF,KAAK,EAAEY,OAAO,CAAC9E,EAAG;UAAA+L,QAAA,EACxCjH,OAAO,CAACE,SAAS,IAAIF,OAAO,CAACG;QAAQ,GAD3BH,OAAO,CAAC9E,EAAE;UAAA4F,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEf,CACT;MAAC;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAEZ,CAAC,CACF;IAED,oBACEtN,OAAA;MAAAmN,QAAA,gBACEnN,OAAA,CAACjB,KAAK;QACJR,OAAO,EAAC,4CAAS;QACjByH,WAAW,eACThG,OAAA;UAAAmN,QAAA,gBACEnN,OAAA;YAAAmN,QAAA,EAAG;UAAkC;YAAAnG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzCtN,OAAA;YAAAmN,QAAA,GAAG,iCAAM,EAAC9K,eAAe,CAACgB,MAAM,EAAC,GAAC,EAAClB,cAAc,CAACkB,MAAM,EAAC,qBAAI;UAAA;YAAA2D,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN;QACDhG,IAAI,EAAC,MAAM;QACXgJ,QAAQ;QACR5C,KAAK,EAAE;UAAEa,YAAY,EAAE;QAAG;MAAE;QAAAvH,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEFtN,OAAA,CAAChB,KAAK;QACJuR,UAAU,EAAEpO,cAAe;QAC3B6N,OAAO,EAAEA,OAAQ;QACjBQ,MAAM,EAAEA,CAACH,MAAM,EAAExC,KAAK,KAAKA,KAAM;QACjChF,IAAI,EAAC,OAAO;QACZ4H,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAA1J,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAtG,QAAA,EAAAoG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACEtN,OAAA;IAAKsO,SAAS,EAAC,iCAAiC;IAAAnB,QAAA,gBAC9CnN,OAAA,CAACC,KAAK;MAAC0Q,KAAK,EAAE,CAAE;MAAAxD,QAAA,EAAC;IAAI;MAAAnG,QAAA,EAAAoG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE7BtN,OAAA,CAACpB,IAAI;MAACgS,QAAQ,EAAE9P,OAAO,IAAIa,WAAY;MAAAwL,QAAA,EACpCnM,aAAa,gBACZhB,OAAA,CAACnB,MAAM;QACLoF,MAAM,EAAC,SAAS;QAChBG,KAAK,EAAC,4CAAS;QACfyM,QAAQ,EAAC,0HAAsB;QAC/BC,KAAK,EAAE,cACL9Q,OAAA,CAAC5B,MAAM;UACLkJ,IAAI,EAAC,SAAS;UAEdkI,OAAO,EAAEA,CAAA,KAAM9O,QAAQ,CAAC,WAAW,CAAE;UAAAyM,QAAA,EACtC;QAED,GAJM,MAAM;UAAAnG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIJ,CAAC,eACTtN,OAAA,CAAC5B,MAAM;UAELoR,OAAO,EAAEA,CAAA,KAAM;YACbvO,gBAAgB,CAAC,KAAK,CAAC;YACvBE,iBAAiB,CAAC,CAAC;cAAEC,EAAE,EAAE,CAAC;cAAEC,SAAS,EAAE,IAAI;cAAEC,KAAK,EAAE;YAAG,CAAC,CAAC,CAAC;YAC1Dc,iBAAiB,CAAC,EAAE,CAAC;YACrBE,kBAAkB,CAAC,EAAE,CAAC;YACtB1B,IAAI,CAACmQ,WAAW,CAAC,CAAC;UACpB,CAAE;UAAA5D,QAAA,EACH;QAED,GAVM,OAAO;UAAAnG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUL,CAAC;MACT;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEFtN,OAAA,CAAC/B,IAAI;QACH2C,IAAI,EAAEA,IAAK;QACXoQ,MAAM,EAAC,UAAU;QACjBzG,QAAQ,EAAEA,QAAS;QAAA4C,QAAA,gBAGnBnN,OAAA,CAACjB,KAAK;UACJR,OAAO,EAAC,+DAAa;UACrByH,WAAW,eACThG,OAAA;YAAAmN,QAAA,gBACEnN,OAAA,CAACvB,GAAG;cAACkQ,MAAM,EAAE,EAAG;cAAAxB,QAAA,gBACdnN,OAAA,CAACtB,GAAG;gBAACmQ,IAAI,EAAE,EAAG;gBAAA1B,QAAA,eACZnN,OAAA;kBAAK0N,KAAK,EAAE;oBAAEa,YAAY,EAAE;kBAAE,CAAE;kBAAApB,QAAA,gBAC9BnN,OAAA,CAACE,IAAI;oBAACuN,MAAM;oBAAAN,QAAA,EAAC;kBAAQ;oBAAAnG,QAAA,EAAAoG,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5BtN,OAAA;oBAAI0N,KAAK,EAAE;sBAAE0B,SAAS,EAAE,CAAC;sBAAEb,YAAY,EAAE;oBAAE,CAAE;oBAAApB,QAAA,gBAC3CnN,OAAA;sBAAAmN,QAAA,GAAI,gCAAK,EAAC3C,eAAe,CAACG,iBAAiB,CAACW,IAAI,CAAC,IAAI,CAAC;oBAAA;sBAAAtE,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5DtN,OAAA;sBAAAmN,QAAA,GAAI,gCAAK,EAAC3C,eAAe,CAACE,SAAS,EAAC,GAAC,EAACF,eAAe,CAACC,SAAS,EAAC,QAAC;oBAAA;sBAAAzD,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtEtN,OAAA;sBAAAmN,QAAA,GAAI,kDAAQ,EAAC3C,eAAe,CAACI,gBAAgB,EAAC,IAAE;oBAAA;sBAAA5D,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrDtN,OAAA;sBAAAmN,QAAA,GAAI,4CAAO,EAAC3C,eAAe,CAACK,iBAAiB,EAAC,IAAE;oBAAA;sBAAA7D,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAtG,QAAA,EAAAoG,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC;gBAAA;kBAAAtG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtN,OAAA,CAACtB,GAAG;gBAACmQ,IAAI,EAAE,EAAG;gBAAA1B,QAAA,eACZnN,OAAA;kBAAK0N,KAAK,EAAE;oBAAEa,YAAY,EAAE;kBAAE,CAAE;kBAAApB,QAAA,gBAC9BnN,OAAA,CAACE,IAAI;oBAACuN,MAAM;oBAAAN,QAAA,EAAC;kBAAU;oBAAAnG,QAAA,EAAAoG,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9BtN,OAAA;oBAAI0N,KAAK,EAAE;sBAAE0B,SAAS,EAAE,CAAC;sBAAEb,YAAY,EAAE;oBAAE,CAAE;oBAAApB,QAAA,gBAC3CnN,OAAA;sBAAAmN,QAAA,gBAAInN,OAAA,CAACE,IAAI;wBAACoH,IAAI,EAAC,WAAW;wBAAA6F,QAAA,EAAC;sBAAG;wBAAAnG,QAAA,EAAAoG,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,0EAAe;oBAAA;sBAAAtG,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzDtN,OAAA;sBAAAmN,QAAA,gBAAInN,OAAA,CAACE,IAAI;wBAACoH,IAAI,EAAC,WAAW;wBAAA6F,QAAA,EAAC;sBAAG;wBAAAnG,QAAA,EAAAoG,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,2GAAkB;oBAAA;sBAAAtG,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5DtN,OAAA;sBAAAmN,QAAA,gBAAInN,OAAA,CAACE,IAAI;wBAACoH,IAAI,EAAC,WAAW;wBAAA6F,QAAA,EAAC;sBAAG;wBAAAnG,QAAA,EAAAoG,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,sHAAmB;oBAAA;sBAAAtG,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7DtN,OAAA;sBAAAmN,QAAA,gBAAInN,OAAA,CAACE,IAAI;wBAACoH,IAAI,EAAC,WAAW;wBAAA6F,QAAA,EAAC;sBAAG;wBAAAnG,QAAA,EAAAoG,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,4HAAoB;oBAAA;sBAAAtG,QAAA,EAAAoG,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAtG,QAAA,EAAAoG,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAtG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtN,OAAA;cAAK0N,KAAK,EAAE;gBAAE0B,SAAS,EAAE,CAAC;gBAAEZ,OAAO,EAAE,CAAC;gBAAEiB,UAAU,EAAE,SAAS;gBAAEf,YAAY,EAAE;cAAE,CAAE;cAAAvB,QAAA,eAC/EnN,OAAA,CAACE,IAAI;gBAACoH,IAAI,EAAC,WAAW;gBAACoG,KAAK,EAAE;kBAAEK,QAAQ,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEpD;gBAAAnG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;UACDhG,IAAI,EAAC,MAAM;UACXgJ,QAAQ;UACR5C,KAAK,EAAE;YAAEa,YAAY,EAAE;UAAG;QAAE;UAAAvH,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFtN,OAAA,CAACxB,IAAI;UAAC4F,KAAK,EAAC,0BAAM;UAACkK,SAAS,EAAC,MAAM;UAAAnB,QAAA,gBACjCnN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;YACRnI,IAAI,EAAC,eAAe;YACpBoI,KAAK,EAAC,0BAAM;YACZkC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE3S,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA4O,QAAA,eAEhDnN,OAAA,CAAC3B,MAAM;cACL6Q,WAAW,EAAC,4CAAS;cACrBpO,OAAO,EAAEiB,kBAAmB;cAC5BiN,QAAQ,EAAE3J,sBAAuB;cAAA8H,QAAA,EAEhCtL,WAAW,CAAC0B,GAAG,CAAC4N,UAAU,iBACzBnR,OAAA,CAACI,MAAM;gBAAqBkF,KAAK,EAAE6L,UAAU,CAAC/P,EAAG;gBAAA+L,QAAA,EAC9CgE,UAAU,CAAC/M;cAAK,GADN+M,UAAU,CAAC/P,EAAE;gBAAA4F,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAElB,CACT;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZtN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;YACRnI,IAAI,EAAC,UAAU;YACfoI,KAAK,EAAC,cAAI;YACVkC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE3S,OAAO,EAAE;YAAQ,CAAC,CAAE;YAAA4O,QAAA,eAE9CnN,OAAA,CAAC3B,MAAM;cACL6Q,WAAW,EAAC,gCAAO;cACnBF,QAAQ,EAAElM,iBAAkB;cAAAqK,QAAA,EAE3B5L,OAAO,CAACgC,GAAG,CAAC6N,GAAG,iBACdpR,OAAA,CAACI,MAAM;gBAAckF,KAAK,EAAE8L,GAAG,CAAChQ,EAAG;gBAAA+L,QAAA,EAAEiE,GAAG,CAACzK;cAAI,GAAhCyK,GAAG,CAAChQ,EAAE;gBAAA4F,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAmC,CACvD;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZtN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;YACRnI,IAAI,EAAC,aAAa;YAClBoI,KAAK,EAAC,cAAI;YAAA5B,QAAA,eAEVnN,OAAA,CAACK,QAAQ;cACP6O,WAAW,EAAC,oEAAa;cACzBmC,IAAI,EAAE;YAAE;cAAArK,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGN/M,IAAI,CAAC+D,UAAU,iBACdtE,OAAA,CAACxB,IAAI;UAAC4F,KAAK,EAAC,0BAAM;UAACkK,SAAS,EAAC,MAAM;UAAAnB,QAAA,gBACjCnN,OAAA,CAAClB,KAAK,CAACwS,KAAK;YACVhM,KAAK,EAAErD,UAAW;YAClB+M,QAAQ,EAAE3G,CAAC,IAAInG,aAAa,CAACmG,CAAC,CAACC,MAAM,CAAChD,KAAK,CAAE;YAC7CoI,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAG,CAAE;YAAApB,QAAA,gBAE5BnN,OAAA,CAAClB,KAAK,CAACV,MAAM;cAACkH,KAAK,EAAC,QAAQ;cAAA6H,QAAA,EAAC;YAAS;cAAAnG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC,eACrDtN,OAAA,CAAClB,KAAK,CAACV,MAAM;cAACkH,KAAK,EAAC,WAAW;cAAA6H,QAAA,EAAC;YAAS;cAAAnG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAAC;UAAA;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,EAEbrL,UAAU,KAAK,WAAW,iBACzBjC,OAAA;YAAKsO,SAAS,EAAC,uBAAuB;YAAAnB,QAAA,eACpCnN,OAAA,CAACjB,KAAK;cACJR,OAAO,EAAC,oEAAa;cACrByH,WAAW,eACThG,OAAA;gBAAAmN,QAAA,gBACEnN,OAAA,CAACG,SAAS;kBAAAgN,QAAA,EAAC;gBAEX;kBAAAnG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZtN,OAAA,CAACG,SAAS;kBAAAgN,QAAA,EAAC;gBAEX;kBAAAnG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZtN,OAAA,CAACG,SAAS;kBAAAgN,QAAA,EAAC;gBAEX;kBAAAnG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACZtN,OAAA,CAACG,SAAS;kBAAAgN,QAAA,EAAC;gBAEX;kBAAAnG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACN;cACDhG,IAAI,EAAC,MAAM;cACXgJ,QAAQ;YAAA;cAAAtJ,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACP,EAGA,CAAC/M,IAAI,CAAC+D,UAAU,gBACftE,OAAA,CAACxB,IAAI;UAAC4F,KAAK,EAAC,mDAAW;UAACkK,SAAS,EAAC,MAAM;UAAAnB,QAAA,eACtCnN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;YACRC,KAAK,eACH/O,OAAA;cAAAmN,QAAA,GAAM,0BAEJ,eAAAnN,OAAA,CAACE,IAAI;gBAACoH,IAAI,EAAC,WAAW;gBAACoG,KAAK,EAAE;kBAAEE,UAAU,EAAE,CAAC;kBAAEG,QAAQ,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,GAAC,GAChE,EAAC3C,eAAe,CAACE,SAAS,EAAC,GAAC,EAACF,eAAe,CAACC,SAAS,EAAC,0BAAI,EAACD,eAAe,CAACG,iBAAiB,CAACW,IAAI,CAAC,GAAG,CAAC,EAAC,eAC3G;cAAA;gBAAAtE,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACP;YACD4D,QAAQ;YACRK,OAAO,EAAE,YAAY/G,eAAe,CAACG,iBAAiB,CAACW,IAAI,CAAC,GAAG,CAAC,WAAWd,eAAe,CAACI,gBAAgB,IAAK;YAAAuC,QAAA,gBAEhHnN,OAAA,CAAC7B,MAAM;cACLqT,QAAQ,EAAC,cAAc;cACvB/I,QAAQ,EAAE,EAAG;cACbsD,YAAY,EAAEA,YAAa;cAC3BiD,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAE;cACnBK,QAAQ;cACRoC,MAAM,EAAEjH,eAAe,CAACG,iBAAiB,CAACW,IAAI,CAAC,GAAG,CAAE;cAAA6B,QAAA,eAEpDnN,OAAA;gBAAAmN,QAAA,gBACEnN,OAAA,CAACb,YAAY;kBAAA6H,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChBtN,OAAA;kBAAK0N,KAAK,EAAE;oBAAE0B,SAAS,EAAE;kBAAE,CAAE;kBAAAjC,QAAA,EAAC;gBAAI;kBAAAnG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACTtN,OAAA;cAAK0N,KAAK,EAAE;gBAAE0B,SAAS,EAAE;cAAE,CAAE;cAAAjC,QAAA,eAC3BnN,OAAA,CAACjB,KAAK;gBACJR,OAAO,eACLyB,OAAA;kBAAAmN,QAAA,eACEnN,OAAA,CAACE,IAAI;oBAACoH,IAAI,EAAC,WAAW;oBAAA6F,QAAA,GAAC,qEACT,EAACpC,aAAa,CAAC,CAAC,CAAC2G,KAAK,CAAC,CAAC,EAAElH,eAAe,CAACC,SAAS,CAAC,CAACa,IAAI,CAAC,KAAK,CAAC;kBAAA;oBAAAtE,QAAA,EAAAoG,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE;gBAAC;kBAAAtG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN;gBACDhG,IAAI,EAAC,MAAM;gBACXgJ,QAAQ,EAAE,KAAM;gBAChB5C,KAAK,EAAE;kBAAEK,QAAQ,EAAE;gBAAO;cAAE;gBAAA/G,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,GACLrL,UAAU,KAAK,WAAW,gBAC5BjC,OAAA,CAACxB,IAAI;UAAC4F,KAAK,EAAC,wDAAW;UAACkK,SAAS,EAAC,MAAM;UAAAnB,QAAA,eACtCnN,OAAA;YAAKsO,SAAS,EAAC,0BAA0B;YAAAnB,QAAA,gBACvCnN,OAAA;cACE2R,GAAG,EAAElP,iBAAkB;cACvB6E,IAAI,EAAC,MAAM;cACXsK,eAAe,EAAC,MAAM;cACtBC,SAAS,EAAC,MAAM;cAChBxC,QAAQ;cACR3B,KAAK,EAAE;gBAAEkC,OAAO,EAAE;cAAO,CAAE;cAC3BZ,QAAQ,EAAE5G;YAAsB;cAAApB,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEFtN,OAAA;cAAK0N,KAAK,EAAE;gBAAE6B,SAAS,EAAE,QAAQ;gBAAEhB,YAAY,EAAE;cAAG,CAAE;cAAApB,QAAA,gBACpDnN,OAAA,CAAC5B,MAAM;gBACLkJ,IAAI,EAAC,SAAS;gBACdgI,IAAI,eAAEtP,OAAA,CAACV,kBAAkB;kBAAA0H,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7BzE,IAAI,EAAC,OAAO;gBACZ2G,OAAO,EAAEA,CAAA,KAAM;kBACb,MAAMjH,eAAe,GAAG3H,IAAI,CAAC4H,aAAa,CAAC,UAAU,CAAC;kBACtD,IAAI,CAACD,eAAe,EAAE;oBACpBhK,OAAO,CAACsF,OAAO,CAAC,eAAe,CAAC;oBAChC;kBACF;kBACApB,iBAAiB,CAACqP,OAAO,IAAIrP,iBAAiB,CAACqP,OAAO,CAACC,KAAK,CAAC,CAAC;gBAChE,CAAE;gBACF5C,QAAQ,EAAE5M,eAAe,IAAIG,aAAa,CAACW,MAAM,KAAK,CAAE;gBAAA8J,QAAA,GAEvD5K,eAAe,gBAAGvC,OAAA,CAACR,eAAe;kBAAAwH,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG,IAAI,EAAC,gCAEhD;cAAA;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtN,OAAA;gBAAK0N,KAAK,EAAE;kBAAE0B,SAAS,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,EAC1BzK,aAAa,CAACW,MAAM,KAAK,CAAC,gBACzBrD,OAAA,CAACE,IAAI;kBAACoH,IAAI,EAAC,SAAS;kBAAA6F,QAAA,EAAC;gBAAM;kBAAAnG,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAElCtN,OAAA,CAACE,IAAI;kBAACoH,IAAI,EAAC,WAAW;kBAAA6F,QAAA,GAAC,qBACjB,EAAChL,cAAc,CAACkB,MAAM,EAAC,qDACtB,EAAChB,eAAe,CAACgB,MAAM,EAAC,qBAC/B;gBAAA;kBAAA2D,QAAA,EAAAoG,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACP;gBAAAtG,QAAA,EAAAoG,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAtG,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELnL,cAAc,CAACkB,MAAM,GAAG,CAAC,IAAI0M,yBAAyB,CAAC,CAAC;UAAA;YAAA/I,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,gBAEPtN,OAAA;UAAKsO,SAAS,EAAC,sBAAsB;UAAAnB,QAAA,gBACnCnN,OAAA,CAACrB,OAAO;YAACqT,WAAW,EAAC,MAAM;YAAA7E,QAAA,EAAC;UAAQ;YAAAnG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,EAE7CpM,cAAc,CAACqC,GAAG,CAAC,CAACC,IAAI,EAAEqK,KAAK,KAC9BG,uBAAuB,CAACxK,IAAI,EAAEqK,KAAK,CACrC,CAAC,eAED7N,OAAA,CAAC5B,MAAM;YACLkJ,IAAI,EAAC,QAAQ;YACbkI,OAAO,EAAEvD,gBAAiB;YAC1ByB,KAAK,EAAE;cAAEuB,KAAK,EAAE,MAAM;cAAEV,YAAY,EAAE;YAAG,CAAE;YAAApB,QAAA,gBAE3CnN,OAAA,CAACb,YAAY;cAAA6H,QAAA,EAAAoG,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAClB;UAAA;YAAAtG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAEDtN,OAAA,CAAC/B,IAAI,CAAC6Q,IAAI;UAAA3B,QAAA,gBACRnN,OAAA,CAAC5B,MAAM;YACLkJ,IAAI,EAAC,SAAS;YACd2K,QAAQ,EAAC,QAAQ;YACjBnR,OAAO,EAAEA,OAAQ;YACjBqO,QAAQ,EAAElN,UAAU,KAAK,WAAW,IAAII,eAAe,CAACgB,MAAM,KAAK,CAAE;YAAA8J,QAAA,EAEpE5M,IAAI,CAAC+D,UAAU,GAAG,MAAM,GAAG;UAAM;YAAA0C,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACTtN,OAAA,CAAC5B,MAAM;YACLsP,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAE,CAAE;YACzB4B,OAAO,EAAEA,CAAA,KAAM9O,QAAQ,CAAC,WAAW,CAAE;YAAAyM,QAAA,EACtC;UAED;YAAAnG,QAAA,EAAAoG,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAtG,QAAA,EAAAoG,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAtG,QAAA,EAAAoG,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACP;MAAAtG,QAAA,EAAAoG,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAtG,QAAA,EAAAoG,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9M,EAAA,CAxzCIF,cAAc;EAAA,QACDvC,WAAW,EACXC,WAAW,EACbC,IAAI,CAAC4C,OAAO;AAAA;AAAAqR,EAAA,GAHvB5R,cAAc;AA0zCpB,eAAeA,cAAc;AAAC,IAAA4R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}