{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    iconCls,\n    avatarBg,\n    avatarColor,\n    containerSize,\n    containerSizeLG,\n    containerSizeSM,\n    textFontSize,\n    textFontSizeLG,\n    textFontSizeSM,\n    borderRadius,\n    borderRadiusLG,\n    borderRadiusSM,\n    lineWidth,\n    lineType\n  } = token;\n  // Avatar size style\n  const avatarSizeStyle = (size, fontSize, radius) => ({\n    width: size,\n    height: size,\n    borderRadius: '50%',\n    [`&${componentCls}-square`]: {\n      borderRadius: radius\n    },\n    [`&${componentCls}-icon`]: {\n      fontSize,\n      [`> ${iconCls}`]: {\n        margin: 0\n      }\n    }\n  });\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      overflow: 'hidden',\n      color: avatarColor,\n      whiteSpace: 'nowrap',\n      textAlign: 'center',\n      verticalAlign: 'middle',\n      background: avatarBg,\n      border: `${unit(lineWidth)} ${lineType} transparent`,\n      '&-image': {\n        background: 'transparent'\n      },\n      [`${antCls}-image-img`]: {\n        display: 'block'\n      }\n    }), avatarSizeStyle(containerSize, textFontSize, borderRadius)), {\n      '&-lg': Object.assign({}, avatarSizeStyle(containerSizeLG, textFontSizeLG, borderRadiusLG)),\n      '&-sm': Object.assign({}, avatarSizeStyle(containerSizeSM, textFontSizeSM, borderRadiusSM)),\n      '> img': {\n        display: 'block',\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    })\n  };\n};\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    groupBorderColor,\n    groupOverlapping,\n    groupSpace\n  } = token;\n  return {\n    [`${componentCls}-group`]: {\n      display: 'inline-flex',\n      [componentCls]: {\n        borderColor: groupBorderColor\n      },\n      '> *:not(:first-child)': {\n        marginInlineStart: groupOverlapping\n      }\n    },\n    [`${componentCls}-group-popover`]: {\n      [`${componentCls} + ${componentCls}`]: {\n        marginInlineStart: groupSpace\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    fontSize,\n    fontSizeLG,\n    fontSizeXL,\n    fontSizeHeading3,\n    marginXS,\n    marginXXS,\n    colorBorderBg\n  } = token;\n  return {\n    containerSize: controlHeight,\n    containerSizeLG: controlHeightLG,\n    containerSizeSM: controlHeightSM,\n    textFontSize: Math.round((fontSizeLG + fontSizeXL) / 2),\n    textFontSizeLG: fontSizeHeading3,\n    textFontSizeSM: fontSize,\n    groupSpace: marginXXS,\n    groupOverlapping: -marginXS,\n    groupBorderColor: colorBorderBg\n  };\n};\nexport default genStyleHooks('Avatar', token => {\n  const {\n    colorTextLightSolid,\n    colorTextPlaceholder\n  } = token;\n  const avatarToken = mergeToken(token, {\n    avatarBg: colorTextPlaceholder,\n    avatarColor: colorTextLightSolid\n  });\n  return [genBaseStyle(avatarToken), genGroupStyle(avatarToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}