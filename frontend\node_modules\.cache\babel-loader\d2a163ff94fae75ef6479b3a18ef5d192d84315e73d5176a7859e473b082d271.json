{"ast": null, "code": "'use strict';\n\n/** @type {import('./shams')} */\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n  if (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') {\n    return false;\n  }\n  if (typeof Symbol.iterator === 'symbol') {\n    return true;\n  }\n\n  /** @type {{ [k in symbol]?: unknown }} */\n  var obj = {};\n  var sym = Symbol('test');\n  var symObj = Object(sym);\n  if (typeof sym === 'string') {\n    return false;\n  }\n  if (Object.prototype.toString.call(sym) !== '[object Symbol]') {\n    return false;\n  }\n  if (Object.prototype.toString.call(symObj) !== '[object Symbol]') {\n    return false;\n  }\n\n  // temp disabled per https://github.com/ljharb/object.assign/issues/17\n  // if (sym instanceof Symbol) { return false; }\n  // temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n  // if (!(symObj instanceof Symbol)) { return false; }\n\n  // if (typeof Symbol.prototype.toString !== 'function') { return false; }\n  // if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n  var symVal = 42;\n  obj[sym] = symVal;\n  for (var _ in obj) {\n    return false;\n  } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n  if (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) {\n    return false;\n  }\n  if (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) {\n    return false;\n  }\n  var syms = Object.getOwnPropertySymbols(obj);\n  if (syms.length !== 1 || syms[0] !== sym) {\n    return false;\n  }\n  if (!Object.prototype.propertyIsEnumerable.call(obj, sym)) {\n    return false;\n  }\n  if (typeof Object.getOwnPropertyDescriptor === 'function') {\n    // eslint-disable-next-line no-extra-parens\n    var descriptor = /** @type {PropertyDescriptor} */Object.getOwnPropertyDescriptor(obj, sym);\n    if (descriptor.value !== symVal || descriptor.enumerable !== true) {\n      return false;\n    }\n  }\n  return true;\n};", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}