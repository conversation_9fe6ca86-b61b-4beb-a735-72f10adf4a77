{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport DingtalkSquareFilledSvg from \"@ant-design/icons-svg/es/asn/DingtalkSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar DingtalkSquareFilled = function DingtalkSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: DingtalkSquareFilledSvg\n  }));\n};\n\n/**![dingtalk-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzM5IDQ0OS4zYy0xIDQuMi0zLjUgMTAuNC03IDE3LjhoLjFsLS40LjdjLTIwLjMgNDMuMS03My4xIDEyNy43LTczLjEgMTI3LjdzLS4xLS4yLS4zLS41bC0xNS41IDI2LjhoNzQuNUw1NzUuMSA4MTBsMzIuMy0xMjhoLTU4LjZsMjAuNC04NC43Yy0xNi41IDMuOS0zNS45IDkuNC01OSAxNi44IDAgMC0zMS4yIDE4LjItODkuOS0zNSAwIDAtMzkuNi0zNC43LTE2LjYtNDMuNCA5LjgtMy43IDQ3LjQtOC40IDc3LTEyLjMgNDAtNS40IDY0LjYtOC4yIDY0LjYtOC4yUzQyMiA1MTcgMzkyLjcgNTEyLjVjLTI5LjMtNC42LTY2LjQtNTMuMS03NC4zLTk1LjggMCAwLTEyLjItMjMuNCAyNi4zLTEyLjMgMzguNSAxMS4xIDE5Ny45IDQzLjIgMTk3LjkgNDMuMnMtMjA3LjQtNjMuMy0yMjEuMi03OC43Yy0xMy44LTE1LjQtNDAuNi04NC4yLTM3LjEtMTI2LjUgMCAwIDEuNS0xMC41IDEyLjQtNy43IDAgMCAxNTMuMyA2OS43IDI1OC4xIDEwNy45IDEwNC44IDM3LjkgMTk1LjkgNTcuMyAxODQuMiAxMDYuN3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(DingtalkSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'DingtalkSquareFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}