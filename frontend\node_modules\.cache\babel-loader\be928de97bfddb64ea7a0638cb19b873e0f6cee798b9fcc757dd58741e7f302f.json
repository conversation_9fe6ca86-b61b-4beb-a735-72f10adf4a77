{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"defaultValue\", \"value\", \"count\", \"allowHalf\", \"allowClear\", \"keyboard\", \"character\", \"characterRender\", \"disabled\", \"direction\", \"tabIndex\", \"autoFocus\", \"onHoverChange\", \"onChange\", \"onFocus\", \"onBlur\", \"onKeyDown\", \"onMouseLeave\"];\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport React from 'react';\nimport Star from \"./Star\";\nimport useRefs from \"./useRefs\";\nimport { getOffsetLeft } from \"./util\";\nfunction Rate(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-rate' : _props$prefixCls,\n    className = props.className,\n    defaultValue = props.defaultValue,\n    propValue = props.value,\n    _props$count = props.count,\n    count = _props$count === void 0 ? 5 : _props$count,\n    _props$allowHalf = props.allowHalf,\n    allowHalf = _props$allowHalf === void 0 ? false : _props$allowHalf,\n    _props$allowClear = props.allowClear,\n    allowClear = _props$allowClear === void 0 ? true : _props$allowClear,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    _props$character = props.character,\n    character = _props$character === void 0 ? '★' : _props$character,\n    characterRender = props.characterRender,\n    disabled = props.disabled,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    autoFocus = props.autoFocus,\n    onHoverChange = props.onHoverChange,\n    onChange = props.onChange,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyDown = props.onKeyDown,\n    onMouseLeave = props.onMouseLeave,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _useRefs = useRefs(),\n    _useRefs2 = _slicedToArray(_useRefs, 2),\n    getStarRef = _useRefs2[0],\n    setStarRef = _useRefs2[1];\n  var rateRef = React.useRef(null);\n\n  // ============================ Ref =============================\n  var triggerFocus = function triggerFocus() {\n    if (!disabled) {\n      var _rateRef$current;\n      (_rateRef$current = rateRef.current) === null || _rateRef$current === void 0 || _rateRef$current.focus();\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: triggerFocus,\n      blur: function blur() {\n        if (!disabled) {\n          var _rateRef$current2;\n          (_rateRef$current2 = rateRef.current) === null || _rateRef$current2 === void 0 || _rateRef$current2.blur();\n        }\n      }\n    };\n  });\n\n  // =========================== Value ============================\n  var _useMergedState = useMergedState(defaultValue || 0, {\n      value: propValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(null),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    cleanedValue = _useMergedState4[0],\n    setCleanedValue = _useMergedState4[1];\n  var getStarValue = function getStarValue(index, x) {\n    var reverse = direction === 'rtl';\n    var starValue = index + 1;\n    if (allowHalf) {\n      var starEle = getStarRef(index);\n      var leftDis = getOffsetLeft(starEle);\n      var width = starEle.clientWidth;\n      if (reverse && x - leftDis > width / 2) {\n        starValue -= 0.5;\n      } else if (!reverse && x - leftDis < width / 2) {\n        starValue -= 0.5;\n      }\n    }\n    return starValue;\n  };\n\n  // >>>>> Change\n  var changeValue = function changeValue(nextValue) {\n    setValue(nextValue);\n    onChange === null || onChange === void 0 || onChange(nextValue);\n  };\n\n  // =========================== Focus ============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var onInternalFocus = function onInternalFocus() {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 || onFocus();\n  };\n  var onInternalBlur = function onInternalBlur() {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 || onBlur();\n  };\n\n  // =========================== Hover ============================\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    hoverValue = _React$useState4[0],\n    setHoverValue = _React$useState4[1];\n  var onHover = function onHover(event, index) {\n    var nextHoverValue = getStarValue(index, event.pageX);\n    if (nextHoverValue !== cleanedValue) {\n      setHoverValue(nextHoverValue);\n      setCleanedValue(null);\n    }\n    onHoverChange === null || onHoverChange === void 0 || onHoverChange(nextHoverValue);\n  };\n  var onMouseLeaveCallback = function onMouseLeaveCallback(event) {\n    if (!disabled) {\n      setHoverValue(null);\n      setCleanedValue(null);\n      onHoverChange === null || onHoverChange === void 0 || onHoverChange(undefined);\n    }\n    if (event) {\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave(event);\n    }\n  };\n\n  // =========================== Click ============================\n  var onClick = function onClick(event, index) {\n    var newValue = getStarValue(index, event.pageX);\n    var isReset = false;\n    if (allowClear) {\n      isReset = newValue === value;\n    }\n    onMouseLeaveCallback();\n    changeValue(isReset ? 0 : newValue);\n    setCleanedValue(isReset ? newValue : null);\n  };\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var keyCode = event.keyCode;\n    var reverse = direction === 'rtl';\n    var step = allowHalf ? 0.5 : 1;\n    if (keyboard) {\n      if (keyCode === KeyCode.RIGHT && value < count && !reverse) {\n        changeValue(value + step);\n        event.preventDefault();\n      } else if (keyCode === KeyCode.LEFT && value > 0 && !reverse) {\n        changeValue(value - step);\n        event.preventDefault();\n      } else if (keyCode === KeyCode.RIGHT && value > 0 && reverse) {\n        changeValue(value - step);\n        event.preventDefault();\n      } else if (keyCode === KeyCode.LEFT && value < count && reverse) {\n        changeValue(value + step);\n        event.preventDefault();\n      }\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n  };\n\n  // =========================== Effect ===========================\n\n  React.useEffect(function () {\n    if (autoFocus && !disabled) {\n      triggerFocus();\n    }\n  }, []);\n\n  // =========================== Render ===========================\n  // >>> Star\n  var starNodes = new Array(count).fill(0).map(function (item, index) {\n    return /*#__PURE__*/React.createElement(Star, {\n      ref: setStarRef(index),\n      index: index,\n      count: count,\n      disabled: disabled,\n      prefixCls: \"\".concat(prefixCls, \"-star\"),\n      allowHalf: allowHalf,\n      value: hoverValue === null ? value : hoverValue,\n      onClick: onClick,\n      onHover: onHover,\n      key: item || index,\n      character: character,\n      characterRender: characterRender,\n      focused: focused\n    });\n  });\n  var classString = classNames(prefixCls, className, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'));\n\n  // >>> Node\n  return /*#__PURE__*/React.createElement(\"ul\", _extends({\n    className: classString,\n    onMouseLeave: onMouseLeaveCallback,\n    tabIndex: disabled ? -1 : tabIndex,\n    onFocus: disabled ? null : onInternalFocus,\n    onBlur: disabled ? null : onInternalBlur,\n    onKeyDown: disabled ? null : onInternalKeyDown,\n    ref: rateRef\n  }, pickAttrs(restProps, {\n    aria: true,\n    data: true,\n    attr: true\n  })), starNodes);\n}\nexport default /*#__PURE__*/React.forwardRef(Rate);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}