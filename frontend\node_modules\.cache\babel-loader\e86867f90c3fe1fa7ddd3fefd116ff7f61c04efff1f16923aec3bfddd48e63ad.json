{"ast": null, "code": "import * as React from 'react';\nimport useSelectIcons from '../select/useIcons';\nexport function getPlaceholder(locale, picker, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.yearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.quarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.monthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.weekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.placeholder;\n  }\n  return locale.lang.placeholder;\n}\nexport function getRangePlaceholder(locale, picker, customizePlaceholder) {\n  if (customizePlaceholder !== undefined) {\n    return customizePlaceholder;\n  }\n  if (picker === 'year' && locale.lang.yearPlaceholder) {\n    return locale.lang.rangeYearPlaceholder;\n  }\n  if (picker === 'quarter' && locale.lang.quarterPlaceholder) {\n    return locale.lang.rangeQuarterPlaceholder;\n  }\n  if (picker === 'month' && locale.lang.monthPlaceholder) {\n    return locale.lang.rangeMonthPlaceholder;\n  }\n  if (picker === 'week' && locale.lang.weekPlaceholder) {\n    return locale.lang.rangeWeekPlaceholder;\n  }\n  if (picker === 'time' && locale.timePickerLocale.placeholder) {\n    return locale.timePickerLocale.rangePlaceholder;\n  }\n  return locale.lang.rangePlaceholder;\n}\nexport function useIcons(props, prefixCls) {\n  const {\n    allowClear = true\n  } = props;\n  const {\n    clearIcon,\n    removeIcon\n  } = useSelectIcons(Object.assign(Object.assign({}, props), {\n    prefixCls,\n    componentName: 'DatePicker'\n  }));\n  const mergedAllowClear = React.useMemo(() => {\n    if (allowClear === false) {\n      return false;\n    }\n    const allowClearConfig = allowClear === true ? {} : allowClear;\n    return Object.assign({\n      clearIcon: clearIcon\n    }, allowClearConfig);\n  }, [allowClear, clearIcon]);\n  return [mergedAllowClear, removeIcon];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}