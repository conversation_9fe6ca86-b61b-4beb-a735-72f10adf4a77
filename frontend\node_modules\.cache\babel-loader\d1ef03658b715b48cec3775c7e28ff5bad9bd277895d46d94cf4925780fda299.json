{"ast": null, "code": "// Style as confirm component\nimport { unit } from '@ant-design/cssinjs';\nimport { prepareComponentToken, prepareToken } from '.';\nimport { clearFix } from '../../style';\nimport { genSubStyleComponent } from '../../theme/internal';\n// ============================= Confirm ==============================\nconst genModalConfirmStyle = token => {\n  const {\n    componentCls,\n    titleFontSize,\n    titleLineHeight,\n    modalConfirmIconSize,\n    fontSize,\n    lineHeight,\n    modalTitleHeight,\n    fontHeight,\n    confirmBodyPadding\n  } = token;\n  const confirmComponentCls = `${componentCls}-confirm`;\n  return {\n    [confirmComponentCls]: {\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      [`${token.antCls}-modal-header`]: {\n        display: 'none'\n      },\n      [`${confirmComponentCls}-body-wrapper`]: Object.assign({}, clearFix()),\n      [`&${componentCls} ${componentCls}-body`]: {\n        padding: confirmBodyPadding\n      },\n      // ====================== Body ======================\n      [`${confirmComponentCls}-body`]: {\n        display: 'flex',\n        flexWrap: 'nowrap',\n        alignItems: 'start',\n        [`> ${token.iconCls}`]: {\n          flex: 'none',\n          fontSize: modalConfirmIconSize,\n          marginInlineEnd: token.confirmIconMarginInlineEnd,\n          marginTop: token.calc(token.calc(fontHeight).sub(modalConfirmIconSize).equal()).div(2).equal()\n        },\n        [`&-has-title > ${token.iconCls}`]: {\n          marginTop: token.calc(token.calc(modalTitleHeight).sub(modalConfirmIconSize).equal()).div(2).equal()\n        }\n      },\n      [`${confirmComponentCls}-paragraph`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        flex: 'auto',\n        rowGap: token.marginXS,\n        // https://github.com/ant-design/ant-design/issues/51912\n        maxWidth: `calc(100% - ${unit(token.marginSM)})`\n      },\n      // https://github.com/ant-design/ant-design/issues/48159\n      [`${token.iconCls} + ${confirmComponentCls}-paragraph`]: {\n        maxWidth: `calc(100% - ${unit(token.calc(token.modalConfirmIconSize).add(token.marginSM).equal())})`\n      },\n      [`${confirmComponentCls}-title`]: {\n        color: token.colorTextHeading,\n        fontWeight: token.fontWeightStrong,\n        fontSize: titleFontSize,\n        lineHeight: titleLineHeight\n      },\n      [`${confirmComponentCls}-content`]: {\n        color: token.colorText,\n        fontSize,\n        lineHeight\n      },\n      // ===================== Footer =====================\n      [`${confirmComponentCls}-btns`]: {\n        textAlign: 'end',\n        marginTop: token.confirmBtnsMarginTop,\n        [`${token.antCls}-btn + ${token.antCls}-btn`]: {\n          marginBottom: 0,\n          marginInlineStart: token.marginXS\n        }\n      }\n    },\n    [`${confirmComponentCls}-error ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorError\n    },\n    [`${confirmComponentCls}-warning ${confirmComponentCls}-body > ${token.iconCls},\n        ${confirmComponentCls}-confirm ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorWarning\n    },\n    [`${confirmComponentCls}-info ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorInfo\n    },\n    [`${confirmComponentCls}-success ${confirmComponentCls}-body > ${token.iconCls}`]: {\n      color: token.colorSuccess\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Modal', 'confirm'], token => {\n  const modalToken = prepareToken(token);\n  return [genModalConfirmStyle(modalToken)];\n}, prepareComponentToken, {\n  // confirm is weak than modal since no conflict here\n  order: -1000\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}