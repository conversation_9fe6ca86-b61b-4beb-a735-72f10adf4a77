{"ast": null, "code": "import { compute as t } from \"compute-scroll-into-view\";\nconst o = t => !1 === t ? {\n  block: \"end\",\n  inline: \"nearest\"\n} : (t => t === Object(t) && 0 !== Object.keys(t).length)(t) ? t : {\n  block: \"start\",\n  inline: \"nearest\"\n};\nfunction e(e, r) {\n  if (!e.isConnected || !(t => {\n    let o = t;\n    for (; o && o.parentNode;) {\n      if (o.parentNode === document) return !0;\n      o = o.parentNode instanceof ShadowRoot ? o.parentNode.host : o.parentNode;\n    }\n    return !1;\n  })(e)) return;\n  const n = (t => {\n    const o = window.getComputedStyle(t);\n    return {\n      top: parseFloat(o.scrollMarginTop) || 0,\n      right: parseFloat(o.scrollMarginRight) || 0,\n      bottom: parseFloat(o.scrollMarginBottom) || 0,\n      left: parseFloat(o.scrollMarginLeft) || 0\n    };\n  })(e);\n  if ((t => \"object\" == typeof t && \"function\" == typeof t.behavior)(r)) return r.behavior(t(e, r));\n  const l = \"boolean\" == typeof r || null == r ? void 0 : r.behavior;\n  for (const {\n    el: a,\n    top: i,\n    left: s\n  } of t(e, o(r))) {\n    const t = i - n.top + n.bottom,\n      o = s - n.left + n.right;\n    a.scroll({\n      top: t,\n      left: o,\n      behavior: l\n    });\n  }\n}\nexport { e as default }; //# sourceMappingURL=index.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}