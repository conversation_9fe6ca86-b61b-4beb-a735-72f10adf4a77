{"ast": null, "code": "import classNames from 'classnames';\nimport React from 'react';\nvar ColorBlock = function ColorBlock(_ref) {\n  var color = _ref.color,\n    prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    style = _ref.style,\n    onClick = _ref.onClick;\n  var colorBlockCls = \"\".concat(prefixCls, \"-color-block\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(colorBlockCls, className),\n    style: style,\n    onClick: onClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(colorBlockCls, \"-inner\"),\n    style: {\n      background: color\n    }\n  }));\n};\nexport default ColorBlock;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}