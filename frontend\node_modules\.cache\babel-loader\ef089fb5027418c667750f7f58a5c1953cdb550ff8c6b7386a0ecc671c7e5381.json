{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport Cell from './Cell';\nimport DescriptionsContext from './DescriptionsContext';\nfunction renderCells(items, {\n  colon,\n  prefixCls,\n  bordered\n}, {\n  component,\n  type,\n  showLabel,\n  showContent,\n  labelStyle: rootLabelStyle,\n  contentStyle: rootContentStyle,\n  styles: rootStyles\n}) {\n  return items.map(({\n    label,\n    children,\n    prefixCls: itemPrefixCls = prefixCls,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    span = 1,\n    key,\n    styles\n  }, index) => {\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: `${type}-${key || index}`,\n        className: className,\n        style: style,\n        styles: {\n          label: Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n          content: Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n        },\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null,\n        type: type\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: `label-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), style), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label,\n      type: \"label\"\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: `content-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), style), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children,\n      type: \"content\"\n    })];\n  });\n}\nconst Row = props => {\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    prefixCls,\n    vertical,\n    row,\n    index,\n    bordered\n  } = props;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: `label-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: `content-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: `${prefixCls}-row`\n  }, renderCells(row, props, Object.assign({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}