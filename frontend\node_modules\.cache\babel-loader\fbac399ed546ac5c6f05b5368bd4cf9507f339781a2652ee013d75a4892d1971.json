{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../input/Input';\nconst Search = props => {\n  const {\n    placeholder = '',\n    value,\n    prefixCls,\n    disabled,\n    onChange,\n    handleClear\n  } = props;\n  const handleChange = React.useCallback(e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  Search.displayName = 'Search';\n}\nexport default Search;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}