{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport CheckCircleFilled from \"@ant-design/icons/es/icons/CheckCircleFilled\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport WarningFilled from \"@ant-design/icons/es/icons/WarningFilled\";\nimport classNames from 'classnames';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport noFound from './noFound';\nimport serverError from './serverError';\nimport useStyle from './style';\nimport unauthorized from './unauthorized';\nexport const IconMap = {\n  success: CheckCircleFilled,\n  error: CloseCircleFilled,\n  info: ExclamationCircleFilled,\n  warning: WarningFilled\n};\nexport const ExceptionMap = {\n  '404': noFound,\n  '500': serverError,\n  '403': unauthorized\n};\n// ExceptionImageMap keys\nconst ExceptionStatus = Object.keys(ExceptionMap);\nconst Icon = ({\n  prefixCls,\n  icon,\n  status\n}) => {\n  const className = classNames(`${prefixCls}-icon`);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Result');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  if (ExceptionStatus.includes(`${status}`)) {\n    const SVGComponent = ExceptionMap[status];\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: `${className} ${prefixCls}-image`\n    }, /*#__PURE__*/React.createElement(SVGComponent, null));\n  }\n  const iconNode = /*#__PURE__*/React.createElement(IconMap[status]);\n  if (icon === null || icon === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, icon || iconNode);\n};\nconst Extra = ({\n  prefixCls,\n  extra\n}) => {\n  if (!extra) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-extra`\n  }, extra);\n};\nconst Result = ({\n  prefixCls: customizePrefixCls,\n  className: customizeClassName,\n  rootClassName,\n  subTitle,\n  title,\n  style,\n  children,\n  status = 'info',\n  icon,\n  extra\n}) => {\n  const {\n    getPrefixCls,\n    direction,\n    result\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('result', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const className = classNames(prefixCls, `${prefixCls}-${status}`, customizeClassName, result === null || result === void 0 ? void 0 : result.className, rootClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, result === null || result === void 0 ? void 0 : result.style), style);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    style: mergedStyle\n  }, /*#__PURE__*/React.createElement(Icon, {\n    prefixCls: prefixCls,\n    status: status,\n    icon: icon\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), subTitle && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-subtitle`\n  }, subTitle), /*#__PURE__*/React.createElement(Extra, {\n    prefixCls: prefixCls,\n    extra: extra\n  }), children && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-content`\n  }, children)));\n};\nResult.PRESENTED_IMAGE_403 = ExceptionMap['403'];\nResult.PRESENTED_IMAGE_404 = ExceptionMap['404'];\nResult.PRESENTED_IMAGE_500 = ExceptionMap['500'];\nif (process.env.NODE_ENV !== 'production') {\n  Result.displayName = 'Result';\n}\nexport default Result;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}