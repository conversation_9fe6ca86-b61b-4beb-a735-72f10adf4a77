{"ast": null, "code": "\"use client\";\n\nimport BackTop from './BackTop';\nimport FloatButton from './FloatButton';\nimport FloatButtonGroup from './FloatButtonGroup';\nimport PurePanel from './PurePanel';\nFloatButton.BackTop = BackTop;\nFloatButton.Group = FloatButtonGroup;\nFloatButton._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default FloatButton;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}