{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext, useEffect, useState } from 'react';\nimport VerticalAlignTopOutlined from \"@ant-design/icons/es/icons/VerticalAlignTopOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { composeRef } from \"rc-util/es/ref\";\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport throttleByAnimationFrame from '../_util/throttleByAnimationFrame';\nimport { ConfigContext } from '../config-provider';\nimport FloatButtonGroupContext from './context';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nconst BackTop = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      type = 'default',\n      shape = 'circle',\n      visibilityHeight = 400,\n      icon = /*#__PURE__*/React.createElement(VerticalAlignTopOutlined, null),\n      target,\n      onClick,\n      duration = 450\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"type\", \"shape\", \"visibilityHeight\", \"icon\", \"target\", \"onClick\", \"duration\"]);\n  const [visible, setVisible] = useState(visibilityHeight === 0);\n  const internalRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => ({\n    nativeElement: internalRef.current\n  }));\n  const getDefaultTarget = () => {\n    var _a;\n    return ((_a = internalRef.current) === null || _a === void 0 ? void 0 : _a.ownerDocument) || window;\n  };\n  const handleScroll = throttleByAnimationFrame(e => {\n    const scrollTop = getScroll(e.target);\n    setVisible(scrollTop >= visibilityHeight);\n  });\n  useEffect(() => {\n    const getTarget = target || getDefaultTarget;\n    const container = getTarget();\n    handleScroll({\n      target: container\n    });\n    container === null || container === void 0 ? void 0 : container.addEventListener('scroll', handleScroll);\n    return () => {\n      handleScroll.cancel();\n      container === null || container === void 0 ? void 0 : container.removeEventListener('scroll', handleScroll);\n    };\n  }, [target]);\n  const scrollToTop = e => {\n    scrollTo(0, {\n      getContainer: target || getDefaultTarget,\n      duration\n    });\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const {\n    getPrefixCls\n  } = useContext(ConfigContext);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const groupShape = useContext(FloatButtonGroupContext);\n  const mergedShape = groupShape || shape;\n  const contentProps = Object.assign({\n    prefixCls,\n    icon,\n    type,\n    shape: mergedShape\n  }, restProps);\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    motionName: `${rootPrefixCls}-fade`\n  }, ({\n    className: motionClassName\n  }, setRef) => (/*#__PURE__*/React.createElement(FloatButton, Object.assign({\n    ref: composeRef(internalRef, setRef)\n  }, contentProps, {\n    onClick: scrollToTop,\n    className: classNames(className, motionClassName)\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  BackTop.displayName = 'BackTop';\n}\nexport default BackTop;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}