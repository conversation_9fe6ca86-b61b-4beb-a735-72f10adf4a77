{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\student\\\\StudentHomeworkDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { Card, Typography, Spin, Alert, Button, Descriptions, Tag, Progress, List, Divider, Space, Row, Col, Image, Tabs } from 'antd';\nimport { ArrowLeftOutlined, TrophyOutlined, ClockCircleOutlined, CheckCircleOutlined, FileTextOutlined, MessageOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { getHomework, getAnnotatedImages } from '../../utils/api';\nimport StatusBadge, { ScoreBadge } from './StatusBadge';\nimport { processImageUrls } from './utils';\nimport '../../styles/student.css';\nimport '../../styles/mobile-optimization.css';\nimport moment from 'moment';\nimport { getImageUrl } from '../../utils/imageUrl';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst StudentHomeworkDetail = ({\n  user\n}) => {\n  _s();\n  const {\n    homeworkId\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [homework, setHomework] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [annotatedImages, setAnnotatedImages] = useState([]);\n  const [activeTabKey, setActiveTabKey] = useState('info');\n\n  // 获取作业详情\n  useEffect(() => {\n    const fetchHomeworkDetail = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // 优先使用传递的作业数据\n        if (location.state && location.state.assignmentData) {\n          console.log('使用传递的作业数据:', location.state.assignmentData);\n          console.log('作业数据字段:', Object.keys(location.state.assignmentData));\n          console.log('作业数据详细内容:', JSON.stringify(location.state.assignmentData, null, 2));\n          const assignmentData = location.state.assignmentData;\n\n          // 将作业任务数据转换为作业详情格式\n          const homeworkData = {\n            id: assignmentData.homework_id,\n            title: assignmentData.title,\n            assignment_title: assignmentData.title,\n            display_title: assignmentData.title,\n            subject_name: assignmentData.subject_name,\n            submit_time: assignmentData.submitted_at,\n            // 正确的字段名\n            grading_time: assignmentData.graded_at,\n            // 正确的字段名\n            submit_status: assignmentData.submission_status,\n            grading_status: assignmentData.grading_status,\n            score: assignmentData.score,\n            accuracy: assignmentData.accuracy,\n            homework_comment: assignmentData.homework_comment || \"暂无作业点评\",\n            student_id: user.id,\n            assignment_id: assignmentData.assignment_id || assignmentData.id,\n            due_date: assignmentData.due_date,\n            created_at: assignmentData.created_at\n          };\n          console.log('转换后的作业数据:', homeworkData);\n\n          // 如果有homework_id，获取详细的作业记录（包含AI点评等）\n          if (assignmentData.homework_id) {\n            try {\n              console.log('获取详细作业记录，homework_id:', assignmentData.homework_id);\n              const detailData = await getHomework(assignmentData.homework_id);\n              console.log('获取到的详细作业记录:', detailData);\n              console.log('详细作业记录字段:', Object.keys(detailData));\n              console.log('详细作业记录完整内容:', JSON.stringify(detailData, null, 2));\n\n              // 合并数据，优先使用assignment数据，补充detail数据\n              homeworkData.homework_comment = detailData.homework_comment || assignmentData.homework_comment || \"暂无作业点评\";\n              homeworkData.corrections = detailData.corrections;\n              homeworkData.images = detailData.images;\n\n              // 使用作业详情API返回的批注图片数据，避免重复获取\n              if (detailData.annotated_images && detailData.annotated_images.length > 0) {\n                // 对批注图片进行去重处理，以防后端返回重复数据\n                const uniqueAnnotatedImages = detailData.annotated_images.filter((image, index, self) => index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number));\n                setAnnotatedImages(uniqueAnnotatedImages);\n                console.log('使用作业详情API返回的批注图片（已去重）:', uniqueAnnotatedImages);\n              } else {\n                // 如果作业详情API没有返回批注图片，则单独获取\n                try {\n                  const annotatedImagesData = await getAnnotatedImages(assignmentData.homework_id);\n                  // 对单独获取的批注图片也进行去重处理\n                  const uniqueAnnotatedImages = annotatedImagesData.filter((image, index, self) => index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number));\n                  setAnnotatedImages(uniqueAnnotatedImages);\n                  console.log('单独获取到的批注图片（已去重）:', uniqueAnnotatedImages);\n                } catch (error) {\n                  console.error('获取批注图片失败:', error);\n                }\n              }\n\n              // 处理corrections数据，提取AI点评和错误分析\n              if (detailData.corrections && detailData.corrections.length > 0) {\n                try {\n                  const correctionData = JSON.parse(detailData.corrections[0].correction_data);\n                  console.log('解析的批改数据:', correctionData);\n                  if (correctionData.questions) {\n                    homeworkData.questions = correctionData.questions;\n\n                    // 分析题目类型和得分情况\n                    const wrongQuestions = correctionData.questions.filter(q => !q.is_correct);\n                    const correctQuestions = correctionData.questions.filter(q => q.is_correct);\n\n                    // 计算详细得分信息\n                    const correctNumbers = correctQuestions.map(q => q.question_number).join('、');\n                    const wrongNumbers = wrongQuestions.map(q => q.question_number).join('、');\n\n                    // 计算主观题和客观题得分\n                    const totalQuestions = correctionData.questions.length;\n                    const correctCount = correctQuestions.length;\n\n                    // 使用实际总分和正确率来计算，而不是依赖题目数量\n                    const totalScore = homeworkData.score || 0;\n                    const accuracy = homeworkData.accuracy || 0;\n\n                    // 根据题目类型区分主观题和客观题\n                    // 主观题：凡是需要写文字的题目（填空题、简答题、作文题、解答题、问答题、计算题等）\n                    // 客观题：只有选择题和判断题\n                    const objectiveQuestions = correctionData.questions.filter(q => q.question_type === '选择题' || q.question_type === '判断题' || q.question_type === '单选题' || q.question_type === '多选题' || q.question_type === '是非题' || q.question_type === '对错题');\n                    const subjectiveQuestions = correctionData.questions.filter(q => !objectiveQuestions.includes(q));\n                    const subjectiveCorrect = subjectiveQuestions.filter(q => q.is_correct).length;\n                    const objectiveCorrect = objectiveQuestions.filter(q => q.is_correct).length;\n                    console.log('题型分类详情:');\n                    console.log('- 总题数:', totalQuestions);\n                    console.log('- 主观题数量:', subjectiveQuestions.length);\n                    console.log('- 客观题数量:', objectiveQuestions.length);\n                    console.log('- 主观题正确数:', subjectiveCorrect);\n                    console.log('- 客观题正确数:', objectiveCorrect);\n                    console.log('- 总分:', totalScore);\n                    console.log('- 正确率:', accuracy);\n\n                    // 根据题型比例分配得分\n                    let subjectiveScore, objectiveScore;\n                    if (objectiveQuestions.length === 0) {\n                      // 如果没有客观题，所有分数都是主观题得分\n                      subjectiveScore = totalScore;\n                      objectiveScore = 0;\n                    } else if (subjectiveQuestions.length === 0) {\n                      // 如果没有主观题，所有分数都是客观题得分\n                      subjectiveScore = 0;\n                      objectiveScore = totalScore;\n                    } else {\n                      // 如果两种题型都有，按正确题目比例分配\n                      const subjectiveRatio = subjectiveCorrect / (subjectiveCorrect + objectiveCorrect);\n                      const objectiveRatio = objectiveCorrect / (subjectiveCorrect + objectiveCorrect);\n                      subjectiveScore = Math.round(totalScore * subjectiveRatio);\n                      objectiveScore = Math.round(totalScore * objectiveRatio);\n                    }\n                    console.log('- 主观题得分:', subjectiveScore);\n                    console.log('- 客观题得分:', objectiveScore);\n\n                    // 保存详细信息\n                    homeworkData.correctNumbers = correctNumbers || '无';\n                    homeworkData.wrongNumbers = wrongNumbers || '无';\n                    homeworkData.subjectiveScore = Math.round(subjectiveScore);\n                    homeworkData.objectiveScore = Math.round(objectiveScore);\n                    homeworkData.subjectiveCount = subjectiveQuestions.length;\n                    homeworkData.objectiveCount = objectiveQuestions.length;\n                    let aiComment = `本次作业共${correctionData.questions.length}题，答对${correctQuestions.length}题，答错${wrongQuestions.length}题。\\n\\n`;\n                    if (wrongQuestions.length > 0) {\n                      aiComment += `错题分析：\\n`;\n                      wrongQuestions.forEach((q, index) => {\n                        aiComment += `${index + 1}. 第${q.question_number}题：${q.analysis || '需要加强练习'}\\n`;\n                        if (q.reinforcement) {\n                          aiComment += `   ${q.reinforcement}\\n`;\n                        }\n                      });\n                    } else {\n                      aiComment += `恭喜你全部答对！继续保持这种学习状态。`;\n                    }\n                    homeworkData.ai_comment = aiComment;\n\n                    // 生成错误分析\n                    if (wrongQuestions.length > 0) {\n                      let errorAnalysis = `本次作业错误题目分析：\\n\\n`;\n                      wrongQuestions.forEach((q, index) => {\n                        errorAnalysis += `错题${index + 1}：第${q.question_number}题\\n`;\n                        errorAnalysis += `题目：${q.question_content}\\n`;\n                        errorAnalysis += `你的答案：${q.student_answer}\\n`;\n                        errorAnalysis += `正确答案：${q.correct_answer}\\n`;\n                        errorAnalysis += `错误分析：${q.analysis || '答案不正确，需要重新理解题意'}\\n`;\n                        if (q.reinforcement) {\n                          errorAnalysis += `改进建议：${q.reinforcement}\\n`;\n                        }\n                        errorAnalysis += `\\n`;\n                      });\n                      homeworkData.error_analysis = errorAnalysis;\n                    }\n                  }\n                } catch (parseError) {\n                  console.error('解析批改数据失败:', parseError);\n                }\n              }\n              console.log('合并后的完整作业数据:', homeworkData);\n            } catch (detailError) {\n              console.error('获取详细作业记录失败:', detailError);\n              // 即使获取详细记录失败，也继续显示基本信息\n            }\n          }\n          console.log('转换后的作业数据:', homeworkData);\n          setHomework(homeworkData);\n          setLoading(false);\n          return;\n        }\n\n        // 如果没有传递数据，则通过API获取\n        if (homeworkId && homeworkId !== 'preview') {\n          const data = await getHomework(homeworkId);\n          console.log('获取到的作业详情:', data);\n          console.log('作业详情字段:', Object.keys(data));\n          console.log('作业标题:', data.title);\n          console.log('作业任务标题:', data.assignment_title);\n          console.log('当前用户ID:', user.id);\n          console.log('作业学生ID:', data.student_id);\n\n          // 如果有传递的assignment标题，优先使用\n          if (location.state && location.state.assignmentTitle) {\n            data.display_title = location.state.assignmentTitle;\n            console.log('使用传递的作业任务标题:', data.display_title);\n          } else if (data.assignment_title) {\n            data.display_title = data.assignment_title;\n            console.log('使用API返回的作业任务标题:', data.display_title);\n          } else {\n            data.display_title = data.title;\n            console.log('使用作业记录标题:', data.display_title);\n          }\n          setHomework(data);\n\n          // 使用作业详情API返回的批注图片数据，避免重复获取\n          if (data.annotated_images && data.annotated_images.length > 0) {\n            // 对批注图片进行去重处理，以防后端返回重复数据\n            const uniqueAnnotatedImages = data.annotated_images.filter((image, index, self) => index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number));\n            setAnnotatedImages(uniqueAnnotatedImages);\n            console.log('使用作业详情API返回的批注图片（已去重）:', uniqueAnnotatedImages);\n          } else {\n            // 如果作业详情API没有返回批注图片，则单独获取\n            try {\n              const annotatedImagesData = await getAnnotatedImages(homeworkId);\n              // 对单独获取的批注图片也进行去重处理\n              const uniqueAnnotatedImages = annotatedImagesData.filter((image, index, self) => index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number));\n              setAnnotatedImages(uniqueAnnotatedImages);\n              console.log('单独获取到的批注图片（已去重）:', uniqueAnnotatedImages);\n            } catch (error) {\n              console.error('获取批注图片失败:', error);\n            }\n          }\n        } else {\n          setError('无效的作业ID');\n        }\n      } catch (error) {\n        console.error('获取作业详情失败:', error);\n        setError(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchHomeworkDetail();\n  }, [homeworkId, location.state]);\n\n  // 渲染作业图片\n  const renderImages = () => {\n    if (!homework || !homework.images || homework.images.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '48px',\n            marginBottom: '16px'\n          },\n          children: \"\\uD83D\\uDCF7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u6682\\u65E0\\u4F5C\\u4E1A\\u56FE\\u7247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 对原始图片进行去重处理，以防后端返回重复数据\n    const uniqueImages = homework.images.filter((image, index, self) => index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number));\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '16px'\n      },\n      children: uniqueImages.map((image, index) => {\n        // 使用统一的图片URL处理函数\n        const imagePath = getImageUrl(image.image_path);\n        return /*#__PURE__*/_jsxDEV(Card, {\n          hoverable: true,\n          style: {\n            borderRadius: '12px'\n          },\n          bodyStyle: {\n            padding: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: imagePath,\n            alt: `作业图片 ${index + 1}`,\n            style: {\n              width: '100%',\n              borderRadius: '8px',\n              cursor: 'pointer'\n            },\n            fallback: \"/broken-image.png\",\n            preview: {\n              mask: '点击查看大图',\n              maskClassName: 'student-image-mask'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginTop: '8px',\n              fontSize: '14px',\n              color: '#666666'\n            },\n            children: [\"\\u7B2C \", image.page_number, \" \\u9875\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染批注图片\n  const renderAnnotatedImages = () => {\n    if (!annotatedImages || annotatedImages.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '48px',\n            marginBottom: '16px'\n          },\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u6682\\u65E0\\u6279\\u6CE8\\u56FE\\u7247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '12px',\n            color: '#999'\n          },\n          children: \"\\u6279\\u6CE8\\u56FE\\u7247\\u7531\\u8001\\u5E08\\u751F\\u6210\\uFF0C\\u5305\\u542B\\u8BE6\\u7EC6\\u7684\\u6279\\u6539\\u6807\\u8BB0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n        gap: '16px'\n      },\n      children: annotatedImages.map((image, index) => {\n        // 使用统一的图片URL处理函数\n        const imagePath = getImageUrl(image.image_path);\n        return /*#__PURE__*/_jsxDEV(Card, {\n          hoverable: true,\n          style: {\n            borderRadius: '12px'\n          },\n          bodyStyle: {\n            padding: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Image, {\n            src: imagePath,\n            alt: `批注图片 ${index + 1}`,\n            style: {\n              width: '100%',\n              borderRadius: '8px',\n              cursor: 'pointer'\n            },\n            fallback: \"/broken-image.png\",\n            preview: {\n              mask: '点击查看大图',\n              maskClassName: 'student-image-mask'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginTop: '8px',\n              fontSize: '14px',\n              color: '#666666'\n            },\n            children: [\"\\u7B2C \", image.page_number, \" \\u9875 - \\u6279\\u6CE8\\u7248\\u672C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染加载状态\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-interface\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-page\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '100px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '16px',\n              color: '#666666'\n            },\n            children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u4F5C\\u4E1A\\u8BE6\\u60C5...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 错误状态\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-interface\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-page\",\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\uD83D\\uDE14 \\u52A0\\u8F7D\\u5931\\u8D25\",\n          description: error,\n          type: \"error\",\n          showIcon: true,\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: () => window.location.reload(),\n            children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this),\n          style: {\n            borderRadius: '12px',\n            marginBottom: '24px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this);\n  }\n  if (!homework) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-interface\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-page\",\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\uD83D\\uDE14 \\u4F5C\\u4E1A\\u4E0D\\u5B58\\u5728\",\n          description: \"\\u627E\\u4E0D\\u5230\\u6307\\u5B9A\\u7684\\u4F5C\\u4E1A\\uFF0C\\u53EF\\u80FD\\u5DF2\\u88AB\\u5220\\u9664\\u6216\\u60A8\\u6CA1\\u6709\\u8BBF\\u95EE\\u6743\\u9650\",\n          type: \"warning\",\n          showIcon: true,\n          action: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: () => navigate('/homework/review'),\n            children: \"\\u8FD4\\u56DE\\u4F5C\\u4E1A\\u70B9\\u8BC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this),\n          style: {\n            borderRadius: '12px',\n            marginBottom: '24px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"student-interface\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/homework/review'),\n          style: {\n            marginBottom: '16px'\n          },\n          children: \"\\u8FD4\\u56DE\\u4F5C\\u4E1A\\u70B9\\u8BC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"student-page-title\",\n          children: \"\\uD83D\\uDCDD \\u4F5C\\u4E1A\\u8BE6\\u60C5\\u4E0E\\u70B9\\u8BC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"student-page-description\",\n          children: \"\\u67E5\\u770B\\u4F5C\\u4E1A\\u7684\\u8BE6\\u7EC6\\u4FE1\\u606F\\u3001\\u6279\\u6539\\u7ED3\\u679C\\u548C\\u8001\\u5E08\\u70B9\\u8BC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), \"\\u57FA\\u672C\\u4FE1\\u606F\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this),\n        style: {\n          borderRadius: '12px',\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 18,\n            children: /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4F5C\\u4E1A\\u6807\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  fontSize: '18px'\n                },\n                children: homework.display_title || homework.assignment_title || homework.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '32px',\n                  fontWeight: 700,\n                  color: '#4A90E2'\n                },\n                children: homework.score || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#666666'\n                },\n                children: \"\\u603B\\u5206\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n          column: {\n            xs: 1,\n            sm: 2,\n            md: 3\n          },\n          size: \"small\",\n          style: {\n            marginTop: '16px'\n          },\n          labelStyle: {\n            minWidth: '70px',\n            fontWeight: 500\n          },\n          contentStyle: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u79D1\\u76EE\",\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: homework.subject_name || '未设置'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6B63\\u786E\\u7387\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                color: homework.accuracy >= 0.8 ? '#52c41a' : homework.accuracy >= 0.6 ? '#faad14' : '#ff4d4f',\n                fontSize: '16px'\n              },\n              children: homework.accuracy ? `${Math.round(homework.accuracy * 100)}%` : '0%'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u603B\\u9898\\u6570\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                fontSize: '16px'\n              },\n              children: [homework.questions ? homework.questions.length : 0, \" \\u9898\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u63D0\\u4EA4\\u65F6\\u95F4\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), homework.submit_time ? moment(homework.submit_time).utcOffset(480).format('YYYY-MM-DD HH:mm:ss') : '未提交']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6279\\u6539\\u65F6\\u95F4\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this), homework.grading_time ? moment(homework.grading_time).utcOffset(480).format('YYYY-MM-DD HH:mm:ss') : '未批改']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6210\\u7EE9\\u7B49\\u7EA7\",\n            children: /*#__PURE__*/_jsxDEV(ScoreBadge, {\n              score: homework.score,\n              accuracy: homework.accuracy,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4E3B\\u89C2\\u9898\\u5F97\\u5206\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                color: '#1890ff'\n              },\n              children: [homework.subjectiveScore !== undefined ? `${homework.subjectiveScore}分` : `${homework.score || 0}分`, homework.subjectiveCount !== undefined && /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  marginLeft: '4px',\n                  fontSize: '12px'\n                },\n                children: [\"(\", homework.subjectiveCount, \"\\u9898)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5BA2\\u89C2\\u9898\\u5F97\\u5206\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              style: {\n                color: '#1890ff'\n              },\n              children: [homework.objectiveScore !== undefined ? `${homework.objectiveScore}分` : '0分', homework.objectiveCount !== undefined && /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  marginLeft: '4px',\n                  fontSize: '12px'\n                },\n                children: [\"(\", homework.objectiveCount, \"\\u9898)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6279\\u6539\\u72B6\\u6001\",\n            children: /*#__PURE__*/_jsxDEV(StatusBadge, {\n              status: homework.grading_status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '20px',\n            padding: '16px',\n            background: '#fafafa',\n            borderRadius: '8px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [0, 12],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  style: {\n                    color: '#52c41a',\n                    marginRight: '8px'\n                  },\n                  children: \"\\u2705 \\u6B63\\u786E\\u9898\\u53F7\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#52c41a',\n                    fontWeight: 500,\n                    wordBreak: 'break-all',\n                    lineHeight: 1.6\n                  },\n                  children: homework.correctNumbers || '无'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 24,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  style: {\n                    color: '#ff4d4f',\n                    marginRight: '8px'\n                  },\n                  children: \"\\u274C \\u9519\\u8BEF\\u9898\\u53F7\\uFF1A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    color: '#ff4d4f',\n                    fontWeight: 500,\n                    wordBreak: 'break-all',\n                    lineHeight: 1.6\n                  },\n                  children: homework.wrongNumbers || '无'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), homework.grading_status === '已批改' && homework.error_analysis && /*#__PURE__*/_jsxDEV(Card, {\n        title: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 17\n          }, this), \"\\u672C\\u6B21\\u4F5C\\u4E1A\\u9519\\u8BEF\\u5206\\u6790\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 15\n        }, this),\n        style: {\n          borderRadius: '12px',\n          marginTop: '24px',\n          border: '2px solid #faad14'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'rgba(250, 173, 20, 0.05)',\n            padding: '12px',\n            /* 移动端减少padding */\n            borderRadius: '8px',\n            border: '1px solid rgba(250, 173, 20, 0.2)'\n          },\n          children: /*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              fontSize: '15px',\n              /* 移动端稍微减小字体 */\n              lineHeight: 1.6,\n              /* 减少行高，节省空间 */\n              marginBottom: 0,\n              whiteSpace: 'pre-line'\n            },\n            children: homework.error_analysis\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px',\n            padding: '12px',\n            background: '#fff7e6',\n            borderRadius: '6px',\n            fontSize: '12px',\n            color: '#d48806'\n          },\n          children: [\"\\uD83D\\uDCCA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u9519\\u8BEF\\u5206\\u6790\\u8BF4\\u660E\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 18\n          }, this), \"\\u7CFB\\u7EDF\\u5206\\u6790\\u4E86\\u4F60\\u5728\\u672C\\u6B21\\u4F5C\\u4E1A\\u4E2D\\u7684\\u9519\\u8BEF\\u7C7B\\u578B\\u548C\\u539F\\u56E0\\uFF0C\\u5E2E\\u52A9\\u4F60\\u9488\\u5BF9\\u6027\\u5730\\u6539\\u8FDB\\u5B66\\u4E60\\u65B9\\u6CD5\\u3002\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 11\n      }, this), homework.grading_status === '已批改' && /*#__PURE__*/_jsxDEV(Card, {\n        title: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 17\n          }, this), \"AI\\u4F5C\\u4E1A\\u70B9\\u8BC4\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 15\n        }, this),\n        style: {\n          borderRadius: '12px',\n          marginTop: '24px',\n          border: '2px solid #52c41a'\n        },\n        children: homework.homework_comment && homework.homework_comment !== \"暂无作业点评\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n            style: {\n              fontSize: '15px',\n              /* 移动端稍微减小字体 */\n              lineHeight: 1.6,\n              /* 减少行高，节省空间 */\n              background: 'rgba(82, 196, 26, 0.05)',\n              padding: '12px',\n              /* 移动端减少padding */\n              borderRadius: '8px',\n              border: '1px solid rgba(82, 196, 26, 0.2)',\n              whiteSpace: 'pre-line'\n            },\n            children: homework.homework_comment\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '16px',\n              padding: '12px',\n              background: '#f6f8fa',\n              borderRadius: '6px',\n              fontSize: '12px',\n              color: '#666666'\n            },\n            children: [\"\\uD83E\\uDD16 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"AI\\u667A\\u80FD\\u5206\\u6790\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 22\n            }, this), \"\\u57FA\\u4E8E\\u4F60\\u7684\\u7B54\\u9898\\u60C5\\u51B5\\uFF0CAI\\u4E3A\\u4F60\\u751F\\u6210\\u4E86\\u4E2A\\u6027\\u5316\\u7684\\u5B66\\u4E60\\u5EFA\\u8BAE\\u548C\\u6539\\u8FDB\\u65B9\\u5411\\u3002\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '40px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              marginBottom: '16px'\n            },\n            children: \"\\uD83E\\uDD16\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"AI\\u4F5C\\u4E1A\\u70B9\\u8BC4\\u751F\\u6210\\u4E2D\\uFF0C\\u8BF7\\u7A0D\\u540E\\u67E5\\u770B...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 11\n      }, this), homework.grading_status === '已批改' && homework.accuracy < 0.8 && /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\uD83D\\uDCDA \\u5B66\\u4E60\\u5EFA\\u8BAE\",\n        style: {\n          borderRadius: '12px',\n          marginTop: '24px',\n          border: '2px solid #faad14'\n        },\n        children: /*#__PURE__*/_jsxDEV(List, {\n          size: \"small\",\n          dataSource: ['仔细复习错题，理解错误原因', '加强相关知识点的练习', '主动向老师或同学请教疑难问题', '制定针对性的学习计划', '定期回顾和总结学习内容'],\n          renderItem: (item, index) => /*#__PURE__*/_jsxDEV(List.Item, {\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: 'inline-block',\n                  width: '20px',\n                  height: '20px',\n                  background: '#faad14',\n                  color: 'white',\n                  borderRadius: '50%',\n                  textAlign: 'center',\n                  lineHeight: '20px',\n                  fontSize: '12px',\n                  marginRight: '8px'\n                },\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 21\n              }, this), item]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        style: {\n          borderRadius: '12px',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          activeKey: activeTabKey,\n          onChange: setActiveTabKey,\n          size: \"large\",\n          tabBarStyle: {\n            marginBottom: '24px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\uD83D\\uDCCB \\u57FA\\u672C\\u4FE1\\u606F\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '0 8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '14px'\n                },\n                children: \"\\u67E5\\u770B\\u4F5C\\u4E1A\\u7684\\u57FA\\u672C\\u4FE1\\u606F\\u548C\\u6210\\u7EE9\\u8BE6\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 15\n            }, this)\n          }, \"info\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\uD83D\\uDCF7 \\u4F5C\\u4E1A\\u56FE\\u7247\",\n            children: renderImages()\n          }, \"images\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\uD83D\\uDCDD \\u6279\\u6CE8\\u56FE\\u7247\",\n            children: renderAnnotatedImages()\n          }, \"annotations\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: '40px',\n          padding: '20px',\n          background: '#f6f8fa',\n          borderRadius: '12px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            onClick: () => navigate('/homework/review'),\n            children: \"\\u8FD4\\u56DE\\u4F5C\\u4E1A\\u70B9\\u8BC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            size: \"large\",\n            onClick: () => navigate('/homework'),\n            children: \"\\u67E5\\u770B\\u6240\\u6709\\u4F5C\\u4E1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            size: \"large\",\n            onClick: () => navigate('/training'),\n            children: \"\\u9519\\u9898\\u8BAD\\u7EC3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 827,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentHomeworkDetail, \"uPWRjsPdsdBdofroSX+MStpdJBk=\", false, function () {\n  return [useParams, useNavigate, useLocation];\n});\n_c = StudentHomeworkDetail;\nexport default StudentHomeworkDetail;\nvar _c;\n$RefreshReg$(_c, \"StudentHomeworkDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "Card", "Typography", "Spin", "<PERSON><PERSON>", "<PERSON><PERSON>", "Descriptions", "Tag", "Progress", "List", "Divider", "Space", "Row", "Col", "Image", "Tabs", "ArrowLeftOutlined", "TrophyOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "FileTextOutlined", "MessageOutlined", "ExclamationCircleOutlined", "getHomework", "getAnnotatedImages", "StatusBadge", "ScoreBadge", "processImageUrls", "moment", "getImageUrl", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TabPane", "StudentHomeworkDetail", "user", "_s", "homeworkId", "navigate", "location", "homework", "setHomework", "loading", "setLoading", "error", "setError", "annotatedImages", "setAnnotatedImages", "activeTabKey", "setActiveTabKey", "fetchHomeworkDetail", "state", "assignmentData", "console", "log", "Object", "keys", "JSON", "stringify", "homeworkData", "id", "homework_id", "title", "assignment_title", "display_title", "subject_name", "submit_time", "submitted_at", "grading_time", "graded_at", "submit_status", "submission_status", "grading_status", "score", "accuracy", "homework_comment", "student_id", "assignment_id", "due_date", "created_at", "detailData", "corrections", "images", "annotated_images", "length", "uniqueAnnotatedImages", "filter", "image", "index", "self", "findIndex", "i", "page_number", "annotatedImagesData", "correctionData", "parse", "correction_data", "questions", "wrongQuestions", "q", "is_correct", "correctQuestions", "correctNumbers", "map", "question_number", "join", "wrongNumbers", "totalQuestions", "correctCount", "totalScore", "objectiveQuestions", "question_type", "subjectiveQuestions", "includes", "subjectiveCorrect", "objectiveCorrect", "subjectiveScore", "objectiveScore", "subjectiveRatio", "objectiveRatio", "Math", "round", "subjectiveCount", "objectiveCount", "aiComment", "for<PERSON>ach", "analysis", "reinforcement", "ai_comment", "errorAnalysis", "question_content", "student_answer", "correct_answer", "error_analysis", "parseError", "detailError", "data", "assignmentTitle", "message", "renderImages", "style", "textAlign", "padding", "children", "fontSize", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "uniqueImages", "display", "gridTemplateColumns", "gap", "imagePath", "image_path", "hoverable", "borderRadius", "bodyStyle", "src", "alt", "width", "cursor", "fallback", "preview", "mask", "maskClassName", "marginTop", "color", "renderAnnotatedImages", "className", "size", "description", "showIcon", "action", "onClick", "window", "reload", "icon", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "strong", "fontWeight", "column", "sm", "labelStyle", "min<PERSON><PERSON><PERSON>", "contentStyle", "flex", "utcOffset", "format", "undefined", "marginLeft", "status", "background", "span", "marginRight", "wordBreak", "lineHeight", "border", "whiteSpace", "dataSource", "renderItem", "item", "height", "active<PERSON><PERSON>", "onChange", "tabBarStyle", "tab", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/student/StudentHomeworkDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Card, Typography, Spin, Alert, Button, Descriptions,\n  Tag, Progress, List, Divider, Space, Row, Col, Image, Tabs\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  TrophyOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  FileTextOutlined,\n  MessageOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { getHomework, getAnnotatedImages } from '../../utils/api';\nimport StatusBadge, { ScoreBadge } from './StatusBadge';\nimport { processImageUrls } from './utils';\nimport '../../styles/student.css';\nimport '../../styles/mobile-optimization.css';\nimport moment from 'moment';\nimport { getImageUrl } from '../../utils/imageUrl';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TabPane } = Tabs;\n\nconst StudentHomeworkDetail = ({ user }) => {\n  const { homeworkId } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [homework, setHomework] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [annotatedImages, setAnnotatedImages] = useState([]);\n  const [activeTabKey, setActiveTabKey] = useState('info');\n\n  // 获取作业详情\n  useEffect(() => {\n    const fetchHomeworkDetail = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        // 优先使用传递的作业数据\n        if (location.state && location.state.assignmentData) {\n          console.log('使用传递的作业数据:', location.state.assignmentData);\n          console.log('作业数据字段:', Object.keys(location.state.assignmentData));\n          console.log('作业数据详细内容:', JSON.stringify(location.state.assignmentData, null, 2));\n          const assignmentData = location.state.assignmentData;\n\n          // 将作业任务数据转换为作业详情格式\n          const homeworkData = {\n            id: assignmentData.homework_id,\n            title: assignmentData.title,\n            assignment_title: assignmentData.title,\n            display_title: assignmentData.title,\n            subject_name: assignmentData.subject_name,\n            submit_time: assignmentData.submitted_at,  // 正确的字段名\n            grading_time: assignmentData.graded_at,    // 正确的字段名\n            submit_status: assignmentData.submission_status,\n            grading_status: assignmentData.grading_status,\n            score: assignmentData.score,\n            accuracy: assignmentData.accuracy,\n            homework_comment: assignmentData.homework_comment || \"暂无作业点评\",\n            student_id: user.id,\n            assignment_id: assignmentData.assignment_id || assignmentData.id,\n            due_date: assignmentData.due_date,\n            created_at: assignmentData.created_at\n          };\n\n          console.log('转换后的作业数据:', homeworkData);\n\n          // 如果有homework_id，获取详细的作业记录（包含AI点评等）\n          if (assignmentData.homework_id) {\n            try {\n              console.log('获取详细作业记录，homework_id:', assignmentData.homework_id);\n              const detailData = await getHomework(assignmentData.homework_id);\n              console.log('获取到的详细作业记录:', detailData);\n              console.log('详细作业记录字段:', Object.keys(detailData));\n              console.log('详细作业记录完整内容:', JSON.stringify(detailData, null, 2));\n\n              // 合并数据，优先使用assignment数据，补充detail数据\n              homeworkData.homework_comment = detailData.homework_comment || assignmentData.homework_comment || \"暂无作业点评\";\n              homeworkData.corrections = detailData.corrections;\n              homeworkData.images = detailData.images;\n\n              // 使用作业详情API返回的批注图片数据，避免重复获取\n              if (detailData.annotated_images && detailData.annotated_images.length > 0) {\n                // 对批注图片进行去重处理，以防后端返回重复数据\n                const uniqueAnnotatedImages = detailData.annotated_images.filter((image, index, self) =>\n                  index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number)\n                );\n                setAnnotatedImages(uniqueAnnotatedImages);\n                console.log('使用作业详情API返回的批注图片（已去重）:', uniqueAnnotatedImages);\n              } else {\n                // 如果作业详情API没有返回批注图片，则单独获取\n                try {\n                  const annotatedImagesData = await getAnnotatedImages(assignmentData.homework_id);\n                  // 对单独获取的批注图片也进行去重处理\n                  const uniqueAnnotatedImages = annotatedImagesData.filter((image, index, self) =>\n                    index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number)\n                  );\n                  setAnnotatedImages(uniqueAnnotatedImages);\n                  console.log('单独获取到的批注图片（已去重）:', uniqueAnnotatedImages);\n                } catch (error) {\n                  console.error('获取批注图片失败:', error);\n                }\n              }\n\n              // 处理corrections数据，提取AI点评和错误分析\n              if (detailData.corrections && detailData.corrections.length > 0) {\n                try {\n                  const correctionData = JSON.parse(detailData.corrections[0].correction_data);\n                  console.log('解析的批改数据:', correctionData);\n\n                  if (correctionData.questions) {\n                    homeworkData.questions = correctionData.questions;\n\n                    // 分析题目类型和得分情况\n                    const wrongQuestions = correctionData.questions.filter(q => !q.is_correct);\n                    const correctQuestions = correctionData.questions.filter(q => q.is_correct);\n\n                    // 计算详细得分信息\n                    const correctNumbers = correctQuestions.map(q => q.question_number).join('、');\n                    const wrongNumbers = wrongQuestions.map(q => q.question_number).join('、');\n\n                    // 计算主观题和客观题得分\n                    const totalQuestions = correctionData.questions.length;\n                    const correctCount = correctQuestions.length;\n\n                    // 使用实际总分和正确率来计算，而不是依赖题目数量\n                    const totalScore = homeworkData.score || 0;\n                    const accuracy = homeworkData.accuracy || 0;\n\n                    // 根据题目类型区分主观题和客观题\n                    // 主观题：凡是需要写文字的题目（填空题、简答题、作文题、解答题、问答题、计算题等）\n                    // 客观题：只有选择题和判断题\n                    const objectiveQuestions = correctionData.questions.filter(q =>\n                      q.question_type === '选择题' ||\n                      q.question_type === '判断题' ||\n                      q.question_type === '单选题' ||\n                      q.question_type === '多选题' ||\n                      q.question_type === '是非题' ||\n                      q.question_type === '对错题'\n                    );\n                    const subjectiveQuestions = correctionData.questions.filter(q =>\n                      !objectiveQuestions.includes(q)\n                    );\n\n                    const subjectiveCorrect = subjectiveQuestions.filter(q => q.is_correct).length;\n                    const objectiveCorrect = objectiveQuestions.filter(q => q.is_correct).length;\n\n                    console.log('题型分类详情:');\n                    console.log('- 总题数:', totalQuestions);\n                    console.log('- 主观题数量:', subjectiveQuestions.length);\n                    console.log('- 客观题数量:', objectiveQuestions.length);\n                    console.log('- 主观题正确数:', subjectiveCorrect);\n                    console.log('- 客观题正确数:', objectiveCorrect);\n                    console.log('- 总分:', totalScore);\n                    console.log('- 正确率:', accuracy);\n\n                    // 根据题型比例分配得分\n                    let subjectiveScore, objectiveScore;\n\n                    if (objectiveQuestions.length === 0) {\n                      // 如果没有客观题，所有分数都是主观题得分\n                      subjectiveScore = totalScore;\n                      objectiveScore = 0;\n                    } else if (subjectiveQuestions.length === 0) {\n                      // 如果没有主观题，所有分数都是客观题得分\n                      subjectiveScore = 0;\n                      objectiveScore = totalScore;\n                    } else {\n                      // 如果两种题型都有，按正确题目比例分配\n                      const subjectiveRatio = subjectiveCorrect / (subjectiveCorrect + objectiveCorrect);\n                      const objectiveRatio = objectiveCorrect / (subjectiveCorrect + objectiveCorrect);\n                      subjectiveScore = Math.round(totalScore * subjectiveRatio);\n                      objectiveScore = Math.round(totalScore * objectiveRatio);\n                    }\n\n                    console.log('- 主观题得分:', subjectiveScore);\n                    console.log('- 客观题得分:', objectiveScore);\n\n                    // 保存详细信息\n                    homeworkData.correctNumbers = correctNumbers || '无';\n                    homeworkData.wrongNumbers = wrongNumbers || '无';\n                    homeworkData.subjectiveScore = Math.round(subjectiveScore);\n                    homeworkData.objectiveScore = Math.round(objectiveScore);\n                    homeworkData.subjectiveCount = subjectiveQuestions.length;\n                    homeworkData.objectiveCount = objectiveQuestions.length;\n\n                    let aiComment = `本次作业共${correctionData.questions.length}题，答对${correctQuestions.length}题，答错${wrongQuestions.length}题。\\n\\n`;\n\n                    if (wrongQuestions.length > 0) {\n                      aiComment += `错题分析：\\n`;\n                      wrongQuestions.forEach((q, index) => {\n                        aiComment += `${index + 1}. 第${q.question_number}题：${q.analysis || '需要加强练习'}\\n`;\n                        if (q.reinforcement) {\n                          aiComment += `   ${q.reinforcement}\\n`;\n                        }\n                      });\n                    } else {\n                      aiComment += `恭喜你全部答对！继续保持这种学习状态。`;\n                    }\n\n                    homeworkData.ai_comment = aiComment;\n\n                    // 生成错误分析\n                    if (wrongQuestions.length > 0) {\n                      let errorAnalysis = `本次作业错误题目分析：\\n\\n`;\n                      wrongQuestions.forEach((q, index) => {\n                        errorAnalysis += `错题${index + 1}：第${q.question_number}题\\n`;\n                        errorAnalysis += `题目：${q.question_content}\\n`;\n                        errorAnalysis += `你的答案：${q.student_answer}\\n`;\n                        errorAnalysis += `正确答案：${q.correct_answer}\\n`;\n                        errorAnalysis += `错误分析：${q.analysis || '答案不正确，需要重新理解题意'}\\n`;\n                        if (q.reinforcement) {\n                          errorAnalysis += `改进建议：${q.reinforcement}\\n`;\n                        }\n                        errorAnalysis += `\\n`;\n                      });\n                      homeworkData.error_analysis = errorAnalysis;\n                    }\n                  }\n                } catch (parseError) {\n                  console.error('解析批改数据失败:', parseError);\n                }\n              }\n\n              console.log('合并后的完整作业数据:', homeworkData);\n            } catch (detailError) {\n              console.error('获取详细作业记录失败:', detailError);\n              // 即使获取详细记录失败，也继续显示基本信息\n            }\n          }\n\n          console.log('转换后的作业数据:', homeworkData);\n          setHomework(homeworkData);\n          setLoading(false);\n          return;\n        }\n\n        // 如果没有传递数据，则通过API获取\n        if (homeworkId && homeworkId !== 'preview') {\n          const data = await getHomework(homeworkId);\n          console.log('获取到的作业详情:', data);\n          console.log('作业详情字段:', Object.keys(data));\n          console.log('作业标题:', data.title);\n          console.log('作业任务标题:', data.assignment_title);\n          console.log('当前用户ID:', user.id);\n          console.log('作业学生ID:', data.student_id);\n\n          // 如果有传递的assignment标题，优先使用\n          if (location.state && location.state.assignmentTitle) {\n            data.display_title = location.state.assignmentTitle;\n            console.log('使用传递的作业任务标题:', data.display_title);\n          } else if (data.assignment_title) {\n            data.display_title = data.assignment_title;\n            console.log('使用API返回的作业任务标题:', data.display_title);\n          } else {\n            data.display_title = data.title;\n            console.log('使用作业记录标题:', data.display_title);\n          }\n\n          setHomework(data);\n\n          // 使用作业详情API返回的批注图片数据，避免重复获取\n          if (data.annotated_images && data.annotated_images.length > 0) {\n            // 对批注图片进行去重处理，以防后端返回重复数据\n            const uniqueAnnotatedImages = data.annotated_images.filter((image, index, self) =>\n              index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number)\n            );\n            setAnnotatedImages(uniqueAnnotatedImages);\n            console.log('使用作业详情API返回的批注图片（已去重）:', uniqueAnnotatedImages);\n          } else {\n            // 如果作业详情API没有返回批注图片，则单独获取\n            try {\n              const annotatedImagesData = await getAnnotatedImages(homeworkId);\n              // 对单独获取的批注图片也进行去重处理\n              const uniqueAnnotatedImages = annotatedImagesData.filter((image, index, self) =>\n                index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number)\n              );\n              setAnnotatedImages(uniqueAnnotatedImages);\n              console.log('单独获取到的批注图片（已去重）:', uniqueAnnotatedImages);\n            } catch (error) {\n              console.error('获取批注图片失败:', error);\n            }\n          }\n        } else {\n          setError('无效的作业ID');\n        }\n\n      } catch (error) {\n        console.error('获取作业详情失败:', error);\n        setError(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchHomeworkDetail();\n  }, [homeworkId, location.state]);\n\n  // 渲染作业图片\n  const renderImages = () => {\n    if (!homework || !homework.images || homework.images.length === 0) {\n      return (\n        <div style={{ textAlign: 'center', padding: '40px 0' }}>\n          <div style={{ fontSize: '48px', marginBottom: '16px' }}>\n            📷\n          </div>\n          <Text type=\"secondary\">暂无作业图片</Text>\n        </div>\n      );\n    }\n\n    // 对原始图片进行去重处理，以防后端返回重复数据\n    const uniqueImages = homework.images.filter((image, index, self) =>\n      index === self.findIndex(i => i.id === image.id && i.page_number === image.page_number)\n    );\n\n    return (\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>\n        {uniqueImages.map((image, index) => {\n          // 使用统一的图片URL处理函数\n          const imagePath = getImageUrl(image.image_path);\n\n          return (\n            <Card\n              key={image.id}\n              hoverable\n              style={{ borderRadius: '12px' }}\n              bodyStyle={{ padding: '12px' }}\n            >\n              <Image\n                src={imagePath}\n                alt={`作业图片 ${index + 1}`}\n                style={{\n                  width: '100%',\n                  borderRadius: '8px',\n                  cursor: 'pointer'\n                }}\n                fallback=\"/broken-image.png\"\n                preview={{\n                  mask: '点击查看大图',\n                  maskClassName: 'student-image-mask'\n                }}\n              />\n              <div style={{\n                textAlign: 'center',\n                marginTop: '8px',\n                fontSize: '14px',\n                color: '#666666'\n              }}>\n                第 {image.page_number} 页\n              </div>\n            </Card>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // 渲染批注图片\n  const renderAnnotatedImages = () => {\n    if (!annotatedImages || annotatedImages.length === 0) {\n      return (\n        <div style={{ textAlign: 'center', padding: '40px 0' }}>\n          <div style={{ fontSize: '48px', marginBottom: '16px' }}>\n            📝\n          </div>\n          <Text type=\"secondary\">暂无批注图片</Text>\n          <div style={{ marginTop: '8px', fontSize: '12px', color: '#999' }}>\n            批注图片由老师生成，包含详细的批改标记\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>\n        {annotatedImages.map((image, index) => {\n          // 使用统一的图片URL处理函数\n          const imagePath = getImageUrl(image.image_path);\n          \n          return (\n            <Card\n              key={image.id}\n              hoverable\n              style={{ borderRadius: '12px' }}\n              bodyStyle={{ padding: '12px' }}\n            >\n              <Image\n                src={imagePath}\n                alt={`批注图片 ${index + 1}`}\n                style={{\n                  width: '100%',\n                  borderRadius: '8px',\n                  cursor: 'pointer'\n                }}\n                fallback=\"/broken-image.png\"\n                preview={{\n                  mask: '点击查看大图',\n                  maskClassName: 'student-image-mask'\n                }}\n              />\n              <div style={{\n                textAlign: 'center',\n                marginTop: '8px',\n                fontSize: '14px',\n                color: '#666666'\n              }}>\n                第 {image.page_number} 页 - 批注版本\n              </div>\n            </Card>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // 渲染加载状态\n  if (loading) {\n    return (\n      <div className=\"student-interface\">\n        <div className=\"student-page\">\n          <div style={{ textAlign: 'center', padding: '100px 0' }}>\n            <Spin size=\"large\" />\n            <div style={{ marginTop: '16px', color: '#666666' }}>\n              正在加载作业详情...\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // 错误状态\n  if (error) {\n    return (\n      <div className=\"student-interface\">\n        <div className=\"student-page\">\n          <Alert\n            message=\"😔 加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            action={\n              <Button type=\"primary\" onClick={() => window.location.reload()}>\n                重新加载\n              </Button>\n            }\n            style={{\n              borderRadius: '12px',\n              marginBottom: '24px'\n            }}\n          />\n        </div>\n      </div>\n    );\n  }\n\n  if (!homework) {\n    return (\n      <div className=\"student-interface\">\n        <div className=\"student-page\">\n          <Alert\n            message=\"😔 作业不存在\"\n            description=\"找不到指定的作业，可能已被删除或您没有访问权限\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button type=\"primary\" onClick={() => navigate('/homework/review')}>\n                返回作业点评\n              </Button>\n            }\n            style={{\n              borderRadius: '12px',\n              marginBottom: '24px'\n            }}\n          />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"student-interface\">\n      <div className=\"student-page\">\n        {/* 页面头部 */}\n        <div style={{ marginBottom: '24px' }}>\n          <Button \n            icon={<ArrowLeftOutlined />} \n            onClick={() => navigate('/homework/review')}\n            style={{ marginBottom: '16px' }}\n          >\n            返回作业点评\n          </Button>\n          \n          <div className=\"student-page-title\">\n            📝 作业详情与点评\n          </div>\n          <div className=\"student-page-description\">\n            查看作业的详细信息、批改结果和老师点评\n          </div>\n        </div>\n\n        {/* 基本信息（全宽显示） */}\n        <Card\n          title={\n            <Space>\n              <FileTextOutlined />\n              基本信息\n            </Space>\n          }\n          style={{ borderRadius: '12px', marginBottom: '24px' }}\n        >\n          <Row gutter={[24, 16]}>\n            {/* 第一行：作业标题和总分 */}\n            <Col xs={24} md={18}>\n              <Descriptions.Item label=\"作业标题\">\n                <Text strong style={{ fontSize: '18px' }}>\n                  {homework.display_title || homework.assignment_title || homework.title}\n                </Text>\n              </Descriptions.Item>\n            </Col>\n            <Col xs={24} md={6}>\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '32px', fontWeight: 700, color: '#4A90E2' }}>\n                  {homework.score || 0}\n                </div>\n                <div style={{ fontSize: '14px', color: '#666666' }}>\n                  总分\n                </div>\n              </div>\n            </Col>\n          </Row>\n\n          <Descriptions\n            column={{ xs: 1, sm: 2, md: 3 }}\n            size=\"small\"\n            style={{ marginTop: '16px' }}\n            labelStyle={{ minWidth: '70px', fontWeight: 500 }}\n            contentStyle={{ flex: 1, minWidth: 0 }}\n          >\n            <Descriptions.Item label=\"科目\">\n              <Tag color=\"blue\">{homework.subject_name || '未设置'}</Tag>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"正确率\">\n              <Text strong style={{\n                color: homework.accuracy >= 0.8 ? '#52c41a' : homework.accuracy >= 0.6 ? '#faad14' : '#ff4d4f',\n                fontSize: '16px'\n              }}>\n                {homework.accuracy ? `${Math.round(homework.accuracy * 100)}%` : '0%'}\n              </Text>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"总题数\">\n              <Text strong style={{ fontSize: '16px' }}>\n                {homework.questions ? homework.questions.length : 0} 题\n              </Text>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"提交时间\">\n              <Space>\n                <ClockCircleOutlined />\n                {homework.submit_time ?\n                  moment(homework.submit_time).utcOffset(480).format('YYYY-MM-DD HH:mm:ss') :\n                  '未提交'\n                }\n              </Space>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"批改时间\">\n              <Space>\n                <CheckCircleOutlined />\n                {homework.grading_time ?\n                  moment(homework.grading_time).utcOffset(480).format('YYYY-MM-DD HH:mm:ss') :\n                  '未批改'\n                }\n              </Space>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"成绩等级\">\n              <ScoreBadge\n                score={homework.score}\n                accuracy={homework.accuracy}\n                size=\"small\"\n              />\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"主观题得分\">\n              <Text strong style={{ color: '#1890ff' }}>\n                {homework.subjectiveScore !== undefined ? `${homework.subjectiveScore}分` : `${homework.score || 0}分`}\n                {homework.subjectiveCount !== undefined && (\n                  <Text type=\"secondary\" style={{ marginLeft: '4px', fontSize: '12px' }}>\n                    ({homework.subjectiveCount}题)\n                  </Text>\n                )}\n              </Text>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"客观题得分\">\n              <Text strong style={{ color: '#1890ff' }}>\n                {homework.objectiveScore !== undefined ? `${homework.objectiveScore}分` : '0分'}\n                {homework.objectiveCount !== undefined && (\n                  <Text type=\"secondary\" style={{ marginLeft: '4px', fontSize: '12px' }}>\n                    ({homework.objectiveCount}题)\n                  </Text>\n                )}\n              </Text>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"批改状态\">\n              <StatusBadge status={homework.grading_status} />\n            </Descriptions.Item>\n          </Descriptions>\n\n          {/* 题号信息（独立行，确保完整显示） */}\n          <div style={{ marginTop: '20px', padding: '16px', background: '#fafafa', borderRadius: '8px' }}>\n            <Row gutter={[0, 12]}>\n              <Col span={24}>\n                <div style={{ marginBottom: '8px' }}>\n                  <Text strong style={{ color: '#52c41a', marginRight: '8px' }}>\n                    ✅ 正确题号：\n                  </Text>\n                  <Text style={{\n                    color: '#52c41a',\n                    fontWeight: 500,\n                    wordBreak: 'break-all',\n                    lineHeight: 1.6\n                  }}>\n                    {homework.correctNumbers || '无'}\n                  </Text>\n                </div>\n              </Col>\n              <Col span={24}>\n                <div>\n                  <Text strong style={{ color: '#ff4d4f', marginRight: '8px' }}>\n                    ❌ 错误题号：\n                  </Text>\n                  <Text style={{\n                    color: '#ff4d4f',\n                    fontWeight: 500,\n                    wordBreak: 'break-all',\n                    lineHeight: 1.6\n                  }}>\n                    {homework.wrongNumbers || '无'}\n                  </Text>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        </Card>\n\n        {/* 错误分析 */}\n        {homework.grading_status === '已批改' && homework.error_analysis && (\n          <Card\n            title={\n              <Space>\n                <ExclamationCircleOutlined />\n                本次作业错误分析\n              </Space>\n            }\n            style={{\n              borderRadius: '12px',\n              marginTop: '24px',\n              border: '2px solid #faad14'\n            }}\n          >\n            <div style={{\n              background: 'rgba(250, 173, 20, 0.05)',\n              padding: '12px', /* 移动端减少padding */\n              borderRadius: '8px',\n              border: '1px solid rgba(250, 173, 20, 0.2)'\n            }}>\n              <Paragraph style={{\n                fontSize: '15px', /* 移动端稍微减小字体 */\n                lineHeight: 1.6, /* 减少行高，节省空间 */\n                marginBottom: 0,\n                whiteSpace: 'pre-line'\n              }}>\n                {homework.error_analysis}\n              </Paragraph>\n            </div>\n\n            <div style={{\n              marginTop: '16px',\n              padding: '12px',\n              background: '#fff7e6',\n              borderRadius: '6px',\n              fontSize: '12px',\n              color: '#d48806'\n            }}>\n              📊 <strong>错误分析说明：</strong>\n              系统分析了你在本次作业中的错误类型和原因，帮助你针对性地改进学习方法。\n            </div>\n          </Card>\n        )}\n\n        {/* AI作业点评 */}\n        {homework.grading_status === '已批改' && (\n          <Card\n            title={\n              <Space>\n                <MessageOutlined />\n                AI作业点评\n              </Space>\n            }\n            style={{\n              borderRadius: '12px',\n              marginTop: '24px',\n              border: '2px solid #52c41a'\n            }}\n          >\n            {homework.homework_comment && homework.homework_comment !== \"暂无作业点评\" ? (\n              <div>\n                <Paragraph style={{\n                  fontSize: '15px', /* 移动端稍微减小字体 */\n                  lineHeight: 1.6, /* 减少行高，节省空间 */\n                  background: 'rgba(82, 196, 26, 0.05)',\n                  padding: '12px', /* 移动端减少padding */\n                  borderRadius: '8px',\n                  border: '1px solid rgba(82, 196, 26, 0.2)',\n                  whiteSpace: 'pre-line'\n                }}>\n                  {homework.homework_comment}\n                </Paragraph>\n\n                <div style={{\n                  marginTop: '16px',\n                  padding: '12px',\n                  background: '#f6f8fa',\n                  borderRadius: '6px',\n                  fontSize: '12px',\n                  color: '#666666'\n                }}>\n                  🤖 <strong>AI智能分析：</strong>\n                  基于你的答题情况，AI为你生成了个性化的学习建议和改进方向。\n                </div>\n              </div>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '40px 0' }}>\n                <div style={{ fontSize: '48px', marginBottom: '16px' }}>\n                  🤖\n                </div>\n                <Text type=\"secondary\">\n                  AI作业点评生成中，请稍后查看...\n                </Text>\n              </div>\n            )}\n          </Card>\n        )}\n\n        {/* 学习建议 */}\n        {homework.grading_status === '已批改' && homework.accuracy < 0.8 && (\n          <Card\n            title=\"📚 学习建议\"\n            style={{ \n              borderRadius: '12px', \n              marginTop: '24px',\n              border: '2px solid #faad14'\n            }}\n          >\n            <List\n              size=\"small\"\n              dataSource={[\n                '仔细复习错题，理解错误原因',\n                '加强相关知识点的练习',\n                '主动向老师或同学请教疑难问题',\n                '制定针对性的学习计划',\n                '定期回顾和总结学习内容'\n              ]}\n              renderItem={(item, index) => (\n                <List.Item>\n                  <Text>\n                    <span style={{ \n                      display: 'inline-block', \n                      width: '20px', \n                      height: '20px', \n                      background: '#faad14', \n                      color: 'white', \n                      borderRadius: '50%', \n                      textAlign: 'center', \n                      lineHeight: '20px', \n                      fontSize: '12px',\n                      marginRight: '8px'\n                    }}>\n                      {index + 1}\n                    </span>\n                    {item}\n                  </Text>\n                </List.Item>\n              )}\n            />\n          </Card>\n        )}\n\n        {/* 标签页内容 */}\n        <Card style={{ borderRadius: '12px', marginTop: '24px' }}>\n          <Tabs\n            activeKey={activeTabKey}\n            onChange={setActiveTabKey}\n            size=\"large\"\n            tabBarStyle={{ marginBottom: '24px' }}\n          >\n            <TabPane tab=\"📋 基本信息\" key=\"info\">\n              <div style={{ padding: '0 8px' }}>\n                <Text type=\"secondary\" style={{ fontSize: '14px' }}>\n                  查看作业的基本信息和成绩详情\n                </Text>\n              </div>\n            </TabPane>\n\n            <TabPane tab=\"📷 作业图片\" key=\"images\">\n              {renderImages()}\n            </TabPane>\n\n            <TabPane tab=\"📝 批注图片\" key=\"annotations\">\n              {renderAnnotatedImages()}\n            </TabPane>\n          </Tabs>\n        </Card>\n\n        {/* 底部操作 */}\n        <div style={{\n          textAlign: 'center',\n          marginTop: '40px',\n          padding: '20px',\n          background: '#f6f8fa',\n          borderRadius: '12px'\n        }}>\n          <Space size=\"large\">\n            <Button\n              size=\"large\"\n              onClick={() => navigate('/homework/review')}\n            >\n              返回作业点评\n            </Button>\n            <Button\n              type=\"primary\"\n              size=\"large\"\n              onClick={() => navigate('/homework')}\n            >\n              查看所有作业\n            </Button>\n            <Button\n              size=\"large\"\n              onClick={() => navigate('/training')}\n            >\n              错题训练\n            </Button>\n          </Space>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentHomeworkDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SACEC,IAAI,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EACnDC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,IAAI,QACrD,MAAM;AACb,SACEC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,eAAe,EACfC,yBAAyB,QACpB,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,iBAAiB;AACjE,OAAOC,WAAW,IAAIC,UAAU,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,SAAS;AAC1C,OAAO,0BAA0B;AACjC,OAAO,sCAAsC;AAC7C,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,WAAW,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGhC,UAAU;AAC7C,MAAM;EAAEiC;AAAQ,CAAC,GAAGpB,IAAI;AAExB,MAAMqB,qBAAqB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM;IAAEC;EAAW,CAAC,GAAGzC,SAAS,CAAC,CAAC;EAClC,MAAM0C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsD,YAAY,EAAEC,eAAe,CAAC,GAAGvD,QAAQ,CAAC,MAAM,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAI;QACFP,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,IAAIN,QAAQ,CAACY,KAAK,IAAIZ,QAAQ,CAACY,KAAK,CAACC,cAAc,EAAE;UACnDC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEf,QAAQ,CAACY,KAAK,CAACC,cAAc,CAAC;UACxDC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACjB,QAAQ,CAACY,KAAK,CAACC,cAAc,CAAC,CAAC;UAClEC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEG,IAAI,CAACC,SAAS,CAACnB,QAAQ,CAACY,KAAK,CAACC,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAChF,MAAMA,cAAc,GAAGb,QAAQ,CAACY,KAAK,CAACC,cAAc;;UAEpD;UACA,MAAMO,YAAY,GAAG;YACnBC,EAAE,EAAER,cAAc,CAACS,WAAW;YAC9BC,KAAK,EAAEV,cAAc,CAACU,KAAK;YAC3BC,gBAAgB,EAAEX,cAAc,CAACU,KAAK;YACtCE,aAAa,EAAEZ,cAAc,CAACU,KAAK;YACnCG,YAAY,EAAEb,cAAc,CAACa,YAAY;YACzCC,WAAW,EAAEd,cAAc,CAACe,YAAY;YAAG;YAC3CC,YAAY,EAAEhB,cAAc,CAACiB,SAAS;YAAK;YAC3CC,aAAa,EAAElB,cAAc,CAACmB,iBAAiB;YAC/CC,cAAc,EAAEpB,cAAc,CAACoB,cAAc;YAC7CC,KAAK,EAAErB,cAAc,CAACqB,KAAK;YAC3BC,QAAQ,EAAEtB,cAAc,CAACsB,QAAQ;YACjCC,gBAAgB,EAAEvB,cAAc,CAACuB,gBAAgB,IAAI,QAAQ;YAC7DC,UAAU,EAAEzC,IAAI,CAACyB,EAAE;YACnBiB,aAAa,EAAEzB,cAAc,CAACyB,aAAa,IAAIzB,cAAc,CAACQ,EAAE;YAChEkB,QAAQ,EAAE1B,cAAc,CAAC0B,QAAQ;YACjCC,UAAU,EAAE3B,cAAc,CAAC2B;UAC7B,CAAC;UAED1B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,YAAY,CAAC;;UAEtC;UACA,IAAIP,cAAc,CAACS,WAAW,EAAE;YAC9B,IAAI;cACFR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,cAAc,CAACS,WAAW,CAAC;cAChE,MAAMmB,UAAU,GAAG,MAAM3D,WAAW,CAAC+B,cAAc,CAACS,WAAW,CAAC;cAChER,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE0B,UAAU,CAAC;cACtC3B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,MAAM,CAACC,IAAI,CAACwB,UAAU,CAAC,CAAC;cACjD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEG,IAAI,CAACC,SAAS,CAACsB,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;cAE/D;cACArB,YAAY,CAACgB,gBAAgB,GAAGK,UAAU,CAACL,gBAAgB,IAAIvB,cAAc,CAACuB,gBAAgB,IAAI,QAAQ;cAC1GhB,YAAY,CAACsB,WAAW,GAAGD,UAAU,CAACC,WAAW;cACjDtB,YAAY,CAACuB,MAAM,GAAGF,UAAU,CAACE,MAAM;;cAEvC;cACA,IAAIF,UAAU,CAACG,gBAAgB,IAAIH,UAAU,CAACG,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;gBACzE;gBACA,MAAMC,qBAAqB,GAAGL,UAAU,CAACG,gBAAgB,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,KAClFD,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAK2B,KAAK,CAAC3B,EAAE,IAAI+B,CAAC,CAACC,WAAW,KAAKL,KAAK,CAACK,WAAW,CACxF,CAAC;gBACD7C,kBAAkB,CAACsC,qBAAqB,CAAC;gBACzChC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+B,qBAAqB,CAAC;cAC9D,CAAC,MAAM;gBACL;gBACA,IAAI;kBACF,MAAMQ,mBAAmB,GAAG,MAAMvE,kBAAkB,CAAC8B,cAAc,CAACS,WAAW,CAAC;kBAChF;kBACA,MAAMwB,qBAAqB,GAAGQ,mBAAmB,CAACP,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,KAC1ED,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAK2B,KAAK,CAAC3B,EAAE,IAAI+B,CAAC,CAACC,WAAW,KAAKL,KAAK,CAACK,WAAW,CACxF,CAAC;kBACD7C,kBAAkB,CAACsC,qBAAqB,CAAC;kBACzChC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+B,qBAAqB,CAAC;gBACxD,CAAC,CAAC,OAAOzC,KAAK,EAAE;kBACdS,OAAO,CAACT,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;gBACnC;cACF;;cAEA;cACA,IAAIoC,UAAU,CAACC,WAAW,IAAID,UAAU,CAACC,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;gBAC/D,IAAI;kBACF,MAAMU,cAAc,GAAGrC,IAAI,CAACsC,KAAK,CAACf,UAAU,CAACC,WAAW,CAAC,CAAC,CAAC,CAACe,eAAe,CAAC;kBAC5E3C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwC,cAAc,CAAC;kBAEvC,IAAIA,cAAc,CAACG,SAAS,EAAE;oBAC5BtC,YAAY,CAACsC,SAAS,GAAGH,cAAc,CAACG,SAAS;;oBAEjD;oBACA,MAAMC,cAAc,GAAGJ,cAAc,CAACG,SAAS,CAACX,MAAM,CAACa,CAAC,IAAI,CAACA,CAAC,CAACC,UAAU,CAAC;oBAC1E,MAAMC,gBAAgB,GAAGP,cAAc,CAACG,SAAS,CAACX,MAAM,CAACa,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC;;oBAE3E;oBACA,MAAME,cAAc,GAAGD,gBAAgB,CAACE,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACK,eAAe,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;oBAC7E,MAAMC,YAAY,GAAGR,cAAc,CAACK,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACK,eAAe,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;oBAEzE;oBACA,MAAME,cAAc,GAAGb,cAAc,CAACG,SAAS,CAACb,MAAM;oBACtD,MAAMwB,YAAY,GAAGP,gBAAgB,CAACjB,MAAM;;oBAE5C;oBACA,MAAMyB,UAAU,GAAGlD,YAAY,CAACc,KAAK,IAAI,CAAC;oBAC1C,MAAMC,QAAQ,GAAGf,YAAY,CAACe,QAAQ,IAAI,CAAC;;oBAE3C;oBACA;oBACA;oBACA,MAAMoC,kBAAkB,GAAGhB,cAAc,CAACG,SAAS,CAACX,MAAM,CAACa,CAAC,IAC1DA,CAAC,CAACY,aAAa,KAAK,KAAK,IACzBZ,CAAC,CAACY,aAAa,KAAK,KAAK,IACzBZ,CAAC,CAACY,aAAa,KAAK,KAAK,IACzBZ,CAAC,CAACY,aAAa,KAAK,KAAK,IACzBZ,CAAC,CAACY,aAAa,KAAK,KAAK,IACzBZ,CAAC,CAACY,aAAa,KAAK,KACtB,CAAC;oBACD,MAAMC,mBAAmB,GAAGlB,cAAc,CAACG,SAAS,CAACX,MAAM,CAACa,CAAC,IAC3D,CAACW,kBAAkB,CAACG,QAAQ,CAACd,CAAC,CAChC,CAAC;oBAED,MAAMe,iBAAiB,GAAGF,mBAAmB,CAAC1B,MAAM,CAACa,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAChB,MAAM;oBAC9E,MAAM+B,gBAAgB,GAAGL,kBAAkB,CAACxB,MAAM,CAACa,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAChB,MAAM;oBAE5E/B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;oBACtBD,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEqD,cAAc,CAAC;oBACrCtD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0D,mBAAmB,CAAC5B,MAAM,CAAC;oBACnD/B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwD,kBAAkB,CAAC1B,MAAM,CAAC;oBAClD/B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE4D,iBAAiB,CAAC;oBAC3C7D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE6D,gBAAgB,CAAC;oBAC1C9D,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEuD,UAAU,CAAC;oBAChCxD,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEoB,QAAQ,CAAC;;oBAE/B;oBACA,IAAI0C,eAAe,EAAEC,cAAc;oBAEnC,IAAIP,kBAAkB,CAAC1B,MAAM,KAAK,CAAC,EAAE;sBACnC;sBACAgC,eAAe,GAAGP,UAAU;sBAC5BQ,cAAc,GAAG,CAAC;oBACpB,CAAC,MAAM,IAAIL,mBAAmB,CAAC5B,MAAM,KAAK,CAAC,EAAE;sBAC3C;sBACAgC,eAAe,GAAG,CAAC;sBACnBC,cAAc,GAAGR,UAAU;oBAC7B,CAAC,MAAM;sBACL;sBACA,MAAMS,eAAe,GAAGJ,iBAAiB,IAAIA,iBAAiB,GAAGC,gBAAgB,CAAC;sBAClF,MAAMI,cAAc,GAAGJ,gBAAgB,IAAID,iBAAiB,GAAGC,gBAAgB,CAAC;sBAChFC,eAAe,GAAGI,IAAI,CAACC,KAAK,CAACZ,UAAU,GAAGS,eAAe,CAAC;sBAC1DD,cAAc,GAAGG,IAAI,CAACC,KAAK,CAACZ,UAAU,GAAGU,cAAc,CAAC;oBAC1D;oBAEAlE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE8D,eAAe,CAAC;oBACxC/D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+D,cAAc,CAAC;;oBAEvC;oBACA1D,YAAY,CAAC2C,cAAc,GAAGA,cAAc,IAAI,GAAG;oBACnD3C,YAAY,CAAC+C,YAAY,GAAGA,YAAY,IAAI,GAAG;oBAC/C/C,YAAY,CAACyD,eAAe,GAAGI,IAAI,CAACC,KAAK,CAACL,eAAe,CAAC;oBAC1DzD,YAAY,CAAC0D,cAAc,GAAGG,IAAI,CAACC,KAAK,CAACJ,cAAc,CAAC;oBACxD1D,YAAY,CAAC+D,eAAe,GAAGV,mBAAmB,CAAC5B,MAAM;oBACzDzB,YAAY,CAACgE,cAAc,GAAGb,kBAAkB,CAAC1B,MAAM;oBAEvD,IAAIwC,SAAS,GAAG,QAAQ9B,cAAc,CAACG,SAAS,CAACb,MAAM,OAAOiB,gBAAgB,CAACjB,MAAM,OAAOc,cAAc,CAACd,MAAM,QAAQ;oBAEzH,IAAIc,cAAc,CAACd,MAAM,GAAG,CAAC,EAAE;sBAC7BwC,SAAS,IAAI,SAAS;sBACtB1B,cAAc,CAAC2B,OAAO,CAAC,CAAC1B,CAAC,EAAEX,KAAK,KAAK;wBACnCoC,SAAS,IAAI,GAAGpC,KAAK,GAAG,CAAC,MAAMW,CAAC,CAACK,eAAe,KAAKL,CAAC,CAAC2B,QAAQ,IAAI,QAAQ,IAAI;wBAC/E,IAAI3B,CAAC,CAAC4B,aAAa,EAAE;0BACnBH,SAAS,IAAI,MAAMzB,CAAC,CAAC4B,aAAa,IAAI;wBACxC;sBACF,CAAC,CAAC;oBACJ,CAAC,MAAM;sBACLH,SAAS,IAAI,qBAAqB;oBACpC;oBAEAjE,YAAY,CAACqE,UAAU,GAAGJ,SAAS;;oBAEnC;oBACA,IAAI1B,cAAc,CAACd,MAAM,GAAG,CAAC,EAAE;sBAC7B,IAAI6C,aAAa,GAAG,iBAAiB;sBACrC/B,cAAc,CAAC2B,OAAO,CAAC,CAAC1B,CAAC,EAAEX,KAAK,KAAK;wBACnCyC,aAAa,IAAI,KAAKzC,KAAK,GAAG,CAAC,KAAKW,CAAC,CAACK,eAAe,KAAK;wBAC1DyB,aAAa,IAAI,MAAM9B,CAAC,CAAC+B,gBAAgB,IAAI;wBAC7CD,aAAa,IAAI,QAAQ9B,CAAC,CAACgC,cAAc,IAAI;wBAC7CF,aAAa,IAAI,QAAQ9B,CAAC,CAACiC,cAAc,IAAI;wBAC7CH,aAAa,IAAI,QAAQ9B,CAAC,CAAC2B,QAAQ,IAAI,gBAAgB,IAAI;wBAC3D,IAAI3B,CAAC,CAAC4B,aAAa,EAAE;0BACnBE,aAAa,IAAI,QAAQ9B,CAAC,CAAC4B,aAAa,IAAI;wBAC9C;wBACAE,aAAa,IAAI,IAAI;sBACvB,CAAC,CAAC;sBACFtE,YAAY,CAAC0E,cAAc,GAAGJ,aAAa;oBAC7C;kBACF;gBACF,CAAC,CAAC,OAAOK,UAAU,EAAE;kBACnBjF,OAAO,CAACT,KAAK,CAAC,WAAW,EAAE0F,UAAU,CAAC;gBACxC;cACF;cAEAjF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEK,YAAY,CAAC;YAC1C,CAAC,CAAC,OAAO4E,WAAW,EAAE;cACpBlF,OAAO,CAACT,KAAK,CAAC,aAAa,EAAE2F,WAAW,CAAC;cACzC;YACF;UACF;UAEAlF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,YAAY,CAAC;UACtClB,WAAW,CAACkB,YAAY,CAAC;UACzBhB,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIN,UAAU,IAAIA,UAAU,KAAK,SAAS,EAAE;UAC1C,MAAMmG,IAAI,GAAG,MAAMnH,WAAW,CAACgB,UAAU,CAAC;UAC1CgB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkF,IAAI,CAAC;UAC9BnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACgF,IAAI,CAAC,CAAC;UACzCnF,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEkF,IAAI,CAAC1E,KAAK,CAAC;UAChCT,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkF,IAAI,CAACzE,gBAAgB,CAAC;UAC7CV,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEnB,IAAI,CAACyB,EAAE,CAAC;UAC/BP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkF,IAAI,CAAC5D,UAAU,CAAC;;UAEvC;UACA,IAAIrC,QAAQ,CAACY,KAAK,IAAIZ,QAAQ,CAACY,KAAK,CAACsF,eAAe,EAAE;YACpDD,IAAI,CAACxE,aAAa,GAAGzB,QAAQ,CAACY,KAAK,CAACsF,eAAe;YACnDpF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkF,IAAI,CAACxE,aAAa,CAAC;UACjD,CAAC,MAAM,IAAIwE,IAAI,CAACzE,gBAAgB,EAAE;YAChCyE,IAAI,CAACxE,aAAa,GAAGwE,IAAI,CAACzE,gBAAgB;YAC1CV,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEkF,IAAI,CAACxE,aAAa,CAAC;UACpD,CAAC,MAAM;YACLwE,IAAI,CAACxE,aAAa,GAAGwE,IAAI,CAAC1E,KAAK;YAC/BT,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkF,IAAI,CAACxE,aAAa,CAAC;UAC9C;UAEAvB,WAAW,CAAC+F,IAAI,CAAC;;UAEjB;UACA,IAAIA,IAAI,CAACrD,gBAAgB,IAAIqD,IAAI,CAACrD,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;YAC7D;YACA,MAAMC,qBAAqB,GAAGmD,IAAI,CAACrD,gBAAgB,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,KAC5ED,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAK2B,KAAK,CAAC3B,EAAE,IAAI+B,CAAC,CAACC,WAAW,KAAKL,KAAK,CAACK,WAAW,CACxF,CAAC;YACD7C,kBAAkB,CAACsC,qBAAqB,CAAC;YACzChC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+B,qBAAqB,CAAC;UAC9D,CAAC,MAAM;YACL;YACA,IAAI;cACF,MAAMQ,mBAAmB,GAAG,MAAMvE,kBAAkB,CAACe,UAAU,CAAC;cAChE;cACA,MAAMgD,qBAAqB,GAAGQ,mBAAmB,CAACP,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,KAC1ED,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAK2B,KAAK,CAAC3B,EAAE,IAAI+B,CAAC,CAACC,WAAW,KAAKL,KAAK,CAACK,WAAW,CACxF,CAAC;cACD7C,kBAAkB,CAACsC,qBAAqB,CAAC;cACzChC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+B,qBAAqB,CAAC;YACxD,CAAC,CAAC,OAAOzC,KAAK,EAAE;cACdS,OAAO,CAACT,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;YACnC;UACF;QACF,CAAC,MAAM;UACLC,QAAQ,CAAC,SAAS,CAAC;QACrB;MAEF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdS,OAAO,CAACT,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCC,QAAQ,CAACD,KAAK,CAAC8F,OAAO,CAAC;MACzB,CAAC,SAAS;QACR/F,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACb,UAAU,EAAEE,QAAQ,CAACY,KAAK,CAAC,CAAC;;EAEhC;EACA,MAAMwF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACnG,QAAQ,IAAI,CAACA,QAAQ,CAAC0C,MAAM,IAAI1C,QAAQ,CAAC0C,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACjE,oBACEvD,OAAA;QAAK+G,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAC,QAAA,gBACrDlH,OAAA;UAAK+G,KAAK,EAAE;YAAEI,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAExD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxH,OAAA,CAACE,IAAI;UAACuH,IAAI,EAAC,WAAW;UAAAP,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAEV;;IAEA;IACA,MAAME,YAAY,GAAG/G,QAAQ,CAAC0C,MAAM,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,KAC7DD,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/B,EAAE,KAAK2B,KAAK,CAAC3B,EAAE,IAAI+B,CAAC,CAACC,WAAW,KAAKL,KAAK,CAACK,WAAW,CACxF,CAAC;IAED,oBACE/D,OAAA;MAAK+G,KAAK,EAAE;QAAEY,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAX,QAAA,EACvGQ,YAAY,CAAChD,GAAG,CAAC,CAAChB,KAAK,EAAEC,KAAK,KAAK;QAClC;QACA,MAAMmE,SAAS,GAAGhI,WAAW,CAAC4D,KAAK,CAACqE,UAAU,CAAC;QAE/C,oBACE/H,OAAA,CAAC9B,IAAI;UAEH8J,SAAS;UACTjB,KAAK,EAAE;YAAEkB,YAAY,EAAE;UAAO,CAAE;UAChCC,SAAS,EAAE;YAAEjB,OAAO,EAAE;UAAO,CAAE;UAAAC,QAAA,gBAE/BlH,OAAA,CAACjB,KAAK;YACJoJ,GAAG,EAAEL,SAAU;YACfM,GAAG,EAAE,QAAQzE,KAAK,GAAG,CAAC,EAAG;YACzBoD,KAAK,EAAE;cACLsB,KAAK,EAAE,MAAM;cACbJ,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE;YACV,CAAE;YACFC,QAAQ,EAAC,mBAAmB;YAC5BC,OAAO,EAAE;cACPC,IAAI,EAAE,QAAQ;cACdC,aAAa,EAAE;YACjB;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFxH,OAAA;YAAK+G,KAAK,EAAE;cACVC,SAAS,EAAE,QAAQ;cACnB2B,SAAS,EAAE,KAAK;cAChBxB,QAAQ,EAAE,MAAM;cAChByB,KAAK,EAAE;YACT,CAAE;YAAA1B,QAAA,GAAC,SACC,EAACxD,KAAK,CAACK,WAAW,EAAC,SACvB;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,GA1BD9D,KAAK,CAAC3B,EAAE;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BT,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMqB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC5H,eAAe,IAAIA,eAAe,CAACsC,MAAM,KAAK,CAAC,EAAE;MACpD,oBACEvD,OAAA;QAAK+G,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAAC,QAAA,gBACrDlH,OAAA;UAAK+G,KAAK,EAAE;YAAEI,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EAAC;QAExD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxH,OAAA,CAACE,IAAI;UAACuH,IAAI,EAAC,WAAW;UAAAP,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCxH,OAAA;UAAK+G,KAAK,EAAE;YAAE4B,SAAS,EAAE,KAAK;YAAExB,QAAQ,EAAE,MAAM;YAAEyB,KAAK,EAAE;UAAO,CAAE;UAAA1B,QAAA,EAAC;QAEnE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,oBACExH,OAAA;MAAK+G,KAAK,EAAE;QAAEY,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE;MAAO,CAAE;MAAAX,QAAA,EACvGjG,eAAe,CAACyD,GAAG,CAAC,CAAChB,KAAK,EAAEC,KAAK,KAAK;QACrC;QACA,MAAMmE,SAAS,GAAGhI,WAAW,CAAC4D,KAAK,CAACqE,UAAU,CAAC;QAE/C,oBACE/H,OAAA,CAAC9B,IAAI;UAEH8J,SAAS;UACTjB,KAAK,EAAE;YAAEkB,YAAY,EAAE;UAAO,CAAE;UAChCC,SAAS,EAAE;YAAEjB,OAAO,EAAE;UAAO,CAAE;UAAAC,QAAA,gBAE/BlH,OAAA,CAACjB,KAAK;YACJoJ,GAAG,EAAEL,SAAU;YACfM,GAAG,EAAE,QAAQzE,KAAK,GAAG,CAAC,EAAG;YACzBoD,KAAK,EAAE;cACLsB,KAAK,EAAE,MAAM;cACbJ,YAAY,EAAE,KAAK;cACnBK,MAAM,EAAE;YACV,CAAE;YACFC,QAAQ,EAAC,mBAAmB;YAC5BC,OAAO,EAAE;cACPC,IAAI,EAAE,QAAQ;cACdC,aAAa,EAAE;YACjB;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFxH,OAAA;YAAK+G,KAAK,EAAE;cACVC,SAAS,EAAE,QAAQ;cACnB2B,SAAS,EAAE,KAAK;cAChBxB,QAAQ,EAAE,MAAM;cAChByB,KAAK,EAAE;YACT,CAAE;YAAA1B,QAAA,GAAC,SACC,EAACxD,KAAK,CAACK,WAAW,EAAC,oCACvB;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,GA1BD9D,KAAK,CAAC3B,EAAE;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BT,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,IAAI3G,OAAO,EAAE;IACX,oBACEb,OAAA;MAAK8I,SAAS,EAAC,mBAAmB;MAAA5B,QAAA,eAChClH,OAAA;QAAK8I,SAAS,EAAC,cAAc;QAAA5B,QAAA,eAC3BlH,OAAA;UAAK+G,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAC,QAAA,gBACtDlH,OAAA,CAAC5B,IAAI;YAAC2K,IAAI,EAAC;UAAO;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBxH,OAAA;YAAK+G,KAAK,EAAE;cAAE4B,SAAS,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAA1B,QAAA,EAAC;UAErD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAIzG,KAAK,EAAE;IACT,oBACEf,OAAA;MAAK8I,SAAS,EAAC,mBAAmB;MAAA5B,QAAA,eAChClH,OAAA;QAAK8I,SAAS,EAAC,cAAc;QAAA5B,QAAA,eAC3BlH,OAAA,CAAC3B,KAAK;UACJwI,OAAO,EAAC,uCAAS;UACjBmC,WAAW,EAAEjI,KAAM;UACnB0G,IAAI,EAAC,OAAO;UACZwB,QAAQ;UACRC,MAAM,eACJlJ,OAAA,CAAC1B,MAAM;YAACmJ,IAAI,EAAC,SAAS;YAAC0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAAC1I,QAAQ,CAAC2I,MAAM,CAAC,CAAE;YAAAnC,QAAA,EAAC;UAEhE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACDT,KAAK,EAAE;YACLkB,YAAY,EAAE,MAAM;YACpBb,YAAY,EAAE;UAChB;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC7G,QAAQ,EAAE;IACb,oBACEX,OAAA;MAAK8I,SAAS,EAAC,mBAAmB;MAAA5B,QAAA,eAChClH,OAAA;QAAK8I,SAAS,EAAC,cAAc;QAAA5B,QAAA,eAC3BlH,OAAA,CAAC3B,KAAK;UACJwI,OAAO,EAAC,6CAAU;UAClBmC,WAAW,EAAC,4IAAyB;UACrCvB,IAAI,EAAC,SAAS;UACdwB,QAAQ;UACRC,MAAM,eACJlJ,OAAA,CAAC1B,MAAM;YAACmJ,IAAI,EAAC,SAAS;YAAC0B,OAAO,EAAEA,CAAA,KAAM1I,QAAQ,CAAC,kBAAkB,CAAE;YAAAyG,QAAA,EAAC;UAEpE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UACDT,KAAK,EAAE;YACLkB,YAAY,EAAE,MAAM;YACpBb,YAAY,EAAE;UAChB;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExH,OAAA;IAAK8I,SAAS,EAAC,mBAAmB;IAAA5B,QAAA,eAChClH,OAAA;MAAK8I,SAAS,EAAC,cAAc;MAAA5B,QAAA,gBAE3BlH,OAAA;QAAK+G,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBACnClH,OAAA,CAAC1B,MAAM;UACLgL,IAAI,eAAEtJ,OAAA,CAACf,iBAAiB;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5B2B,OAAO,EAAEA,CAAA,KAAM1I,QAAQ,CAAC,kBAAkB,CAAE;UAC5CsG,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,EACjC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETxH,OAAA;UAAK8I,SAAS,EAAC,oBAAoB;UAAA5B,QAAA,EAAC;QAEpC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxH,OAAA;UAAK8I,SAAS,EAAC,0BAA0B;UAAA5B,QAAA,EAAC;QAE1C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxH,OAAA,CAAC9B,IAAI;QACH+D,KAAK,eACHjC,OAAA,CAACpB,KAAK;UAAAsI,QAAA,gBACJlH,OAAA,CAACX,gBAAgB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;QACDT,KAAK,EAAE;UAAEkB,YAAY,EAAE,MAAM;UAAEb,YAAY,EAAE;QAAO,CAAE;QAAAF,QAAA,gBAEtDlH,OAAA,CAACnB,GAAG;UAAC0K,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAArC,QAAA,gBAEpBlH,OAAA,CAAClB,GAAG;YAAC0K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAvC,QAAA,eAClBlH,OAAA,CAACzB,YAAY,CAACmL,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAAzC,QAAA,eAC7BlH,OAAA,CAACE,IAAI;gBAAC0J,MAAM;gBAAC7C,KAAK,EAAE;kBAAEI,QAAQ,EAAE;gBAAO,CAAE;gBAAAD,QAAA,EACtCvG,QAAQ,CAACwB,aAAa,IAAIxB,QAAQ,CAACuB,gBAAgB,IAAIvB,QAAQ,CAACsB;cAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACNxH,OAAA,CAAClB,GAAG;YAAC0K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAvC,QAAA,eACjBlH,OAAA;cAAK+G,KAAK,EAAE;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAE,QAAA,gBAClClH,OAAA;gBAAK+G,KAAK,EAAE;kBAAEI,QAAQ,EAAE,MAAM;kBAAE0C,UAAU,EAAE,GAAG;kBAAEjB,KAAK,EAAE;gBAAU,CAAE;gBAAA1B,QAAA,EACjEvG,QAAQ,CAACiC,KAAK,IAAI;cAAC;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACNxH,OAAA;gBAAK+G,KAAK,EAAE;kBAAEI,QAAQ,EAAE,MAAM;kBAAEyB,KAAK,EAAE;gBAAU,CAAE;gBAAA1B,QAAA,EAAC;cAEpD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxH,OAAA,CAACzB,YAAY;UACXuL,MAAM,EAAE;YAAEN,EAAE,EAAE,CAAC;YAAEO,EAAE,EAAE,CAAC;YAAEN,EAAE,EAAE;UAAE,CAAE;UAChCV,IAAI,EAAC,OAAO;UACZhC,KAAK,EAAE;YAAE4B,SAAS,EAAE;UAAO,CAAE;UAC7BqB,UAAU,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEJ,UAAU,EAAE;UAAI,CAAE;UAClDK,YAAY,EAAE;YAAEC,IAAI,EAAE,CAAC;YAAEF,QAAQ,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBAEvClH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,cAAI;YAAAzC,QAAA,eAC3BlH,OAAA,CAACxB,GAAG;cAACoK,KAAK,EAAC,MAAM;cAAA1B,QAAA,EAAEvG,QAAQ,CAACyB,YAAY,IAAI;YAAK;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,oBAAK;YAAAzC,QAAA,eAC5BlH,OAAA,CAACE,IAAI;cAAC0J,MAAM;cAAC7C,KAAK,EAAE;gBAClB6B,KAAK,EAAEjI,QAAQ,CAACkC,QAAQ,IAAI,GAAG,GAAG,SAAS,GAAGlC,QAAQ,CAACkC,QAAQ,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS;gBAC9FsE,QAAQ,EAAE;cACZ,CAAE;cAAAD,QAAA,EACCvG,QAAQ,CAACkC,QAAQ,GAAG,GAAG8C,IAAI,CAACC,KAAK,CAACjF,QAAQ,CAACkC,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG;YAAI;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,oBAAK;YAAAzC,QAAA,eAC5BlH,OAAA,CAACE,IAAI;cAAC0J,MAAM;cAAC7C,KAAK,EAAE;gBAAEI,QAAQ,EAAE;cAAO,CAAE;cAAAD,QAAA,GACtCvG,QAAQ,CAACyD,SAAS,GAAGzD,QAAQ,CAACyD,SAAS,CAACb,MAAM,GAAG,CAAC,EAAC,SACtD;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAzC,QAAA,eAC7BlH,OAAA,CAACpB,KAAK;cAAAsI,QAAA,gBACJlH,OAAA,CAACb,mBAAmB;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACtB7G,QAAQ,CAAC0B,WAAW,GACnBxC,MAAM,CAACc,QAAQ,CAAC0B,WAAW,CAAC,CAAC+H,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC,GACzE,KAAK;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAzC,QAAA,eAC7BlH,OAAA,CAACpB,KAAK;cAAAsI,QAAA,gBACJlH,OAAA,CAACZ,mBAAmB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACtB7G,QAAQ,CAAC4B,YAAY,GACpB1C,MAAM,CAACc,QAAQ,CAAC4B,YAAY,CAAC,CAAC6H,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC,GAC1E,KAAK;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAzC,QAAA,eAC7BlH,OAAA,CAACL,UAAU;cACTiD,KAAK,EAAEjC,QAAQ,CAACiC,KAAM;cACtBC,QAAQ,EAAElC,QAAQ,CAACkC,QAAS;cAC5BkG,IAAI,EAAC;YAAO;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACe,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,gCAAO;YAAAzC,QAAA,eAC9BlH,OAAA,CAACE,IAAI;cAAC0J,MAAM;cAAC7C,KAAK,EAAE;gBAAE6B,KAAK,EAAE;cAAU,CAAE;cAAA1B,QAAA,GACtCvG,QAAQ,CAAC4E,eAAe,KAAK+E,SAAS,GAAG,GAAG3J,QAAQ,CAAC4E,eAAe,GAAG,GAAG,GAAG5E,QAAQ,CAACiC,KAAK,IAAI,CAAC,GAAG,EACnGjC,QAAQ,CAACkF,eAAe,KAAKyE,SAAS,iBACrCtK,OAAA,CAACE,IAAI;gBAACuH,IAAI,EAAC,WAAW;gBAACV,KAAK,EAAE;kBAAEwD,UAAU,EAAE,KAAK;kBAAEpD,QAAQ,EAAE;gBAAO,CAAE;gBAAAD,QAAA,GAAC,GACpE,EAACvG,QAAQ,CAACkF,eAAe,EAAC,SAC7B;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,gCAAO;YAAAzC,QAAA,eAC9BlH,OAAA,CAACE,IAAI;cAAC0J,MAAM;cAAC7C,KAAK,EAAE;gBAAE6B,KAAK,EAAE;cAAU,CAAE;cAAA1B,QAAA,GACtCvG,QAAQ,CAAC6E,cAAc,KAAK8E,SAAS,GAAG,GAAG3J,QAAQ,CAAC6E,cAAc,GAAG,GAAG,IAAI,EAC5E7E,QAAQ,CAACmF,cAAc,KAAKwE,SAAS,iBACpCtK,OAAA,CAACE,IAAI;gBAACuH,IAAI,EAAC,WAAW;gBAACV,KAAK,EAAE;kBAAEwD,UAAU,EAAE,KAAK;kBAAEpD,QAAQ,EAAE;gBAAO,CAAE;gBAAAD,QAAA,GAAC,GACpE,EAACvG,QAAQ,CAACmF,cAAc,EAAC,SAC5B;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eAEpBxH,OAAA,CAACzB,YAAY,CAACmL,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAzC,QAAA,eAC7BlH,OAAA,CAACN,WAAW;cAAC8K,MAAM,EAAE7J,QAAQ,CAACgC;YAAe;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGfxH,OAAA;UAAK+G,KAAK,EAAE;YAAE4B,SAAS,EAAE,MAAM;YAAE1B,OAAO,EAAE,MAAM;YAAEwD,UAAU,EAAE,SAAS;YAAExC,YAAY,EAAE;UAAM,CAAE;UAAAf,QAAA,eAC7FlH,OAAA,CAACnB,GAAG;YAAC0K,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAE;YAAArC,QAAA,gBACnBlH,OAAA,CAAClB,GAAG;cAAC4L,IAAI,EAAE,EAAG;cAAAxD,QAAA,eACZlH,OAAA;gBAAK+G,KAAK,EAAE;kBAAEK,YAAY,EAAE;gBAAM,CAAE;gBAAAF,QAAA,gBAClClH,OAAA,CAACE,IAAI;kBAAC0J,MAAM;kBAAC7C,KAAK,EAAE;oBAAE6B,KAAK,EAAE,SAAS;oBAAE+B,WAAW,EAAE;kBAAM,CAAE;kBAAAzD,QAAA,EAAC;gBAE9D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPxH,OAAA,CAACE,IAAI;kBAAC6G,KAAK,EAAE;oBACX6B,KAAK,EAAE,SAAS;oBAChBiB,UAAU,EAAE,GAAG;oBACfe,SAAS,EAAE,WAAW;oBACtBC,UAAU,EAAE;kBACd,CAAE;kBAAA3D,QAAA,EACCvG,QAAQ,CAAC8D,cAAc,IAAI;gBAAG;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxH,OAAA,CAAClB,GAAG;cAAC4L,IAAI,EAAE,EAAG;cAAAxD,QAAA,eACZlH,OAAA;gBAAAkH,QAAA,gBACElH,OAAA,CAACE,IAAI;kBAAC0J,MAAM;kBAAC7C,KAAK,EAAE;oBAAE6B,KAAK,EAAE,SAAS;oBAAE+B,WAAW,EAAE;kBAAM,CAAE;kBAAAzD,QAAA,EAAC;gBAE9D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPxH,OAAA,CAACE,IAAI;kBAAC6G,KAAK,EAAE;oBACX6B,KAAK,EAAE,SAAS;oBAChBiB,UAAU,EAAE,GAAG;oBACfe,SAAS,EAAE,WAAW;oBACtBC,UAAU,EAAE;kBACd,CAAE;kBAAA3D,QAAA,EACCvG,QAAQ,CAACkE,YAAY,IAAI;gBAAG;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGN7G,QAAQ,CAACgC,cAAc,KAAK,KAAK,IAAIhC,QAAQ,CAAC6F,cAAc,iBAC3DxG,OAAA,CAAC9B,IAAI;QACH+D,KAAK,eACHjC,OAAA,CAACpB,KAAK;UAAAsI,QAAA,gBACJlH,OAAA,CAACT,yBAAyB;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oDAE/B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;QACDT,KAAK,EAAE;UACLkB,YAAY,EAAE,MAAM;UACpBU,SAAS,EAAE,MAAM;UACjBmC,MAAM,EAAE;QACV,CAAE;QAAA5D,QAAA,gBAEFlH,OAAA;UAAK+G,KAAK,EAAE;YACV0D,UAAU,EAAE,0BAA0B;YACtCxD,OAAO,EAAE,MAAM;YAAE;YACjBgB,YAAY,EAAE,KAAK;YACnB6C,MAAM,EAAE;UACV,CAAE;UAAA5D,QAAA,eACAlH,OAAA,CAACG,SAAS;YAAC4G,KAAK,EAAE;cAChBI,QAAQ,EAAE,MAAM;cAAE;cAClB0D,UAAU,EAAE,GAAG;cAAE;cACjBzD,YAAY,EAAE,CAAC;cACf2D,UAAU,EAAE;YACd,CAAE;YAAA7D,QAAA,EACCvG,QAAQ,CAAC6F;UAAc;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENxH,OAAA;UAAK+G,KAAK,EAAE;YACV4B,SAAS,EAAE,MAAM;YACjB1B,OAAO,EAAE,MAAM;YACfwD,UAAU,EAAE,SAAS;YACrBxC,YAAY,EAAE,KAAK;YACnBd,QAAQ,EAAE,MAAM;YAChByB,KAAK,EAAE;UACT,CAAE;UAAA1B,QAAA,GAAC,eACE,eAAAlH,OAAA;YAAAkH,QAAA,EAAQ;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,sNAE7B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,EAGA7G,QAAQ,CAACgC,cAAc,KAAK,KAAK,iBAChC3C,OAAA,CAAC9B,IAAI;QACH+D,KAAK,eACHjC,OAAA,CAACpB,KAAK;UAAAsI,QAAA,gBACJlH,OAAA,CAACV,eAAe;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAErB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;QACDT,KAAK,EAAE;UACLkB,YAAY,EAAE,MAAM;UACpBU,SAAS,EAAE,MAAM;UACjBmC,MAAM,EAAE;QACV,CAAE;QAAA5D,QAAA,EAEDvG,QAAQ,CAACmC,gBAAgB,IAAInC,QAAQ,CAACmC,gBAAgB,KAAK,QAAQ,gBAClE9C,OAAA;UAAAkH,QAAA,gBACElH,OAAA,CAACG,SAAS;YAAC4G,KAAK,EAAE;cAChBI,QAAQ,EAAE,MAAM;cAAE;cAClB0D,UAAU,EAAE,GAAG;cAAE;cACjBJ,UAAU,EAAE,yBAAyB;cACrCxD,OAAO,EAAE,MAAM;cAAE;cACjBgB,YAAY,EAAE,KAAK;cACnB6C,MAAM,EAAE,kCAAkC;cAC1CC,UAAU,EAAE;YACd,CAAE;YAAA7D,QAAA,EACCvG,QAAQ,CAACmC;UAAgB;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAEZxH,OAAA;YAAK+G,KAAK,EAAE;cACV4B,SAAS,EAAE,MAAM;cACjB1B,OAAO,EAAE,MAAM;cACfwD,UAAU,EAAE,SAAS;cACrBxC,YAAY,EAAE,KAAK;cACnBd,QAAQ,EAAE,MAAM;cAChByB,KAAK,EAAE;YACT,CAAE;YAAA1B,QAAA,GAAC,eACE,eAAAlH,OAAA;cAAAkH,QAAA,EAAQ;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,8KAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENxH,OAAA;UAAK+G,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAS,CAAE;UAAAC,QAAA,gBACrDlH,OAAA;YAAK+G,KAAK,EAAE;cAAEI,QAAQ,EAAE,MAAM;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAF,QAAA,EAAC;UAExD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxH,OAAA,CAACE,IAAI;YAACuH,IAAI,EAAC,WAAW;YAAAP,QAAA,EAAC;UAEvB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CACP,EAGA7G,QAAQ,CAACgC,cAAc,KAAK,KAAK,IAAIhC,QAAQ,CAACkC,QAAQ,GAAG,GAAG,iBAC3D7C,OAAA,CAAC9B,IAAI;QACH+D,KAAK,EAAC,uCAAS;QACf8E,KAAK,EAAE;UACLkB,YAAY,EAAE,MAAM;UACpBU,SAAS,EAAE,MAAM;UACjBmC,MAAM,EAAE;QACV,CAAE;QAAA5D,QAAA,eAEFlH,OAAA,CAACtB,IAAI;UACHqK,IAAI,EAAC,OAAO;UACZiC,UAAU,EAAE,CACV,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,aAAa,CACb;UACFC,UAAU,EAAEA,CAACC,IAAI,EAAEvH,KAAK,kBACtB3D,OAAA,CAACtB,IAAI,CAACgL,IAAI;YAAAxC,QAAA,eACRlH,OAAA,CAACE,IAAI;cAAAgH,QAAA,gBACHlH,OAAA;gBAAM+G,KAAK,EAAE;kBACXY,OAAO,EAAE,cAAc;kBACvBU,KAAK,EAAE,MAAM;kBACb8C,MAAM,EAAE,MAAM;kBACdV,UAAU,EAAE,SAAS;kBACrB7B,KAAK,EAAE,OAAO;kBACdX,YAAY,EAAE,KAAK;kBACnBjB,SAAS,EAAE,QAAQ;kBACnB6D,UAAU,EAAE,MAAM;kBAClB1D,QAAQ,EAAE,MAAM;kBAChBwD,WAAW,EAAE;gBACf,CAAE;gBAAAzD,QAAA,EACCvD,KAAK,GAAG;cAAC;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EACN0D,IAAI;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eAGDxH,OAAA,CAAC9B,IAAI;QAAC6I,KAAK,EAAE;UAAEkB,YAAY,EAAE,MAAM;UAAEU,SAAS,EAAE;QAAO,CAAE;QAAAzB,QAAA,eACvDlH,OAAA,CAAChB,IAAI;UACHoM,SAAS,EAAEjK,YAAa;UACxBkK,QAAQ,EAAEjK,eAAgB;UAC1B2H,IAAI,EAAC,OAAO;UACZuC,WAAW,EAAE;YAAElE,YAAY,EAAE;UAAO,CAAE;UAAAF,QAAA,gBAEtClH,OAAA,CAACI,OAAO;YAACmL,GAAG,EAAC,uCAAS;YAAArE,QAAA,eACpBlH,OAAA;cAAK+G,KAAK,EAAE;gBAAEE,OAAO,EAAE;cAAQ,CAAE;cAAAC,QAAA,eAC/BlH,OAAA,CAACE,IAAI;gBAACuH,IAAI,EAAC,WAAW;gBAACV,KAAK,EAAE;kBAAEI,QAAQ,EAAE;gBAAO,CAAE;gBAAAD,QAAA,EAAC;cAEpD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GALmB,MAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMxB,CAAC,eAEVxH,OAAA,CAACI,OAAO;YAACmL,GAAG,EAAC,uCAAS;YAAArE,QAAA,EACnBJ,YAAY,CAAC;UAAC,GADU,QAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE1B,CAAC,eAEVxH,OAAA,CAACI,OAAO;YAACmL,GAAG,EAAC,uCAAS;YAAArE,QAAA,EACnB2B,qBAAqB,CAAC;UAAC,GADC,aAAa;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPxH,OAAA;QAAK+G,KAAK,EAAE;UACVC,SAAS,EAAE,QAAQ;UACnB2B,SAAS,EAAE,MAAM;UACjB1B,OAAO,EAAE,MAAM;UACfwD,UAAU,EAAE,SAAS;UACrBxC,YAAY,EAAE;QAChB,CAAE;QAAAf,QAAA,eACAlH,OAAA,CAACpB,KAAK;UAACmK,IAAI,EAAC,OAAO;UAAA7B,QAAA,gBACjBlH,OAAA,CAAC1B,MAAM;YACLyK,IAAI,EAAC,OAAO;YACZI,OAAO,EAAEA,CAAA,KAAM1I,QAAQ,CAAC,kBAAkB,CAAE;YAAAyG,QAAA,EAC7C;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxH,OAAA,CAAC1B,MAAM;YACLmJ,IAAI,EAAC,SAAS;YACdsB,IAAI,EAAC,OAAO;YACZI,OAAO,EAAEA,CAAA,KAAM1I,QAAQ,CAAC,WAAW,CAAE;YAAAyG,QAAA,EACtC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxH,OAAA,CAAC1B,MAAM;YACLyK,IAAI,EAAC,OAAO;YACZI,OAAO,EAAEA,CAAA,KAAM1I,QAAQ,CAAC,WAAW,CAAE;YAAAyG,QAAA,EACtC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjH,EAAA,CAh0BIF,qBAAqB;EAAA,QACFtC,SAAS,EACfC,WAAW,EACXC,WAAW;AAAA;AAAAuN,EAAA,GAHxBnL,qBAAqB;AAk0B3B,eAAeA,qBAAqB;AAAC,IAAAmL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}