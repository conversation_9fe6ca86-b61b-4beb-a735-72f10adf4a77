{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { devUseWarning } from '../../../_util/warning';\nimport { getColumnKey, getColumnPos, renderColumnTitle } from '../../util';\nimport FilterDropdown, { flattenKeys } from './FilterDropdown';\nconst collectFilterStates = (columns, init, pos) => {\n  let filterStates = [];\n  (columns || []).forEach((column, index) => {\n    var _a;\n    const columnPos = getColumnPos(index, pos);\n    const filterDropdownIsDefined = column.filterDropdown !== undefined;\n    if (column.filters || filterDropdownIsDefined || 'onFilter' in column) {\n      if ('filteredValue' in column) {\n        // Controlled\n        let filteredValues = column.filteredValue;\n        if (!filterDropdownIsDefined) {\n          filteredValues = (_a = filteredValues === null || filteredValues === void 0 ? void 0 : filteredValues.map(String)) !== null && _a !== void 0 ? _a : filteredValues;\n        }\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: filteredValues,\n          forceFiltered: column.filtered\n        });\n      } else {\n        // Uncontrolled\n        filterStates.push({\n          column,\n          key: getColumnKey(column, columnPos),\n          filteredKeys: init && column.defaultFilteredValue ? column.defaultFilteredValue : undefined,\n          forceFiltered: column.filtered\n        });\n      }\n    }\n    if ('children' in column) {\n      filterStates = [].concat(_toConsumableArray(filterStates), _toConsumableArray(collectFilterStates(column.children, init, columnPos)));\n    }\n  });\n  return filterStates;\n};\nfunction injectFilter(prefixCls, dropdownPrefixCls, columns, filterStates, locale, triggerFilter, getPopupContainer, pos, rootClassName) {\n  return columns.map((column, index) => {\n    const columnPos = getColumnPos(index, pos);\n    const {\n      filterOnClose = true,\n      filterMultiple = true,\n      filterMode,\n      filterSearch\n    } = column;\n    let newColumn = column;\n    if (newColumn.filters || newColumn.filterDropdown) {\n      const columnKey = getColumnKey(newColumn, columnPos);\n      const filterState = filterStates.find(({\n        key\n      }) => columnKey === key);\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        title: renderProps => (/*#__PURE__*/React.createElement(FilterDropdown, {\n          tablePrefixCls: prefixCls,\n          prefixCls: `${prefixCls}-filter`,\n          dropdownPrefixCls: dropdownPrefixCls,\n          column: newColumn,\n          columnKey: columnKey,\n          filterState: filterState,\n          filterOnClose: filterOnClose,\n          filterMultiple: filterMultiple,\n          filterMode: filterMode,\n          filterSearch: filterSearch,\n          triggerFilter: triggerFilter,\n          locale: locale,\n          getPopupContainer: getPopupContainer,\n          rootClassName: rootClassName\n        }, renderColumnTitle(column.title, renderProps)))\n      });\n    }\n    if ('children' in newColumn) {\n      newColumn = Object.assign(Object.assign({}, newColumn), {\n        children: injectFilter(prefixCls, dropdownPrefixCls, newColumn.children, filterStates, locale, triggerFilter, getPopupContainer, columnPos, rootClassName)\n      });\n    }\n    return newColumn;\n  });\n}\nconst generateFilterInfo = filterStates => {\n  const currentFilters = {};\n  filterStates.forEach(({\n    key,\n    filteredKeys,\n    column\n  }) => {\n    const keyAsString = key;\n    const {\n      filters,\n      filterDropdown\n    } = column;\n    if (filterDropdown) {\n      currentFilters[keyAsString] = filteredKeys || null;\n    } else if (Array.isArray(filteredKeys)) {\n      const keys = flattenKeys(filters);\n      currentFilters[keyAsString] = keys.filter(originKey => filteredKeys.includes(String(originKey)));\n    } else {\n      currentFilters[keyAsString] = null;\n    }\n  });\n  return currentFilters;\n};\nexport const getFilterData = (data, filterStates, childrenColumnName) => {\n  const filterDatas = filterStates.reduce((currentData, filterState) => {\n    const {\n      column: {\n        onFilter,\n        filters\n      },\n      filteredKeys\n    } = filterState;\n    if (onFilter && filteredKeys && filteredKeys.length) {\n      return currentData\n      // shallow copy\n      .map(record => Object.assign({}, record)).filter(record => filteredKeys.some(key => {\n        const keys = flattenKeys(filters);\n        const keyIndex = keys.findIndex(k => String(k) === String(key));\n        const realKey = keyIndex !== -1 ? keys[keyIndex] : key;\n        // filter children\n        if (record[childrenColumnName]) {\n          record[childrenColumnName] = getFilterData(record[childrenColumnName], filterStates, childrenColumnName);\n        }\n        return onFilter(realKey, record);\n      }));\n    }\n    return currentData;\n  }, data);\n  return filterDatas;\n};\nconst getMergedColumns = rawMergedColumns => rawMergedColumns.flatMap(column => {\n  if ('children' in column) {\n    return [column].concat(_toConsumableArray(getMergedColumns(column.children || [])));\n  }\n  return [column];\n});\nconst useFilter = props => {\n  const {\n    prefixCls,\n    dropdownPrefixCls,\n    mergedColumns: rawMergedColumns,\n    onFilterChange,\n    getPopupContainer,\n    locale: tableLocale,\n    rootClassName\n  } = props;\n  const warning = devUseWarning('Table');\n  const mergedColumns = React.useMemo(() => getMergedColumns(rawMergedColumns || []), [rawMergedColumns]);\n  const [filterStates, setFilterStates] = React.useState(() => collectFilterStates(mergedColumns, true));\n  const mergedFilterStates = React.useMemo(() => {\n    const collectedStates = collectFilterStates(mergedColumns, false);\n    if (collectedStates.length === 0) {\n      return collectedStates;\n    }\n    let filteredKeysIsAllNotControlled = true;\n    let filteredKeysIsAllControlled = true;\n    collectedStates.forEach(({\n      filteredKeys\n    }) => {\n      if (filteredKeys !== undefined) {\n        filteredKeysIsAllNotControlled = false;\n      } else {\n        filteredKeysIsAllControlled = false;\n      }\n    });\n    // Return if not controlled\n    if (filteredKeysIsAllNotControlled) {\n      // Filter column may have been removed\n      const keyList = (mergedColumns || []).map((column, index) => getColumnKey(column, getColumnPos(index)));\n      return filterStates.filter(({\n        key\n      }) => keyList.includes(key)).map(item => {\n        const col = mergedColumns[keyList.findIndex(key => key === item.key)];\n        return Object.assign(Object.assign({}, item), {\n          column: Object.assign(Object.assign({}, item.column), col),\n          forceFiltered: col.filtered\n        });\n      });\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(filteredKeysIsAllControlled, 'usage', 'Columns should all contain `filteredValue` or not contain `filteredValue`.') : void 0;\n    return collectedStates;\n  }, [mergedColumns, filterStates]);\n  const filters = React.useMemo(() => generateFilterInfo(mergedFilterStates), [mergedFilterStates]);\n  const triggerFilter = filterState => {\n    const newFilterStates = mergedFilterStates.filter(({\n      key\n    }) => key !== filterState.key);\n    newFilterStates.push(filterState);\n    setFilterStates(newFilterStates);\n    onFilterChange(generateFilterInfo(newFilterStates), newFilterStates);\n  };\n  const transformColumns = innerColumns => injectFilter(prefixCls, dropdownPrefixCls, innerColumns, mergedFilterStates, tableLocale, triggerFilter, getPopupContainer, undefined, rootClassName);\n  return [transformColumns, mergedFilterStates, filters];\n};\nexport { flattenKeys };\nexport default useFilter;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}