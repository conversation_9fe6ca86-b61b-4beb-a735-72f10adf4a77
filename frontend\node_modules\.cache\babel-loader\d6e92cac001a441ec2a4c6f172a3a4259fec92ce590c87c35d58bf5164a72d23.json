{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\StudentHomeworkAssignmentList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Button, Space, message, Spin, Alert, Typography, Row, Col, Empty } from 'antd';\nimport { UploadOutlined, CalendarOutlined, TrophyOutlined, BookOutlined } from '@ant-design/icons';\nimport { getStudentHomeworkAssignments } from '../utils/api';\nimport StudentCard, { EmptyStateCard, StudentCardSkeleton } from './student/StudentCard';\nimport QuickActions, { QuickStats, LearningMotivation, FloatingQuickActions } from './student/QuickActions';\nimport SmartReminder from './student/SmartReminder';\nimport '../styles/student.css';\nimport moment from 'moment';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst StudentHomeworkAssignmentList = ({\n  user\n}) => {\n  _s();\n  const [assignments, setAssignments] = useState([]);\n  const [filteredAssignments, setFilteredAssignments] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({});\n  const [currentFilter, setCurrentFilter] = useState('all');\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取作业任务列表\n  const fetchAssignments = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const data = await getStudentHomeworkAssignments();\n      console.log('获取到的学生作业任务:', data);\n      setAssignments(data);\n      setFilteredAssignments(data);\n\n      // 计算统计数据\n      calculateStats(data);\n    } catch (error) {\n      console.error('获取作业任务失败:', error);\n      setError(error.message);\n      message.error(`获取作业任务失败: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 判断作业是否已结束（基于description中的状态标记）\n  const isAssignmentEnded = assignment => {\n    if (!assignment.description) return false;\n    // 检查description中是否包含【状态】finished】\n    return assignment.description.includes('【状态】finished】');\n  };\n\n  // 判断是否为补交作业（已结束但已提交的作业）\n  const isMakeupSubmission = assignment => {\n    return assignment.submission_status === '已提交' && isAssignmentEnded(assignment);\n  };\n\n  // 计算统计数据\n  const calculateStats = data => {\n    // 未提交的作业\n    const unsubmittedAssignments = data.filter(item => item.submission_status === '未提交');\n\n    // 待提交：未提交且未结束的作业\n    const pending = unsubmittedAssignments.filter(item => !isAssignmentEnded(item)).length;\n\n    // 未完成：未提交且已结束的作业\n    const incomplete = unsubmittedAssignments.filter(item => isAssignmentEnded(item)).length;\n\n    // 已提交的作业\n    const submittedAssignments = data.filter(item => item.submission_status === '已提交');\n\n    // 正常提交：已提交且未结束的作业\n    const submitted = submittedAssignments.filter(item => !isAssignmentEnded(item)).length;\n\n    // 已补交：已提交且已结束的作业\n    const makeup = submittedAssignments.filter(item => isAssignmentEnded(item)).length;\n    const graded = data.filter(item => item.grading_status === '已批改').length;\n\n    // 计算平均分\n    const gradedAssignments = data.filter(item => item.score !== null);\n    const avgScore = gradedAssignments.length > 0 ? Math.round(gradedAssignments.reduce((sum, item) => sum + item.score, 0) / gradedAssignments.length) : 0;\n    setStats({\n      pending,\n      incomplete,\n      submitted,\n      makeup,\n      graded,\n      avgScore,\n      total: data.length\n    });\n  };\n  useEffect(() => {\n    fetchAssignments();\n  }, []);\n\n  // 处理来自首页的过滤参数\n  useEffect(() => {\n    if (location.state && location.state.filter && assignments.length > 0) {\n      console.log('从首页接收到过滤参数:', location.state.filter);\n      filterAssignments(location.state.filter);\n      // 清除location.state，避免重复触发\n      window.history.replaceState({}, document.title);\n    }\n  }, [assignments, location.state]);\n\n  // 获取紧急作业数量\n  const getUrgentCount = () => {\n    return assignments.filter(assignment => {\n      if (assignment.submission_status === '已提交') return false;\n      if (!assignment.due_date) return false;\n      const now = moment();\n      const due = moment(assignment.due_date);\n      const daysDiff = due.diff(now, 'days');\n      return daysDiff <= 1 && daysDiff >= 0;\n    }).length;\n  };\n\n  // 处理上传作业\n  const handleUpload = assignment => {\n    console.log('handleUpload called with assignment:', assignment);\n    navigate('/homework/upload/single', {\n      state: {\n        assignmentId: assignment.id,\n        assignmentTitle: assignment.title\n      }\n    });\n  };\n\n  // 处理查看详情\n  const handleViewDetails = assignment => {\n    if (assignment.homework_id) {\n      navigate(`/homework/${assignment.homework_id}`);\n    }\n  };\n\n  // 快速操作处理函数\n  const handleQuickUpload = () => {\n    const pendingAssignments = assignments.filter(a => a.submission_status === '未提交');\n    if (pendingAssignments.length > 0) {\n      handleUpload(pendingAssignments[0]); // 跳转到第一个待提交的作业\n    } else {\n      message.info('暂无待提交的作业');\n    }\n  };\n  const handleViewHomeworks = filter => {\n    if (filter === 'urgent') {\n      // 可以添加筛选逻辑\n      message.info('显示紧急作业');\n    } else {\n      navigate('/homework/history');\n    }\n  };\n  const handleViewCalendar = () => {\n    navigate('/homework/calendar');\n  };\n  const handleViewAchievements = () => {\n    // 学生用户跳转到个人中心，教师和管理员跳转到统计页面\n    if (user && (user.is_teacher || user.is_admin)) {\n      navigate('/profile/statistics');\n    } else {\n      navigate('/profile');\n    }\n  };\n\n  // 处理通知导航\n  const handleNotificationNavigate = (path, options) => {\n    navigate(path, options);\n  };\n\n  // 过滤作业任务\n  const filterAssignments = filter => {\n    let filtered = assignments;\n    switch (filter) {\n      case 'pending':\n        // 待提交：未提交且未结束的作业\n        filtered = assignments.filter(a => a.submission_status === '未提交' && !isAssignmentEnded(a));\n        break;\n      case 'incomplete':\n        // 未完成：未提交且已结束的作业\n        filtered = assignments.filter(a => a.submission_status === '未提交' && isAssignmentEnded(a));\n        break;\n      case 'submitted':\n        // 正常提交：已提交且未结束的作业\n        filtered = assignments.filter(a => a.submission_status === '已提交' && !isAssignmentEnded(a));\n        break;\n      case 'makeup':\n        // 已补交：已提交且已结束的作业\n        filtered = assignments.filter(a => a.submission_status === '已提交' && isAssignmentEnded(a));\n        break;\n      case 'graded':\n        filtered = assignments.filter(a => a.grading_status === '已批改');\n        break;\n      case 'all':\n      default:\n        filtered = assignments;\n        break;\n    }\n    setFilteredAssignments(filtered);\n    setCurrentFilter(filter);\n  };\n\n  // 处理统计卡片点击\n  const handleStatClick = statKey => {\n    console.log('Stat clicked:', statKey);\n    filterAssignments(statKey);\n  };\n\n  // 重置过滤器\n  const resetFilter = () => {\n    filterAssignments('all');\n  };\n\n  // 获取过滤器标签\n  const getFilterLabel = filter => {\n    const labels = {\n      pending: '待提交作业',\n      incomplete: '未完成作业',\n      submitted: '已提交作业',\n      makeup: '已补交作业',\n      graded: '已批改作业',\n      all: '全部作业'\n    };\n    return labels[filter] || '全部作业';\n  };\n\n  // 渲染加载状态\n  const renderLoading = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"student-page\",\n    children: [/*#__PURE__*/_jsxDEV(QuickStats, {\n      stats: stats\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: [1, 2, 3, 4, 5, 6].map(i => /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(StudentCardSkeleton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 5\n  }, this);\n\n  // 渲染空状态\n  const renderEmpty = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"student-page\",\n    children: [/*#__PURE__*/_jsxDEV(QuickStats, {\n      stats: stats\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [24, 24],\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(EmptyStateCard, {\n          title: \"\\u6682\\u65E0\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n          description: \"\\u8001\\u5E08\\u8FD8\\u6CA1\\u6709\\u53D1\\u5E03\\u4F5C\\u4E1A\\u4EFB\\u52A1\\uFF0C\\u8BF7\\u8010\\u5FC3\\u7B49\\u5F85\\u3002\\u4F60\\u53EF\\u4EE5\\u5148\\u67E5\\u770B\\u4E4B\\u524D\\u7684\\u4F5C\\u4E1A\\u6216\\u8005\\u6D4F\\u89C8\\u5B66\\u4E60\\u8D44\\u6599\\u3002\",\n          icon: \"\\uD83D\\uDCDA\",\n          action: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 25\n              }, this),\n              onClick: () => navigate('/homework/history'),\n              children: \"\\u67E5\\u770B\\u5386\\u53F2\\u4F5C\\u4E1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 25\n              }, this),\n              onClick: handleViewCalendar,\n              children: \"\\u4F5C\\u4E1A\\u65E5\\u5386\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n\n  // 错误状态\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-page\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\uD83D\\uDE14 \\u52A0\\u8F7D\\u5931\\u8D25\",\n        description: error,\n        type: \"error\",\n        showIcon: true,\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: fetchAssignments,\n          children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this),\n        style: {\n          borderRadius: '12px',\n          marginBottom: '24px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 加载状态\n  if (loading) {\n    return renderLoading();\n  }\n\n  // 空状态\n  if (assignments.length === 0) {\n    return renderEmpty();\n  }\n  const urgentCount = getUrgentCount();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"student-interface\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-page\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-page-title\",\n        children: \"\\uD83D\\uDCDA \\u6211\\u7684\\u4F5C\\u4E1A\\u4EFB\\u52A1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-page-description\",\n        children: \"\\u7BA1\\u7406\\u4F60\\u7684\\u4F5C\\u4E1A\\u4EFB\\u52A1\\uFF0C\\u8DDF\\u8E2A\\u5B66\\u4E60\\u8FDB\\u5EA6\\uFF0C\\u8BA9\\u5B66\\u4E60\\u66F4\\u9AD8\\u6548\\uFF01\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LearningMotivation, {\n        streakDays: 7,\n        weeklyGoal: 5,\n        weeklyCompleted: stats.submitted || 0\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuickStats, {\n        stats: stats,\n        onStatClick: handleStatClick,\n        activeFilter: currentFilter\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n        pendingCount: stats.pending || 0,\n        urgentCount: urgentCount,\n        onUploadHomework: handleQuickUpload,\n        onViewHomeworks: handleViewHomeworks,\n        onViewCalendar: handleViewCalendar,\n        onViewAchievements: handleViewAchievements,\n        style: {\n          marginBottom: '32px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography.Title, {\n            level: 4,\n            style: {\n              margin: 0\n            },\n            children: [\"\\uD83D\\uDCCB \\u4F5C\\u4E1A\\u5217\\u8868 (\", filteredAssignments.length, \")\", currentFilter !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '14px',\n                fontWeight: 'normal',\n                color: '#666666',\n                marginLeft: '8px'\n              },\n              children: [\"- \", getFilterLabel(currentFilter)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), currentFilter !== 'all' && /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            onClick: resetFilter,\n            style: {\n              padding: 0\n            },\n            children: \"\\u663E\\u793A\\u5168\\u90E8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), filteredAssignments.length > 0 ? /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: filteredAssignments.map(assignment => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            lg: 8,\n            children: /*#__PURE__*/_jsxDEV(StudentCard, {\n              assignment: assignment,\n              onUpload: handleUpload,\n              onViewDetails: handleViewDetails\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 19\n            }, this)\n          }, assignment.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '60px 20px',\n            background: 'white',\n            borderRadius: '12px',\n            border: '2px dashed #E8E8E8'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '48px',\n              marginBottom: '16px'\n            },\n            children: [currentFilter === 'pending' && '📝', currentFilter === 'incomplete' && '⚠️', currentFilter === 'submitted' && '✅', currentFilter === 'makeup' && '🔄', currentFilter === 'graded' && '⭐', currentFilter === 'all' && '📚']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography.Title, {\n            level: 4,\n            style: {\n              color: '#666666',\n              marginBottom: '8px'\n            },\n            children: [\"\\u6682\\u65E0\", getFilterLabel(currentFilter)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography.Text, {\n            style: {\n              color: '#999999'\n            },\n            children: [currentFilter === 'pending' && '所有作业都已提交，真棒！', currentFilter === 'incomplete' && '太好了！没有逾期未完成的作业', currentFilter === 'submitted' && '还没有正常提交的作业', currentFilter === 'makeup' && '还没有补交的作业', currentFilter === 'graded' && '还没有已批改的作业', currentFilter === 'all' && '暂时没有作业任务']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), currentFilter === 'all' && stats.pending > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 149, 0, 0.1)',\n          border: '1px solid rgba(255, 149, 0, 0.3)',\n          borderRadius: '12px',\n          padding: '16px',\n          textAlign: 'center',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          style: {\n            color: '#FF9500',\n            fontWeight: 500\n          },\n          children: [\"\\u23F0 \\u4F60\\u8FD8\\u6709 \", stats.pending, \" \\u4EFD\\u4F5C\\u4E1A\\u5F85\\u63D0\\u4EA4\\uFF0C\\u52A0\\u6CB9\\u5B8C\\u6210\\u5427\\uFF01\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this), currentFilter === 'pending' && filteredAssignments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 149, 0, 0.1)',\n          border: '1px solid rgba(255, 149, 0, 0.3)',\n          borderRadius: '12px',\n          padding: '16px',\n          textAlign: 'center',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          style: {\n            color: '#FF9500',\n            fontWeight: 500\n          },\n          children: \"\\uD83D\\uDCDD \\u8FD9\\u91CC\\u662F\\u6240\\u6709\\u5F85\\u63D0\\u4EA4\\u7684\\u4F5C\\u4E1A\\uFF0C\\u6293\\u7D27\\u65F6\\u95F4\\u5B8C\\u6210\\u5427\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 11\n      }, this), currentFilter === 'incomplete' && filteredAssignments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(255, 59, 48, 0.1)',\n          border: '1px solid rgba(255, 59, 48, 0.3)',\n          borderRadius: '12px',\n          padding: '16px',\n          textAlign: 'center',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          style: {\n            color: '#FF3B30',\n            fontWeight: 500\n          },\n          children: \"\\u26A0\\uFE0F \\u8FD9\\u4E9B\\u4F5C\\u4E1A\\u5DF2\\u7ECF\\u622A\\u6B62\\u4F46\\u5C1A\\u672A\\u63D0\\u4EA4\\uFF0C\\u5EFA\\u8BAE\\u8054\\u7CFB\\u8001\\u5E08\\u4E86\\u89E3\\u8865\\u4EA4\\u653F\\u7B56\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 11\n      }, this), currentFilter === 'submitted' && filteredAssignments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(52, 199, 89, 0.1)',\n          border: '1px solid rgba(52, 199, 89, 0.3)',\n          borderRadius: '12px',\n          padding: '16px',\n          textAlign: 'center',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          style: {\n            color: '#34C759',\n            fontWeight: 500\n          },\n          children: \"\\u2705 \\u8FD9\\u4E9B\\u662F\\u4F60\\u6B63\\u5E38\\u63D0\\u4EA4\\u7684\\u4F5C\\u4E1A\\uFF0C\\u7B49\\u5F85\\u8001\\u5E08\\u6279\\u6539\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 11\n      }, this), currentFilter === 'makeup' && filteredAssignments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(175, 82, 222, 0.1)',\n          border: '1px solid rgba(175, 82, 222, 0.3)',\n          borderRadius: '12px',\n          padding: '16px',\n          textAlign: 'center',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          style: {\n            color: '#AF52DE',\n            fontWeight: 500\n          },\n          children: \"\\uD83D\\uDD04 \\u8FD9\\u4E9B\\u662F\\u4F60\\u8865\\u4EA4\\u7684\\u4F5C\\u4E1A\\uFF0C\\u867D\\u7136\\u903E\\u671F\\u4F46\\u5DF2\\u6210\\u529F\\u63D0\\u4EA4\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 11\n      }, this), currentFilter === 'graded' && filteredAssignments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'rgba(48, 209, 88, 0.1)',\n          border: '1px solid rgba(48, 209, 88, 0.3)',\n          borderRadius: '12px',\n          padding: '16px',\n          textAlign: 'center',\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography.Text, {\n          style: {\n            color: '#30D158',\n            fontWeight: 500\n          },\n          children: \"\\u2B50 \\u8FD9\\u4E9B\\u662F\\u5DF2\\u7ECF\\u6279\\u6539\\u5B8C\\u6210\\u7684\\u4F5C\\u4E1A\\uFF0C\\u70B9\\u51FB\\\"\\u8BE6\\u60C5\\\"\\u67E5\\u770B\\u6279\\u6539\\u7ED3\\u679C\\uFF01\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(SmartReminder, {\n        assignments: assignments,\n        user: user,\n        onNavigate: handleNotificationNavigate,\n        style: {\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          zIndex: 1000\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingQuickActions, {\n        onUploadHomework: handleQuickUpload,\n        onViewNotifications: () => {},\n        notificationCount: urgentCount\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 334,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentHomeworkAssignmentList, \"251YP0XTpuDijVlhit1hGwHQ4jM=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = StudentHomeworkAssignmentList;\nexport default StudentHomeworkAssignmentList;\nvar _c;\n$RefreshReg$(_c, \"StudentHomeworkAssignmentList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "<PERSON><PERSON>", "Space", "message", "Spin", "<PERSON><PERSON>", "Typography", "Row", "Col", "Empty", "UploadOutlined", "CalendarOutlined", "TrophyOutlined", "BookOutlined", "getStudentHomeworkAssignments", "StudentCard", "EmptyStateCard", "StudentCardSkeleton", "QuickActions", "QuickStats", "LearningMotivation", "FloatingQuickActions", "SmartReminder", "moment", "jsxDEV", "_jsxDEV", "Title", "Text", "StudentHomeworkAssignmentList", "user", "_s", "assignments", "setAssignments", "filteredAssignments", "setFilteredAssignments", "loading", "setLoading", "error", "setError", "stats", "setStats", "currentFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "location", "fetchAssignments", "data", "console", "log", "calculateStats", "isAssignmentEnded", "assignment", "description", "includes", "isMakeupSubmission", "submission_status", "unsubmittedAssignments", "filter", "item", "pending", "length", "incomplete", "submittedAssignments", "submitted", "makeup", "graded", "grading_status", "gradedAssignments", "score", "avgScore", "Math", "round", "reduce", "sum", "total", "state", "filterAssignments", "window", "history", "replaceState", "document", "title", "getUrgentCount", "due_date", "now", "due", "daysDiff", "diff", "handleUpload", "assignmentId", "id", "assignmentTitle", "handleViewDetails", "homework_id", "handleQuickUpload", "pendingAssignments", "a", "info", "handleViewHomeworks", "handleViewCalendar", "handleViewAchievements", "is_teacher", "is_admin", "handleNotificationNavigate", "path", "options", "filtered", "handleStatClick", "statKey", "resetFilter", "getFilterLabel", "labels", "all", "renderLoading", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "map", "i", "xs", "sm", "lg", "renderEmpty", "span", "icon", "action", "type", "onClick", "showIcon", "style", "borderRadius", "marginBottom", "urgentCount", "streakDays", "weeklyGoal", "weeklyCompleted", "onStatClick", "activeFilter", "pendingCount", "onUploadHomework", "onViewHomeworks", "onViewCalendar", "onViewAchievements", "display", "justifyContent", "alignItems", "level", "margin", "fontSize", "fontWeight", "color", "marginLeft", "padding", "onUpload", "onViewDetails", "textAlign", "background", "border", "marginTop", "onNavigate", "position", "top", "right", "zIndex", "onViewNotifications", "notificationCount", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/StudentHomeworkAssignmentList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Button, Space, message, Spin, Alert, Typography, Row, Col, Empty\n} from 'antd';\nimport {\n  UploadOutlined, CalendarOutlined, TrophyOutlined, BookOutlined\n} from '@ant-design/icons';\nimport { getStudentHomeworkAssignments } from '../utils/api';\nimport StudentCard, { EmptyStateCard, StudentCardSkeleton } from './student/StudentCard';\nimport QuickActions, { QuickStats, LearningMotivation, FloatingQuickActions } from './student/QuickActions';\nimport SmartReminder from './student/SmartReminder';\nimport '../styles/student.css';\nimport moment from 'moment';\n\nconst { Title, Text } = Typography;\n\nconst StudentHomeworkAssignmentList = ({ user }) => {\n  const [assignments, setAssignments] = useState([]);\n  const [filteredAssignments, setFilteredAssignments] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [stats, setStats] = useState({});\n  const [currentFilter, setCurrentFilter] = useState('all');\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取作业任务列表\n  const fetchAssignments = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const data = await getStudentHomeworkAssignments();\n      console.log('获取到的学生作业任务:', data);\n      setAssignments(data);\n      setFilteredAssignments(data);\n\n      // 计算统计数据\n      calculateStats(data);\n\n    } catch (error) {\n      console.error('获取作业任务失败:', error);\n      setError(error.message);\n      message.error(`获取作业任务失败: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 判断作业是否已结束（基于description中的状态标记）\n  const isAssignmentEnded = (assignment) => {\n    if (!assignment.description) return false;\n    // 检查description中是否包含【状态】finished】\n    return assignment.description.includes('【状态】finished】');\n  };\n\n  // 判断是否为补交作业（已结束但已提交的作业）\n  const isMakeupSubmission = (assignment) => {\n    return assignment.submission_status === '已提交' && isAssignmentEnded(assignment);\n  };\n\n  // 计算统计数据\n  const calculateStats = (data) => {\n    // 未提交的作业\n    const unsubmittedAssignments = data.filter(item => item.submission_status === '未提交');\n\n    // 待提交：未提交且未结束的作业\n    const pending = unsubmittedAssignments.filter(item => !isAssignmentEnded(item)).length;\n\n    // 未完成：未提交且已结束的作业\n    const incomplete = unsubmittedAssignments.filter(item => isAssignmentEnded(item)).length;\n\n    // 已提交的作业\n    const submittedAssignments = data.filter(item => item.submission_status === '已提交');\n\n    // 正常提交：已提交且未结束的作业\n    const submitted = submittedAssignments.filter(item => !isAssignmentEnded(item)).length;\n\n    // 已补交：已提交且已结束的作业\n    const makeup = submittedAssignments.filter(item => isAssignmentEnded(item)).length;\n\n    const graded = data.filter(item => item.grading_status === '已批改').length;\n\n    // 计算平均分\n    const gradedAssignments = data.filter(item => item.score !== null);\n    const avgScore = gradedAssignments.length > 0\n      ? Math.round(gradedAssignments.reduce((sum, item) => sum + item.score, 0) / gradedAssignments.length)\n      : 0;\n\n    setStats({\n      pending,\n      incomplete,\n      submitted,\n      makeup,\n      graded,\n      avgScore,\n      total: data.length\n    });\n  };\n\n  useEffect(() => {\n    fetchAssignments();\n  }, []);\n\n  // 处理来自首页的过滤参数\n  useEffect(() => {\n    if (location.state && location.state.filter && assignments.length > 0) {\n      console.log('从首页接收到过滤参数:', location.state.filter);\n      filterAssignments(location.state.filter);\n      // 清除location.state，避免重复触发\n      window.history.replaceState({}, document.title);\n    }\n  }, [assignments, location.state]);\n\n  // 获取紧急作业数量\n  const getUrgentCount = () => {\n    return assignments.filter(assignment => {\n      if (assignment.submission_status === '已提交') return false;\n      if (!assignment.due_date) return false;\n\n      const now = moment();\n      const due = moment(assignment.due_date);\n      const daysDiff = due.diff(now, 'days');\n\n      return daysDiff <= 1 && daysDiff >= 0;\n    }).length;\n  };\n\n  // 处理上传作业\n  const handleUpload = (assignment) => {\n    console.log('handleUpload called with assignment:', assignment);\n    navigate('/homework/upload/single', {\n      state: {\n        assignmentId: assignment.id,\n        assignmentTitle: assignment.title\n      }\n    });\n  };\n\n  // 处理查看详情\n  const handleViewDetails = (assignment) => {\n    if (assignment.homework_id) {\n      navigate(`/homework/${assignment.homework_id}`);\n    }\n  };\n\n  // 快速操作处理函数\n  const handleQuickUpload = () => {\n    const pendingAssignments = assignments.filter(a => a.submission_status === '未提交');\n    if (pendingAssignments.length > 0) {\n      handleUpload(pendingAssignments[0]); // 跳转到第一个待提交的作业\n    } else {\n      message.info('暂无待提交的作业');\n    }\n  };\n\n  const handleViewHomeworks = (filter) => {\n    if (filter === 'urgent') {\n      // 可以添加筛选逻辑\n      message.info('显示紧急作业');\n    } else {\n      navigate('/homework/history');\n    }\n  };\n\n  const handleViewCalendar = () => {\n    navigate('/homework/calendar');\n  };\n\n  const handleViewAchievements = () => {\n    // 学生用户跳转到个人中心，教师和管理员跳转到统计页面\n    if (user && (user.is_teacher || user.is_admin)) {\n      navigate('/profile/statistics');\n    } else {\n      navigate('/profile');\n    }\n  };\n\n  // 处理通知导航\n  const handleNotificationNavigate = (path, options) => {\n    navigate(path, options);\n  };\n\n  // 过滤作业任务\n  const filterAssignments = (filter) => {\n    let filtered = assignments;\n\n    switch (filter) {\n      case 'pending':\n        // 待提交：未提交且未结束的作业\n        filtered = assignments.filter(a =>\n          a.submission_status === '未提交' && !isAssignmentEnded(a)\n        );\n        break;\n      case 'incomplete':\n        // 未完成：未提交且已结束的作业\n        filtered = assignments.filter(a =>\n          a.submission_status === '未提交' && isAssignmentEnded(a)\n        );\n        break;\n      case 'submitted':\n        // 正常提交：已提交且未结束的作业\n        filtered = assignments.filter(a =>\n          a.submission_status === '已提交' && !isAssignmentEnded(a)\n        );\n        break;\n      case 'makeup':\n        // 已补交：已提交且已结束的作业\n        filtered = assignments.filter(a =>\n          a.submission_status === '已提交' && isAssignmentEnded(a)\n        );\n        break;\n      case 'graded':\n        filtered = assignments.filter(a => a.grading_status === '已批改');\n        break;\n      case 'all':\n      default:\n        filtered = assignments;\n        break;\n    }\n\n    setFilteredAssignments(filtered);\n    setCurrentFilter(filter);\n  };\n\n  // 处理统计卡片点击\n  const handleStatClick = (statKey) => {\n    console.log('Stat clicked:', statKey);\n    filterAssignments(statKey);\n  };\n\n  // 重置过滤器\n  const resetFilter = () => {\n    filterAssignments('all');\n  };\n\n  // 获取过滤器标签\n  const getFilterLabel = (filter) => {\n    const labels = {\n      pending: '待提交作业',\n      incomplete: '未完成作业',\n      submitted: '已提交作业',\n      makeup: '已补交作业',\n      graded: '已批改作业',\n      all: '全部作业'\n    };\n    return labels[filter] || '全部作业';\n  };\n\n  // 渲染加载状态\n  const renderLoading = () => (\n    <div className=\"student-page\">\n      <QuickStats stats={stats} />\n      <Row gutter={[24, 24]}>\n        {[1, 2, 3, 4, 5, 6].map(i => (\n          <Col key={i} xs={24} sm={12} lg={8}>\n            <StudentCardSkeleton />\n          </Col>\n        ))}\n      </Row>\n    </div>\n  );\n\n  // 渲染空状态\n  const renderEmpty = () => (\n    <div className=\"student-page\">\n      <QuickStats stats={stats} />\n      <Row gutter={[24, 24]}>\n        <Col span={24}>\n          <EmptyStateCard\n            title=\"暂无作业任务\"\n            description=\"老师还没有发布作业任务，请耐心等待。你可以先查看之前的作业或者浏览学习资料。\"\n            icon=\"📚\"\n            action={\n              <Space>\n                <Button\n                  type=\"primary\"\n                  icon={<BookOutlined />}\n                  onClick={() => navigate('/homework/history')}\n                >\n                  查看历史作业\n                </Button>\n                <Button\n                  icon={<CalendarOutlined />}\n                  onClick={handleViewCalendar}\n                >\n                  作业日历\n                </Button>\n              </Space>\n            }\n          />\n        </Col>\n      </Row>\n    </div>\n  );\n\n  // 错误状态\n  if (error) {\n    return (\n      <div className=\"student-page\">\n        <Alert\n          message=\"😔 加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          action={\n            <Button type=\"primary\" onClick={fetchAssignments}>\n              重新加载\n            </Button>\n          }\n          style={{\n            borderRadius: '12px',\n            marginBottom: '24px'\n          }}\n        />\n      </div>\n    );\n  }\n\n  // 加载状态\n  if (loading) {\n    return renderLoading();\n  }\n\n  // 空状态\n  if (assignments.length === 0) {\n    return renderEmpty();\n  }\n\n  const urgentCount = getUrgentCount();\n\n  return (\n    <div className=\"student-interface\">\n      <div className=\"student-page\">\n        {/* 页面标题 */}\n        <div className=\"student-page-title\">\n          📚 我的作业任务\n        </div>\n        <div className=\"student-page-description\">\n          管理你的作业任务，跟踪学习进度，让学习更高效！\n        </div>\n\n        {/* 学习激励 */}\n        <LearningMotivation\n          streakDays={7}\n          weeklyGoal={5}\n          weeklyCompleted={stats.submitted || 0}\n        />\n\n        {/* 快速统计 */}\n        <QuickStats\n          stats={stats}\n          onStatClick={handleStatClick}\n          activeFilter={currentFilter}\n        />\n\n        {/* 快速操作 */}\n        <QuickActions\n          pendingCount={stats.pending || 0}\n          urgentCount={urgentCount}\n          onUploadHomework={handleQuickUpload}\n          onViewHomeworks={handleViewHomeworks}\n          onViewCalendar={handleViewCalendar}\n          onViewAchievements={handleViewAchievements}\n          style={{ marginBottom: '32px' }}\n        />\n\n        {/* 作业卡片网格 */}\n        <div style={{ marginBottom: '24px' }}>\n          <div style={{\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '16px'\n          }}>\n            <Typography.Title level={4} style={{ margin: 0 }}>\n              📋 作业列表 ({filteredAssignments.length})\n              {currentFilter !== 'all' && (\n                <span style={{\n                  fontSize: '14px',\n                  fontWeight: 'normal',\n                  color: '#666666',\n                  marginLeft: '8px'\n                }}>\n                  - {getFilterLabel(currentFilter)}\n                </span>\n              )}\n            </Typography.Title>\n\n            {currentFilter !== 'all' && (\n              <Button\n                type=\"link\"\n                onClick={resetFilter}\n                style={{ padding: 0 }}\n              >\n                显示全部\n              </Button>\n            )}\n          </div>\n\n          {filteredAssignments.length > 0 ? (\n            <Row gutter={[24, 24]}>\n              {filteredAssignments.map(assignment => (\n                <Col key={assignment.id} xs={24} sm={12} lg={8}>\n                  <StudentCard\n                    assignment={assignment}\n                    onUpload={handleUpload}\n                    onViewDetails={handleViewDetails}\n                  />\n                </Col>\n              ))}\n            </Row>\n          ) : (\n            <div style={{\n              textAlign: 'center',\n              padding: '60px 20px',\n              background: 'white',\n              borderRadius: '12px',\n              border: '2px dashed #E8E8E8'\n            }}>\n              <div style={{ fontSize: '48px', marginBottom: '16px' }}>\n                {currentFilter === 'pending' && '📝'}\n                {currentFilter === 'incomplete' && '⚠️'}\n                {currentFilter === 'submitted' && '✅'}\n                {currentFilter === 'makeup' && '🔄'}\n                {currentFilter === 'graded' && '⭐'}\n                {currentFilter === 'all' && '📚'}\n              </div>\n              <Typography.Title level={4} style={{ color: '#666666', marginBottom: '8px' }}>\n                暂无{getFilterLabel(currentFilter)}\n              </Typography.Title>\n              <Typography.Text style={{ color: '#999999' }}>\n                {currentFilter === 'pending' && '所有作业都已提交，真棒！'}\n                {currentFilter === 'incomplete' && '太好了！没有逾期未完成的作业'}\n                {currentFilter === 'submitted' && '还没有正常提交的作业'}\n                {currentFilter === 'makeup' && '还没有补交的作业'}\n                {currentFilter === 'graded' && '还没有已批改的作业'}\n                {currentFilter === 'all' && '暂时没有作业任务'}\n              </Typography.Text>\n            </div>\n          )}\n        </div>\n\n        {/* 底部提示 */}\n        {currentFilter === 'all' && stats.pending > 0 && (\n          <div style={{\n            background: 'rgba(255, 149, 0, 0.1)',\n            border: '1px solid rgba(255, 149, 0, 0.3)',\n            borderRadius: '12px',\n            padding: '16px',\n            textAlign: 'center',\n            marginTop: '24px'\n          }}>\n            <Typography.Text style={{ color: '#FF9500', fontWeight: 500 }}>\n              ⏰ 你还有 {stats.pending} 份作业待提交，加油完成吧！\n            </Typography.Text>\n          </div>\n        )}\n\n        {currentFilter === 'pending' && filteredAssignments.length > 0 && (\n          <div style={{\n            background: 'rgba(255, 149, 0, 0.1)',\n            border: '1px solid rgba(255, 149, 0, 0.3)',\n            borderRadius: '12px',\n            padding: '16px',\n            textAlign: 'center',\n            marginTop: '24px'\n          }}>\n            <Typography.Text style={{ color: '#FF9500', fontWeight: 500 }}>\n              📝 这里是所有待提交的作业，抓紧时间完成吧！\n            </Typography.Text>\n          </div>\n        )}\n\n        {currentFilter === 'incomplete' && filteredAssignments.length > 0 && (\n          <div style={{\n            background: 'rgba(255, 59, 48, 0.1)',\n            border: '1px solid rgba(255, 59, 48, 0.3)',\n            borderRadius: '12px',\n            padding: '16px',\n            textAlign: 'center',\n            marginTop: '24px'\n          }}>\n            <Typography.Text style={{ color: '#FF3B30', fontWeight: 500 }}>\n              ⚠️ 这些作业已经截止但尚未提交，建议联系老师了解补交政策\n            </Typography.Text>\n          </div>\n        )}\n\n        {currentFilter === 'submitted' && filteredAssignments.length > 0 && (\n          <div style={{\n            background: 'rgba(52, 199, 89, 0.1)',\n            border: '1px solid rgba(52, 199, 89, 0.3)',\n            borderRadius: '12px',\n            padding: '16px',\n            textAlign: 'center',\n            marginTop: '24px'\n          }}>\n            <Typography.Text style={{ color: '#34C759', fontWeight: 500 }}>\n              ✅ 这些是你正常提交的作业，等待老师批改中...\n            </Typography.Text>\n          </div>\n        )}\n\n        {currentFilter === 'makeup' && filteredAssignments.length > 0 && (\n          <div style={{\n            background: 'rgba(175, 82, 222, 0.1)',\n            border: '1px solid rgba(175, 82, 222, 0.3)',\n            borderRadius: '12px',\n            padding: '16px',\n            textAlign: 'center',\n            marginTop: '24px'\n          }}>\n            <Typography.Text style={{ color: '#AF52DE', fontWeight: 500 }}>\n              🔄 这些是你补交的作业，虽然逾期但已成功提交！\n            </Typography.Text>\n          </div>\n        )}\n\n        {currentFilter === 'graded' && filteredAssignments.length > 0 && (\n          <div style={{\n            background: 'rgba(48, 209, 88, 0.1)',\n            border: '1px solid rgba(48, 209, 88, 0.3)',\n            borderRadius: '12px',\n            padding: '16px',\n            textAlign: 'center',\n            marginTop: '24px'\n          }}>\n            <Typography.Text style={{ color: '#30D158', fontWeight: 500 }}>\n              ⭐ 这些是已经批改完成的作业，点击\"详情\"查看批改结果！\n            </Typography.Text>\n          </div>\n        )}\n\n        {/* 智能提醒 */}\n        <SmartReminder\n          assignments={assignments}\n          user={user}\n          onNavigate={handleNotificationNavigate}\n          style={{\n            position: 'fixed',\n            top: '20px',\n            right: '20px',\n            zIndex: 1000\n          }}\n        />\n\n        {/* 浮动快速操作 */}\n        <FloatingQuickActions\n          onUploadHomework={handleQuickUpload}\n          onViewNotifications={() => {}}\n          notificationCount={urgentCount}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default StudentHomeworkAssignmentList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAC3D,MAAM;AACb,SACEC,cAAc,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,QACzD,mBAAmB;AAC1B,SAASC,6BAA6B,QAAQ,cAAc;AAC5D,OAAOC,WAAW,IAAIC,cAAc,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxF,OAAOC,YAAY,IAAIC,UAAU,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,wBAAwB;AAC3G,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAO,uBAAuB;AAC9B,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGrB,UAAU;AAElC,MAAMsB,6BAA6B,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM8C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM6C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMQ,IAAI,GAAG,MAAMhC,6BAA6B,CAAC,CAAC;MAClDiC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,IAAI,CAAC;MAChCd,cAAc,CAACc,IAAI,CAAC;MACpBZ,sBAAsB,CAACY,IAAI,CAAC;;MAE5B;MACAG,cAAc,CAACH,IAAI,CAAC;IAEtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCC,QAAQ,CAACD,KAAK,CAAClC,OAAO,CAAC;MACvBA,OAAO,CAACkC,KAAK,CAAC,aAAaA,KAAK,CAAClC,OAAO,EAAE,CAAC;IAC7C,CAAC,SAAS;MACRiC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAIC,UAAU,IAAK;IACxC,IAAI,CAACA,UAAU,CAACC,WAAW,EAAE,OAAO,KAAK;IACzC;IACA,OAAOD,UAAU,CAACC,WAAW,CAACC,QAAQ,CAAC,eAAe,CAAC;EACzD,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIH,UAAU,IAAK;IACzC,OAAOA,UAAU,CAACI,iBAAiB,KAAK,KAAK,IAAIL,iBAAiB,CAACC,UAAU,CAAC;EAChF,CAAC;;EAED;EACA,MAAMF,cAAc,GAAIH,IAAI,IAAK;IAC/B;IACA,MAAMU,sBAAsB,GAAGV,IAAI,CAACW,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACH,iBAAiB,KAAK,KAAK,CAAC;;IAEpF;IACA,MAAMI,OAAO,GAAGH,sBAAsB,CAACC,MAAM,CAACC,IAAI,IAAI,CAACR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACE,MAAM;;IAEtF;IACA,MAAMC,UAAU,GAAGL,sBAAsB,CAACC,MAAM,CAACC,IAAI,IAAIR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACE,MAAM;;IAExF;IACA,MAAME,oBAAoB,GAAGhB,IAAI,CAACW,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACH,iBAAiB,KAAK,KAAK,CAAC;;IAElF;IACA,MAAMQ,SAAS,GAAGD,oBAAoB,CAACL,MAAM,CAACC,IAAI,IAAI,CAACR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACE,MAAM;;IAEtF;IACA,MAAMI,MAAM,GAAGF,oBAAoB,CAACL,MAAM,CAACC,IAAI,IAAIR,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAACE,MAAM;IAElF,MAAMK,MAAM,GAAGnB,IAAI,CAACW,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACQ,cAAc,KAAK,KAAK,CAAC,CAACN,MAAM;;IAExE;IACA,MAAMO,iBAAiB,GAAGrB,IAAI,CAACW,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACU,KAAK,KAAK,IAAI,CAAC;IAClE,MAAMC,QAAQ,GAAGF,iBAAiB,CAACP,MAAM,GAAG,CAAC,GACzCU,IAAI,CAACC,KAAK,CAACJ,iBAAiB,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEf,IAAI,KAAKe,GAAG,GAAGf,IAAI,CAACU,KAAK,EAAE,CAAC,CAAC,GAAGD,iBAAiB,CAACP,MAAM,CAAC,GACnG,CAAC;IAELpB,QAAQ,CAAC;MACPmB,OAAO;MACPE,UAAU;MACVE,SAAS;MACTC,MAAM;MACNC,MAAM;MACNI,QAAQ;MACRK,KAAK,EAAE5B,IAAI,CAACc;IACd,CAAC,CAAC;EACJ,CAAC;EAED9D,SAAS,CAAC,MAAM;IACd+C,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/C,SAAS,CAAC,MAAM;IACd,IAAI8C,QAAQ,CAAC+B,KAAK,IAAI/B,QAAQ,CAAC+B,KAAK,CAAClB,MAAM,IAAI1B,WAAW,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACrEb,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEJ,QAAQ,CAAC+B,KAAK,CAAClB,MAAM,CAAC;MACjDmB,iBAAiB,CAAChC,QAAQ,CAAC+B,KAAK,CAAClB,MAAM,CAAC;MACxC;MACAoB,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,CAAC;IACjD;EACF,CAAC,EAAE,CAAClD,WAAW,EAAEa,QAAQ,CAAC+B,KAAK,CAAC,CAAC;;EAEjC;EACA,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOnD,WAAW,CAAC0B,MAAM,CAACN,UAAU,IAAI;MACtC,IAAIA,UAAU,CAACI,iBAAiB,KAAK,KAAK,EAAE,OAAO,KAAK;MACxD,IAAI,CAACJ,UAAU,CAACgC,QAAQ,EAAE,OAAO,KAAK;MAEtC,MAAMC,GAAG,GAAG7D,MAAM,CAAC,CAAC;MACpB,MAAM8D,GAAG,GAAG9D,MAAM,CAAC4B,UAAU,CAACgC,QAAQ,CAAC;MACvC,MAAMG,QAAQ,GAAGD,GAAG,CAACE,IAAI,CAACH,GAAG,EAAE,MAAM,CAAC;MAEtC,OAAOE,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,CAAC;IACvC,CAAC,CAAC,CAAC1B,MAAM;EACX,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAIrC,UAAU,IAAK;IACnCJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEG,UAAU,CAAC;IAC/DR,QAAQ,CAAC,yBAAyB,EAAE;MAClCgC,KAAK,EAAE;QACLc,YAAY,EAAEtC,UAAU,CAACuC,EAAE;QAC3BC,eAAe,EAAExC,UAAU,CAAC8B;MAC9B;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAIzC,UAAU,IAAK;IACxC,IAAIA,UAAU,CAAC0C,WAAW,EAAE;MAC1BlD,QAAQ,CAAC,aAAaQ,UAAU,CAAC0C,WAAW,EAAE,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,kBAAkB,GAAGhE,WAAW,CAAC0B,MAAM,CAACuC,CAAC,IAAIA,CAAC,CAACzC,iBAAiB,KAAK,KAAK,CAAC;IACjF,IAAIwC,kBAAkB,CAACnC,MAAM,GAAG,CAAC,EAAE;MACjC4B,YAAY,CAACO,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,MAAM;MACL5F,OAAO,CAAC8F,IAAI,CAAC,UAAU,CAAC;IAC1B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIzC,MAAM,IAAK;IACtC,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACvB;MACAtD,OAAO,CAAC8F,IAAI,CAAC,QAAQ,CAAC;IACxB,CAAC,MAAM;MACLtD,QAAQ,CAAC,mBAAmB,CAAC;IAC/B;EACF,CAAC;EAED,MAAMwD,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxD,QAAQ,CAAC,oBAAoB,CAAC;EAChC,CAAC;EAED,MAAMyD,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACA,IAAIvE,IAAI,KAAKA,IAAI,CAACwE,UAAU,IAAIxE,IAAI,CAACyE,QAAQ,CAAC,EAAE;MAC9C3D,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC,MAAM;MACLA,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAM4D,0BAA0B,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;IACpD9D,QAAQ,CAAC6D,IAAI,EAAEC,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAM7B,iBAAiB,GAAInB,MAAM,IAAK;IACpC,IAAIiD,QAAQ,GAAG3E,WAAW;IAE1B,QAAQ0B,MAAM;MACZ,KAAK,SAAS;QACZ;QACAiD,QAAQ,GAAG3E,WAAW,CAAC0B,MAAM,CAACuC,CAAC,IAC7BA,CAAC,CAACzC,iBAAiB,KAAK,KAAK,IAAI,CAACL,iBAAiB,CAAC8C,CAAC,CACvD,CAAC;QACD;MACF,KAAK,YAAY;QACf;QACAU,QAAQ,GAAG3E,WAAW,CAAC0B,MAAM,CAACuC,CAAC,IAC7BA,CAAC,CAACzC,iBAAiB,KAAK,KAAK,IAAIL,iBAAiB,CAAC8C,CAAC,CACtD,CAAC;QACD;MACF,KAAK,WAAW;QACd;QACAU,QAAQ,GAAG3E,WAAW,CAAC0B,MAAM,CAACuC,CAAC,IAC7BA,CAAC,CAACzC,iBAAiB,KAAK,KAAK,IAAI,CAACL,iBAAiB,CAAC8C,CAAC,CACvD,CAAC;QACD;MACF,KAAK,QAAQ;QACX;QACAU,QAAQ,GAAG3E,WAAW,CAAC0B,MAAM,CAACuC,CAAC,IAC7BA,CAAC,CAACzC,iBAAiB,KAAK,KAAK,IAAIL,iBAAiB,CAAC8C,CAAC,CACtD,CAAC;QACD;MACF,KAAK,QAAQ;QACXU,QAAQ,GAAG3E,WAAW,CAAC0B,MAAM,CAACuC,CAAC,IAAIA,CAAC,CAAC9B,cAAc,KAAK,KAAK,CAAC;QAC9D;MACF,KAAK,KAAK;MACV;QACEwC,QAAQ,GAAG3E,WAAW;QACtB;IACJ;IAEAG,sBAAsB,CAACwE,QAAQ,CAAC;IAChChE,gBAAgB,CAACe,MAAM,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMkD,eAAe,GAAIC,OAAO,IAAK;IACnC7D,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE4D,OAAO,CAAC;IACrChC,iBAAiB,CAACgC,OAAO,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBjC,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMkC,cAAc,GAAIrD,MAAM,IAAK;IACjC,MAAMsD,MAAM,GAAG;MACbpD,OAAO,EAAE,OAAO;MAChBE,UAAU,EAAE,OAAO;MACnBE,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE,OAAO;MACf+C,GAAG,EAAE;IACP,CAAC;IACD,OAAOD,MAAM,CAACtD,MAAM,CAAC,IAAI,MAAM;EACjC,CAAC;;EAED;EACA,MAAMwD,aAAa,GAAGA,CAAA,kBACpBxF,OAAA;IAAKyF,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1F,OAAA,CAACN,UAAU;MAACoB,KAAK,EAAEA;IAAM;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5B9F,OAAA,CAAClB,GAAG;MAACiH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAL,QAAA,EACnB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACM,GAAG,CAACC,CAAC,iBACvBjG,OAAA,CAACjB,GAAG;QAASmH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACjC1F,OAAA,CAACR,mBAAmB;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GADfG,CAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMO,WAAW,GAAGA,CAAA,kBAClBrG,OAAA;IAAKyF,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B1F,OAAA,CAACN,UAAU;MAACoB,KAAK,EAAEA;IAAM;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5B9F,OAAA,CAAClB,GAAG;MAACiH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAL,QAAA,eACpB1F,OAAA,CAACjB,GAAG;QAACuH,IAAI,EAAE,EAAG;QAAAZ,QAAA,eACZ1F,OAAA,CAACT,cAAc;UACbiE,KAAK,EAAC,sCAAQ;UACd7B,WAAW,EAAC,sOAAwC;UACpD4E,IAAI,EAAC,cAAI;UACTC,MAAM,eACJxG,OAAA,CAACvB,KAAK;YAAAiH,QAAA,gBACJ1F,OAAA,CAACxB,MAAM;cACLiI,IAAI,EAAC,SAAS;cACdF,IAAI,eAAEvG,OAAA,CAACZ,YAAY;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBY,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,mBAAmB,CAAE;cAAAwE,QAAA,EAC9C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT9F,OAAA,CAACxB,MAAM;cACL+H,IAAI,eAAEvG,OAAA,CAACd,gBAAgB;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BY,OAAO,EAAEhC,kBAAmB;cAAAgB,QAAA,EAC7B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAIlF,KAAK,EAAE;IACT,oBACEZ,OAAA;MAAKyF,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3B1F,OAAA,CAACpB,KAAK;QACJF,OAAO,EAAC,uCAAS;QACjBiD,WAAW,EAAEf,KAAM;QACnB6F,IAAI,EAAC,OAAO;QACZE,QAAQ;QACRH,MAAM,eACJxG,OAAA,CAACxB,MAAM;UAACiI,IAAI,EAAC,SAAS;UAACC,OAAO,EAAEtF,gBAAiB;UAAAsE,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;QACDc,KAAK,EAAE;UACLC,YAAY,EAAE,MAAM;UACpBC,YAAY,EAAE;QAChB;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;;EAEA;EACA,IAAIpF,OAAO,EAAE;IACX,OAAO8E,aAAa,CAAC,CAAC;EACxB;;EAEA;EACA,IAAIlF,WAAW,CAAC6B,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAOkE,WAAW,CAAC,CAAC;EACtB;EAEA,MAAMU,WAAW,GAAGtD,cAAc,CAAC,CAAC;EAEpC,oBACEzD,OAAA;IAAKyF,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC1F,OAAA;MAAKyF,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAE3B1F,OAAA;QAAKyF,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAEpC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN9F,OAAA;QAAKyF,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGN9F,OAAA,CAACL,kBAAkB;QACjBqH,UAAU,EAAE,CAAE;QACdC,UAAU,EAAE,CAAE;QACdC,eAAe,EAAEpG,KAAK,CAACwB,SAAS,IAAI;MAAE;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGF9F,OAAA,CAACN,UAAU;QACToB,KAAK,EAAEA,KAAM;QACbqG,WAAW,EAAEjC,eAAgB;QAC7BkC,YAAY,EAAEpG;MAAc;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAGF9F,OAAA,CAACP,YAAY;QACX4H,YAAY,EAAEvG,KAAK,CAACoB,OAAO,IAAI,CAAE;QACjC6E,WAAW,EAAEA,WAAY;QACzBO,gBAAgB,EAAEjD,iBAAkB;QACpCkD,eAAe,EAAE9C,mBAAoB;QACrC+C,cAAc,EAAE9C,kBAAmB;QACnC+C,kBAAkB,EAAE9C,sBAAuB;QAC3CiC,KAAK,EAAE;UAAEE,YAAY,EAAE;QAAO;MAAE;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAGF9F,OAAA;QAAK4G,KAAK,EAAE;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAApB,QAAA,gBACnC1F,OAAA;UAAK4G,KAAK,EAAE;YACVc,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE,QAAQ;YACpBd,YAAY,EAAE;UAChB,CAAE;UAAApB,QAAA,gBACA1F,OAAA,CAACnB,UAAU,CAACoB,KAAK;YAAC4H,KAAK,EAAE,CAAE;YAACjB,KAAK,EAAE;cAAEkB,MAAM,EAAE;YAAE,CAAE;YAAApC,QAAA,GAAC,yCACvC,EAAClF,mBAAmB,CAAC2B,MAAM,EAAC,GACrC,EAACnB,aAAa,KAAK,KAAK,iBACtBhB,OAAA;cAAM4G,KAAK,EAAE;gBACXmB,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,QAAQ;gBACpBC,KAAK,EAAE,SAAS;gBAChBC,UAAU,EAAE;cACd,CAAE;cAAAxC,QAAA,GAAC,IACC,EAACL,cAAc,CAACrE,aAAa,CAAC;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACe,CAAC,EAElB9E,aAAa,KAAK,KAAK,iBACtBhB,OAAA,CAACxB,MAAM;YACLiI,IAAI,EAAC,MAAM;YACXC,OAAO,EAAEtB,WAAY;YACrBwB,KAAK,EAAE;cAAEuB,OAAO,EAAE;YAAE,CAAE;YAAAzC,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAELtF,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,gBAC7BnC,OAAA,CAAClB,GAAG;UAACiH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAL,QAAA,EACnBlF,mBAAmB,CAACwF,GAAG,CAACtE,UAAU,iBACjC1B,OAAA,CAACjB,GAAG;YAAqBmH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eAC7C1F,OAAA,CAACV,WAAW;cACVoC,UAAU,EAAEA,UAAW;cACvB0G,QAAQ,EAAErE,YAAa;cACvBsE,aAAa,EAAElE;YAAkB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC,GALMpE,UAAU,CAACuC,EAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMlB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN9F,OAAA;UAAK4G,KAAK,EAAE;YACV0B,SAAS,EAAE,QAAQ;YACnBH,OAAO,EAAE,WAAW;YACpBI,UAAU,EAAE,OAAO;YACnB1B,YAAY,EAAE,MAAM;YACpB2B,MAAM,EAAE;UACV,CAAE;UAAA9C,QAAA,gBACA1F,OAAA;YAAK4G,KAAK,EAAE;cAAEmB,QAAQ,EAAE,MAAM;cAAEjB,YAAY,EAAE;YAAO,CAAE;YAAApB,QAAA,GACpD1E,aAAa,KAAK,SAAS,IAAI,IAAI,EACnCA,aAAa,KAAK,YAAY,IAAI,IAAI,EACtCA,aAAa,KAAK,WAAW,IAAI,GAAG,EACpCA,aAAa,KAAK,QAAQ,IAAI,IAAI,EAClCA,aAAa,KAAK,QAAQ,IAAI,GAAG,EACjCA,aAAa,KAAK,KAAK,IAAI,IAAI;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACN9F,OAAA,CAACnB,UAAU,CAACoB,KAAK;YAAC4H,KAAK,EAAE,CAAE;YAACjB,KAAK,EAAE;cAAEqB,KAAK,EAAE,SAAS;cAAEnB,YAAY,EAAE;YAAM,CAAE;YAAApB,QAAA,GAAC,cAC1E,EAACL,cAAc,CAACrE,aAAa,CAAC;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACnB9F,OAAA,CAACnB,UAAU,CAACqB,IAAI;YAAC0G,KAAK,EAAE;cAAEqB,KAAK,EAAE;YAAU,CAAE;YAAAvC,QAAA,GAC1C1E,aAAa,KAAK,SAAS,IAAI,cAAc,EAC7CA,aAAa,KAAK,YAAY,IAAI,gBAAgB,EAClDA,aAAa,KAAK,WAAW,IAAI,YAAY,EAC7CA,aAAa,KAAK,QAAQ,IAAI,UAAU,EACxCA,aAAa,KAAK,QAAQ,IAAI,WAAW,EACzCA,aAAa,KAAK,KAAK,IAAI,UAAU;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL9E,aAAa,KAAK,KAAK,IAAIF,KAAK,CAACoB,OAAO,GAAG,CAAC,iBAC3ClC,OAAA;QAAK4G,KAAK,EAAE;UACV2B,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE,kCAAkC;UAC1C3B,YAAY,EAAE,MAAM;UACpBsB,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA1F,OAAA,CAACnB,UAAU,CAACqB,IAAI;UAAC0G,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAI,CAAE;UAAAtC,QAAA,GAAC,4BACvD,EAAC5E,KAAK,CAACoB,OAAO,EAAC,iFACvB;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN,EAEA9E,aAAa,KAAK,SAAS,IAAIR,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,iBAC5DnC,OAAA;QAAK4G,KAAK,EAAE;UACV2B,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE,kCAAkC;UAC1C3B,YAAY,EAAE,MAAM;UACpBsB,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA1F,OAAA,CAACnB,UAAU,CAACqB,IAAI;UAAC0G,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN,EAEA9E,aAAa,KAAK,YAAY,IAAIR,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,iBAC/DnC,OAAA;QAAK4G,KAAK,EAAE;UACV2B,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE,kCAAkC;UAC1C3B,YAAY,EAAE,MAAM;UACpBsB,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA1F,OAAA,CAACnB,UAAU,CAACqB,IAAI;UAAC0G,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN,EAEA9E,aAAa,KAAK,WAAW,IAAIR,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,iBAC9DnC,OAAA;QAAK4G,KAAK,EAAE;UACV2B,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE,kCAAkC;UAC1C3B,YAAY,EAAE,MAAM;UACpBsB,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA1F,OAAA,CAACnB,UAAU,CAACqB,IAAI;UAAC0G,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN,EAEA9E,aAAa,KAAK,QAAQ,IAAIR,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,iBAC3DnC,OAAA;QAAK4G,KAAK,EAAE;UACV2B,UAAU,EAAE,yBAAyB;UACrCC,MAAM,EAAE,mCAAmC;UAC3C3B,YAAY,EAAE,MAAM;UACpBsB,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA1F,OAAA,CAACnB,UAAU,CAACqB,IAAI;UAAC0G,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN,EAEA9E,aAAa,KAAK,QAAQ,IAAIR,mBAAmB,CAAC2B,MAAM,GAAG,CAAC,iBAC3DnC,OAAA;QAAK4G,KAAK,EAAE;UACV2B,UAAU,EAAE,wBAAwB;UACpCC,MAAM,EAAE,kCAAkC;UAC1C3B,YAAY,EAAE,MAAM;UACpBsB,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,QAAQ;UACnBG,SAAS,EAAE;QACb,CAAE;QAAA/C,QAAA,eACA1F,OAAA,CAACnB,UAAU,CAACqB,IAAI;UAAC0G,KAAK,EAAE;YAAEqB,KAAK,EAAE,SAAS;YAAED,UAAU,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACN,eAGD9F,OAAA,CAACH,aAAa;QACZS,WAAW,EAAEA,WAAY;QACzBF,IAAI,EAAEA,IAAK;QACXsI,UAAU,EAAE5D,0BAA2B;QACvC8B,KAAK,EAAE;UACL+B,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,MAAM;UACXC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE;QACV;MAAE;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGF9F,OAAA,CAACJ,oBAAoB;QACnB0H,gBAAgB,EAAEjD,iBAAkB;QACpC0E,mBAAmB,EAAEA,CAAA,KAAM,CAAC,CAAE;QAC9BC,iBAAiB,EAAEjC;MAAY;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CA5hBIF,6BAA6B;EAAA,QAOhB7B,WAAW,EACXC,WAAW;AAAA;AAAA0K,EAAA,GARxB9I,6BAA6B;AA8hBnC,eAAeA,6BAA6B;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}