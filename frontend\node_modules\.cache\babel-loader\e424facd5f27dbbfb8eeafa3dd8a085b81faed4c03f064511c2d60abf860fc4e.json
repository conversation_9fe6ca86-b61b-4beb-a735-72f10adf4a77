{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { getRowFormat, toArray } from \"../../utils/miscUtil\";\nexport function useFieldFormat(picker, locale, format) {\n  return React.useMemo(function () {\n    var rawFormat = getRowFormat(picker, locale, format);\n    var formatList = toArray(rawFormat);\n    var firstFormat = formatList[0];\n    var maskFormat = _typeof(firstFormat) === 'object' && firstFormat.type === 'mask' ? firstFormat.format : null;\n    return [\n    // Format list\n    formatList.map(function (config) {\n      return typeof config === 'string' || typeof config === 'function' ? config : config.format;\n    }),\n    // Mask Format\n    maskFormat];\n  }, [picker, locale, format]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}