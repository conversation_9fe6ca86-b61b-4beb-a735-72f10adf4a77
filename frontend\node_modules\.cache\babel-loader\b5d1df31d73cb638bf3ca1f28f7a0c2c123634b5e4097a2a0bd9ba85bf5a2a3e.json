{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\pages\\\\StudentAchievement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Typography, Card, Row, Col, Statistic, Spin, Alert, Progress, Space, Badge } from 'antd';\nimport { TrophyOutlined, BookOutlined, CheckCircleOutlined, ClockCircleOutlined, FireOutlined, StarOutlined, BarChartOutlined } from '@ant-design/icons';\nimport { getStudentStatistics } from '../utils/api';\nimport moment from 'moment';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst StudentAchievement = ({\n  user\n}) => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [statistics, setStatistics] = useState(null);\n  const [accuracyTrend, setAccuracyTrend] = useState([]);\n  useEffect(() => {\n    const fetchStatistics = async () => {\n      try {\n        setLoading(true);\n        const stats = await getStudentStatistics();\n        setStatistics(stats);\n\n        // 处理正确率趋势数据\n        if (stats.accuracy_trend && Array.isArray(stats.accuracy_trend)) {\n          setAccuracyTrend(stats.accuracy_trend);\n        }\n      } catch (err) {\n        console.error('获取学生统计数据失败:', err);\n        setError(err.message || '获取数据失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (user) {\n      fetchStatistics();\n    }\n  }, [user]);\n\n  // 根据平均分判断学习等级\n  const getScoreLevel = score => {\n    if (score >= 90) return {\n      level: '优秀',\n      color: '#52c41a',\n      icon: '🏆'\n    };\n    if (score >= 80) return {\n      level: '良好',\n      color: '#1890ff',\n      icon: '👍'\n    };\n    if (score >= 70) return {\n      level: '中等',\n      color: '#faad14',\n      icon: '👌'\n    };\n    if (score >= 60) return {\n      level: '及格',\n      color: '#ff7a45',\n      icon: '✍️'\n    };\n    return {\n      level: '待提高',\n      color: '#ff4d4f',\n      icon: '💪'\n    };\n  };\n\n  // 根据作业完成情况计算学习连续性\n  const calculateStreak = () => {\n    if (!statistics || !accuracyTrend.length) return 0;\n    let streak = 0;\n    // 简化计算，实际应该基于连续提交作业的天数\n    const recentHomeworks = accuracyTrend.slice(-7); // 最近7次作业\n    for (let i = recentHomeworks.length - 1; i >= 0; i--) {\n      if (recentHomeworks[i].correct_count > 0) {\n        streak++;\n      } else {\n        break;\n      }\n    }\n    return streak;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u52A0\\u8F7D\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u6570\\u636E\\u52A0\\u8F7D\\u5931\\u8D25\",\n      description: error,\n      type: \"error\",\n      showIcon: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this);\n  }\n  if (!statistics) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u6682\\u65E0\\u6570\\u636E\",\n      description: \"\\u6682\\u65E0\\u5B66\\u4E60\\u7EDF\\u8BA1\\u6570\\u636E\",\n      type: \"info\",\n      showIcon: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this);\n  }\n  const scoreLevel = getScoreLevel(statistics.average_accuracy * 100);\n  const streak = calculateStreak();\n  const avgAccuracy = statistics.average_accuracy || 0;\n  const avgScore = avgAccuracy * 100; // 简化处理，假设满分为100分\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), \" \\u5B66\\u4E60\\u6210\\u5C31\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n      children: \"\\u67E5\\u770B\\u4E2A\\u4EBA\\u5B66\\u4E60\\u8FDB\\u5EA6\\u548C\\u6210\\u5C31\\u7EDF\\u8BA1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4F5C\\u4E1A\\u6570\",\n            value: statistics.total_homework_count || 0,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: statistics.completed_homework_count || 0,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u5B8C\\u6210\",\n            value: statistics.pending_homework_count || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B66\\u4E60\\u7B49\\u7EA7\",\n            value: scoreLevel.level,\n            prefix: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '18px'\n              },\n              children: scoreLevel.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: scoreLevel.color\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '24px',\n                color: scoreLevel.color,\n                marginBottom: 8\n              },\n              children: scoreLevel.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              style: {\n                color: scoreLevel.color,\n                marginBottom: 8\n              },\n              children: \"\\u5E73\\u5747\\u8868\\u73B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              type: \"circle\",\n              percent: Math.round(avgScore),\n              width: 120,\n              strokeColor: scoreLevel.color,\n              format: () => `${Math.round(avgScore)}分`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                color: scoreLevel.color,\n                text: scoreLevel.level\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '24px',\n                color: '#722ed1',\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(FireOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u5B66\\u4E60\\u8FDE\\u7EED\\u6027\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '32px',\n                color: '#722ed1',\n                fontWeight: 'bold'\n              },\n              children: streak\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: \"\\u8FDE\\u7EED\\u5B8C\\u6210\\u4F5C\\u4E1A\\u5929\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '24px',\n                color: '#13c2c2',\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              style: {\n                marginBottom: 8\n              },\n              children: \"\\u6B63\\u786E\\u7387\\u8D8B\\u52BF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '80px',\n                display: 'flex',\n                alignItems: 'flex-end',\n                justifyContent: 'space-around'\n              },\n              children: accuracyTrend.slice(-5).map((item, index) => {\n                var _item$title;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '20px',\n                      height: `${Math.max(10, (item.correct_count || 0) * 10)}px`,\n                      backgroundColor: '#13c2c2',\n                      borderRadius: '2px 2px 0 0'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      marginTop: '4px'\n                    },\n                    children: ((_item$title = item.title) === null || _item$title === void 0 ? void 0 : _item$title.substring(0, 4)) || `作业${index + 1}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: /*#__PURE__*/_jsxDEV(\"span\", {\n        children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 26\n        }, this), \" \\u8BE6\\u7EC6\\u7EDF\\u8BA1\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 20\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6700\\u9AD8\\u6B63\\u786E\\u9898\\u6570\",\n              value: statistics.highest_correct_count || 0,\n              valueStyle: {\n                color: '#3f8600'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u5E73\\u5747\\u6B63\\u786E\\u9898\\u6570\",\n              value: statistics.average_correct_count ? statistics.average_correct_count.toFixed(1) : 0,\n              valueStyle: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u6700\\u4F4E\\u6B63\\u786E\\u9898\\u6570\",\n              value: statistics.lowest_correct_count || 0,\n              valueStyle: {\n                color: '#cf1322'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u9519\\u9898\\u6570\\u91CF\",\n              value: statistics.wrong_question_count || 0,\n              valueStyle: {\n                color: '#ff4d4f'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B66\\u4E60\\u5EFA\\u8BAE\",\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#1890ff',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCD8 \\u4F5C\\u4E1A\\u5B8C\\u6210\\u60C5\\u51B5\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), \"\\u60A8\\u5DF2\\u5B8C\\u6210 \", statistics.completed_homework_count || 0, \" \\u4EFD\\u4F5C\\u4E1A\\uFF0C\\u7EE7\\u7EED\\u4FDD\\u6301\\uFF01\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#52c41a',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCC8 \\u5B66\\u4E60\\u8868\\u73B0\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), \"\\u60A8\\u7684\\u5E73\\u5747\\u5206\\u4E3A \", Math.round(avgScore), \" \\u5206\\uFF0C\\u5C5E\\u4E8E \", scoreLevel.level, \" \\u6C34\\u5E73\\u3002\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#faad14',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDD25 \\u5B66\\u4E60\\u8FDE\\u7EED\\u6027\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), \"\\u60A8\\u5DF2\\u8FDE\\u7EED \", streak, \" \\u5929\\u5B8C\\u6210\\u4F5C\\u4E1A\\uFF0C\\u575A\\u6301\\u5C31\\u662F\\u80DC\\u5229\\uFF01\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), avgScore < 80 && /*#__PURE__*/_jsxDEV(Text, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#ff4d4f',\n              fontWeight: 'bold'\n            },\n            children: \"\\uD83D\\uDCA1 \\u63D0\\u5347\\u5EFA\\u8BAE\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), \"\\u5EFA\\u8BAE\\u52A0\\u5F3A\\u9519\\u9898\\u7EC3\\u4E60\\uFF0C\\u63D0\\u9AD8\\u4F5C\\u4E1A\\u51C6\\u786E\\u7387\\u3002\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentAchievement, \"OVBcU9MwCceTgDFArF+lsQpp8dI=\");\n_c = StudentAchievement;\nexport default StudentAchievement;\nvar _c;\n$RefreshReg$(_c, \"StudentAchievement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Typography", "Card", "Row", "Col", "Statistic", "Spin", "<PERSON><PERSON>", "Progress", "Space", "Badge", "TrophyOutlined", "BookOutlined", "CheckCircleOutlined", "ClockCircleOutlined", "FireOutlined", "StarOutlined", "BarChartOutlined", "getStudentStatistics", "moment", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "StudentAchievement", "user", "_s", "loading", "setLoading", "error", "setError", "statistics", "setStatistics", "accuracyTrend", "setAccuracyTrend", "fetchStatistics", "stats", "accuracy_trend", "Array", "isArray", "err", "console", "message", "getScoreLevel", "score", "level", "color", "icon", "calculateStreak", "length", "streak", "recentHomeworks", "slice", "i", "correct_count", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "description", "type", "showIcon", "scoreLevel", "average_accuracy", "avgAccuracy", "avgScore", "gutter", "marginBottom", "xs", "sm", "md", "title", "value", "total_homework_count", "prefix", "valueStyle", "completed_homework_count", "pending_homework_count", "fontSize", "percent", "Math", "round", "width", "strokeColor", "format", "text", "fontWeight", "height", "display", "alignItems", "justifyContent", "map", "item", "index", "_item$title", "flexDirection", "max", "backgroundColor", "borderRadius", "substring", "highest_correct_count", "average_correct_count", "toFixed", "lowest_correct_count", "wrong_question_count", "direction", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/pages/StudentAchievement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Typography, Card, Row, Col, Statistic, Spin, Alert, Progress, Space, Badge } from 'antd';\nimport { \n  TrophyOutlined, \n  BookOutlined, \n  CheckCircleOutlined, \n  ClockCircleOutlined,\n  FireOutlined,\n  StarOutlined,\n  BarChartOutlined\n} from '@ant-design/icons';\nimport { getStudentStatistics } from '../utils/api';\nimport moment from 'moment';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst StudentAchievement = ({ user }) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [statistics, setStatistics] = useState(null);\n  const [accuracyTrend, setAccuracyTrend] = useState([]);\n\n  useEffect(() => {\n    const fetchStatistics = async () => {\n      try {\n        setLoading(true);\n        const stats = await getStudentStatistics();\n        setStatistics(stats);\n        \n        // 处理正确率趋势数据\n        if (stats.accuracy_trend && Array.isArray(stats.accuracy_trend)) {\n          setAccuracyTrend(stats.accuracy_trend);\n        }\n      } catch (err) {\n        console.error('获取学生统计数据失败:', err);\n        setError(err.message || '获取数据失败');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user) {\n      fetchStatistics();\n    }\n  }, [user]);\n\n  // 根据平均分判断学习等级\n  const getScoreLevel = (score) => {\n    if (score >= 90) return { level: '优秀', color: '#52c41a', icon: '🏆' };\n    if (score >= 80) return { level: '良好', color: '#1890ff', icon: '👍' };\n    if (score >= 70) return { level: '中等', color: '#faad14', icon: '👌' };\n    if (score >= 60) return { level: '及格', color: '#ff7a45', icon: '✍️' };\n    return { level: '待提高', color: '#ff4d4f', icon: '💪' };\n  };\n\n  // 根据作业完成情况计算学习连续性\n  const calculateStreak = () => {\n    if (!statistics || !accuracyTrend.length) return 0;\n    \n    let streak = 0;\n    // 简化计算，实际应该基于连续提交作业的天数\n    const recentHomeworks = accuracyTrend.slice(-7); // 最近7次作业\n    for (let i = recentHomeworks.length - 1; i >= 0; i--) {\n      if (recentHomeworks[i].correct_count > 0) {\n        streak++;\n      } else {\n        break;\n      }\n    }\n    return streak;\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>加载中...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"数据加载失败\"\n        description={error}\n        type=\"error\"\n        showIcon\n      />\n    );\n  }\n\n  if (!statistics) {\n    return (\n      <Alert\n        message=\"暂无数据\"\n        description=\"暂无学习统计数据\"\n        type=\"info\"\n        showIcon\n      />\n    );\n  }\n\n  const scoreLevel = getScoreLevel(statistics.average_accuracy * 100);\n  const streak = calculateStreak();\n  const avgAccuracy = statistics.average_accuracy || 0;\n  const avgScore = avgAccuracy * 100; // 简化处理，假设满分为100分\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>\n        <TrophyOutlined /> 学习成就\n      </Title>\n      <Paragraph>\n        查看个人学习进度和成就统计\n      </Paragraph>\n\n      {/* 核心统计数据 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总作业数\"\n              value={statistics.total_homework_count || 0}\n              prefix={<BookOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={statistics.completed_homework_count || 0}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"待完成\"\n              value={statistics.pending_homework_count || 0}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"学习等级\"\n              value={scoreLevel.level}\n              prefix={<span style={{ fontSize: '18px' }}>{scoreLevel.icon}</span>}\n              valueStyle={{ color: scoreLevel.color }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 学习表现 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={8}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', color: scoreLevel.color, marginBottom: 8 }}>\n                {scoreLevel.icon}\n              </div>\n              <Title level={4} style={{ color: scoreLevel.color, marginBottom: 8 }}>\n                平均表现\n              </Title>\n              <Progress\n                type=\"circle\"\n                percent={Math.round(avgScore)}\n                width={120}\n                strokeColor={scoreLevel.color}\n                format={() => `${Math.round(avgScore)}分`}\n              />\n              <div style={{ marginTop: 16 }}>\n                <Badge color={scoreLevel.color} text={scoreLevel.level} />\n              </div>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', color: '#722ed1', marginBottom: 8 }}>\n                <FireOutlined />\n              </div>\n              <Title level={4} style={{ marginBottom: 8 }}>\n                学习连续性\n              </Title>\n              <div style={{ fontSize: '32px', color: '#722ed1', fontWeight: 'bold' }}>\n                {streak}\n              </div>\n              <div style={{ marginTop: 8 }}>连续完成作业天数</div>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '24px', color: '#13c2c2', marginBottom: 8 }}>\n                <StarOutlined />\n              </div>\n              <Title level={4} style={{ marginBottom: 8 }}>\n                正确率趋势\n              </Title>\n              <div style={{ height: '80px', display: 'flex', alignItems: 'flex-end', justifyContent: 'space-around' }}>\n                {accuracyTrend.slice(-5).map((item, index) => (\n                  <div key={index} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n                    <div \n                      style={{ \n                        width: '20px', \n                        height: `${Math.max(10, (item.correct_count || 0) * 10)}px`, \n                        backgroundColor: '#13c2c2',\n                        borderRadius: '2px 2px 0 0'\n                      }}\n                    />\n                    <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                      {item.title?.substring(0, 4) || `作业${index+1}`}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 详细统计数据 */}\n      <Card title={<span><BarChartOutlined /> 详细统计</span>}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={6}>\n            <Card size=\"small\">\n              <Statistic\n                title=\"最高正确题数\"\n                value={statistics.highest_correct_count || 0}\n                valueStyle={{ color: '#3f8600' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Card size=\"small\">\n              <Statistic\n                title=\"平均正确题数\"\n                value={statistics.average_correct_count ? statistics.average_correct_count.toFixed(1) : 0}\n                valueStyle={{ color: '#1890ff' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Card size=\"small\">\n              <Statistic\n                title=\"最低正确题数\"\n                value={statistics.lowest_correct_count || 0}\n                valueStyle={{ color: '#cf1322' }}\n              />\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={6}>\n            <Card size=\"small\">\n              <Statistic\n                title=\"错题数量\"\n                value={statistics.wrong_question_count || 0}\n                valueStyle={{ color: '#ff4d4f' }}\n              />\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 学习建议 */}\n      <Card title=\"学习建议\" style={{ marginTop: 24 }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Text>\n            <span style={{ color: '#1890ff', fontWeight: 'bold' }}>📘 作业完成情况：</span>\n            您已完成 {statistics.completed_homework_count || 0} 份作业，继续保持！\n          </Text>\n          <Text>\n            <span style={{ color: '#52c41a', fontWeight: 'bold' }}>📈 学习表现：</span>\n            您的平均分为 {Math.round(avgScore)} 分，属于 {scoreLevel.level} 水平。\n          </Text>\n          <Text>\n            <span style={{ color: '#faad14', fontWeight: 'bold' }}>🔥 学习连续性：</span>\n            您已连续 {streak} 天完成作业，坚持就是胜利！\n          </Text>\n          {avgScore < 80 && (\n            <Text>\n              <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>💡 提升建议：</span>\n              建议加强错题练习，提高作业准确率。\n            </Text>\n          )}\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default StudentAchievement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACjG,SACEC,cAAc,EACdC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,YAAY,EACZC,YAAY,EACZC,gBAAgB,QACX,mBAAmB;AAC1B,SAASC,oBAAoB,QAAQ,cAAc;AACnD,OAAOC,MAAM,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGvB,UAAU;AAE7C,MAAMwB,kBAAkB,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,MAAMoC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFP,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMQ,KAAK,GAAG,MAAMnB,oBAAoB,CAAC,CAAC;QAC1Ce,aAAa,CAACI,KAAK,CAAC;;QAEpB;QACA,IAAIA,KAAK,CAACC,cAAc,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAACC,cAAc,CAAC,EAAE;UAC/DH,gBAAgB,CAACE,KAAK,CAACC,cAAc,CAAC;QACxC;MACF,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZC,OAAO,CAACZ,KAAK,CAAC,aAAa,EAAEW,GAAG,CAAC;QACjCV,QAAQ,CAACU,GAAG,CAACE,OAAO,IAAI,QAAQ,CAAC;MACnC,CAAC,SAAS;QACRd,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,IAAI,EAAE;MACRU,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACV,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMkB,aAAa,GAAIC,KAAK,IAAK;IAC/B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IACrE,IAAIH,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IACrE,IAAIH,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IACrE,IAAIH,KAAK,IAAI,EAAE,EAAE,OAAO;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IACrE,OAAO;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACjB,UAAU,IAAI,CAACE,aAAa,CAACgB,MAAM,EAAE,OAAO,CAAC;IAElD,IAAIC,MAAM,GAAG,CAAC;IACd;IACA,MAAMC,eAAe,GAAGlB,aAAa,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,KAAK,IAAIC,CAAC,GAAGF,eAAe,CAACF,MAAM,GAAG,CAAC,EAAEI,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpD,IAAIF,eAAe,CAACE,CAAC,CAAC,CAACC,aAAa,GAAG,CAAC,EAAE;QACxCJ,MAAM,EAAE;MACV,CAAC,MAAM;QACL;MACF;IACF;IACA,OAAOA,MAAM;EACf,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKmC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnDtC,OAAA,CAACf,IAAI;QAACsD,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB3C,OAAA;QAAKmC,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC;EAEV;EAEA,IAAIlC,KAAK,EAAE;IACT,oBACET,OAAA,CAACd,KAAK;MACJoC,OAAO,EAAC,sCAAQ;MAChBuB,WAAW,EAAEpC,KAAM;MACnBqC,IAAI,EAAC,OAAO;MACZC,QAAQ;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEN;EAEA,IAAI,CAAChC,UAAU,EAAE;IACf,oBACEX,OAAA,CAACd,KAAK;MACJoC,OAAO,EAAC,0BAAM;MACduB,WAAW,EAAC,kDAAU;MACtBC,IAAI,EAAC,MAAM;MACXC,QAAQ;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAEN;EAEA,MAAMK,UAAU,GAAGzB,aAAa,CAACZ,UAAU,CAACsC,gBAAgB,GAAG,GAAG,CAAC;EACnE,MAAMnB,MAAM,GAAGF,eAAe,CAAC,CAAC;EAChC,MAAMsB,WAAW,GAAGvC,UAAU,CAACsC,gBAAgB,IAAI,CAAC;EACpD,MAAME,QAAQ,GAAGD,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEpC,oBACElD,OAAA;IAAKmC,KAAK,EAAE;MAAEE,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BtC,OAAA,CAACC,KAAK;MAACwB,KAAK,EAAE,CAAE;MAAAa,QAAA,gBACdtC,OAAA,CAACV,cAAc;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACR3C,OAAA,CAACG,SAAS;MAAAmC,QAAA,EAAC;IAEX;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAGZ3C,OAAA,CAAClB,GAAG;MAACsE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACjB,KAAK,EAAE;QAAEkB,YAAY,EAAE;MAAG,CAAE;MAAAf,QAAA,gBACjDtC,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA,CAAChB,SAAS;YACRyE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE/C,UAAU,CAACgD,oBAAoB,IAAI,CAAE;YAC5CC,MAAM,eAAE5D,OAAA,CAACT,YAAY;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBkB,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA,CAAChB,SAAS;YACRyE,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE/C,UAAU,CAACmD,wBAAwB,IAAI,CAAE;YAChDF,MAAM,eAAE5D,OAAA,CAACR,mBAAmB;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCkB,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA,CAAChB,SAAS;YACRyE,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE/C,UAAU,CAACoD,sBAAsB,IAAI,CAAE;YAC9CH,MAAM,eAAE5D,OAAA,CAACP,mBAAmB;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCkB,UAAU,EAAE;cAAEnC,KAAK,EAAE;YAAU;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA,CAAChB,SAAS;YACRyE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAEV,UAAU,CAACvB,KAAM;YACxBmC,MAAM,eAAE5D,OAAA;cAAMmC,KAAK,EAAE;gBAAE6B,QAAQ,EAAE;cAAO,CAAE;cAAA1B,QAAA,EAAEU,UAAU,CAACrB;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAE;YACpEkB,UAAU,EAAE;cAAEnC,KAAK,EAAEsB,UAAU,CAACtB;YAAM;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA,CAAClB,GAAG;MAACsE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACjB,KAAK,EAAE;QAAEkB,YAAY,EAAE;MAAG,CAAE;MAAAf,QAAA,gBACjDtC,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA;YAAKmC,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAE,QAAA,gBAClCtC,OAAA;cAAKmC,KAAK,EAAE;gBAAE6B,QAAQ,EAAE,MAAM;gBAAEtC,KAAK,EAAEsB,UAAU,CAACtB,KAAK;gBAAE2B,YAAY,EAAE;cAAE,CAAE;cAAAf,QAAA,EACxEU,UAAU,CAACrB;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACN3C,OAAA,CAACC,KAAK;cAACwB,KAAK,EAAE,CAAE;cAACU,KAAK,EAAE;gBAAET,KAAK,EAAEsB,UAAU,CAACtB,KAAK;gBAAE2B,YAAY,EAAE;cAAE,CAAE;cAAAf,QAAA,EAAC;YAEtE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3C,OAAA,CAACb,QAAQ;cACP2D,IAAI,EAAC,QAAQ;cACbmB,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAChB,QAAQ,CAAE;cAC9BiB,KAAK,EAAE,GAAI;cACXC,WAAW,EAAErB,UAAU,CAACtB,KAAM;cAC9B4C,MAAM,EAAEA,CAAA,KAAM,GAAGJ,IAAI,CAACC,KAAK,CAAChB,QAAQ,CAAC;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACF3C,OAAA;cAAKmC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAG,CAAE;cAAAN,QAAA,eAC5BtC,OAAA,CAACX,KAAK;gBAACqC,KAAK,EAAEsB,UAAU,CAACtB,KAAM;gBAAC6C,IAAI,EAAEvB,UAAU,CAACvB;cAAM;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA;YAAKmC,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAE,QAAA,gBAClCtC,OAAA;cAAKmC,KAAK,EAAE;gBAAE6B,QAAQ,EAAE,MAAM;gBAAEtC,KAAK,EAAE,SAAS;gBAAE2B,YAAY,EAAE;cAAE,CAAE;cAAAf,QAAA,eAClEtC,OAAA,CAACN,YAAY;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACN3C,OAAA,CAACC,KAAK;cAACwB,KAAK,EAAE,CAAE;cAACU,KAAK,EAAE;gBAAEkB,YAAY,EAAE;cAAE,CAAE;cAAAf,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3C,OAAA;cAAKmC,KAAK,EAAE;gBAAE6B,QAAQ,EAAE,MAAM;gBAAEtC,KAAK,EAAE,SAAS;gBAAE8C,UAAU,EAAE;cAAO,CAAE;cAAAlC,QAAA,EACpER;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN3C,OAAA;cAAKmC,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;UAAAyD,QAAA,eACHtC,OAAA;YAAKmC,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAE,QAAA,gBAClCtC,OAAA;cAAKmC,KAAK,EAAE;gBAAE6B,QAAQ,EAAE,MAAM;gBAAEtC,KAAK,EAAE,SAAS;gBAAE2B,YAAY,EAAE;cAAE,CAAE;cAAAf,QAAA,eAClEtC,OAAA,CAACL,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACN3C,OAAA,CAACC,KAAK;cAACwB,KAAK,EAAE,CAAE;cAACU,KAAK,EAAE;gBAAEkB,YAAY,EAAE;cAAE,CAAE;cAAAf,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR3C,OAAA;cAAKmC,KAAK,EAAE;gBAAEsC,MAAM,EAAE,MAAM;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,UAAU;gBAAEC,cAAc,EAAE;cAAe,CAAE;cAAAtC,QAAA,EACrGzB,aAAa,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC6C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;gBAAA,IAAAC,WAAA;gBAAA,oBACvChF,OAAA;kBAAiBmC,KAAK,EAAE;oBAAEuC,OAAO,EAAE,MAAM;oBAAEO,aAAa,EAAE,QAAQ;oBAAEN,UAAU,EAAE;kBAAS,CAAE;kBAAArC,QAAA,gBACzFtC,OAAA;oBACEmC,KAAK,EAAE;sBACLiC,KAAK,EAAE,MAAM;sBACbK,MAAM,EAAE,GAAGP,IAAI,CAACgB,GAAG,CAAC,EAAE,EAAE,CAACJ,IAAI,CAAC5C,aAAa,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI;sBAC3DiD,eAAe,EAAE,SAAS;sBAC1BC,YAAY,EAAE;oBAChB;kBAAE;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF3C,OAAA;oBAAKmC,KAAK,EAAE;sBAAE6B,QAAQ,EAAE,MAAM;sBAAEpB,SAAS,EAAE;oBAAM,CAAE;oBAAAN,QAAA,EAChD,EAAA0C,WAAA,GAAAF,IAAI,CAACrB,KAAK,cAAAuB,WAAA,uBAAVA,WAAA,CAAYK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,KAAKN,KAAK,GAAC,CAAC;kBAAE;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA,GAXEoC,KAAK;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYV,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA,CAACnB,IAAI;MAAC4E,KAAK,eAAEzD,OAAA;QAAAsC,QAAA,gBAAMtC,OAAA,CAACJ,gBAAgB;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAAK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAE;MAAAL,QAAA,eAClDtC,OAAA,CAAClB,GAAG;QAACsE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAd,QAAA,gBACpBtC,OAAA,CAACjB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;YAAC0D,IAAI,EAAC,OAAO;YAAAD,QAAA,eAChBtC,OAAA,CAAChB,SAAS;cACRyE,KAAK,EAAC,sCAAQ;cACdC,KAAK,EAAE/C,UAAU,CAAC2E,qBAAqB,IAAI,CAAE;cAC7CzB,UAAU,EAAE;gBAAEnC,KAAK,EAAE;cAAU;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;YAAC0D,IAAI,EAAC,OAAO;YAAAD,QAAA,eAChBtC,OAAA,CAAChB,SAAS;cACRyE,KAAK,EAAC,sCAAQ;cACdC,KAAK,EAAE/C,UAAU,CAAC4E,qBAAqB,GAAG5E,UAAU,CAAC4E,qBAAqB,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE;cAC1F3B,UAAU,EAAE;gBAAEnC,KAAK,EAAE;cAAU;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;YAAC0D,IAAI,EAAC,OAAO;YAAAD,QAAA,eAChBtC,OAAA,CAAChB,SAAS;cACRyE,KAAK,EAAC,sCAAQ;cACdC,KAAK,EAAE/C,UAAU,CAAC8E,oBAAoB,IAAI,CAAE;cAC5C5B,UAAU,EAAE;gBAAEnC,KAAK,EAAE;cAAU;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN3C,OAAA,CAACjB,GAAG;UAACuE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAlB,QAAA,eACzBtC,OAAA,CAACnB,IAAI;YAAC0D,IAAI,EAAC,OAAO;YAAAD,QAAA,eAChBtC,OAAA,CAAChB,SAAS;cACRyE,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE/C,UAAU,CAAC+E,oBAAoB,IAAI,CAAE;cAC5C7B,UAAU,EAAE;gBAAEnC,KAAK,EAAE;cAAU;YAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP3C,OAAA,CAACnB,IAAI;MAAC4E,KAAK,EAAC,0BAAM;MAACtB,KAAK,EAAE;QAAES,SAAS,EAAE;MAAG,CAAE;MAAAN,QAAA,eAC1CtC,OAAA,CAACZ,KAAK;QAACuG,SAAS,EAAC,UAAU;QAACxD,KAAK,EAAE;UAAEiC,KAAK,EAAE;QAAO,CAAE;QAAA9B,QAAA,gBACnDtC,OAAA,CAACE,IAAI;UAAAoC,QAAA,gBACHtC,OAAA;YAAMmC,KAAK,EAAE;cAAET,KAAK,EAAE,SAAS;cAAE8C,UAAU,EAAE;YAAO,CAAE;YAAAlC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,6BACnE,EAAChC,UAAU,CAACmD,wBAAwB,IAAI,CAAC,EAAC,yDACjD;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP3C,OAAA,CAACE,IAAI;UAAAoC,QAAA,gBACHtC,OAAA;YAAMmC,KAAK,EAAE;cAAET,KAAK,EAAE,SAAS;cAAE8C,UAAU,EAAE;YAAO,CAAE;YAAAlC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,yCAC/D,EAACuB,IAAI,CAACC,KAAK,CAAChB,QAAQ,CAAC,EAAC,4BAAM,EAACH,UAAU,CAACvB,KAAK,EAAC,qBACvD;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP3C,OAAA,CAACE,IAAI;UAAAoC,QAAA,gBACHtC,OAAA;YAAMmC,KAAK,EAAE;cAAET,KAAK,EAAE,SAAS;cAAE8C,UAAU,EAAE;YAAO,CAAE;YAAAlC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,6BAClE,EAACb,MAAM,EAAC,iFACf;QAAA;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACNQ,QAAQ,GAAG,EAAE,iBACZnD,OAAA,CAACE,IAAI;UAAAoC,QAAA,gBACHtC,OAAA;YAAMmC,KAAK,EAAE;cAAET,KAAK,EAAE,SAAS;cAAE8C,UAAU,EAAE;YAAO,CAAE;YAAAlC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,0GAExE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrC,EAAA,CA3RIF,kBAAkB;AAAAwF,EAAA,GAAlBxF,kBAAkB;AA6RxB,eAAeA,kBAAkB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}