{"ast": null, "code": "/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\nimport { normalize } from '../normalize.js';\nimport { DefinedInfo } from './defined-info.js';\nimport { Schema } from './schema.js';\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nexport function create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {};\n  /** @type {Record<string, string>} */\n  const normals = {};\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new DefinedInfo(property, definition.transform(definition.attributes || {}, property), value, definition.space);\n    if (definition.mustUseProperty && definition.mustUseProperty.includes(property)) {\n      info.mustUseProperty = true;\n    }\n    properties[property] = info;\n    normals[normalize(property)] = property;\n    normals[normalize(info.attribute)] = property;\n  }\n  return new Schema(properties, normals, definition.space);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}