{"ast": null, "code": "import { useToken } from '../../theme/internal';\n/**\n * This hook is only for cssVar to add root className for components.\n * If root ClassName is needed, this hook could be refactored with `-root`\n * @param prefixCls\n */\nconst useCSSVarCls = prefixCls => {\n  const [,,,, cssVar] = useToken();\n  return cssVar ? `${prefixCls}-css-var` : '';\n};\nexport default useCSSVarCls;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}