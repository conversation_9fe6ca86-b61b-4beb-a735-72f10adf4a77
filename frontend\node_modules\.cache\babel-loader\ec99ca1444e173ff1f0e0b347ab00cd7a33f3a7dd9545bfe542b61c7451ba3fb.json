{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\HomeworkDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation, useSearchParams } from 'react-router-dom';\nimport { Typography, Card, Descriptions, Tag, Divider, Spin, Image, Button, Tabs, Alert, message, List, Space, Modal, Table } from 'antd';\nimport { CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, ArrowLeftOutlined, ClockCircleOutlined, ExclamationCircleOutlined, FileImageOutlined, DownloadOutlined } from '@ant-design/icons';\nimport { getHomework, getSubjects, correctHomework, getHomeworkHistory, getAnnotatedImages, generateAnnotations } from '../utils/api';\nimport ReactMarkdown from 'react-markdown';\nimport './HomeworkDetail.css';\nimport HomeworkCorrectionEditor from './HomeworkCorrectionEditor';\nimport { useAuth } from '../utils/auth';\nimport { getImageUrl } from '../utils/imageUrl';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst HomeworkDetail = ({\n  user,\n  showHistory\n}) => {\n  _s();\n  const {\n    homeworkId\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const [loading, setLoading] = useState(true);\n  const [homework, setHomework] = useState(null);\n  const [error, setError] = useState(null);\n  const [activeTabKey, setActiveTabKey] = useState('1');\n  const [subjects, setSubjects] = useState([]);\n  const [correcting, setCorrecting] = useState(false);\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [historyVersions, setHistoryVersions] = useState([]);\n  const [historyLoading, setHistoryLoading] = useState(false);\n  const [annotatedImages, setAnnotatedImages] = useState([]);\n  const [generatingAnnotations, setGeneratingAnnotations] = useState(false);\n  const {\n    user: authUser\n  } = useAuth();\n\n  // 获取作业详情或历史记录\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n\n        // 如果是显示历史记录\n        if (showHistory) {\n          const studentId = searchParams.get('student_id');\n          const assignmentId = searchParams.get('assignment_id');\n          if (!studentId || !assignmentId) {\n            setError('缺少必要参数：学生ID和作业任务ID');\n            return;\n          }\n          setHistoryLoading(true);\n          const historyData = await getHomeworkHistory(studentId, assignmentId);\n          console.log('获取到的作业历史记录:', historyData);\n          setHistoryVersions(historyData || []);\n          setHistoryLoading(false);\n\n          // 如果有历史记录，设置第一条作为当前作业\n          if (historyData && historyData.length > 0) {\n            setHomework(historyData[0]);\n          }\n        } else {\n          // 获取单个作业详情\n          const data = await getHomework(homeworkId);\n          console.log('获取到的作业详情:', data);\n          setHomework(data);\n\n          // 如果有批改结果，默认显示批改结果标签页\n          if (data && data.corrections && data.corrections.length > 0) {\n            setActiveTabKey('2');\n          }\n        }\n\n        // 获取学科列表\n        const subjectsData = await getSubjects();\n        setSubjects(subjectsData);\n\n        // 获取带批注的图片\n        try {\n          const annotatedImagesData = await getAnnotatedImages(homeworkId);\n          setAnnotatedImages(annotatedImagesData);\n          console.log('获取到的批注图片:', annotatedImagesData);\n        } catch (error) {\n          console.error('获取批注图片失败:', error);\n        }\n      } catch (err) {\n        console.error(showHistory ? '获取作业历史记录失败:' : '获取作业详情失败:', err);\n        setError((showHistory ? '获取作业历史记录失败: ' : '获取作业详情失败: ') + (err.message || '未知错误'));\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [homeworkId, showHistory, searchParams]);\n\n  // 处理手动批改\n  const handleCorrect = async () => {\n    try {\n      setCorrecting(true);\n      await correctHomework(homeworkId);\n      message.success('已开始批改作业，请稍后刷新查看结果');\n\n      // 3秒后自动刷新页面\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n    } catch (error) {\n      console.error('批改作业失败:', error);\n      message.error('批改作业失败，请稍后重试');\n    } finally {\n      setCorrecting(false);\n    }\n  };\n\n  // 处理图片预览\n  const handlePreview = imagePath => {\n    setPreviewImage(imagePath);\n    setPreviewVisible(true);\n  };\n\n  // 获取带批注的图片\n  const fetchAnnotatedImages = async () => {\n    try {\n      const data = await getAnnotatedImages(homeworkId);\n      setAnnotatedImages(data);\n    } catch (error) {\n      console.error('获取批注图片失败:', error);\n    }\n  };\n\n  // 生成批注图片\n  const handleGenerateAnnotations = async () => {\n    console.log('🚨🚨🚨 点击生成批注图片按钮 🚨🚨🚨');\n    console.log('作业状态:', homework === null || homework === void 0 ? void 0 : homework.status);\n    console.log('作业ID:', homeworkId);\n    console.log('按钮是否禁用:', generatingAnnotations || !homework || (homework === null || homework === void 0 ? void 0 : homework.status) !== 'corrected' && (homework === null || homework === void 0 ? void 0 : homework.status) !== 'graded');\n    try {\n      setGeneratingAnnotations(true);\n      console.log('🚨🚨🚨 开始调用generateAnnotations API 🚨🚨🚨');\n      const result = await generateAnnotations(homeworkId);\n      console.log('🚨🚨🚨 generateAnnotations API 调用成功 🚨🚨🚨', result);\n      message.success(result.message || '批注生成成功');\n      await fetchAnnotatedImages();\n    } catch (error) {\n      console.error('❌ 生成批注图片失败:', error);\n      message.error('生成批注图片失败，请稍后重试');\n    } finally {\n      setGeneratingAnnotations(false);\n    }\n  };\n\n  // 渲染作业状态标签\n  const renderStatusTag = status => {\n    let color = 'default';\n    let icon = null;\n    let text = '未知状态';\n    switch (status) {\n      case 'submitted':\n        color = 'warning';\n        icon = /*#__PURE__*/_jsxDEV(SyncOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 16\n        }, this);\n        text = '已提交';\n        break;\n      case 'grading':\n      case 'correcting':\n        color = 'processing';\n        icon = /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          spin: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 16\n        }, this);\n        text = '批改中';\n        break;\n      case 'graded':\n      case 'corrected':\n        color = 'success';\n        icon = /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 16\n        }, this);\n        text = '已批改';\n        break;\n      default:\n        break;\n    }\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: color,\n      icon: icon,\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 渲染批改结果\n  const renderCorrections = () => {\n    if (!homework || !homework.corrections || homework.corrections.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6682\\u65E0\\u6279\\u6539\\u6570\\u636E\",\n        type: \"info\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 14\n      }, this);\n    }\n    console.log('渲染批改结果:', homework.corrections);\n\n    // 如果用户是教师，显示批改编辑器\n    const showEditor = user && user.is_teacher && (homework.status === 'graded' || homework.status === 'corrected');\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [showEditor && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(HomeworkCorrectionEditor, {\n          homework: homework,\n          onSaved: updatedHomework => {\n            // 更新本地作业数据\n            setHomework({\n              ...homework,\n              ...updatedHomework\n            });\n            message.success('批改结果已更新');\n          },\n          onCancel: () => console.log('取消编辑批改结果')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), homework.corrections.map((correction, index) => {\n        // 解析批改数据\n        let correctionData = {};\n        try {\n          if (typeof correction.correction_data === 'string') {\n            correctionData = JSON.parse(correction.correction_data);\n          } else if (typeof correction.correction_data === 'object') {\n            correctionData = correction.correction_data;\n          }\n          console.log('解析后的批改数据:', correctionData);\n        } catch (error) {\n          console.error('解析批改数据失败:', error, correction.correction_data);\n          return /*#__PURE__*/_jsxDEV(Alert, {\n            message: `批改数据格式错误: ${error.message}`,\n            description: `原始数据: ${JSON.stringify(correction.correction_data).substring(0, 100)}...`,\n            type: \"error\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 20\n          }, this);\n        }\n\n        // 如果没有questions字段，尝试显示原始数据\n        if (!correctionData.questions) {\n          return /*#__PURE__*/_jsxDEV(Card, {\n            title: `第${correction.page_number}页批改结果`,\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u6279\\u6539\\u6570\\u636E\\u683C\\u5F0F\\u4E0D\\u6807\\u51C6\",\n              type: \"warning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"left\",\n              children: \"\\u539F\\u59CB\\u6279\\u6539\\u7ED3\\u679C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                whiteSpace: 'pre-wrap',\n                wordBreak: 'break-word'\n              },\n              children: typeof correctionData === 'object' ? JSON.stringify(correctionData, null, 2) : correctionData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this);\n        }\n        const questions = correctionData.questions || [];\n        const summary = correctionData.summary || {\n          total_questions: 0,\n          correct_count: 0,\n          accuracy: 0\n        };\n\n        // 获取正确和错误的题号\n        const correctQuestionNumbers = questions.filter(q => q.is_correct).map(q => q.question_number || '未知').join(', ');\n        const incorrectQuestionNumbers = questions.filter(q => !q.is_correct).map(q => q.question_number || '未知').join(', ');\n\n        // 显示是否由教师修改过\n        const isTeacherEdited = correctionData.edited_by_teacher;\n        return /*#__PURE__*/_jsxDEV(Card, {\n          title: `第${correction.page_number}页批改结果`,\n          style: {\n            marginBottom: 16\n          },\n          extra: isTeacherEdited ? /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: \"\\u6559\\u5E08\\u5DF2\\u4FEE\\u6539\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 40\n          }, this) : null,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u9898\\u76EE\\u603B\\u6570\",\n              children: summary.total_questions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6B63\\u786E\\u6570\\u91CF\",\n              children: summary.correct_count\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6B63\\u786E\\u7387\",\n              children: [(summary.accuracy * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6B63\\u786E\\u9898\\u53F7\",\n              children: correctQuestionNumbers || '无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u9519\\u8BEF\\u9898\\u53F7\",\n              children: incorrectQuestionNumbers || '无'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), isTeacherEdited && correctionData.edit_time && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6559\\u5E08\\u4FEE\\u6539\\u65F6\\u95F4\",\n              children: new Date(correctionData.edit_time).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            orientation: \"left\",\n            children: \"\\u9898\\u76EE\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), questions.map((q, qIndex) => /*#__PURE__*/_jsxDEV(Card, {\n            type: \"inner\",\n            title: `题目${q.question_number || qIndex + 1}: ${q.question_type || '未知类型'}`,\n            style: {\n              marginBottom: 10\n            },\n            className: \"question-card\",\n            extra: q.is_correct ? /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"success\",\n              icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 52\n              }, this),\n              children: \"\\u6B63\\u786E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"error\",\n              icon: /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 50\n              }, this),\n              children: \"\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 25\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u9898\\u76EE\\u5185\\u5BB9\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this), \" \", q.question_content]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5B66\\u751F\\u7B54\\u6848\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 21\n              }, this), \" \", q.student_answer]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u6B63\\u786E\\u7B54\\u6848\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 21\n              }, this), \" \", q.correct_answer]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 19\n            }, this), (!q.is_correct || q.is_correct && q.analysis) && /*#__PURE__*/_jsxDEV(Alert, {\n              message: q.is_correct ? \"教师评语\" : \"错误分析\",\n              description: q.analysis || (q.is_correct ? \"\" : \"该题目缺少分析\"),\n              type: q.is_correct ? \"info\" : \"warning\",\n              showIcon: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 21\n            }, this), q.reinforcement && /*#__PURE__*/_jsxDEV(Alert, {\n              message: \"\\u5F3A\\u5316\\u5EFA\\u8BAE\",\n              description: q.reinforcement,\n              type: \"info\",\n              showIcon: true,\n              style: {\n                marginTop: 10\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 21\n            }, this), q.edited && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 10\n              },\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: \"\\u6559\\u5E08\\u5DF2\\u4FEE\\u6539\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 21\n            }, this)]\n          }, qIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this))]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true);\n  };\n\n  // 渲染作业图片\n  const renderImages = () => {\n    if (!homework || !homework.images || homework.images.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6682\\u65E0\\u4F5C\\u4E1A\\u56FE\\u7247\",\n        type: \"info\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"homework-images\",\n      children: homework.images.map((image, index) => {\n        // 处理图片路径\n        let imagePath = getImageUrl(image.image_path);\n\n        // 获取文件名\n        const fileName = imagePath ? imagePath.split('/').pop() : `图片 ${index + 1}`;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: /*#__PURE__*/_jsxDEV(Image, {\n              src: imagePath,\n              alt: `作业图片 ${index + 1} - ${fileName}`,\n              style: {\n                maxWidth: '100%'\n              },\n              fallback: \"/broken-image.png\",\n              preview: false,\n              onClick: () => handlePreview(imagePath)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-caption\",\n            children: [\"\\u7B2C \", image.page_number, \" \\u9875\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染批注图片\n  const renderAnnotatedImages = () => {\n    if (!annotatedImages || annotatedImages.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u6682\\u65E0\\u6279\\u6CE8\\u56FE\\u7247\",\n          description: \"\\u70B9\\u51FB\\u4E0B\\u65B9\\u6309\\u94AE\\u751F\\u6210\\u6279\\u6CE8\\u56FE\\u7247\",\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: handleGenerateAnnotations,\n          loading: generatingAnnotations,\n          style: {\n            marginTop: '16px'\n          },\n          disabled: generatingAnnotations || !homework || homework.status !== 'corrected' && homework.status !== 'graded',\n          children: generatingAnnotations ? '生成中...' : '生成批注图片'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"homework-images\",\n      children: [annotatedImages.map(image => {\n        // 处理图片路径\n        let imagePath = image.image_path;\n        console.log('原始批注图片路径:', imagePath);\n        if (imagePath && !imagePath.startsWith('http')) {\n          // 根据当前域名构建完整的图片URL\n          const currentHost = window.location.hostname;\n          const apiUrl = process.env.REACT_APP_API_URL;\n          let staticBaseUrl;\n          if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\n            staticBaseUrl = 'http://localhost:8083';\n          } else if (currentHost === '17learn.cn') {\n            staticBaseUrl = 'http://danphy.xicp.net:23277';\n          } else if (apiUrl) {\n            staticBaseUrl = apiUrl.replace('/api', '');\n          }\n          if (imagePath.startsWith('/')) {\n            imagePath = `${staticBaseUrl}${imagePath}`;\n          } else {\n            imagePath = `${staticBaseUrl}/${imagePath}`;\n          }\n          console.log('处理后的批注图片路径:', imagePath);\n        }\n\n        // 获取文件名\n        const fileName = imagePath ? imagePath.split('/').pop() : `批注图片 ${image.page_number}`;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"image-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: /*#__PURE__*/_jsxDEV(Image, {\n              src: imagePath,\n              alt: `批注图片 ${image.page_number} - ${fileName}`,\n              style: {\n                maxWidth: '100%'\n              },\n              fallback: \"/broken-image.png\",\n              preview: false,\n              onClick: () => handlePreview(imagePath)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-caption\",\n            children: [\"\\u7B2C \", image.page_number, \" \\u9875 - \\u6279\\u6CE8\\u7248\\u672C\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px',\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 25\n              }, this),\n              onClick: () => window.open(imagePath, '_blank'),\n              children: \"\\u4E0B\\u8F7D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          clear: 'both',\n          textAlign: 'center',\n          marginTop: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: handleGenerateAnnotations,\n          loading: generatingAnnotations,\n          disabled: generatingAnnotations,\n          children: generatingAnnotations ? '重新生成中...' : '重新生成批注'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 提取科目名称\n  const getSubjectName = () => {\n    if (!homework) return \"未关联作业科目\";\n\n    // 直接使用作业中的科目名称（如果存在）\n    if (homework.subject_name) {\n      return homework.subject_name;\n    }\n\n    // 如果有科目ID但没有科目名称，尝试从subjects列表中查找\n    if (homework.subject_id && subjects && subjects.length > 0) {\n      const subject = subjects.find(s => s.id === homework.subject_id);\n      if (subject) return subject.name;\n    }\n\n    // 查看作业任务关联的班级ID，这可能与特定科目相关联\n    if (homework.assignment_id && homework.assignment_title) {\n      // 从作业任务标题中提取科目名称，使用动态获取的科目数据\n      const subjectPatterns = subjects.map(s => s.name);\n\n      // 尝试从作业任务标题中匹配科目名称\n      for (const subject of subjectPatterns) {\n        if (homework.assignment_title.includes(subject)) {\n          return subject;\n        }\n      }\n\n      // 如果无法提取科目名称，直接返回作业任务标题\n      return homework.assignment_title;\n    }\n\n    // 如果没有作业任务标题，尝试从作业标题中提取科目名称\n    if (homework.title) {\n      const subjectPatterns = subjects.map(s => s.name);\n      for (const subject of subjectPatterns) {\n        if (homework.title.includes(subject)) {\n          return subject;\n        }\n      }\n    }\n\n    // 如果都无法提取，返回默认值\n    return subjects.length > 0 ? subjects[0].name : \"未关联作业科目\";\n  };\n\n  // 获取科目名称\n  const subjectName = homework ? getSubjectName() : \"未关联作业科目\";\n\n  // 渲染作业历史记录表格\n  const renderHistoryTable = () => {\n    var _historyVersions$, _historyVersions$2, _historyVersions$3;\n    if (historyLoading) {\n      return /*#__PURE__*/_jsxDEV(Spin, {\n        tip: \"\\u52A0\\u8F7D\\u4F5C\\u4E1A\\u5386\\u53F2\\u8BB0\\u5F55\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 14\n      }, this);\n    }\n    if (!historyVersions || historyVersions.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u6CA1\\u6709\\u627E\\u5230\\u4F5C\\u4E1A\\u5386\\u53F2\\u8BB0\\u5F55\",\n        type: \"info\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 14\n      }, this);\n    }\n    const columns = [{\n      title: '提交时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: date => new Date(date).toLocaleString()\n    }, {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: status => renderStatusTag(status)\n    }, {\n      title: '分数',\n      dataIndex: 'score',\n      key: 'score',\n      render: score => score ? `${score}分` : '-'\n    }, {\n      title: '准确率',\n      dataIndex: 'accuracy',\n      key: 'accuracy',\n      render: accuracy => accuracy ? `${(accuracy * 100).toFixed(2)}%` : '-'\n    }, {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => navigate(`/homework/${record.id}`),\n        children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 577,\n        columnNumber: 11\n      }, this)\n    }];\n    return /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F5C\\u4E1A\\u5386\\u53F2\\u8BB0\\u5F55\",\n      style: {\n        marginBottom: 20\n      },\n      children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n        size: \"small\",\n        column: 3,\n        style: {\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5B66\\u751F\\u59D3\\u540D\",\n          children: ((_historyVersions$ = historyVersions[0]) === null || _historyVersions$ === void 0 ? void 0 : _historyVersions$.student_name) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4F5C\\u4E1A\\u4EFB\\u52A1\",\n          children: ((_historyVersions$2 = historyVersions[0]) === null || _historyVersions$2 === void 0 ? void 0 : _historyVersions$2.assignment_title) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u73ED\\u7EA7\",\n          children: ((_historyVersions$3 = historyVersions[0]) === null || _historyVersions$3 === void 0 ? void 0 : _historyVersions$3.class_name) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u63D0\\u4EA4\\u6B21\\u6570\",\n          children: historyVersions.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        dataSource: historyVersions,\n        columns: columns,\n        rowKey: \"id\",\n        pagination: false,\n        size: \"middle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 渲染内容\n  const renderContent = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px 0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          size: \"large\",\n          tip: \"\\u52A0\\u8F7D\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this);\n    }\n    if (error) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u9519\\u8BEF\",\n        description: error,\n        type: \"error\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 14\n      }, this);\n    }\n\n    // 如果是显示历史记录模式\n    if (showHistory) {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [renderHistoryTable(), homework && /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6700\\u65B0\\u63D0\\u4EA4\\u8BE6\\u60C5\",\n          children: renderHomeworkDetail()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true);\n    }\n    return renderHomeworkDetail();\n  };\n\n  // 渲染作业详情\n  const renderHomeworkDetail = () => {\n    if (!homework) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u672A\\u627E\\u5230\\u4F5C\\u4E1A\\u6570\\u636E\",\n        type: \"warning\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(-1),\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          title: homework.title,\n          bordered: true,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u72B6\\u6001\",\n            children: renderStatusTag(homework.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5B66\\u751F\",\n            children: homework.student_name || `学生ID: ${homework.student_id}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u73ED\\u7EA7\",\n            children: homework.class_name || '未分配班级'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u63D0\\u4EA4\\u65F6\\u95F4\",\n            children: new Date(homework.created_at).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6279\\u6539\\u65F6\\u95F4\",\n            children: homework.graded_at ? new Date(homework.graded_at).toLocaleString() : '未批改'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5B66\\u79D1\",\n            children: getSubjectName()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), homework.score !== null && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5206\\u6570\",\n            children: homework.score\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this), homework.accuracy !== null && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u51C6\\u786E\\u7387\",\n            children: [(homework.accuracy * 100).toFixed(2), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 15\n          }, this), homework.version_count && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u63D0\\u4EA4\\u6B21\\u6570\",\n            children: homework.version_count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), homework.description && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '16px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u4F5C\\u4E1A\\u63CF\\u8FF0\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: homework.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 13\n        }, this), user && user.is_teacher && homework.status === 'submitted' && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '16px 0'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleCorrect,\n            loading: correcting,\n            disabled: correcting,\n            children: correcting ? '批改中...' : '开始批改'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTabKey,\n        onChange: setActiveTabKey,\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u4F5C\\u4E1A\\u56FE\\u7247\",\n          children: renderImages()\n        }, \"1\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6279\\u6539\\u7ED3\\u679C\",\n          children: renderCorrections()\n        }, \"2\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6279\\u6CE8\\u56FE\\u7247\",\n          children: renderAnnotatedImages()\n        }, \"3\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        visible: previewVisible,\n        title: \"\\u56FE\\u7247\\u9884\\u89C8\",\n        footer: null,\n        onCancel: () => setPreviewVisible(false),\n        width: \"80%\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          alt: \"\\u9884\\u89C8\",\n          style: {\n            width: '100%'\n          },\n          src: previewImage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n  return renderContent();\n};\n_s(HomeworkDetail, \"F3ZF4+gHWqAxaO0F0gy4HJQCIuo=\", false, function () {\n  return [useParams, useNavigate, useLocation, useSearchParams, useAuth];\n});\n_c = HomeworkDetail;\nexport default HomeworkDetail;\nvar _c;\n$RefreshReg$(_c, \"HomeworkDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useSearchParams", "Typography", "Card", "Descriptions", "Tag", "Divider", "Spin", "Image", "<PERSON><PERSON>", "Tabs", "<PERSON><PERSON>", "message", "List", "Space", "Modal", "Table", "CheckCircleOutlined", "CloseCircleOutlined", "SyncOutlined", "ArrowLeftOutlined", "ClockCircleOutlined", "ExclamationCircleOutlined", "FileImageOutlined", "DownloadOutlined", "getHomework", "getSubjects", "correctHomework", "getHomeworkHistory", "getAnnotatedImages", "generateAnnotations", "ReactMarkdown", "HomeworkCorrectionEditor", "useAuth", "getImageUrl", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Paragraph", "TabPane", "HomeworkDetail", "user", "showHistory", "_s", "homeworkId", "navigate", "location", "searchParams", "loading", "setLoading", "homework", "setHomework", "error", "setError", "activeTabKey", "setActiveTabKey", "subjects", "setSubjects", "correcting", "setCorrecting", "previewVisible", "setPreviewVisible", "previewImage", "setPreviewImage", "historyVersions", "setHistoryVersions", "historyLoading", "setHistoryLoading", "annotatedImages", "setAnnotatedImages", "generatingAnnotations", "setGeneratingAnnotations", "authUser", "fetchData", "studentId", "get", "assignmentId", "historyData", "console", "log", "length", "data", "corrections", "subjectsData", "annotatedImagesData", "err", "handleCorrect", "success", "setTimeout", "window", "reload", "handlePreview", "imagePath", "fetchAnnotatedImages", "handleGenerateAnnotations", "status", "result", "renderStatusTag", "color", "icon", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "spin", "children", "renderCorrections", "type", "showEditor", "is_teacher", "onSaved", "updatedHomework", "onCancel", "map", "correction", "index", "correctionData", "correction_data", "JSON", "parse", "description", "stringify", "substring", "questions", "title", "page_number", "style", "marginBottom", "orientation", "whiteSpace", "wordBreak", "summary", "total_questions", "correct_count", "accuracy", "correctQuestionNumbers", "filter", "q", "is_correct", "question_number", "join", "incorrectQuestionNumbers", "isTeacherEdited", "edited_by_teacher", "extra", "bordered", "column", "size", "<PERSON><PERSON>", "label", "toFixed", "edit_time", "Date", "toLocaleString", "qIndex", "question_type", "className", "strong", "question_content", "student_answer", "correct_answer", "analysis", "showIcon", "reinforcement", "marginTop", "edited", "renderImages", "images", "image", "image_path", "split", "pop", "src", "alt", "max<PERSON><PERSON><PERSON>", "fallback", "preview", "onClick", "id", "renderAnnotatedImages", "textAlign", "padding", "disabled", "startsWith", "currentHost", "hostname", "apiUrl", "process", "env", "REACT_APP_API_URL", "staticBaseUrl", "replace", "open", "clear", "getSubjectName", "subject_name", "subject_id", "subject", "find", "s", "name", "assignment_id", "assignment_title", "subjectPatterns", "includes", "subjectName", "renderHistoryTable", "_historyVersions$", "_historyVersions$2", "_historyVersions$3", "tip", "columns", "dataIndex", "key", "render", "date", "score", "_", "record", "student_name", "class_name", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "renderContent", "renderHomeworkDetail", "student_id", "created_at", "graded_at", "version_count", "margin", "active<PERSON><PERSON>", "onChange", "tab", "visible", "footer", "width", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/HomeworkDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useParams, useNavigate, useLocation, useSearchParams } from 'react-router-dom';\r\nimport {\r\n  Typography, Card, Descriptions, Tag, Divider, Spin, Image,\r\n  Button, Tabs, Alert, message, List, Space, Modal, Table\r\n} from 'antd';\r\nimport { \r\n  CheckCircleOutlined, CloseCircleOutlined, \r\n  SyncOutlined, ArrowLeftOutlined, ClockCircleOutlined, ExclamationCircleOutlined, FileImageOutlined, DownloadOutlined\r\n} from '@ant-design/icons';\r\nimport { getHomework, getSubjects, correctHomework, getHomeworkHistory, getAnnotatedImages, generateAnnotations } from '../utils/api';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport './HomeworkDetail.css';\r\nimport HomeworkCorrectionEditor from './HomeworkCorrectionEditor';\r\nimport { useAuth } from '../utils/auth';\r\nimport { getImageUrl } from '../utils/imageUrl';\r\n\r\nconst { Title, Text, Paragraph } = Typography;\r\nconst { TabPane } = Tabs;\r\n\r\nconst HomeworkDetail = ({ user, showHistory }) => {\r\n  const { homeworkId } = useParams();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [searchParams] = useSearchParams();\r\n  const [loading, setLoading] = useState(true);\r\n  const [homework, setHomework] = useState(null);\r\n  const [error, setError] = useState(null);\r\n  const [activeTabKey, setActiveTabKey] = useState('1');\r\n  const [subjects, setSubjects] = useState([]);\r\n  const [correcting, setCorrecting] = useState(false);\r\n  const [previewVisible, setPreviewVisible] = useState(false);\r\n  const [previewImage, setPreviewImage] = useState('');\r\n  const [historyVersions, setHistoryVersions] = useState([]);\r\n  const [historyLoading, setHistoryLoading] = useState(false);\r\n  const [annotatedImages, setAnnotatedImages] = useState([]);\r\n  const [generatingAnnotations, setGeneratingAnnotations] = useState(false);\r\n  const { user: authUser } = useAuth();\r\n  \r\n  // 获取作业详情或历史记录\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        \r\n        // 如果是显示历史记录\r\n        if (showHistory) {\r\n          const studentId = searchParams.get('student_id');\r\n          const assignmentId = searchParams.get('assignment_id');\r\n          \r\n          if (!studentId || !assignmentId) {\r\n            setError('缺少必要参数：学生ID和作业任务ID');\r\n            return;\r\n          }\r\n          \r\n          setHistoryLoading(true);\r\n          const historyData = await getHomeworkHistory(studentId, assignmentId);\r\n          console.log('获取到的作业历史记录:', historyData);\r\n          setHistoryVersions(historyData || []);\r\n          setHistoryLoading(false);\r\n          \r\n          // 如果有历史记录，设置第一条作为当前作业\r\n          if (historyData && historyData.length > 0) {\r\n            setHomework(historyData[0]);\r\n          }\r\n        } else {\r\n          // 获取单个作业详情\r\n          const data = await getHomework(homeworkId);\r\n          console.log('获取到的作业详情:', data);\r\n          setHomework(data);\r\n          \r\n          // 如果有批改结果，默认显示批改结果标签页\r\n          if (data && data.corrections && data.corrections.length > 0) {\r\n            setActiveTabKey('2');\r\n          }\r\n        }\r\n        \r\n        // 获取学科列表\r\n        const subjectsData = await getSubjects();\r\n        setSubjects(subjectsData);\r\n\r\n        // 获取带批注的图片\r\n        try {\r\n          const annotatedImagesData = await getAnnotatedImages(homeworkId);\r\n          setAnnotatedImages(annotatedImagesData);\r\n          console.log('获取到的批注图片:', annotatedImagesData);\r\n        } catch (error) {\r\n          console.error('获取批注图片失败:', error);\r\n        }\r\n      } catch (err) {\r\n        console.error(showHistory ? '获取作业历史记录失败:' : '获取作业详情失败:', err);\r\n        setError((showHistory ? '获取作业历史记录失败: ' : '获取作业详情失败: ') + (err.message || '未知错误'));\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    \r\n    fetchData();\r\n  }, [homeworkId, showHistory, searchParams]);\r\n  \r\n  // 处理手动批改\r\n  const handleCorrect = async () => {\r\n    try {\r\n      setCorrecting(true);\r\n      await correctHomework(homeworkId);\r\n      message.success('已开始批改作业，请稍后刷新查看结果');\r\n      \r\n      // 3秒后自动刷新页面\r\n      setTimeout(() => {\r\n        window.location.reload();\r\n      }, 3000);\r\n    } catch (error) {\r\n      console.error('批改作业失败:', error);\r\n      message.error('批改作业失败，请稍后重试');\r\n    } finally {\r\n      setCorrecting(false);\r\n    }\r\n  };\r\n  \r\n  // 处理图片预览\r\n  const handlePreview = (imagePath) => {\r\n    setPreviewImage(imagePath);\r\n    setPreviewVisible(true);\r\n  };\r\n\r\n  // 获取带批注的图片\r\n  const fetchAnnotatedImages = async () => {\r\n    try {\r\n      const data = await getAnnotatedImages(homeworkId);\r\n      setAnnotatedImages(data);\r\n    } catch (error) {\r\n      console.error('获取批注图片失败:', error);\r\n    }\r\n  };\r\n\r\n  // 生成批注图片\r\n  const handleGenerateAnnotations = async () => {\r\n    console.log('🚨🚨🚨 点击生成批注图片按钮 🚨🚨🚨');\r\n    console.log('作业状态:', homework?.status);\r\n    console.log('作业ID:', homeworkId);\r\n    console.log('按钮是否禁用:', generatingAnnotations || !homework || (homework?.status !== 'corrected' && homework?.status !== 'graded'));\r\n    \r\n    try {\r\n      setGeneratingAnnotations(true);\r\n      console.log('🚨🚨🚨 开始调用generateAnnotations API 🚨🚨🚨');\r\n      const result = await generateAnnotations(homeworkId);\r\n      console.log('🚨🚨🚨 generateAnnotations API 调用成功 🚨🚨🚨', result);\r\n      message.success(result.message || '批注生成成功');\r\n      await fetchAnnotatedImages();\r\n    } catch (error) {\r\n      console.error('❌ 生成批注图片失败:', error);\r\n      message.error('生成批注图片失败，请稍后重试');\r\n    } finally {\r\n      setGeneratingAnnotations(false);\r\n    }\r\n  };\r\n  \r\n  // 渲染作业状态标签\r\n  const renderStatusTag = (status) => {\r\n    let color = 'default';\r\n    let icon = null;\r\n    let text = '未知状态';\r\n    \r\n    switch (status) {\r\n      case 'submitted':\r\n        color = 'warning';\r\n        icon = <SyncOutlined />;\r\n        text = '已提交';\r\n        break;\r\n      case 'grading':\r\n      case 'correcting':\r\n        color = 'processing';\r\n        icon = <SyncOutlined spin />;\r\n        text = '批改中';\r\n        break;\r\n      case 'graded':\r\n      case 'corrected':\r\n        color = 'success';\r\n        icon = <CheckCircleOutlined />;\r\n        text = '已批改';\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    \r\n    return <Tag color={color} icon={icon}>{text}</Tag>;\r\n  };\r\n  \r\n  // 渲染批改结果\r\n  const renderCorrections = () => {\r\n    if (!homework || !homework.corrections || homework.corrections.length === 0) {\r\n      return <Alert message=\"暂无批改数据\" type=\"info\" />;\r\n    }\r\n    \r\n    console.log('渲染批改结果:', homework.corrections);\r\n    \r\n    // 如果用户是教师，显示批改编辑器\r\n    const showEditor = user && user.is_teacher && (homework.status === 'graded' || homework.status === 'corrected');\r\n    \r\n    return (\r\n      <>\r\n        {showEditor && (\r\n          <>\r\n            <HomeworkCorrectionEditor \r\n              homework={homework} \r\n              onSaved={(updatedHomework) => {\r\n                // 更新本地作业数据\r\n                setHomework({\r\n                  ...homework,\r\n                  ...updatedHomework\r\n                });\r\n                message.success('批改结果已更新');\r\n              }}\r\n              onCancel={() => console.log('取消编辑批改结果')}\r\n            />\r\n            <Divider />\r\n          </>\r\n        )}\r\n        \r\n        {homework.corrections.map((correction, index) => {\r\n          // 解析批改数据\r\n          let correctionData = {};\r\n          try {\r\n            if (typeof correction.correction_data === 'string') {\r\n              correctionData = JSON.parse(correction.correction_data);\r\n            } else if (typeof correction.correction_data === 'object') {\r\n              correctionData = correction.correction_data;\r\n            }\r\n            console.log('解析后的批改数据:', correctionData);\r\n          } catch (error) {\r\n            console.error('解析批改数据失败:', error, correction.correction_data);\r\n            return <Alert key={index} message={`批改数据格式错误: ${error.message}`} description={`原始数据: ${JSON.stringify(correction.correction_data).substring(0, 100)}...`} type=\"error\" />;\r\n          }\r\n          \r\n          // 如果没有questions字段，尝试显示原始数据\r\n          if (!correctionData.questions) {\r\n            return (\r\n              <Card \r\n                key={index} \r\n                title={`第${correction.page_number}页批改结果`}\r\n                style={{ marginBottom: 16 }}\r\n              >\r\n                <Alert message=\"批改数据格式不标准\" type=\"warning\" />\r\n                <Divider orientation=\"left\">原始批改结果</Divider>\r\n                <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>\r\n                  {typeof correctionData === 'object' ? JSON.stringify(correctionData, null, 2) : correctionData}\r\n                </pre>\r\n              </Card>\r\n            );\r\n          }\r\n          \r\n          const questions = correctionData.questions || [];\r\n          const summary = correctionData.summary || { \r\n            total_questions: 0, \r\n            correct_count: 0, \r\n            accuracy: 0 \r\n          };\r\n          \r\n          // 获取正确和错误的题号\r\n          const correctQuestionNumbers = questions\r\n            .filter(q => q.is_correct)\r\n            .map(q => q.question_number || '未知')\r\n            .join(', ');\r\n          \r\n          const incorrectQuestionNumbers = questions\r\n            .filter(q => !q.is_correct)\r\n            .map(q => q.question_number || '未知')\r\n            .join(', ');\r\n          \r\n          // 显示是否由教师修改过\r\n          const isTeacherEdited = correctionData.edited_by_teacher;\r\n          \r\n          return (\r\n            <Card \r\n              key={index} \r\n              title={`第${correction.page_number}页批改结果`}\r\n              style={{ marginBottom: 16 }}\r\n              extra={isTeacherEdited ? <Tag color=\"blue\">教师已修改</Tag> : null}\r\n            >\r\n              <Descriptions bordered column={1} size=\"small\">\r\n                <Descriptions.Item label=\"题目总数\">{summary.total_questions}</Descriptions.Item>\r\n                <Descriptions.Item label=\"正确数量\">{summary.correct_count}</Descriptions.Item>\r\n                <Descriptions.Item label=\"正确率\">\r\n                  {(summary.accuracy * 100).toFixed(1)}%\r\n                </Descriptions.Item>\r\n                <Descriptions.Item label=\"正确题号\">\r\n                  {correctQuestionNumbers || '无'}\r\n                </Descriptions.Item>\r\n                <Descriptions.Item label=\"错误题号\">\r\n                  {incorrectQuestionNumbers || '无'}\r\n                </Descriptions.Item>\r\n                {isTeacherEdited && correctionData.edit_time && (\r\n                  <Descriptions.Item label=\"教师修改时间\">\r\n                    {new Date(correctionData.edit_time).toLocaleString()}\r\n                  </Descriptions.Item>\r\n                )}\r\n              </Descriptions>\r\n              \r\n              <Divider orientation=\"left\">题目详情</Divider>\r\n              \r\n              {questions.map((q, qIndex) => (\r\n                <Card \r\n                  key={qIndex}\r\n                  type=\"inner\"\r\n                  title={`题目${q.question_number || qIndex + 1}: ${q.question_type || '未知类型'}`}\r\n                  style={{ marginBottom: 10 }}\r\n                  className=\"question-card\"\r\n                  extra={\r\n                    q.is_correct \r\n                      ? <Tag color=\"success\" icon={<CheckCircleOutlined />}>正确</Tag>\r\n                      : <Tag color=\"error\" icon={<CloseCircleOutlined />}>错误</Tag>\r\n                  }\r\n                >\r\n                  <Paragraph>\r\n                    <Text strong>题目内容：</Text> {q.question_content}\r\n                  </Paragraph>\r\n                  <Paragraph>\r\n                    <Text strong>学生答案：</Text> {q.student_answer}\r\n                  </Paragraph>\r\n                  <Paragraph>\r\n                    <Text strong>正确答案：</Text> {q.correct_answer}\r\n                  </Paragraph>\r\n                  \r\n                  {/* 错误题目必须显示分析，正确题目只在有评语时才显示 */}\r\n                  {(!q.is_correct || (q.is_correct && q.analysis)) && (\r\n                    <Alert\r\n                      message={q.is_correct ? \"教师评语\" : \"错误分析\"}\r\n                      description={q.analysis || (q.is_correct ? \"\" : \"该题目缺少分析\")}\r\n                      type={q.is_correct ? \"info\" : \"warning\"}\r\n                      showIcon\r\n                    />\r\n                  )}\r\n                  \r\n                  {q.reinforcement && (\r\n                    <Alert\r\n                      message=\"强化建议\"\r\n                      description={q.reinforcement}\r\n                      type=\"info\"\r\n                      showIcon\r\n                      style={{ marginTop: 10 }}\r\n                    />\r\n                  )}\r\n                  \r\n                  {q.edited && (\r\n                    <div style={{ marginTop: 10 }}>\r\n                      <Tag color=\"blue\">教师已修改</Tag>\r\n                    </div>\r\n                  )}\r\n                </Card>\r\n              ))}\r\n            </Card>\r\n          );\r\n        })}\r\n      </>\r\n    );\r\n  };\r\n  \r\n  // 渲染作业图片\r\n  const renderImages = () => {\r\n    if (!homework || !homework.images || homework.images.length === 0) {\r\n      return <Alert message=\"暂无作业图片\" type=\"info\" />;\r\n    }\r\n    \r\n    return (\r\n      <div className=\"homework-images\">\r\n        {homework.images.map((image, index) => {\r\n          // 处理图片路径\r\n          let imagePath = getImageUrl(image.image_path);\r\n          \r\n          // 获取文件名\r\n          const fileName = imagePath ? imagePath.split('/').pop() : `图片 ${index + 1}`;\r\n          \r\n          return (\r\n            <div key={image.id} className=\"image-item\">\r\n              <div className=\"image-container\">\r\n                <Image\r\n                  src={imagePath}\r\n                  alt={`作业图片 ${index + 1} - ${fileName}`}\r\n                  style={{ maxWidth: '100%' }}\r\n                  fallback=\"/broken-image.png\"\r\n                  preview={false}\r\n                  onClick={() => handlePreview(imagePath)}\r\n                />\r\n              </div>\r\n              <div className=\"image-caption\">第 {image.page_number} 页</div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // 渲染批注图片\r\n  const renderAnnotatedImages = () => {\r\n    if (!annotatedImages || annotatedImages.length === 0) {\r\n      return (\r\n        <div style={{ textAlign: 'center', padding: '20px' }}>\r\n          <Alert\r\n            message=\"暂无批注图片\"\r\n            description=\"点击下方按钮生成批注图片\"\r\n            type=\"info\"\r\n            showIcon\r\n          />\r\n          <Button\r\n            type=\"primary\"\r\n            onClick={handleGenerateAnnotations}\r\n            loading={generatingAnnotations}\r\n            style={{ marginTop: '16px' }}\r\n            disabled={generatingAnnotations || !homework || (homework.status !== 'corrected' && homework.status !== 'graded')}\r\n          >\r\n            {generatingAnnotations ? '生成中...' : '生成批注图片'}\r\n          </Button>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    return (\r\n      <div className=\"homework-images\">\r\n        {annotatedImages.map((image) => {\r\n          // 处理图片路径\r\n          let imagePath = image.image_path;\r\n          console.log('原始批注图片路径:', imagePath);\r\n          if (imagePath && !imagePath.startsWith('http')) {\r\n            // 根据当前域名构建完整的图片URL\r\n            const currentHost = window.location.hostname;\r\n            const apiUrl = process.env.REACT_APP_API_URL;\r\n            \r\n            let staticBaseUrl;\r\n            if (currentHost === 'localhost' || currentHost === '127.0.0.1') {\r\n              staticBaseUrl = 'http://localhost:8083';\r\n            } else if (currentHost === '17learn.cn') {\r\n              staticBaseUrl = 'http://danphy.xicp.net:23277';\r\n            } else if (apiUrl) {\r\n              staticBaseUrl = apiUrl.replace('/api', '');\r\n            }\r\n            \r\n            if (imagePath.startsWith('/')) {\r\n              imagePath = `${staticBaseUrl}${imagePath}`;\r\n            } else {\r\n              imagePath = `${staticBaseUrl}/${imagePath}`;\r\n            }\r\n            console.log('处理后的批注图片路径:', imagePath);\r\n          }\r\n          \r\n          // 获取文件名\r\n          const fileName = imagePath ? imagePath.split('/').pop() : `批注图片 ${image.page_number}`;\r\n          \r\n          return (\r\n            <div key={image.id} className=\"image-item\">\r\n              <div className=\"image-container\">\r\n                <Image\r\n                  src={imagePath}\r\n                  alt={`批注图片 ${image.page_number} - ${fileName}`}\r\n                  style={{ maxWidth: '100%' }}\r\n                  fallback=\"/broken-image.png\"\r\n                  preview={false}\r\n                  onClick={() => handlePreview(imagePath)}\r\n                />\r\n              </div>\r\n              <div className=\"image-caption\">第 {image.page_number} 页 - 批注版本</div>\r\n              <div style={{ marginTop: '8px', textAlign: 'center' }}>\r\n                <Button\r\n                  type=\"primary\"\r\n                  size=\"small\"\r\n                  icon={<DownloadOutlined />}\r\n                  onClick={() => window.open(imagePath, '_blank')}\r\n                >\r\n                  下载\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n        \r\n        <div style={{ clear: 'both', textAlign: 'center', marginTop: '20px' }}>\r\n          <Button\r\n            type=\"primary\"\r\n            onClick={handleGenerateAnnotations}\r\n            loading={generatingAnnotations}\r\n            disabled={generatingAnnotations}\r\n          >\r\n            {generatingAnnotations ? '重新生成中...' : '重新生成批注'}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n  \r\n  // 提取科目名称\r\n  const getSubjectName = () => {\r\n    if (!homework) return \"未关联作业科目\";\r\n    \r\n    // 直接使用作业中的科目名称（如果存在）\r\n    if (homework.subject_name) {\r\n      return homework.subject_name;\r\n    }\r\n    \r\n    // 如果有科目ID但没有科目名称，尝试从subjects列表中查找\r\n    if (homework.subject_id && subjects && subjects.length > 0) {\r\n      const subject = subjects.find(s => s.id === homework.subject_id);\r\n      if (subject) return subject.name;\r\n    }\r\n    \r\n    // 查看作业任务关联的班级ID，这可能与特定科目相关联\r\n    if (homework.assignment_id && homework.assignment_title) {\r\n      // 从作业任务标题中提取科目名称，使用动态获取的科目数据\r\n      const subjectPatterns = subjects.map(s => s.name);\r\n\r\n      // 尝试从作业任务标题中匹配科目名称\r\n      for (const subject of subjectPatterns) {\r\n        if (homework.assignment_title.includes(subject)) {\r\n          return subject;\r\n        }\r\n      }\r\n\r\n      // 如果无法提取科目名称，直接返回作业任务标题\r\n      return homework.assignment_title;\r\n    }\r\n    \r\n    // 如果没有作业任务标题，尝试从作业标题中提取科目名称\r\n    if (homework.title) {\r\n      const subjectPatterns = subjects.map(s => s.name);\r\n\r\n      for (const subject of subjectPatterns) {\r\n        if (homework.title.includes(subject)) {\r\n          return subject;\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 如果都无法提取，返回默认值\r\n    return subjects.length > 0 ? subjects[0].name : \"未关联作业科目\";\r\n  };\r\n  \r\n  // 获取科目名称\r\n  const subjectName = homework ? getSubjectName() : \"未关联作业科目\";\r\n  \r\n  // 渲染作业历史记录表格\r\n  const renderHistoryTable = () => {\r\n    if (historyLoading) {\r\n      return <Spin tip=\"加载作业历史记录中...\" />;\r\n    }\r\n    \r\n    if (!historyVersions || historyVersions.length === 0) {\r\n      return <Alert message=\"没有找到作业历史记录\" type=\"info\" />;\r\n    }\r\n    \r\n    const columns = [\r\n      {\r\n        title: '提交时间',\r\n        dataIndex: 'created_at',\r\n        key: 'created_at',\r\n        render: (date) => new Date(date).toLocaleString()\r\n      },\r\n      {\r\n        title: '状态',\r\n        dataIndex: 'status',\r\n        key: 'status',\r\n        render: (status) => renderStatusTag(status)\r\n      },\r\n      {\r\n        title: '分数',\r\n        dataIndex: 'score',\r\n        key: 'score',\r\n        render: (score) => score ? `${score}分` : '-'\r\n      },\r\n      {\r\n        title: '准确率',\r\n        dataIndex: 'accuracy',\r\n        key: 'accuracy',\r\n        render: (accuracy) => accuracy ? `${(accuracy * 100).toFixed(2)}%` : '-'\r\n      },\r\n      {\r\n        title: '操作',\r\n        key: 'action',\r\n        render: (_, record) => (\r\n          <Button \r\n            type=\"primary\" \r\n            onClick={() => navigate(`/homework/${record.id}`)}\r\n          >\r\n            查看详情\r\n          </Button>\r\n        )\r\n      }\r\n    ];\r\n    \r\n    return (\r\n      <Card title=\"作业历史记录\" style={{ marginBottom: 20 }}>\r\n        <Descriptions size=\"small\" column={3} style={{ marginBottom: 16 }}>\r\n          <Descriptions.Item label=\"学生姓名\">{historyVersions[0]?.student_name || '未知'}</Descriptions.Item>\r\n          <Descriptions.Item label=\"作业任务\">{historyVersions[0]?.assignment_title || '未知'}</Descriptions.Item>\r\n          <Descriptions.Item label=\"班级\">{historyVersions[0]?.class_name || '未知'}</Descriptions.Item>\r\n          <Descriptions.Item label=\"提交次数\">{historyVersions.length}</Descriptions.Item>\r\n        </Descriptions>\r\n        <Table \r\n          dataSource={historyVersions} \r\n          columns={columns} \r\n          rowKey=\"id\" \r\n          pagination={false} \r\n          size=\"middle\"\r\n        />\r\n      </Card>\r\n    );\r\n  };\r\n  \r\n  // 渲染内容\r\n  const renderContent = () => {\r\n    if (loading) {\r\n      return (\r\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\r\n          <Spin size=\"large\" tip=\"加载中...\" />\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    if (error) {\r\n      return <Alert message=\"错误\" description={error} type=\"error\" showIcon />;\r\n    }\r\n    \r\n    // 如果是显示历史记录模式\r\n    if (showHistory) {\r\n      return (\r\n        <>\r\n          {renderHistoryTable()}\r\n          {homework && (\r\n            <Card title=\"最新提交详情\">\r\n              {renderHomeworkDetail()}\r\n            </Card>\r\n          )}\r\n        </>\r\n      );\r\n    }\r\n    \r\n    return renderHomeworkDetail();\r\n  };\r\n  \r\n  // 渲染作业详情\r\n  const renderHomeworkDetail = () => {\r\n    if (!homework) {\r\n      return <Alert message=\"未找到作业数据\" type=\"warning\" />;\r\n    }\r\n    \r\n    return (\r\n      <>\r\n        <div style={{ marginBottom: 16 }}>\r\n          <Button \r\n            type=\"link\" \r\n            icon={<ArrowLeftOutlined />} \r\n            onClick={() => navigate(-1)}\r\n          >\r\n            返回\r\n          </Button>\r\n        </div>\r\n        \r\n        <Card>\r\n          <Descriptions title={homework.title} bordered>\r\n            <Descriptions.Item label=\"状态\">{renderStatusTag(homework.status)}</Descriptions.Item>\r\n            <Descriptions.Item label=\"学生\">{homework.student_name || `学生ID: ${homework.student_id}`}</Descriptions.Item>\r\n            <Descriptions.Item label=\"班级\">{homework.class_name || '未分配班级'}</Descriptions.Item>\r\n            <Descriptions.Item label=\"提交时间\">{new Date(homework.created_at).toLocaleString()}</Descriptions.Item>\r\n            <Descriptions.Item label=\"批改时间\">{homework.graded_at ? new Date(homework.graded_at).toLocaleString() : '未批改'}</Descriptions.Item>\r\n            <Descriptions.Item label=\"学科\">{getSubjectName()}</Descriptions.Item>\r\n            {homework.score !== null && (\r\n              <Descriptions.Item label=\"分数\">{homework.score}</Descriptions.Item>\r\n            )}\r\n            {homework.accuracy !== null && (\r\n              <Descriptions.Item label=\"准确率\">{(homework.accuracy * 100).toFixed(2)}%</Descriptions.Item>\r\n            )}\r\n            {homework.version_count && (\r\n              <Descriptions.Item label=\"提交次数\">{homework.version_count}</Descriptions.Item>\r\n            )}\r\n          </Descriptions>\r\n          \r\n          {homework.description && (\r\n            <div style={{ margin: '16px 0' }}>\r\n              <Text strong>作业描述：</Text>\r\n              <Paragraph>{homework.description}</Paragraph>\r\n            </div>\r\n          )}\r\n          \r\n          {user && user.is_teacher && homework.status === 'submitted' && (\r\n            <div style={{ margin: '16px 0' }}>\r\n              <Button \r\n                type=\"primary\" \r\n                onClick={handleCorrect} \r\n                loading={correcting}\r\n                disabled={correcting}\r\n              >\r\n                {correcting ? '批改中...' : '开始批改'}\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </Card>\r\n        \r\n        <Tabs activeKey={activeTabKey} onChange={setActiveTabKey} style={{ marginTop: 16 }}>\r\n          <TabPane tab=\"作业图片\" key=\"1\">\r\n            {renderImages()}\r\n          </TabPane>\r\n          <TabPane tab=\"批改结果\" key=\"2\">\r\n            {renderCorrections()}\r\n          </TabPane>\r\n          <TabPane tab=\"批注图片\" key=\"3\">\r\n            {renderAnnotatedImages()}\r\n          </TabPane>\r\n        </Tabs>\r\n        \r\n        <Modal\r\n          visible={previewVisible}\r\n          title=\"图片预览\"\r\n          footer={null}\r\n          onCancel={() => setPreviewVisible(false)}\r\n          width=\"80%\"\r\n        >\r\n          <img alt=\"预览\" style={{ width: '100%' }} src={previewImage} />\r\n        </Modal>\r\n      </>\r\n    );\r\n  };\r\n\r\n  return renderContent();\r\n};\r\n\r\nexport default HomeworkDetail; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AACvF,SACEC,UAAU,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EACzDC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAClD,MAAM;AACb,SACEC,mBAAmB,EAAEC,mBAAmB,EACxCC,YAAY,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,iBAAiB,EAAEC,gBAAgB,QAC/G,mBAAmB;AAC1B,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,cAAc;AACrI,OAAOC,aAAa,MAAM,gBAAgB;AAC1C,OAAO,sBAAsB;AAC7B,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGvC,UAAU;AAC7C,MAAM;EAAEwC;AAAQ,CAAC,GAAGhC,IAAI;AAExB,MAAMiC,cAAc,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM;IAAEC;EAAW,CAAC,GAAGjD,SAAS,CAAC,CAAC;EAClC,MAAMkD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMkD,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkD,YAAY,CAAC,GAAGjD,eAAe,CAAC,CAAC;EACxC,MAAM,CAACkD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,GAAG,CAAC;EACrD,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM;IAAEgD,IAAI,EAAE+B;EAAS,CAAC,GAAG1C,OAAO,CAAC,CAAC;;EAEpC;EACApC,SAAS,CAAC,MAAM;IACd,MAAM+E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFxB,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,IAAIP,WAAW,EAAE;UACf,MAAMgC,SAAS,GAAG3B,YAAY,CAAC4B,GAAG,CAAC,YAAY,CAAC;UAChD,MAAMC,YAAY,GAAG7B,YAAY,CAAC4B,GAAG,CAAC,eAAe,CAAC;UAEtD,IAAI,CAACD,SAAS,IAAI,CAACE,YAAY,EAAE;YAC/BvB,QAAQ,CAAC,oBAAoB,CAAC;YAC9B;UACF;UAEAc,iBAAiB,CAAC,IAAI,CAAC;UACvB,MAAMU,WAAW,GAAG,MAAMpD,kBAAkB,CAACiD,SAAS,EAAEE,YAAY,CAAC;UACrEE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEF,WAAW,CAAC;UACvCZ,kBAAkB,CAACY,WAAW,IAAI,EAAE,CAAC;UACrCV,iBAAiB,CAAC,KAAK,CAAC;;UAExB;UACA,IAAIU,WAAW,IAAIA,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;YACzC7B,WAAW,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAAC;UAC7B;QACF,CAAC,MAAM;UACL;UACA,MAAMI,IAAI,GAAG,MAAM3D,WAAW,CAACsB,UAAU,CAAC;UAC1CkC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEE,IAAI,CAAC;UAC9B9B,WAAW,CAAC8B,IAAI,CAAC;;UAEjB;UACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,MAAM,GAAG,CAAC,EAAE;YAC3DzB,eAAe,CAAC,GAAG,CAAC;UACtB;QACF;;QAEA;QACA,MAAM4B,YAAY,GAAG,MAAM5D,WAAW,CAAC,CAAC;QACxCkC,WAAW,CAAC0B,YAAY,CAAC;;QAEzB;QACA,IAAI;UACF,MAAMC,mBAAmB,GAAG,MAAM1D,kBAAkB,CAACkB,UAAU,CAAC;UAChEyB,kBAAkB,CAACe,mBAAmB,CAAC;UACvCN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,mBAAmB,CAAC;QAC/C,CAAC,CAAC,OAAOhC,KAAK,EAAE;UACd0B,OAAO,CAAC1B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;MACF,CAAC,CAAC,OAAOiC,GAAG,EAAE;QACZP,OAAO,CAAC1B,KAAK,CAACV,WAAW,GAAG,aAAa,GAAG,WAAW,EAAE2C,GAAG,CAAC;QAC7DhC,QAAQ,CAAC,CAACX,WAAW,GAAG,cAAc,GAAG,YAAY,KAAK2C,GAAG,CAAC5E,OAAO,IAAI,MAAM,CAAC,CAAC;MACnF,CAAC,SAAS;QACRwC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDwB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,UAAU,EAAEF,WAAW,EAAEK,YAAY,CAAC,CAAC;;EAE3C;EACA,MAAMuC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF3B,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMnC,eAAe,CAACoB,UAAU,CAAC;MACjCnC,OAAO,CAAC8E,OAAO,CAAC,mBAAmB,CAAC;;MAEpC;MACAC,UAAU,CAAC,MAAM;QACfC,MAAM,CAAC3C,QAAQ,CAAC4C,MAAM,CAAC,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3C,OAAO,CAAC2C,KAAK,CAAC,cAAc,CAAC;IAC/B,CAAC,SAAS;MACRO,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMgC,aAAa,GAAIC,SAAS,IAAK;IACnC7B,eAAe,CAAC6B,SAAS,CAAC;IAC1B/B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMZ,IAAI,GAAG,MAAMvD,kBAAkB,CAACkB,UAAU,CAAC;MACjDyB,kBAAkB,CAACY,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM0C,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5ChB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE7B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,MAAM,CAAC;IACtCjB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEnC,UAAU,CAAC;IAChCkC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAET,qBAAqB,IAAI,CAACpB,QAAQ,IAAK,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,MAAM,MAAK,WAAW,IAAI,CAAA7C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,MAAM,MAAK,QAAS,CAAC;IAEjI,IAAI;MACFxB,wBAAwB,CAAC,IAAI,CAAC;MAC9BO,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,MAAMiB,MAAM,GAAG,MAAMrE,mBAAmB,CAACiB,UAAU,CAAC;MACpDkC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEiB,MAAM,CAAC;MACjEvF,OAAO,CAAC8E,OAAO,CAACS,MAAM,CAACvF,OAAO,IAAI,QAAQ,CAAC;MAC3C,MAAMoF,oBAAoB,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC3C,OAAO,CAAC2C,KAAK,CAAC,gBAAgB,CAAC;IACjC,CAAC,SAAS;MACRmB,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAM0B,eAAe,GAAIF,MAAM,IAAK;IAClC,IAAIG,KAAK,GAAG,SAAS;IACrB,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIC,IAAI,GAAG,MAAM;IAEjB,QAAQL,MAAM;MACZ,KAAK,WAAW;QACdG,KAAK,GAAG,SAAS;QACjBC,IAAI,gBAAGlE,OAAA,CAACjB,YAAY;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACvBJ,IAAI,GAAG,KAAK;QACZ;MACF,KAAK,SAAS;MACd,KAAK,YAAY;QACfF,KAAK,GAAG,YAAY;QACpBC,IAAI,gBAAGlE,OAAA,CAACjB,YAAY;UAACyF,IAAI;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC5BJ,IAAI,GAAG,KAAK;QACZ;MACF,KAAK,QAAQ;MACb,KAAK,WAAW;QACdF,KAAK,GAAG,SAAS;QACjBC,IAAI,gBAAGlE,OAAA,CAACnB,mBAAmB;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QAC9BJ,IAAI,GAAG,KAAK;QACZ;MACF;QACE;IACJ;IAEA,oBAAOnE,OAAA,CAAC/B,GAAG;MAACgG,KAAK,EAAEA,KAAM;MAACC,IAAI,EAAEA,IAAK;MAAAO,QAAA,EAAEN;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACpD,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACzD,QAAQ,IAAI,CAACA,QAAQ,CAACgC,WAAW,IAAIhC,QAAQ,CAACgC,WAAW,CAACF,MAAM,KAAK,CAAC,EAAE;MAC3E,oBAAO/C,OAAA,CAACzB,KAAK;QAACC,OAAO,EAAC,sCAAQ;QAACmG,IAAI,EAAC;MAAM;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC/C;IAEA1B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE7B,QAAQ,CAACgC,WAAW,CAAC;;IAE5C;IACA,MAAM2B,UAAU,GAAGpE,IAAI,IAAIA,IAAI,CAACqE,UAAU,KAAK5D,QAAQ,CAAC6C,MAAM,KAAK,QAAQ,IAAI7C,QAAQ,CAAC6C,MAAM,KAAK,WAAW,CAAC;IAE/G,oBACE9D,OAAA,CAAAE,SAAA;MAAAuE,QAAA,GACGG,UAAU,iBACT5E,OAAA,CAAAE,SAAA;QAAAuE,QAAA,gBACEzE,OAAA,CAACJ,wBAAwB;UACvBqB,QAAQ,EAAEA,QAAS;UACnB6D,OAAO,EAAGC,eAAe,IAAK;YAC5B;YACA7D,WAAW,CAAC;cACV,GAAGD,QAAQ;cACX,GAAG8D;YACL,CAAC,CAAC;YACFvG,OAAO,CAAC8E,OAAO,CAAC,SAAS,CAAC;UAC5B,CAAE;UACF0B,QAAQ,EAAEA,CAAA,KAAMnC,OAAO,CAACC,GAAG,CAAC,UAAU;QAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACFvE,OAAA,CAAC9B,OAAO;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eACX,CACH,EAEAtD,QAAQ,CAACgC,WAAW,CAACgC,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QAC/C;QACA,IAAIC,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI;UACF,IAAI,OAAOF,UAAU,CAACG,eAAe,KAAK,QAAQ,EAAE;YAClDD,cAAc,GAAGE,IAAI,CAACC,KAAK,CAACL,UAAU,CAACG,eAAe,CAAC;UACzD,CAAC,MAAM,IAAI,OAAOH,UAAU,CAACG,eAAe,KAAK,QAAQ,EAAE;YACzDD,cAAc,GAAGF,UAAU,CAACG,eAAe;UAC7C;UACAxC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsC,cAAc,CAAC;QAC1C,CAAC,CAAC,OAAOjE,KAAK,EAAE;UACd0B,OAAO,CAAC1B,KAAK,CAAC,WAAW,EAAEA,KAAK,EAAE+D,UAAU,CAACG,eAAe,CAAC;UAC7D,oBAAOrF,OAAA,CAACzB,KAAK;YAAaC,OAAO,EAAE,aAAa2C,KAAK,CAAC3C,OAAO,EAAG;YAACgH,WAAW,EAAE,SAASF,IAAI,CAACG,SAAS,CAACP,UAAU,CAACG,eAAe,CAAC,CAACK,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAM;YAACf,IAAI,EAAC;UAAO,GAAnJQ,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgJ,CAAC;QAC3K;;QAEA;QACA,IAAI,CAACa,cAAc,CAACO,SAAS,EAAE;UAC7B,oBACE3F,OAAA,CAACjC,IAAI;YAEH6H,KAAK,EAAE,IAAIV,UAAU,CAACW,WAAW,OAAQ;YACzCC,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAAtB,QAAA,gBAE5BzE,OAAA,CAACzB,KAAK;cAACC,OAAO,EAAC,wDAAW;cAACmG,IAAI,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5CvE,OAAA,CAAC9B,OAAO;cAAC8H,WAAW,EAAC,MAAM;cAAAvB,QAAA,EAAC;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC5CvE,OAAA;cAAK8F,KAAK,EAAE;gBAAEG,UAAU,EAAE,UAAU;gBAAEC,SAAS,EAAE;cAAa,CAAE;cAAAzB,QAAA,EAC7D,OAAOW,cAAc,KAAK,QAAQ,GAAGE,IAAI,CAACG,SAAS,CAACL,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC,GAAGA;YAAc;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA,GARDY,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASN,CAAC;QAEX;QAEA,MAAMoB,SAAS,GAAGP,cAAc,CAACO,SAAS,IAAI,EAAE;QAChD,MAAMQ,OAAO,GAAGf,cAAc,CAACe,OAAO,IAAI;UACxCC,eAAe,EAAE,CAAC;UAClBC,aAAa,EAAE,CAAC;UAChBC,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA,MAAMC,sBAAsB,GAAGZ,SAAS,CACrCa,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CACzBzB,GAAG,CAACwB,CAAC,IAAIA,CAAC,CAACE,eAAe,IAAI,IAAI,CAAC,CACnCC,IAAI,CAAC,IAAI,CAAC;QAEb,MAAMC,wBAAwB,GAAGlB,SAAS,CACvCa,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACC,UAAU,CAAC,CAC1BzB,GAAG,CAACwB,CAAC,IAAIA,CAAC,CAACE,eAAe,IAAI,IAAI,CAAC,CACnCC,IAAI,CAAC,IAAI,CAAC;;QAEb;QACA,MAAME,eAAe,GAAG1B,cAAc,CAAC2B,iBAAiB;QAExD,oBACE/G,OAAA,CAACjC,IAAI;UAEH6H,KAAK,EAAE,IAAIV,UAAU,CAACW,WAAW,OAAQ;UACzCC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAC5BiB,KAAK,EAAEF,eAAe,gBAAG9G,OAAA,CAAC/B,GAAG;YAACgG,KAAK,EAAC,MAAM;YAAAQ,QAAA,EAAC;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAAG,IAAK;UAAAE,QAAA,gBAE9DzE,OAAA,CAAChC,YAAY;YAACiJ,QAAQ;YAACC,MAAM,EAAE,CAAE;YAACC,IAAI,EAAC,OAAO;YAAA1C,QAAA,gBAC5CzE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5C,QAAA,EAAE0B,OAAO,CAACC;YAAe;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC7EvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5C,QAAA,EAAE0B,OAAO,CAACE;YAAa;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC3EvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAA5C,QAAA,GAC3B,CAAC0B,OAAO,CAACG,QAAQ,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC,EAAC,GACvC;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACpBvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5C,QAAA,EAC5B8B,sBAAsB,IAAI;YAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACpBvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5C,QAAA,EAC5BoC,wBAAwB,IAAI;YAAG;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACnBuC,eAAe,IAAI1B,cAAc,CAACmC,SAAS,iBAC1CvH,OAAA,CAAChC,YAAY,CAACoJ,IAAI;cAACC,KAAK,EAAC,sCAAQ;cAAA5C,QAAA,EAC9B,IAAI+C,IAAI,CAACpC,cAAc,CAACmC,SAAS,CAAC,CAACE,cAAc,CAAC;YAAC;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,eAEfvE,OAAA,CAAC9B,OAAO;YAAC8H,WAAW,EAAC,MAAM;YAAAvB,QAAA,EAAC;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,EAEzCoB,SAAS,CAACV,GAAG,CAAC,CAACwB,CAAC,EAAEiB,MAAM,kBACvB1H,OAAA,CAACjC,IAAI;YAEH4G,IAAI,EAAC,OAAO;YACZiB,KAAK,EAAE,KAAKa,CAAC,CAACE,eAAe,IAAIe,MAAM,GAAG,CAAC,KAAKjB,CAAC,CAACkB,aAAa,IAAI,MAAM,EAAG;YAC5E7B,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAC5B6B,SAAS,EAAC,eAAe;YACzBZ,KAAK,EACHP,CAAC,CAACC,UAAU,gBACR1G,OAAA,CAAC/B,GAAG;cAACgG,KAAK,EAAC,SAAS;cAACC,IAAI,eAAElE,OAAA,CAACnB,mBAAmB;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAE,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAC5DvE,OAAA,CAAC/B,GAAG;cAACgG,KAAK,EAAC,OAAO;cAACC,IAAI,eAAElE,OAAA,CAAClB,mBAAmB;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAE,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAC9D;YAAAE,QAAA,gBAEDzE,OAAA,CAACK,SAAS;cAAAoE,QAAA,gBACRzE,OAAA,CAACI,IAAI;gBAACyH,MAAM;gBAAApD,QAAA,EAAC;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,KAAC,EAACkC,CAAC,CAACqB,gBAAgB;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACZvE,OAAA,CAACK,SAAS;cAAAoE,QAAA,gBACRzE,OAAA,CAACI,IAAI;gBAACyH,MAAM;gBAAApD,QAAA,EAAC;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,KAAC,EAACkC,CAAC,CAACsB,cAAc;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACZvE,OAAA,CAACK,SAAS;cAAAoE,QAAA,gBACRzE,OAAA,CAACI,IAAI;gBAACyH,MAAM;gBAAApD,QAAA,EAAC;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,KAAC,EAACkC,CAAC,CAACuB,cAAc;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EAGX,CAAC,CAACkC,CAAC,CAACC,UAAU,IAAKD,CAAC,CAACC,UAAU,IAAID,CAAC,CAACwB,QAAS,kBAC7CjI,OAAA,CAACzB,KAAK;cACJC,OAAO,EAAEiI,CAAC,CAACC,UAAU,GAAG,MAAM,GAAG,MAAO;cACxClB,WAAW,EAAEiB,CAAC,CAACwB,QAAQ,KAAKxB,CAAC,CAACC,UAAU,GAAG,EAAE,GAAG,SAAS,CAAE;cAC3D/B,IAAI,EAAE8B,CAAC,CAACC,UAAU,GAAG,MAAM,GAAG,SAAU;cACxCwB,QAAQ;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACF,EAEAkC,CAAC,CAAC0B,aAAa,iBACdnI,OAAA,CAACzB,KAAK;cACJC,OAAO,EAAC,0BAAM;cACdgH,WAAW,EAAEiB,CAAC,CAAC0B,aAAc;cAC7BxD,IAAI,EAAC,MAAM;cACXuD,QAAQ;cACRpC,KAAK,EAAE;gBAAEsC,SAAS,EAAE;cAAG;YAAE;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACF,EAEAkC,CAAC,CAAC4B,MAAM,iBACPrI,OAAA;cAAK8F,KAAK,EAAE;gBAAEsC,SAAS,EAAE;cAAG,CAAE;cAAA3D,QAAA,eAC5BzE,OAAA,CAAC/B,GAAG;gBAACgG,KAAK,EAAC,MAAM;gBAAAQ,QAAA,EAAC;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CACN;UAAA,GA7CImD,MAAM;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CP,CACP,CAAC;QAAA,GA3EGY,KAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4EN,CAAC;MAEX,CAAC,CAAC;IAAA,eACF,CAAC;EAEP,CAAC;;EAED;EACA,MAAM+D,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACrH,QAAQ,IAAI,CAACA,QAAQ,CAACsH,MAAM,IAAItH,QAAQ,CAACsH,MAAM,CAACxF,MAAM,KAAK,CAAC,EAAE;MACjE,oBAAO/C,OAAA,CAACzB,KAAK;QAACC,OAAO,EAAC,sCAAQ;QAACmG,IAAI,EAAC;MAAM;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC/C;IAEA,oBACEvE,OAAA;MAAK4H,SAAS,EAAC,iBAAiB;MAAAnD,QAAA,EAC7BxD,QAAQ,CAACsH,MAAM,CAACtD,GAAG,CAAC,CAACuD,KAAK,EAAErD,KAAK,KAAK;QACrC;QACA,IAAIxB,SAAS,GAAG7D,WAAW,CAAC0I,KAAK,CAACC,UAAU,CAAC;;QAE7C;QACA,MAAMrE,QAAQ,GAAGT,SAAS,GAAGA,SAAS,CAAC+E,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,GAAG,MAAMxD,KAAK,GAAG,CAAC,EAAE;QAE3E,oBACEnF,OAAA;UAAoB4H,SAAS,EAAC,YAAY;UAAAnD,QAAA,gBACxCzE,OAAA;YAAK4H,SAAS,EAAC,iBAAiB;YAAAnD,QAAA,eAC9BzE,OAAA,CAAC5B,KAAK;cACJwK,GAAG,EAAEjF,SAAU;cACfkF,GAAG,EAAE,QAAQ1D,KAAK,GAAG,CAAC,MAAMf,QAAQ,EAAG;cACvC0B,KAAK,EAAE;gBAAEgD,QAAQ,EAAE;cAAO,CAAE;cAC5BC,QAAQ,EAAC,mBAAmB;cAC5BC,OAAO,EAAE,KAAM;cACfC,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAACC,SAAS;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvE,OAAA;YAAK4H,SAAS,EAAC,eAAe;YAAAnD,QAAA,GAAC,SAAE,EAAC+D,KAAK,CAAC3C,WAAW,EAAC,SAAE;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA,GAXpDiE,KAAK,CAACU,EAAE;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4E,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAChH,eAAe,IAAIA,eAAe,CAACY,MAAM,KAAK,CAAC,EAAE;MACpD,oBACE/C,OAAA;QAAK8F,KAAK,EAAE;UAAEsD,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAA5E,QAAA,gBACnDzE,OAAA,CAACzB,KAAK;UACJC,OAAO,EAAC,sCAAQ;UAChBgH,WAAW,EAAC,0EAAc;UAC1Bb,IAAI,EAAC,MAAM;UACXuD,QAAQ;QAAA;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFvE,OAAA,CAAC3B,MAAM;UACLsG,IAAI,EAAC,SAAS;UACdsE,OAAO,EAAEpF,yBAA0B;UACnC9C,OAAO,EAAEsB,qBAAsB;UAC/ByD,KAAK,EAAE;YAAEsC,SAAS,EAAE;UAAO,CAAE;UAC7BkB,QAAQ,EAAEjH,qBAAqB,IAAI,CAACpB,QAAQ,IAAKA,QAAQ,CAAC6C,MAAM,KAAK,WAAW,IAAI7C,QAAQ,CAAC6C,MAAM,KAAK,QAAU;UAAAW,QAAA,EAEjHpC,qBAAqB,GAAG,QAAQ,GAAG;QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEV;IAEA,oBACEvE,OAAA;MAAK4H,SAAS,EAAC,iBAAiB;MAAAnD,QAAA,GAC7BtC,eAAe,CAAC8C,GAAG,CAAEuD,KAAK,IAAK;QAC9B;QACA,IAAI7E,SAAS,GAAG6E,KAAK,CAACC,UAAU;QAChC5F,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEa,SAAS,CAAC;QACnC,IAAIA,SAAS,IAAI,CAACA,SAAS,CAAC4F,UAAU,CAAC,MAAM,CAAC,EAAE;UAC9C;UACA,MAAMC,WAAW,GAAGhG,MAAM,CAAC3C,QAAQ,CAAC4I,QAAQ;UAC5C,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;UAE5C,IAAIC,aAAa;UACjB,IAAIN,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,WAAW,EAAE;YAC9DM,aAAa,GAAG,uBAAuB;UACzC,CAAC,MAAM,IAAIN,WAAW,KAAK,YAAY,EAAE;YACvCM,aAAa,GAAG,8BAA8B;UAChD,CAAC,MAAM,IAAIJ,MAAM,EAAE;YACjBI,aAAa,GAAGJ,MAAM,CAACK,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;UAC5C;UAEA,IAAIpG,SAAS,CAAC4F,UAAU,CAAC,GAAG,CAAC,EAAE;YAC7B5F,SAAS,GAAG,GAAGmG,aAAa,GAAGnG,SAAS,EAAE;UAC5C,CAAC,MAAM;YACLA,SAAS,GAAG,GAAGmG,aAAa,IAAInG,SAAS,EAAE;UAC7C;UACAd,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEa,SAAS,CAAC;QACvC;;QAEA;QACA,MAAMS,QAAQ,GAAGT,SAAS,GAAGA,SAAS,CAAC+E,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,GAAG,QAAQH,KAAK,CAAC3C,WAAW,EAAE;QAErF,oBACE7F,OAAA;UAAoB4H,SAAS,EAAC,YAAY;UAAAnD,QAAA,gBACxCzE,OAAA;YAAK4H,SAAS,EAAC,iBAAiB;YAAAnD,QAAA,eAC9BzE,OAAA,CAAC5B,KAAK;cACJwK,GAAG,EAAEjF,SAAU;cACfkF,GAAG,EAAE,QAAQL,KAAK,CAAC3C,WAAW,MAAMzB,QAAQ,EAAG;cAC/C0B,KAAK,EAAE;gBAAEgD,QAAQ,EAAE;cAAO,CAAE;cAC5BC,QAAQ,EAAC,mBAAmB;cAC5BC,OAAO,EAAE,KAAM;cACfC,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAACC,SAAS;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvE,OAAA;YAAK4H,SAAS,EAAC,eAAe;YAAAnD,QAAA,GAAC,SAAE,EAAC+D,KAAK,CAAC3C,WAAW,EAAC,oCAAS;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnEvE,OAAA;YAAK8F,KAAK,EAAE;cAAEsC,SAAS,EAAE,KAAK;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAA3E,QAAA,eACpDzE,OAAA,CAAC3B,MAAM;cACLsG,IAAI,EAAC,SAAS;cACdwC,IAAI,EAAC,OAAO;cACZjD,IAAI,eAAElE,OAAA,CAACZ,gBAAgB;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3B0E,OAAO,EAAEA,CAAA,KAAMzF,MAAM,CAACwG,IAAI,CAACrG,SAAS,EAAE,QAAQ,CAAE;cAAAc,QAAA,EACjD;YAED;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GArBEiE,KAAK,CAACU,EAAE;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBb,CAAC;MAEV,CAAC,CAAC,eAEFvE,OAAA;QAAK8F,KAAK,EAAE;UAAEmE,KAAK,EAAE,MAAM;UAAEb,SAAS,EAAE,QAAQ;UAAEhB,SAAS,EAAE;QAAO,CAAE;QAAA3D,QAAA,eACpEzE,OAAA,CAAC3B,MAAM;UACLsG,IAAI,EAAC,SAAS;UACdsE,OAAO,EAAEpF,yBAA0B;UACnC9C,OAAO,EAAEsB,qBAAsB;UAC/BiH,QAAQ,EAAEjH,qBAAsB;UAAAoC,QAAA,EAE/BpC,qBAAqB,GAAG,UAAU,GAAG;QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAM2F,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACjJ,QAAQ,EAAE,OAAO,SAAS;;IAE/B;IACA,IAAIA,QAAQ,CAACkJ,YAAY,EAAE;MACzB,OAAOlJ,QAAQ,CAACkJ,YAAY;IAC9B;;IAEA;IACA,IAAIlJ,QAAQ,CAACmJ,UAAU,IAAI7I,QAAQ,IAAIA,QAAQ,CAACwB,MAAM,GAAG,CAAC,EAAE;MAC1D,MAAMsH,OAAO,GAAG9I,QAAQ,CAAC+I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrB,EAAE,KAAKjI,QAAQ,CAACmJ,UAAU,CAAC;MAChE,IAAIC,OAAO,EAAE,OAAOA,OAAO,CAACG,IAAI;IAClC;;IAEA;IACA,IAAIvJ,QAAQ,CAACwJ,aAAa,IAAIxJ,QAAQ,CAACyJ,gBAAgB,EAAE;MACvD;MACA,MAAMC,eAAe,GAAGpJ,QAAQ,CAAC0D,GAAG,CAACsF,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC;;MAEjD;MACA,KAAK,MAAMH,OAAO,IAAIM,eAAe,EAAE;QACrC,IAAI1J,QAAQ,CAACyJ,gBAAgB,CAACE,QAAQ,CAACP,OAAO,CAAC,EAAE;UAC/C,OAAOA,OAAO;QAChB;MACF;;MAEA;MACA,OAAOpJ,QAAQ,CAACyJ,gBAAgB;IAClC;;IAEA;IACA,IAAIzJ,QAAQ,CAAC2E,KAAK,EAAE;MAClB,MAAM+E,eAAe,GAAGpJ,QAAQ,CAAC0D,GAAG,CAACsF,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC;MAEjD,KAAK,MAAMH,OAAO,IAAIM,eAAe,EAAE;QACrC,IAAI1J,QAAQ,CAAC2E,KAAK,CAACgF,QAAQ,CAACP,OAAO,CAAC,EAAE;UACpC,OAAOA,OAAO;QAChB;MACF;IACF;;IAEA;IACA,OAAO9I,QAAQ,CAACwB,MAAM,GAAG,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC,CAACiJ,IAAI,GAAG,SAAS;EAC3D,CAAC;;EAED;EACA,MAAMK,WAAW,GAAG5J,QAAQ,GAAGiJ,cAAc,CAAC,CAAC,GAAG,SAAS;;EAE3D;EACA,MAAMY,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;IAC/B,IAAIhJ,cAAc,EAAE;MAClB,oBAAOjC,OAAA,CAAC7B,IAAI;QAAC+M,GAAG,EAAC;MAAc;QAAA9G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpC;IAEA,IAAI,CAACxC,eAAe,IAAIA,eAAe,CAACgB,MAAM,KAAK,CAAC,EAAE;MACpD,oBAAO/C,OAAA,CAACzB,KAAK;QAACC,OAAO,EAAC,8DAAY;QAACmG,IAAI,EAAC;MAAM;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACnD;IAEA,MAAM4G,OAAO,GAAG,CACd;MACEvF,KAAK,EAAE,MAAM;MACbwF,SAAS,EAAE,YAAY;MACvBC,GAAG,EAAE,YAAY;MACjBC,MAAM,EAAGC,IAAI,IAAK,IAAI/D,IAAI,CAAC+D,IAAI,CAAC,CAAC9D,cAAc,CAAC;IAClD,CAAC,EACD;MACE7B,KAAK,EAAE,IAAI;MACXwF,SAAS,EAAE,QAAQ;MACnBC,GAAG,EAAE,QAAQ;MACbC,MAAM,EAAGxH,MAAM,IAAKE,eAAe,CAACF,MAAM;IAC5C,CAAC,EACD;MACE8B,KAAK,EAAE,IAAI;MACXwF,SAAS,EAAE,OAAO;MAClBC,GAAG,EAAE,OAAO;MACZC,MAAM,EAAGE,KAAK,IAAKA,KAAK,GAAG,GAAGA,KAAK,GAAG,GAAG;IAC3C,CAAC,EACD;MACE5F,KAAK,EAAE,KAAK;MACZwF,SAAS,EAAE,UAAU;MACrBC,GAAG,EAAE,UAAU;MACfC,MAAM,EAAGhF,QAAQ,IAAKA,QAAQ,GAAG,GAAG,CAACA,QAAQ,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACvE,CAAC,EACD;MACE1B,KAAK,EAAE,IAAI;MACXyF,GAAG,EAAE,QAAQ;MACbC,MAAM,EAAEA,CAACG,CAAC,EAAEC,MAAM,kBAChB1L,OAAA,CAAC3B,MAAM;QACLsG,IAAI,EAAC,SAAS;QACdsE,OAAO,EAAEA,CAAA,KAAMrI,QAAQ,CAAC,aAAa8K,MAAM,CAACxC,EAAE,EAAE,CAAE;QAAAzE,QAAA,EACnD;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAEZ,CAAC,CACF;IAED,oBACEvE,OAAA,CAACjC,IAAI;MAAC6H,KAAK,EAAC,sCAAQ;MAACE,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAtB,QAAA,gBAC/CzE,OAAA,CAAChC,YAAY;QAACmJ,IAAI,EAAC,OAAO;QAACD,MAAM,EAAE,CAAE;QAACpB,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAtB,QAAA,gBAChEzE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAAE,EAAAsG,iBAAA,GAAAhJ,eAAe,CAAC,CAAC,CAAC,cAAAgJ,iBAAA,uBAAlBA,iBAAA,CAAoBY,YAAY,KAAI;QAAI;UAAAvH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC9FvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAAE,EAAAuG,kBAAA,GAAAjJ,eAAe,CAAC,CAAC,CAAC,cAAAiJ,kBAAA,uBAAlBA,kBAAA,CAAoBN,gBAAgB,KAAI;QAAI;UAAAtG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAClGvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;UAACC,KAAK,EAAC,cAAI;UAAA5C,QAAA,EAAE,EAAAwG,kBAAA,GAAAlJ,eAAe,CAAC,CAAC,CAAC,cAAAkJ,kBAAA,uBAAlBA,kBAAA,CAAoBW,UAAU,KAAI;QAAI;UAAAxH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC1FvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA5C,QAAA,EAAE1C,eAAe,CAACgB;QAAM;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACfvE,OAAA,CAACpB,KAAK;QACJiN,UAAU,EAAE9J,eAAgB;QAC5BoJ,OAAO,EAAEA,OAAQ;QACjBW,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE,KAAM;QAClB5E,IAAI,EAAC;MAAQ;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;;EAED;EACA,MAAMyH,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIjL,OAAO,EAAE;MACX,oBACEf,OAAA;QAAK8F,KAAK,EAAE;UAAEsD,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAS,CAAE;QAAA5E,QAAA,eACrDzE,OAAA,CAAC7B,IAAI;UAACgJ,IAAI,EAAC,OAAO;UAAC+D,GAAG,EAAC;QAAQ;UAAA9G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAEV;IAEA,IAAIpD,KAAK,EAAE;MACT,oBAAOnB,OAAA,CAACzB,KAAK;QAACC,OAAO,EAAC,cAAI;QAACgH,WAAW,EAAErE,KAAM;QAACwD,IAAI,EAAC,OAAO;QAACuD,QAAQ;MAAA;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACzE;;IAEA;IACA,IAAI9D,WAAW,EAAE;MACf,oBACET,OAAA,CAAAE,SAAA;QAAAuE,QAAA,GACGqG,kBAAkB,CAAC,CAAC,EACpB7J,QAAQ,iBACPjB,OAAA,CAACjC,IAAI;UAAC6H,KAAK,EAAC,sCAAQ;UAAAnB,QAAA,EACjBwH,oBAAoB,CAAC;QAAC;UAAA7H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACP;MAAA,eACD,CAAC;IAEP;IAEA,OAAO0H,oBAAoB,CAAC,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMA,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAChL,QAAQ,EAAE;MACb,oBAAOjB,OAAA,CAACzB,KAAK;QAACC,OAAO,EAAC,4CAAS;QAACmG,IAAI,EAAC;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACnD;IAEA,oBACEvE,OAAA,CAAAE,SAAA;MAAAuE,QAAA,gBACEzE,OAAA;QAAK8F,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAtB,QAAA,eAC/BzE,OAAA,CAAC3B,MAAM;UACLsG,IAAI,EAAC,MAAM;UACXT,IAAI,eAAElE,OAAA,CAAChB,iBAAiB;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5B0E,OAAO,EAAEA,CAAA,KAAMrI,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAA6D,QAAA,EAC7B;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvE,OAAA,CAACjC,IAAI;QAAA0G,QAAA,gBACHzE,OAAA,CAAChC,YAAY;UAAC4H,KAAK,EAAE3E,QAAQ,CAAC2E,KAAM;UAACqB,QAAQ;UAAAxC,QAAA,gBAC3CzE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAA5C,QAAA,EAAET,eAAe,CAAC/C,QAAQ,CAAC6C,MAAM;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACpFvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAA5C,QAAA,EAAExD,QAAQ,CAAC0K,YAAY,IAAI,SAAS1K,QAAQ,CAACiL,UAAU;UAAE;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC3GvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAA5C,QAAA,EAAExD,QAAQ,CAAC2K,UAAU,IAAI;UAAO;YAAAxH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAClFvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5C,QAAA,EAAE,IAAI+C,IAAI,CAACvG,QAAQ,CAACkL,UAAU,CAAC,CAAC1E,cAAc,CAAC;UAAC;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACpGvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5C,QAAA,EAAExD,QAAQ,CAACmL,SAAS,GAAG,IAAI5E,IAAI,CAACvG,QAAQ,CAACmL,SAAS,CAAC,CAAC3E,cAAc,CAAC,CAAC,GAAG;UAAK;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAChIvE,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAA5C,QAAA,EAAEyF,cAAc,CAAC;UAAC;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,EACnEtD,QAAQ,CAACuK,KAAK,KAAK,IAAI,iBACtBxL,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAA5C,QAAA,EAAExD,QAAQ,CAACuK;UAAK;YAAApH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAClE,EACAtD,QAAQ,CAACqF,QAAQ,KAAK,IAAI,iBACzBtG,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,oBAAK;YAAA5C,QAAA,GAAE,CAACxD,QAAQ,CAACqF,QAAQ,GAAG,GAAG,EAAEgB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;UAAA;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAC1F,EACAtD,QAAQ,CAACoL,aAAa,iBACrBrM,OAAA,CAAChC,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5C,QAAA,EAAExD,QAAQ,CAACoL;UAAa;YAAAjI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAC5E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,EAEdtD,QAAQ,CAACuE,WAAW,iBACnBxF,OAAA;UAAK8F,KAAK,EAAE;YAAEwG,MAAM,EAAE;UAAS,CAAE;UAAA7H,QAAA,gBAC/BzE,OAAA,CAACI,IAAI;YAACyH,MAAM;YAAApD,QAAA,EAAC;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzBvE,OAAA,CAACK,SAAS;YAAAoE,QAAA,EAAExD,QAAQ,CAACuE;UAAW;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN,EAEA/D,IAAI,IAAIA,IAAI,CAACqE,UAAU,IAAI5D,QAAQ,CAAC6C,MAAM,KAAK,WAAW,iBACzD9D,OAAA;UAAK8F,KAAK,EAAE;YAAEwG,MAAM,EAAE;UAAS,CAAE;UAAA7H,QAAA,eAC/BzE,OAAA,CAAC3B,MAAM;YACLsG,IAAI,EAAC,SAAS;YACdsE,OAAO,EAAE5F,aAAc;YACvBtC,OAAO,EAAEU,UAAW;YACpB6H,QAAQ,EAAE7H,UAAW;YAAAgD,QAAA,EAEpBhD,UAAU,GAAG,QAAQ,GAAG;UAAM;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEPvE,OAAA,CAAC1B,IAAI;QAACiO,SAAS,EAAElL,YAAa;QAACmL,QAAQ,EAAElL,eAAgB;QAACwE,KAAK,EAAE;UAAEsC,SAAS,EAAE;QAAG,CAAE;QAAA3D,QAAA,gBACjFzE,OAAA,CAACM,OAAO;UAACmM,GAAG,EAAC,0BAAM;UAAAhI,QAAA,EAChB6D,YAAY,CAAC;QAAC,GADO,GAAG;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElB,CAAC,eACVvE,OAAA,CAACM,OAAO;UAACmM,GAAG,EAAC,0BAAM;UAAAhI,QAAA,EAChBC,iBAAiB,CAAC;QAAC,GADE,GAAG;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElB,CAAC,eACVvE,OAAA,CAACM,OAAO;UAACmM,GAAG,EAAC,0BAAM;UAAAhI,QAAA,EAChB0E,qBAAqB,CAAC;QAAC,GADF,GAAG;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAElB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEPvE,OAAA,CAACrB,KAAK;QACJ+N,OAAO,EAAE/K,cAAe;QACxBiE,KAAK,EAAC,0BAAM;QACZ+G,MAAM,EAAE,IAAK;QACb3H,QAAQ,EAAEA,CAAA,KAAMpD,iBAAiB,CAAC,KAAK,CAAE;QACzCgL,KAAK,EAAC,KAAK;QAAAnI,QAAA,eAEXzE,OAAA;UAAK6I,GAAG,EAAC,cAAI;UAAC/C,KAAK,EAAE;YAAE8G,KAAK,EAAE;UAAO,CAAE;UAAChE,GAAG,EAAE/G;QAAa;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA,eACR,CAAC;EAEP,CAAC;EAED,OAAOyH,aAAa,CAAC,CAAC;AACxB,CAAC;AAACtL,EAAA,CA5rBIH,cAAc;EAAA,QACK7C,SAAS,EACfC,WAAW,EACXC,WAAW,EACLC,eAAe,EAaXgC,OAAO;AAAA;AAAAgN,EAAA,GAjB9BtM,cAAc;AA8rBpB,eAAeA,cAAc;AAAC,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}