{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport EllipsisOutlinedSvg from \"@ant-design/icons-svg/es/asn/EllipsisOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar EllipsisOutlined = function EllipsisOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: EllipsisOutlinedSvg\n  }));\n};\n\n/**![ellipsis](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE3NiA1MTFhNTYgNTYgMCAxMDExMiAwIDU2IDU2IDAgMTAtMTEyIDB6bTI4MCAwYTU2IDU2IDAgMTAxMTIgMCA1NiA1NiAwIDEwLTExMiAwem0yODAgMGE1NiA1NiAwIDEwMTEyIDAgNTYgNTYgMCAxMC0xMTIgMHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(EllipsisOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EllipsisOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}