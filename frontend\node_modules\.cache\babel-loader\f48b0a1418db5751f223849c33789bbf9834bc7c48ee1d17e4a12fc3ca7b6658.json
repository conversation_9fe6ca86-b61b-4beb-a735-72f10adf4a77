{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport FileTextOutlined from \"@ant-design/icons/es/icons/FileTextOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport { FloatButtonGroupProvider } from './context';\nimport FloatButton, { floatButtonPrefixCls } from './FloatButton';\nimport useStyle from './style';\nconst FloatButtonGroup = props => {\n  var _a;\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      shape = 'circle',\n      type = 'default',\n      placement = 'top',\n      icon = /*#__PURE__*/React.createElement(FileTextOutlined, null),\n      closeIcon,\n      description,\n      trigger,\n      children,\n      onOpenChange,\n      open: customOpen,\n      onClick: onTriggerButtonClick\n    } = props,\n    floatButtonProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"shape\", \"type\", \"placement\", \"icon\", \"closeIcon\", \"description\", \"trigger\", \"children\", \"onOpenChange\", \"open\", \"onClick\"]);\n  const {\n    direction,\n    getPrefixCls,\n    closeIcon: contextCloseIcon\n  } = useComponentConfig('floatButtonGroup');\n  const mergedCloseIcon = (_a = closeIcon !== null && closeIcon !== void 0 ? closeIcon : contextCloseIcon) !== null && _a !== void 0 ? _a : /*#__PURE__*/React.createElement(CloseOutlined, null);\n  const prefixCls = getPrefixCls(floatButtonPrefixCls, customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const isMenuMode = trigger && ['click', 'hover'].includes(trigger);\n  const isValidPlacement = placement && ['top', 'left', 'right', 'bottom'].includes(placement);\n  const groupCls = classNames(groupPrefixCls, hashId, cssVarCls, rootCls, className, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl',\n    [`${groupPrefixCls}-${shape}`]: shape,\n    [`${groupPrefixCls}-${shape}-shadow`]: !isMenuMode,\n    [`${groupPrefixCls}-${placement}`]: isMenuMode && isValidPlacement // 只有菜单模式才支持弹出方向\n  });\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('FloatButton', style === null || style === void 0 ? void 0 : style.zIndex);\n  const mergedStyle = Object.assign(Object.assign({}, style), {\n    zIndex\n  });\n  const wrapperCls = classNames(hashId, `${groupPrefixCls}-wrap`);\n  const [open, setOpen] = useMergedState(false, {\n    value: customOpen\n  });\n  const floatButtonGroupRef = React.useRef(null);\n  // ========================== Open ==========================\n  const hoverTrigger = trigger === 'hover';\n  const clickTrigger = trigger === 'click';\n  const triggerOpen = useEvent(nextOpen => {\n    if (open !== nextOpen) {\n      setOpen(nextOpen);\n      onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(nextOpen);\n    }\n  });\n  // ===================== Trigger: Hover =====================\n  const onMouseEnter = () => {\n    if (hoverTrigger) {\n      triggerOpen(true);\n    }\n  };\n  const onMouseLeave = () => {\n    if (hoverTrigger) {\n      triggerOpen(false);\n    }\n  };\n  // ===================== Trigger: Click =====================\n  const onInternalTriggerButtonClick = e => {\n    if (clickTrigger) {\n      triggerOpen(!open);\n    }\n    onTriggerButtonClick === null || onTriggerButtonClick === void 0 ? void 0 : onTriggerButtonClick(e);\n  };\n  React.useEffect(() => {\n    if (clickTrigger) {\n      const onDocClick = e => {\n        var _a;\n        // Skip if click on the group\n        if ((_a = floatButtonGroupRef.current) === null || _a === void 0 ? void 0 : _a.contains(e.target)) {\n          return;\n        }\n        triggerOpen(false);\n      };\n      document.addEventListener('click', onDocClick, {\n        capture: true\n      });\n      return () => document.removeEventListener('click', onDocClick, {\n        capture: true\n      });\n    }\n  }, [clickTrigger]);\n  // ======================== Warning =========================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('FloatButton.Group');\n    process.env.NODE_ENV !== \"production\" ? warning(!('open' in props) || !!trigger, 'usage', '`open` need to be used together with `trigger`') : void 0;\n  }\n  // ========================= Render =========================\n  return wrapCSSVar(/*#__PURE__*/React.createElement(FloatButtonGroupProvider, {\n    value: shape\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: floatButtonGroupRef,\n    className: groupCls,\n    style: mergedStyle,\n    // Hover trigger\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, isMenuMode ? (/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: open,\n    motionName: `${groupPrefixCls}-wrap`\n  }, ({\n    className: motionClassName\n  }) => (/*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(motionClassName, wrapperCls)\n  }, children))), /*#__PURE__*/React.createElement(FloatButton, Object.assign({\n    type: type,\n    icon: open ? mergedCloseIcon : icon,\n    description: description,\n    \"aria-label\": props['aria-label'],\n    className: `${groupPrefixCls}-trigger`,\n    onClick: onInternalTriggerButtonClick\n  }, floatButtonProps)))) : children)));\n};\nexport default FloatButtonGroup;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}