{"ast": null, "code": "import { create } from './util/create.js';\nimport { caseInsensitiveTransform } from './util/case-insensitive-transform.js';\nexport const xmlns = create({\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  properties: {\n    xmlnsXLink: null,\n    xmlns: null\n  },\n  space: 'xmlns',\n  transform: caseInsensitiveTransform\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}