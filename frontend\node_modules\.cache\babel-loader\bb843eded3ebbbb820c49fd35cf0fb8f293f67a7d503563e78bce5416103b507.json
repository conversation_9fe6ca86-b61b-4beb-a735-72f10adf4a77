{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CloseSquareOutlinedSvg from \"@ant-design/icons-svg/es/asn/CloseSquareOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CloseSquareOutlined = function CloseSquareOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CloseSquareOutlinedSvg\n  }));\n};\n\n/**![close-square](data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CloseSquareOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CloseSquareOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}