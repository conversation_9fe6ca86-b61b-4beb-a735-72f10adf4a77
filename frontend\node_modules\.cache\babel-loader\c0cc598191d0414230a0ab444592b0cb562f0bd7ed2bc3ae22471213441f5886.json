{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nfunction getBreadcrumbName(route, params) {\n  if (route.title === undefined || route.title === null) {\n    return null;\n  }\n  const paramsKeys = Object.keys(params).join('|');\n  return typeof route.title === 'object' ? route.title : String(route.title).replace(new RegExp(`:(${paramsKeys})`, 'g'), (replacement, key) => params[key] || replacement);\n}\nexport function renderItem(prefixCls, item, children, href) {\n  if (children === null || children === undefined) {\n    return null;\n  }\n  const {\n      className,\n      onClick\n    } = item,\n    restItem = __rest(item, [\"className\", \"onClick\"]);\n  const passedProps = Object.assign(Object.assign({}, pickAttrs(restItem, {\n    data: true,\n    aria: true\n  })), {\n    onClick\n  });\n  if (href !== undefined) {\n    return /*#__PURE__*/React.createElement(\"a\", Object.assign({}, passedProps, {\n      className: classNames(`${prefixCls}-link`, className),\n      href: href\n    }), children);\n  }\n  return /*#__PURE__*/React.createElement(\"span\", Object.assign({}, passedProps, {\n    className: classNames(`${prefixCls}-link`, className)\n  }), children);\n}\nexport default function useItemRender(prefixCls, itemRender) {\n  const mergedItemRender = (item, params, routes, path, href) => {\n    if (itemRender) {\n      return itemRender(item, params, routes, path);\n    }\n    const name = getBreadcrumbName(item, params);\n    return renderItem(prefixCls, item, name, href);\n  };\n  return mergedItemRender;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}