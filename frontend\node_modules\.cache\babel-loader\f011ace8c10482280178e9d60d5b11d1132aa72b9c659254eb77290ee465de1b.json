{"ast": null, "code": "/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, Op<PERSON>, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\nimport { stringify as commas } from 'comma-separated-tokens';\nimport { ok as assert } from 'devlop';\nimport { name as isIdentifierName } from 'estree-util-is-identifier-name';\nimport { whitespace } from 'hast-util-whitespace';\nimport { find, hastToReact, html, svg } from 'property-information';\nimport { stringify as spaces } from 'space-separated-tokens';\nimport styleToJs from 'style-to-js';\nimport { pointStart } from 'unist-util-position';\nimport { VFileMessage } from 'vfile-message';\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty;\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map();\nconst cap = /[A-Z]/g;\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr']);\nconst tableCellElement = new Set(['td', 'th']);\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime';\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nexport function toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options');\n  }\n  const filePath = options.filePath || undefined;\n  /** @type {Create} */\n  let create;\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError('Expected `jsxDEV` in options when `development: true`');\n    }\n    create = developmentCreate(filePath, options.jsxDEV);\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options');\n    }\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options');\n    }\n    create = productionCreate(filePath, options.jsx, options.jsxs);\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? svg : html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  };\n  const result = one(state, tree, undefined);\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result;\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(tree, state.Fragment, {\n    children: result || undefined\n  }, undefined);\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key);\n  }\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node);\n  }\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key);\n  }\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node);\n  }\n  if (node.type === 'root') {\n    return root(state, node, key);\n  }\n  if (node.type === 'text') {\n    return text(state, node);\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema;\n  let schema = parentSchema;\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = svg;\n    state.schema = schema;\n  }\n  state.ancestors.push(node);\n  const type = findComponentFromName(state, node.tagName, false);\n  const props = createElementProps(state, node);\n  let children = createChildren(state, node);\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !whitespace(child) : true;\n    });\n  }\n  addNode(state, props, type, node);\n  addChildren(props, children);\n\n  // Restore.\n  state.ancestors.pop();\n  state.schema = parentSchema;\n  return state.create(node, type, props, key);\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree;\n    const expression = program.body[0];\n    assert(expression.type === 'ExpressionStatement');\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */state.evaluater.evaluateExpression(expression.expression);\n  }\n  crashEstree(state, node.position);\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */state.evaluater.evaluateProgram(node.data.estree);\n  }\n  crashEstree(state, node.position);\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema;\n  let schema = parentSchema;\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = svg;\n    state.schema = schema;\n  }\n  state.ancestors.push(node);\n  const type = node.name === null ? state.Fragment : findComponentFromName(state, node.name, true);\n  const props = createJsxElementProps(state, node);\n  const children = createChildren(state, node);\n  addNode(state, props, type, node);\n  addChildren(props, children);\n\n  // Restore.\n  state.ancestors.pop();\n  state.schema = parentSchema;\n  return state.create(node, type, props, key);\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {};\n  addChildren(props, createChildren(state, node));\n  return state.create(node, state.Fragment, props, key);\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value;\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node;\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0];\n    if (value) {\n      props.children = value;\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create;\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children);\n    const fn = isStaticChildren ? jsxs : jsx;\n    return key ? fn(type, props, key) : fn(type, props);\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create;\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children);\n    const point = pointStart(node);\n    return jsxDEV(type, props, key, isStaticChildren, {\n      columnNumber: point ? point.column - 1 : undefined,\n      fileName: filePath,\n      lineNumber: point ? point.line : undefined\n    }, undefined);\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {};\n  /** @type {string | undefined} */\n  let alignValue;\n  /** @type {string} */\n  let prop;\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop]);\n      if (result) {\n        const [key, value] = result;\n        if (state.tableCellAlignToStyle && key === 'align' && typeof value === 'string' && tableCellElement.has(node.tagName)) {\n          alignValue = value;\n        } else {\n          props[key] = value;\n        }\n      }\n    }\n  }\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */props.style || (props.style = {});\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] = alignValue;\n  }\n  return props;\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {};\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree;\n        const expression = program.body[0];\n        assert(expression.type === 'ExpressionStatement');\n        const objectExpression = expression.expression;\n        assert(objectExpression.type === 'ObjectExpression');\n        const property = objectExpression.properties[0];\n        assert(property.type === 'SpreadElement');\n        Object.assign(props, state.evaluater.evaluateExpression(property.argument));\n      } else {\n        crashEstree(state, node.position);\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name;\n      /** @type {unknown} */\n      let value;\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (attribute.value.data && attribute.value.data.estree && state.evaluater) {\n          const program = attribute.value.data.estree;\n          const expression = program.body[0];\n          assert(expression.type === 'ExpressionStatement');\n          value = state.evaluater.evaluateExpression(expression.expression);\n        } else {\n          crashEstree(state, node.position);\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value;\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */value;\n    }\n  }\n  return props;\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = [];\n  let index = -1;\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap;\n  while (++index < node.children.length) {\n    const child = node.children[index];\n    /** @type {string | undefined} */\n    let key;\n    if (state.passKeys) {\n      const name = child.type === 'element' ? child.tagName : child.type === 'mdxJsxFlowElement' || child.type === 'mdxJsxTextElement' ? child.name : undefined;\n      if (name) {\n        const count = countsByName.get(name) || 0;\n        key = name + '-' + count;\n        countsByName.set(name, count + 1);\n      }\n    }\n    const result = one(state, child, key);\n    if (result !== undefined) children.push(result);\n  }\n  return children;\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = find(state.schema, prop);\n\n  // Ignore nullish and `NaN` values.\n  if (value === null || value === undefined || typeof value === 'number' && Number.isNaN(value)) {\n    return;\n  }\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? commas(value) : spaces(value);\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject = typeof value === 'object' ? value : parseStyle(state, String(value));\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject);\n    }\n    return ['style', styleObject];\n  }\n  return [state.elementAttributeNameCase === 'react' && info.space ? hastToReact[info.property] || info.property : info.attribute, value];\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return styleToJs(value, {\n      reactCompat: true\n    });\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {};\n    }\n    const cause = /** @type {Error} */error;\n    const message = new VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    });\n    message.file = state.filePath || undefined;\n    message.url = docs + '#cannot-parse-style-attribute';\n    throw message;\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result;\n  if (!allowExpression) {\n    result = {\n      type: 'Literal',\n      value: name\n    };\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.');\n    let index = -1;\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node;\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = isIdentifierName(identifiers[index]) ? {\n        type: 'Identifier',\n        name: identifiers[index]\n      } : {\n        type: 'Literal',\n        value: identifiers[index]\n      };\n      node = node ? {\n        type: 'MemberExpression',\n        object: node,\n        property: prop,\n        computed: Boolean(index && prop.type === 'Literal'),\n        optional: false\n      } : prop;\n    }\n    assert(node, 'always a result');\n    result = node;\n  } else {\n    result = isIdentifierName(name) && !/^[a-z]/.test(name) ? {\n      type: 'Identifier',\n      name\n    } : {\n      type: 'Literal',\n      value: name\n    };\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */result.value;\n    return own.call(state.components, name) ? state.components[name] : name;\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result);\n  }\n  crashEstree(state);\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new VFileMessage('Cannot handle MDX estrees without `createEvaluater`', {\n    ancestors: state.ancestors,\n    place,\n    ruleId: 'mdx-estree',\n    source: 'hast-util-to-jsx-runtime'\n  });\n  message.file = state.filePath || undefined;\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater';\n  throw message;\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {};\n  /** @type {string} */\n  let from;\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from];\n    }\n  }\n  return cssCasing;\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash);\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to;\n  return to;\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase();\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}