{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { pathKey } from \"../Cache\";\nimport StyleContext from \"../StyleContext\";\nimport useCompatibleInsertionEffect from \"./useCompatibleInsertionEffect\";\nimport useEffectCleanupRegister from \"./useEffectCleanupRegister\";\nimport useHMR from \"./useHMR\";\nexport default function useGlobalCache(prefix, keyPath, cacheFn, onCacheRemove,\n// Add additional effect trigger by `useInsertionEffect`\nonCacheEffect) {\n  var _React$useContext = React.useContext(StyleContext),\n    globalCache = _React$useContext.cache;\n  var fullPath = [prefix].concat(_toConsumableArray(keyPath));\n  var fullPathStr = pathKey(fullPath);\n  var register = useEffectCleanupRegister([fullPathStr]);\n  var HMRUpdate = useHMR();\n  var buildCache = function buildCache(updater) {\n    globalCache.opUpdate(fullPathStr, function (prevCache) {\n      var _ref = prevCache || [undefined, undefined],\n        _ref2 = _slicedToArray(_ref, 2),\n        _ref2$ = _ref2[0],\n        times = _ref2$ === void 0 ? 0 : _ref2$,\n        cache = _ref2[1];\n\n      // HMR should always ignore cache since developer may change it\n      var tmpCache = cache;\n      if (process.env.NODE_ENV !== 'production' && cache && HMRUpdate) {\n        onCacheRemove === null || onCacheRemove === void 0 || onCacheRemove(tmpCache, HMRUpdate);\n        tmpCache = null;\n      }\n      var mergedCache = tmpCache || cacheFn();\n      var data = [times, mergedCache];\n\n      // Call updater if need additional logic\n      return updater ? updater(data) : data;\n    });\n  };\n\n  // Create cache\n  React.useMemo(function () {\n    buildCache();\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [fullPathStr]\n  /* eslint-enable */);\n  var cacheEntity = globalCache.opGet(fullPathStr);\n\n  // HMR clean the cache but not trigger `useMemo` again\n  // Let's fallback of this\n  // ref https://github.com/ant-design/cssinjs/issues/127\n  if (process.env.NODE_ENV !== 'production' && !cacheEntity) {\n    buildCache();\n    cacheEntity = globalCache.opGet(fullPathStr);\n  }\n  var cacheContent = cacheEntity[1];\n\n  // Remove if no need anymore\n  useCompatibleInsertionEffect(function () {\n    onCacheEffect === null || onCacheEffect === void 0 || onCacheEffect(cacheContent);\n  }, function (polyfill) {\n    // It's bad to call build again in effect.\n    // But we have to do this since StrictMode will call effect twice\n    // which will clear cache on the first time.\n    buildCache(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        times = _ref4[0],\n        cache = _ref4[1];\n      if (polyfill && times === 0) {\n        onCacheEffect === null || onCacheEffect === void 0 || onCacheEffect(cacheContent);\n      }\n      return [times + 1, cache];\n    });\n    return function () {\n      globalCache.opUpdate(fullPathStr, function (prevCache) {\n        var _ref5 = prevCache || [],\n          _ref6 = _slicedToArray(_ref5, 2),\n          _ref6$ = _ref6[0],\n          times = _ref6$ === void 0 ? 0 : _ref6$,\n          cache = _ref6[1];\n        var nextCount = times - 1;\n        if (nextCount === 0) {\n          // Always remove styles in useEffect callback\n          register(function () {\n            // With polyfill, registered callback will always be called synchronously\n            // But without polyfill, it will be called in effect clean up,\n            // And by that time this cache is cleaned up.\n            if (polyfill || !globalCache.opGet(fullPathStr)) {\n              onCacheRemove === null || onCacheRemove === void 0 || onCacheRemove(cache, false);\n            }\n          });\n          return null;\n        }\n        return [times - 1, cache];\n      });\n    };\n  }, [fullPathStr]);\n  return cacheContent;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}