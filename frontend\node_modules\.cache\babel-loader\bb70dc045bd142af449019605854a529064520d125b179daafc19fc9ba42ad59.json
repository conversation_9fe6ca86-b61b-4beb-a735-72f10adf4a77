{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport { Color as RcColor } from '@rc-component/color-picker';\nexport const toHexFormat = (value, alpha) => (value === null || value === void 0 ? void 0 : value.replace(/[^\\w/]/g, '').slice(0, alpha ? 8 : 6)) || '';\nexport const getHex = (value, alpha) => value ? toHexFormat(value, alpha) : '';\nexport let AggregationColor = /*#__PURE__*/function () {\n  function AggregationColor(color) {\n    _classCallCheck(this, AggregationColor);\n    var _a;\n    this.cleared = false;\n    // Clone from another AggregationColor\n    if (color instanceof AggregationColor) {\n      this.metaColor = color.metaColor.clone();\n      this.colors = (_a = color.colors) === null || _a === void 0 ? void 0 : _a.map(info => ({\n        color: new AggregationColor(info.color),\n        percent: info.percent\n      }));\n      this.cleared = color.cleared;\n      return;\n    }\n    const isArray = Array.isArray(color);\n    if (isArray && color.length) {\n      this.colors = color.map(({\n        color: c,\n        percent\n      }) => ({\n        color: new AggregationColor(c),\n        percent\n      }));\n      this.metaColor = new RcColor(this.colors[0].color.metaColor);\n    } else {\n      this.metaColor = new RcColor(isArray ? '' : color);\n    }\n    if (!color || isArray && !this.colors) {\n      this.metaColor = this.metaColor.setA(0);\n      this.cleared = true;\n    }\n  }\n  return _createClass(AggregationColor, [{\n    key: \"toHsb\",\n    value: function toHsb() {\n      return this.metaColor.toHsb();\n    }\n  }, {\n    key: \"toHsbString\",\n    value: function toHsbString() {\n      return this.metaColor.toHsbString();\n    }\n  }, {\n    key: \"toHex\",\n    value: function toHex() {\n      return getHex(this.toHexString(), this.metaColor.a < 1);\n    }\n  }, {\n    key: \"toHexString\",\n    value: function toHexString() {\n      return this.metaColor.toHexString();\n    }\n  }, {\n    key: \"toRgb\",\n    value: function toRgb() {\n      return this.metaColor.toRgb();\n    }\n  }, {\n    key: \"toRgbString\",\n    value: function toRgbString() {\n      return this.metaColor.toRgbString();\n    }\n  }, {\n    key: \"isGradient\",\n    value: function isGradient() {\n      return !!this.colors && !this.cleared;\n    }\n  }, {\n    key: \"getColors\",\n    value: function getColors() {\n      return this.colors || [{\n        color: this,\n        percent: 0\n      }];\n    }\n  }, {\n    key: \"toCssString\",\n    value: function toCssString() {\n      const {\n        colors\n      } = this;\n      // CSS line-gradient\n      if (colors) {\n        const colorsStr = colors.map(c => `${c.color.toRgbString()} ${c.percent}%`).join(', ');\n        return `linear-gradient(90deg, ${colorsStr})`;\n      }\n      return this.metaColor.toRgbString();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(color) {\n      if (!color || this.isGradient() !== color.isGradient()) {\n        return false;\n      }\n      if (!this.isGradient()) {\n        return this.toHexString() === color.toHexString();\n      }\n      return this.colors.length === color.colors.length && this.colors.every((c, i) => {\n        const target = color.colors[i];\n        return c.percent === target.percent && c.color.equals(target.color);\n      });\n    }\n  }]);\n}();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}