{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { operationUnit, resetComponent, resetIcon, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genTransferCustomizeStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    listHeight,\n    controlHeightLG\n  } = token;\n  const tableCls = `${antCls}-table`;\n  const inputCls = `${antCls}-input`;\n  return {\n    [`${componentCls}-customize-list`]: {\n      [`${componentCls}-list`]: {\n        flex: '1 1 50%',\n        width: 'auto',\n        height: 'auto',\n        minHeight: listHeight,\n        minWidth: 0\n      },\n      // =================== Hook Components ===================\n      [`${tableCls}-wrapper`]: {\n        [`${tableCls}-small`]: {\n          border: 0,\n          borderRadius: 0,\n          [`${tableCls}-selection-column`]: {\n            width: controlHeightLG,\n            minWidth: controlHeightLG\n          }\n        },\n        [`${tableCls}-pagination${tableCls}-pagination`]: {\n          margin: 0,\n          padding: token.paddingXS\n        }\n      },\n      [`${inputCls}[disabled]`]: {\n        backgroundColor: 'transparent'\n      }\n    }\n  };\n};\nconst genTransferStatusColor = (token, color) => {\n  const {\n    componentCls,\n    colorBorder\n  } = token;\n  return {\n    [`${componentCls}-list`]: {\n      borderColor: color,\n      '&-search:not([disabled])': {\n        borderColor: colorBorder\n      }\n    }\n  };\n};\nconst genTransferStatusStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-status-error`]: Object.assign({}, genTransferStatusColor(token, token.colorError)),\n    [`${componentCls}-status-warning`]: Object.assign({}, genTransferStatusColor(token, token.colorWarning))\n  };\n};\nconst genTransferListStyle = token => {\n  const {\n    componentCls,\n    colorBorder,\n    colorSplit,\n    lineWidth,\n    itemHeight,\n    headerHeight,\n    transferHeaderVerticalPadding,\n    itemPaddingBlock,\n    controlItemBgActive,\n    colorTextDisabled,\n    colorTextSecondary,\n    listHeight,\n    listWidth,\n    listWidthLG,\n    fontSizeIcon,\n    marginXS,\n    paddingSM,\n    lineType,\n    antCls,\n    iconCls,\n    motionDurationSlow,\n    controlItemBgHover,\n    borderRadiusLG,\n    colorBgContainer,\n    colorText,\n    controlItemBgActiveHover\n  } = token;\n  const contentBorderRadius = unit(token.calc(borderRadiusLG).sub(lineWidth).equal());\n  return {\n    display: 'flex',\n    flexDirection: 'column',\n    width: listWidth,\n    height: listHeight,\n    border: `${unit(lineWidth)} ${lineType} ${colorBorder}`,\n    borderRadius: token.borderRadiusLG,\n    '&-with-pagination': {\n      width: listWidthLG,\n      height: 'auto'\n    },\n    '&-search': {\n      [`${iconCls}-search`]: {\n        color: colorTextDisabled\n      }\n    },\n    '&-header': {\n      display: 'flex',\n      flex: 'none',\n      alignItems: 'center',\n      height: headerHeight,\n      // border-top is on the transfer dom. We should minus 1px for this\n      padding: `${unit(token.calc(transferHeaderVerticalPadding).sub(lineWidth).equal())} ${unit(paddingSM)} ${unit(transferHeaderVerticalPadding)}`,\n      color: colorText,\n      background: colorBgContainer,\n      borderBottom: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      borderRadius: `${unit(borderRadiusLG)} ${unit(borderRadiusLG)} 0 0`,\n      '> *:not(:last-child)': {\n        marginInlineEnd: 4 // This is magic and fixed number, DO NOT use token since it may change.\n      },\n      '> *': {\n        flex: 'none'\n      },\n      '&-title': Object.assign(Object.assign({}, textEllipsis), {\n        flex: 'auto',\n        textAlign: 'end'\n      }),\n      '&-dropdown': Object.assign(Object.assign({}, resetIcon()), {\n        fontSize: fontSizeIcon,\n        transform: 'translateY(10%)',\n        cursor: 'pointer',\n        '&[disabled]': {\n          cursor: 'not-allowed'\n        }\n      })\n    },\n    '&-body': {\n      display: 'flex',\n      flex: 'auto',\n      flexDirection: 'column',\n      fontSize: token.fontSize,\n      // https://blog.csdn.net/qq449245884/article/details/107373672/\n      minHeight: 0,\n      '&-search-wrapper': {\n        position: 'relative',\n        flex: 'none',\n        padding: paddingSM\n      }\n    },\n    '&-content': {\n      flex: 'auto',\n      margin: 0,\n      padding: 0,\n      overflow: 'auto',\n      listStyle: 'none',\n      borderRadius: `0 0 ${contentBorderRadius} ${contentBorderRadius}`,\n      '&-item': {\n        display: 'flex',\n        alignItems: 'center',\n        minHeight: itemHeight,\n        padding: `${unit(itemPaddingBlock)} ${unit(paddingSM)}`,\n        transition: `all ${motionDurationSlow}`,\n        '> *:not(:last-child)': {\n          marginInlineEnd: marginXS\n        },\n        '> *': {\n          flex: 'none'\n        },\n        '&-text': Object.assign(Object.assign({}, textEllipsis), {\n          flex: 'auto'\n        }),\n        '&-remove': Object.assign(Object.assign({}, operationUnit(token)), {\n          color: colorBorder,\n          '&:hover, &:focus': {\n            color: colorTextSecondary\n          }\n        }),\n        [`&:not(${componentCls}-list-content-item-disabled)`]: {\n          '&:hover': {\n            backgroundColor: controlItemBgHover,\n            cursor: 'pointer'\n          },\n          [`&${componentCls}-list-content-item-checked:hover`]: {\n            backgroundColor: controlItemBgActiveHover\n          }\n        },\n        '&-checked': {\n          backgroundColor: controlItemBgActive\n        },\n        '&-disabled': {\n          color: colorTextDisabled,\n          cursor: 'not-allowed'\n        }\n      },\n      // Do not change hover style when `oneWay` mode\n      [`&-show-remove ${componentCls}-list-content-item:not(${componentCls}-list-content-item-disabled):hover`]: {\n        background: 'transparent',\n        cursor: 'default'\n      }\n    },\n    '&-pagination': {\n      padding: token.paddingXS,\n      textAlign: 'end',\n      borderTop: `${unit(lineWidth)} ${lineType} ${colorSplit}`,\n      [`${antCls}-pagination-options`]: {\n        paddingInlineEnd: token.paddingXS\n      }\n    },\n    '&-body-not-found': {\n      flex: 'none',\n      width: '100%',\n      margin: 'auto 0',\n      color: colorTextDisabled,\n      textAlign: 'center'\n    },\n    '&-footer': {\n      borderTop: `${unit(lineWidth)} ${lineType} ${colorSplit}`\n    },\n    // fix: https://github.com/ant-design/ant-design/issues/44489\n    '&-checkbox': {\n      lineHeight: 1\n    }\n  };\n};\nconst genTransferStyle = token => {\n  const {\n    antCls,\n    iconCls,\n    componentCls,\n    marginXS,\n    marginXXS,\n    fontSizeIcon,\n    colorBgContainerDisabled\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'flex',\n      alignItems: 'stretch',\n      [`${componentCls}-disabled`]: {\n        [`${componentCls}-list`]: {\n          background: colorBgContainerDisabled\n        }\n      },\n      [`${componentCls}-list`]: genTransferListStyle(token),\n      [`${componentCls}-operation`]: {\n        display: 'flex',\n        flex: 'none',\n        flexDirection: 'column',\n        alignSelf: 'center',\n        margin: `0 ${unit(marginXS)}`,\n        verticalAlign: 'middle',\n        gap: marginXXS,\n        [`${antCls}-btn ${iconCls}`]: {\n          fontSize: fontSizeIcon\n        }\n      }\n    })\n  };\n};\nconst genTransferRTLStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    fontSize,\n    lineHeight,\n    controlHeight,\n    controlHeightLG,\n    lineWidth\n  } = token;\n  const fontHeight = Math.round(fontSize * lineHeight);\n  return {\n    listWidth: 180,\n    listHeight: 200,\n    listWidthLG: 250,\n    headerHeight: controlHeightLG,\n    itemHeight: controlHeight,\n    itemPaddingBlock: (controlHeight - fontHeight) / 2,\n    transferHeaderVerticalPadding: Math.ceil((controlHeightLG - lineWidth - fontHeight) / 2)\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Transfer', token => {\n  const transferToken = mergeToken(token);\n  return [genTransferStyle(transferToken), genTransferCustomizeStyle(transferToken), genTransferStatusStyle(transferToken), genTransferRTLStyle(transferToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}