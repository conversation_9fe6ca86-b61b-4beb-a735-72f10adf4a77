{"ast": null, "code": "/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nexport class Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal;\n    this.property = property;\n    if (space) {\n      this.space = space;\n    }\n  }\n}\nSchema.prototype.normal = {};\nSchema.prototype.property = {};\nSchema.prototype.space = undefined;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}