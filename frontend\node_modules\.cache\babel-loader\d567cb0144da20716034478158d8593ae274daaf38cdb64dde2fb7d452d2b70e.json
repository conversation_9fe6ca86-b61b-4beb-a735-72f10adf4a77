{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport warn from \"rc-util/es/warning\";\nimport React, { useContext, useEffect } from 'react';\nimport IconContext from \"./components/Context\";\nfunction camelCase(input) {\n  return input.replace(/-(.)/g, function (match, g) {\n    return g.toUpperCase();\n  });\n}\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        delete acc[key];\n        acc[camelCase(key)] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-flex;\\n  align-items: center;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles(eleRef) {\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp,\n    prefixCls = _useContext.prefixCls,\n    layer = _useContext.layer;\n  var mergedStyleStr = iconStyles;\n  if (prefixCls) {\n    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);\n  }\n  if (layer) {\n    mergedStyleStr = \"@layer \".concat(layer, \" {\\n\").concat(mergedStyleStr, \"\\n}\");\n  }\n  useEffect(function () {\n    var ele = eleRef.current;\n    var shadowRoot = getShadowRoot(ele);\n    updateCSS(mergedStyleStr, '@ant-design-icons', {\n      prepend: !layer,\n      csp: csp,\n      attachTo: shadowRoot\n    });\n  }, []);\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}