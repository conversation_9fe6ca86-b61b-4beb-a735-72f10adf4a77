{"ast": null, "code": "\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport classNames from 'classnames';\nconst IconWrapper = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    className,\n    style,\n    children,\n    prefixCls\n  } = props;\n  const iconWrapperCls = classNames(`${prefixCls}-icon`, className);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    ref: ref,\n    className: iconWrapperCls,\n    style: style\n  }, children);\n});\nexport default IconWrapper;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}