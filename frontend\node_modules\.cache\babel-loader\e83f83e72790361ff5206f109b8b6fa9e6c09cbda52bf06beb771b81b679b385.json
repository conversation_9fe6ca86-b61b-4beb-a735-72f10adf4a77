{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { genModalMaskStyle } from '../../modal/style';\nimport { textEllipsis } from '../../style';\nimport { initFadeMotion, initZoomMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nexport const genBoxStyle = position => ({\n  position: position || 'absolute',\n  inset: 0\n});\nexport const genImageMaskStyle = token => {\n  const {\n    iconCls,\n    motionDurationSlow,\n    paddingXXS,\n    marginXXS,\n    prefixCls,\n    colorTextLightSolid\n  } = token;\n  return {\n    position: 'absolute',\n    inset: 0,\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    color: colorTextLightSolid,\n    background: new FastColor('#000').setA(0.5).toRgbString(),\n    cursor: 'pointer',\n    opacity: 0,\n    transition: `opacity ${motionDurationSlow}`,\n    [`.${prefixCls}-mask-info`]: Object.assign(Object.assign({}, textEllipsis), {\n      padding: `0 ${unit(paddingXXS)}`,\n      [iconCls]: {\n        marginInlineEnd: marginXXS,\n        svg: {\n          verticalAlign: 'baseline'\n        }\n      }\n    })\n  };\n};\nexport const genPreviewOperationsStyle = token => {\n  const {\n    previewCls,\n    modalMaskBg,\n    paddingSM,\n    marginXL,\n    margin,\n    paddingLG,\n    previewOperationColorDisabled,\n    previewOperationHoverColor,\n    motionDurationSlow,\n    iconCls,\n    colorTextLightSolid\n  } = token;\n  const operationBg = new FastColor(modalMaskBg).setA(0.1);\n  const operationBgHover = operationBg.clone().setA(0.2);\n  return {\n    [`${previewCls}-footer`]: {\n      position: 'fixed',\n      bottom: marginXL,\n      left: {\n        _skip_check_: true,\n        value: '50%'\n      },\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      color: token.previewOperationColor,\n      transform: 'translateX(-50%)'\n    },\n    [`${previewCls}-progress`]: {\n      marginBottom: margin\n    },\n    [`${previewCls}-close`]: {\n      position: 'fixed',\n      top: marginXL,\n      right: {\n        _skip_check_: true,\n        value: marginXL\n      },\n      display: 'flex',\n      color: colorTextLightSolid,\n      backgroundColor: operationBg.toRgbString(),\n      borderRadius: '50%',\n      padding: paddingSM,\n      outline: 0,\n      border: 0,\n      cursor: 'pointer',\n      transition: `all ${motionDurationSlow}`,\n      '&:hover': {\n        backgroundColor: operationBgHover.toRgbString()\n      },\n      [`& > ${iconCls}`]: {\n        fontSize: token.previewOperationSize\n      }\n    },\n    [`${previewCls}-operations`]: {\n      display: 'flex',\n      alignItems: 'center',\n      padding: `0 ${unit(paddingLG)}`,\n      backgroundColor: operationBg.toRgbString(),\n      borderRadius: 100,\n      '&-operation': {\n        marginInlineStart: paddingSM,\n        padding: paddingSM,\n        cursor: 'pointer',\n        transition: `all ${motionDurationSlow}`,\n        userSelect: 'none',\n        [`&:not(${previewCls}-operations-operation-disabled):hover > ${iconCls}`]: {\n          color: previewOperationHoverColor\n        },\n        '&-disabled': {\n          color: previewOperationColorDisabled,\n          cursor: 'not-allowed'\n        },\n        '&:first-of-type': {\n          marginInlineStart: 0\n        },\n        [`& > ${iconCls}`]: {\n          fontSize: token.previewOperationSize\n        }\n      }\n    }\n  };\n};\nexport const genPreviewSwitchStyle = token => {\n  const {\n    modalMaskBg,\n    iconCls,\n    previewOperationColorDisabled,\n    previewCls,\n    zIndexPopup,\n    motionDurationSlow\n  } = token;\n  const operationBg = new FastColor(modalMaskBg).setA(0.1);\n  const operationBgHover = operationBg.clone().setA(0.2);\n  return {\n    [`${previewCls}-switch-left, ${previewCls}-switch-right`]: {\n      position: 'fixed',\n      insetBlockStart: '50%',\n      zIndex: token.calc(zIndexPopup).add(1).equal(),\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      width: token.imagePreviewSwitchSize,\n      height: token.imagePreviewSwitchSize,\n      marginTop: token.calc(token.imagePreviewSwitchSize).mul(-1).div(2).equal(),\n      color: token.previewOperationColor,\n      background: operationBg.toRgbString(),\n      borderRadius: '50%',\n      transform: `translateY(-50%)`,\n      cursor: 'pointer',\n      transition: `all ${motionDurationSlow}`,\n      userSelect: 'none',\n      '&:hover': {\n        background: operationBgHover.toRgbString()\n      },\n      '&-disabled': {\n        '&, &:hover': {\n          color: previewOperationColorDisabled,\n          background: 'transparent',\n          cursor: 'not-allowed',\n          [`> ${iconCls}`]: {\n            cursor: 'not-allowed'\n          }\n        }\n      },\n      [`> ${iconCls}`]: {\n        fontSize: token.previewOperationSize\n      }\n    },\n    [`${previewCls}-switch-left`]: {\n      insetInlineStart: token.marginSM\n    },\n    [`${previewCls}-switch-right`]: {\n      insetInlineEnd: token.marginSM\n    }\n  };\n};\nexport const genImagePreviewStyle = token => {\n  const {\n    motionEaseOut,\n    previewCls,\n    motionDurationSlow,\n    componentCls\n  } = token;\n  return [{\n    [`${componentCls}-preview-root`]: {\n      [previewCls]: {\n        height: '100%',\n        textAlign: 'center',\n        pointerEvents: 'none'\n      },\n      [`${previewCls}-body`]: Object.assign(Object.assign({}, genBoxStyle()), {\n        overflow: 'hidden'\n      }),\n      [`${previewCls}-img`]: {\n        maxWidth: '100%',\n        maxHeight: '70%',\n        verticalAlign: 'middle',\n        transform: 'scale3d(1, 1, 1)',\n        cursor: 'grab',\n        transition: `transform ${motionDurationSlow} ${motionEaseOut} 0s`,\n        userSelect: 'none',\n        '&-wrapper': Object.assign(Object.assign({}, genBoxStyle()), {\n          transition: `transform ${motionDurationSlow} ${motionEaseOut} 0s`,\n          // https://github.com/ant-design/ant-design/issues/39913\n          // TailwindCSS will reset img default style.\n          // Let's set back.\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          '& > *': {\n            pointerEvents: 'auto'\n          },\n          '&::before': {\n            display: 'inline-block',\n            width: 1,\n            height: '50%',\n            marginInlineEnd: -1,\n            content: '\"\"'\n          }\n        })\n      },\n      [`${previewCls}-moving`]: {\n        [`${previewCls}-preview-img`]: {\n          cursor: 'grabbing',\n          '&-wrapper': {\n            transitionDuration: '0s'\n          }\n        }\n      }\n    }\n  },\n  // Override\n  {\n    [`${componentCls}-preview-root`]: {\n      [`${previewCls}-wrap`]: {\n        zIndex: token.zIndexPopup\n      }\n    }\n  },\n  // Preview operations & switch\n  {\n    [`${componentCls}-preview-operations-wrapper`]: {\n      position: 'fixed',\n      zIndex: token.calc(token.zIndexPopup).add(1).equal()\n    },\n    '&': [genPreviewOperationsStyle(token), genPreviewSwitchStyle(token)]\n  }];\n};\nconst genImageStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    // ============================== image ==============================\n    [componentCls]: {\n      position: 'relative',\n      display: 'inline-block',\n      [`${componentCls}-img`]: {\n        width: '100%',\n        height: 'auto',\n        verticalAlign: 'middle'\n      },\n      [`${componentCls}-img-placeholder`]: {\n        backgroundColor: token.colorBgContainerDisabled,\n        backgroundImage: \"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTQuNSAyLjVoLTEzQS41LjUgMCAwIDAgMSAzdjEwYS41LjUgMCAwIDAgLjUuNWgxM2EuNS41IDAgMCAwIC41LS41VjNhLjUuNSAwIDAgMC0uNS0uNXpNNS4yODEgNC43NWExIDEgMCAwIDEgMCAyIDEgMSAwIDAgMSAwLTJ6bTguMDMgNi44M2EuMTI3LjEyNyAwIDAgMS0uMDgxLjAzSDIuNzY5YS4xMjUuMTI1IDAgMCAxLS4wOTYtLjIwN2wyLjY2MS0zLjE1NmEuMTI2LjEyNiAwIDAgMSAuMTc3LS4wMTZsLjAxNi4wMTZMNy4wOCAxMC4wOWwyLjQ3LTIuOTNhLjEyNi4xMjYgMCAwIDEgLjE3Ny0uMDE2bC4wMTUuMDE2IDMuNTg4IDQuMjQ0YS4xMjcuMTI3IDAgMCAxLS4wMi4xNzV6IiBmaWxsPSIjOEM4QzhDIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48L3N2Zz4=')\",\n        backgroundRepeat: 'no-repeat',\n        backgroundPosition: 'center center',\n        backgroundSize: '30%'\n      },\n      [`${componentCls}-mask`]: Object.assign({}, genImageMaskStyle(token)),\n      [`${componentCls}-mask:hover`]: {\n        opacity: 1\n      },\n      [`${componentCls}-placeholder`]: Object.assign({}, genBoxStyle())\n    }\n  };\n};\nconst genPreviewMotion = token => {\n  const {\n    previewCls\n  } = token;\n  return {\n    [`${previewCls}-root`]: initZoomMotion(token, 'zoom'),\n    '&': initFadeMotion(token, true)\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  zIndexPopup: token.zIndexPopupBase + 80,\n  previewOperationColor: new FastColor(token.colorTextLightSolid).setA(0.65).toRgbString(),\n  previewOperationHoverColor: new FastColor(token.colorTextLightSolid).setA(0.85).toRgbString(),\n  previewOperationColorDisabled: new FastColor(token.colorTextLightSolid).setA(0.25).toRgbString(),\n  previewOperationSize: token.fontSizeIcon * 1.5 // FIXME: fontSizeIconLG\n});\nexport default genStyleHooks('Image', token => {\n  const previewCls = `${token.componentCls}-preview`;\n  const imageToken = mergeToken(token, {\n    previewCls,\n    modalMaskBg: new FastColor('#000').setA(0.45).toRgbString(),\n    // FIXME: Shared Token\n    imagePreviewSwitchSize: token.controlHeightLG\n  });\n  return [genImageStyle(imageToken), genImagePreviewStyle(imageToken), genModalMaskStyle(mergeToken(imageToken, {\n    componentCls: previewCls\n  })), genPreviewMotion(imageToken)];\n}, prepareComponentToken);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}