{"ast": null, "code": "/**\n * Webpack has bug for import loop, which is not the same behavior as ES module.\n * When util.js imports the TreeNode for tree generate will cause treeContextTypes be empty.\n */\nimport * as React from 'react';\nexport var TreeContext = /*#__PURE__*/React.createContext(null);\n\n/** Internal usage, safe to remove. Do not use in prod */\nexport var UnstableContext = /*#__PURE__*/React.createContext({});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}