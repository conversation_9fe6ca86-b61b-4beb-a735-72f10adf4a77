{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { useEvent } from 'rc-util';\nimport raf from \"rc-util/es/raf\";\nimport { cloneElement } from '../_util/reactNode';\nimport Statistic from './Statistic';\nimport { formatCounter } from './utils';\nfunction getTime(value) {\n  return new Date(value).getTime();\n}\nconst StatisticTimer = props => {\n  const {\n      value,\n      format = 'HH:mm:ss',\n      onChange,\n      onFinish,\n      type\n    } = props,\n    rest = __rest(props, [\"value\", \"format\", \"onChange\", \"onFinish\", \"type\"]);\n  const down = type === 'countdown';\n  // We reuse state here to do same as `forceUpdate`\n  const [showTime, setShowTime] = React.useState(null);\n  // ======================== Update ========================\n  const update = useEvent(() => {\n    const now = Date.now();\n    const timestamp = getTime(value);\n    setShowTime({});\n    const timeDiff = !down ? now - timestamp : timestamp - now;\n    onChange === null || onChange === void 0 ? void 0 : onChange(timeDiff);\n    // Only countdown will trigger `onFinish`\n    if (down && timestamp < now) {\n      onFinish === null || onFinish === void 0 ? void 0 : onFinish();\n      return false;\n    }\n    return true;\n  });\n  // Effect trigger\n  React.useEffect(() => {\n    let rafId;\n    const clear = () => raf.cancel(rafId);\n    const rafUpdate = () => {\n      rafId = raf(() => {\n        if (update()) {\n          rafUpdate();\n        }\n      });\n    };\n    rafUpdate();\n    return clear;\n  }, [value, down]);\n  React.useEffect(() => {\n    setShowTime({});\n  }, []);\n  // ======================== Format ========================\n  const formatter = (formatValue, config) => showTime ? formatCounter(formatValue, Object.assign(Object.assign({}, config), {\n    format\n  }), down) : '-';\n  const valueRender = node => cloneElement(node, {\n    title: undefined\n  });\n  // ======================== Render ========================\n  return /*#__PURE__*/React.createElement(Statistic, Object.assign({}, rest, {\n    value: value,\n    valueRender: valueRender,\n    formatter: formatter\n  }));\n};\nexport default StatisticTimer;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}