{"ast": null, "code": "import { isUrl } from './minurl.shared.js';\nexport { isUrl } from './minurl.shared.js';\n\n// See: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js>\n\n/**\n * @param {URL | string} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nexport function urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path);\n  } else if (!isUrl(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The \"path\" argument must be of type string or an instance of URL. Received `' + path + '`');\n    error.code = 'ERR_INVALID_ARG_TYPE';\n    throw error;\n  }\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file');\n    error.code = 'ERR_INVALID_URL_SCHEME';\n    throw error;\n  }\n  return getPathFromURLPosix(path);\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('File URL host must be \"localhost\" or empty on darwin');\n    error.code = 'ERR_INVALID_FILE_URL_HOST';\n    throw error;\n  }\n  const pathname = url.pathname;\n  let index = -1;\n  while (++index < pathname.length) {\n    if (pathname.codePointAt(index) === 37 /* `%` */ && pathname.codePointAt(index + 1) === 50 /* `2` */) {\n      const third = pathname.codePointAt(index + 2);\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError('File URL path must not include encoded / characters');\n        error.code = 'ERR_INVALID_FILE_URL_PATH';\n        throw error;\n      }\n    }\n  }\n  return decodeURIComponent(pathname);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}