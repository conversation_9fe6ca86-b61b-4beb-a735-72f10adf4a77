{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    fontSize,\n    lineHeight,\n    calc\n  } = token;\n  const itemCls = `${componentCls}-list-item`;\n  const actionsCls = `${itemCls}-actions`;\n  const actionCls = `${itemCls}-action`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-list`]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: calc(token.lineHeight).mul(fontSize).equal(),\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: `background-color ${token.motionDurationSlow}`,\n          borderRadius: token.borderRadiusSM,\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [`${itemCls}-name`]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: `0 ${unit(token.paddingXS)}`,\n            lineHeight,\n            flex: 'auto',\n            transition: `all ${token.motionDurationSlow}`\n          }),\n          [actionsCls]: {\n            whiteSpace: 'nowrap',\n            [actionCls]: {\n              opacity: 0\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: `all ${token.motionDurationSlow}`\n            },\n            [`\n              ${actionCls}:focus-visible,\n              &.picture ${actionCls}\n            `]: {\n              opacity: 1\n            }\n          },\n          [`${componentCls}-icon ${iconCls}`]: {\n            color: token.colorIcon,\n            fontSize\n          },\n          [`${itemCls}-progress`]: {\n            position: 'absolute',\n            bottom: token.calc(token.uploadProgressOffset).mul(-1).equal(),\n            width: '100%',\n            paddingInlineStart: calc(fontSize).add(token.paddingXS).equal(),\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [`${itemCls}:hover ${actionCls}`]: {\n          opacity: 1\n        },\n        [`${itemCls}-error`]: {\n          color: token.colorError,\n          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [`${iconCls}, ${iconCls}:hover`]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [`${componentCls}-list-item-container`]: {\n          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}