{"ast": null, "code": "// ============================ Directory =============================\nexport const genDirectoryStyle = ({\n  treeCls,\n  treeNodeCls,\n  directoryNodeSelectedBg,\n  directoryNodeSelectedColor,\n  motionDurationMid,\n  borderRadius,\n  controlItemBgHover\n}) => ({\n  [`${treeCls}${treeCls}-directory ${treeNodeCls}`]: {\n    // >>> Title\n    [`${treeCls}-node-content-wrapper`]: {\n      position: 'static',\n      [`> *:not(${treeCls}-drop-indicator)`]: {\n        position: 'relative'\n      },\n      '&:hover': {\n        background: 'transparent'\n      },\n      // Expand interactive area to whole line\n      '&:before': {\n        position: 'absolute',\n        inset: 0,\n        transition: `background-color ${motionDurationMid}`,\n        content: '\"\"',\n        borderRadius\n      },\n      '&:hover:before': {\n        background: controlItemBgHover\n      }\n    },\n    [`${treeCls}-switcher, ${treeCls}-checkbox, ${treeCls}-draggable-icon`]: {\n      zIndex: 1\n    },\n    // ============= Selected =============\n    '&-selected': {\n      [`${treeCls}-switcher, ${treeCls}-draggable-icon`]: {\n        color: directoryNodeSelectedColor\n      },\n      // >>> Title\n      [`${treeCls}-node-content-wrapper`]: {\n        color: directoryNodeSelectedColor,\n        background: 'transparent',\n        '&:before, &:hover:before': {\n          background: directoryNodeSelectedBg\n        }\n      }\n    }\n  }\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}