{"ast": null, "code": "import { VOID, PRIMITIVE, ARRAY, OBJECT, DATE, REGEXP, MAP, SET, ERROR, BIGINT } from './types.js';\nconst env = typeof self === 'object' ? self : globalThis;\nconst deserializer = ($, _) => {\n  const as = (out, index) => {\n    $.set(index, out);\n    return out;\n  };\n  const unpair = index => {\n    if ($.has(index)) return $.get(index);\n    const [type, value] = _[index];\n    switch (type) {\n      case PRIMITIVE:\n      case VOID:\n        return as(value, index);\n      case ARRAY:\n        {\n          const arr = as([], index);\n          for (const index of value) arr.push(unpair(index));\n          return arr;\n        }\n      case OBJECT:\n        {\n          const object = as({}, index);\n          for (const [key, index] of value) object[unpair(key)] = unpair(index);\n          return object;\n        }\n      case DATE:\n        return as(new Date(value), index);\n      case REGEXP:\n        {\n          const {\n            source,\n            flags\n          } = value;\n          return as(new RegExp(source, flags), index);\n        }\n      case MAP:\n        {\n          const map = as(new Map(), index);\n          for (const [key, index] of value) map.set(unpair(key), unpair(index));\n          return map;\n        }\n      case SET:\n        {\n          const set = as(new Set(), index);\n          for (const index of value) set.add(unpair(index));\n          return set;\n        }\n      case ERROR:\n        {\n          const {\n            name,\n            message\n          } = value;\n          return as(new env[name](message), index);\n        }\n      case BIGINT:\n        return as(BigInt(value), index);\n      case 'BigInt':\n        return as(Object(BigInt(value)), index);\n      case 'ArrayBuffer':\n        return as(new Uint8Array(value).buffer, value);\n      case 'DataView':\n        {\n          const {\n            buffer\n          } = new Uint8Array(value);\n          return as(new DataView(buffer), value);\n        }\n    }\n    return as(new env[type](value), index);\n  };\n  return unpair;\n};\n\n/**\n * @typedef {Array<string,any>} Record a type representation\n */\n\n/**\n * Returns a deserialized value from a serialized array of Records.\n * @param {Record[]} serialized a previously serialized value.\n * @returns {any}\n */\nexport const deserialize = serialized => deserializer(new Map(), serialized)(0);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}