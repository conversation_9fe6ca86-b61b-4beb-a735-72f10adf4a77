{"ast": null, "code": "/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType;\n  let suffix = ']';\n  if (subtype === 'collapsed') {\n    suffix += '[]';\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']';\n  }\n  if (node.type === 'imageReference') {\n    return [{\n      type: 'text',\n      value: '![' + node.alt + suffix\n    }];\n  }\n  const contents = state.all(node);\n  const head = contents[0];\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value;\n  } else {\n    contents.unshift({\n      type: 'text',\n      value: '['\n    });\n  }\n  const tail = contents[contents.length - 1];\n  if (tail && tail.type === 'text') {\n    tail.value += suffix;\n  } else {\n    contents.push({\n      type: 'text',\n      value: suffix\n    });\n  }\n  return contents;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}