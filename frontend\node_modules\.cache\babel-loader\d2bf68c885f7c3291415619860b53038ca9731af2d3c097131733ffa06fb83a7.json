{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport cls from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Handles from \"./Handles\";\nimport Marks from \"./Marks\";\nimport Steps from \"./Steps\";\nimport Tracks from \"./Tracks\";\nimport SliderContext from \"./context\";\nimport useDrag from \"./hooks/useDrag\";\nimport useOffset from \"./hooks/useOffset\";\nimport useRange from \"./hooks/useRange\";\n\n/**\n * New:\n * - click mark to update range value\n * - handleRender\n * - Fix handle with count not correct\n * - Fix pushable not work in some case\n * - No more FindDOMNode\n * - Move all position related style into inline style\n * - Key: up is plus, down is minus\n * - fix Key with step = null not align with marks\n * - Change range should not trigger onChange\n * - keyboard support pushable\n */\n\nvar Slider = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-slider' : _props$prefixCls,\n    className = props.className,\n    style = props.style,\n    classNames = props.classNames,\n    styles = props.styles,\n    id = props.id,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$keyboard = props.keyboard,\n    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,\n    autoFocus = props.autoFocus,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    _props$min = props.min,\n    min = _props$min === void 0 ? 0 : _props$min,\n    _props$max = props.max,\n    max = _props$max === void 0 ? 100 : _props$max,\n    _props$step = props.step,\n    step = _props$step === void 0 ? 1 : _props$step,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    range = props.range,\n    count = props.count,\n    onChange = props.onChange,\n    onBeforeChange = props.onBeforeChange,\n    onAfterChange = props.onAfterChange,\n    onChangeComplete = props.onChangeComplete,\n    _props$allowCross = props.allowCross,\n    allowCross = _props$allowCross === void 0 ? true : _props$allowCross,\n    _props$pushable = props.pushable,\n    pushable = _props$pushable === void 0 ? false : _props$pushable,\n    reverse = props.reverse,\n    vertical = props.vertical,\n    _props$included = props.included,\n    included = _props$included === void 0 ? true : _props$included,\n    startPoint = props.startPoint,\n    trackStyle = props.trackStyle,\n    handleStyle = props.handleStyle,\n    railStyle = props.railStyle,\n    dotStyle = props.dotStyle,\n    activeDotStyle = props.activeDotStyle,\n    marks = props.marks,\n    dots = props.dots,\n    handleRender = props.handleRender,\n    activeHandleRender = props.activeHandleRender,\n    track = props.track,\n    _props$tabIndex = props.tabIndex,\n    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,\n    ariaLabelForHandle = props.ariaLabelForHandle,\n    ariaLabelledByForHandle = props.ariaLabelledByForHandle,\n    ariaRequired = props.ariaRequired,\n    ariaValueTextFormatterForHandle = props.ariaValueTextFormatterForHandle;\n  var handlesRef = React.useRef(null);\n  var containerRef = React.useRef(null);\n  var direction = React.useMemo(function () {\n    if (vertical) {\n      return reverse ? 'ttb' : 'btt';\n    }\n    return reverse ? 'rtl' : 'ltr';\n  }, [reverse, vertical]);\n\n  // ============================ Range =============================\n  var _useRange = useRange(range),\n    _useRange2 = _slicedToArray(_useRange, 5),\n    rangeEnabled = _useRange2[0],\n    rangeEditable = _useRange2[1],\n    rangeDraggableTrack = _useRange2[2],\n    minCount = _useRange2[3],\n    maxCount = _useRange2[4];\n  var mergedMin = React.useMemo(function () {\n    return isFinite(min) ? min : 0;\n  }, [min]);\n  var mergedMax = React.useMemo(function () {\n    return isFinite(max) ? max : 100;\n  }, [max]);\n\n  // ============================= Step =============================\n  var mergedStep = React.useMemo(function () {\n    return step !== null && step <= 0 ? 1 : step;\n  }, [step]);\n\n  // ============================= Push =============================\n  var mergedPush = React.useMemo(function () {\n    if (typeof pushable === 'boolean') {\n      return pushable ? mergedStep : false;\n    }\n    return pushable >= 0 ? pushable : false;\n  }, [pushable, mergedStep]);\n\n  // ============================ Marks =============================\n  var markList = React.useMemo(function () {\n    return Object.keys(marks || {}).map(function (key) {\n      var mark = marks[key];\n      var markObj = {\n        value: Number(key)\n      };\n      if (mark && _typeof(mark) === 'object' && ! /*#__PURE__*/React.isValidElement(mark) && ('label' in mark || 'style' in mark)) {\n        markObj.style = mark.style;\n        markObj.label = mark.label;\n      } else {\n        markObj.label = mark;\n      }\n      return markObj;\n    }).filter(function (_ref) {\n      var label = _ref.label;\n      return label || typeof label === 'number';\n    }).sort(function (a, b) {\n      return a.value - b.value;\n    });\n  }, [marks]);\n\n  // ============================ Format ============================\n  var _useOffset = useOffset(mergedMin, mergedMax, mergedStep, markList, allowCross, mergedPush),\n    _useOffset2 = _slicedToArray(_useOffset, 2),\n    formatValue = _useOffset2[0],\n    offsetValues = _useOffset2[1];\n\n  // ============================ Values ============================\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedValue = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var rawValues = React.useMemo(function () {\n    var valueList = mergedValue === null || mergedValue === undefined ? [] : Array.isArray(mergedValue) ? mergedValue : [mergedValue];\n    var _valueList = _slicedToArray(valueList, 1),\n      _valueList$ = _valueList[0],\n      val0 = _valueList$ === void 0 ? mergedMin : _valueList$;\n    var returnValues = mergedValue === null ? [] : [val0];\n\n    // Format as range\n    if (rangeEnabled) {\n      returnValues = _toConsumableArray(valueList);\n\n      // When count provided or value is `undefined`, we fill values\n      if (count || mergedValue === undefined) {\n        var pointCount = count >= 0 ? count + 1 : 2;\n        returnValues = returnValues.slice(0, pointCount);\n\n        // Fill with count\n        while (returnValues.length < pointCount) {\n          var _returnValues;\n          returnValues.push((_returnValues = returnValues[returnValues.length - 1]) !== null && _returnValues !== void 0 ? _returnValues : mergedMin);\n        }\n      }\n      returnValues.sort(function (a, b) {\n        return a - b;\n      });\n    }\n\n    // Align in range\n    returnValues.forEach(function (val, index) {\n      returnValues[index] = formatValue(val);\n    });\n    return returnValues;\n  }, [mergedValue, rangeEnabled, mergedMin, count, formatValue]);\n\n  // =========================== onChange ===========================\n  var getTriggerValue = function getTriggerValue(triggerValues) {\n    return rangeEnabled ? triggerValues : triggerValues[0];\n  };\n  var triggerChange = useEvent(function (nextValues) {\n    // Order first\n    var cloneNextValues = _toConsumableArray(nextValues).sort(function (a, b) {\n      return a - b;\n    });\n\n    // Trigger event if needed\n    if (onChange && !isEqual(cloneNextValues, rawValues, true)) {\n      onChange(getTriggerValue(cloneNextValues));\n    }\n\n    // We set this later since it will re-render component immediately\n    setValue(cloneNextValues);\n  });\n  var finishChange = useEvent(function (draggingDelete) {\n    // Trigger from `useDrag` will tell if it's a delete action\n    if (draggingDelete) {\n      handlesRef.current.hideHelp();\n    }\n    var finishValue = getTriggerValue(rawValues);\n    onAfterChange === null || onAfterChange === void 0 || onAfterChange(finishValue);\n    warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n    onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(finishValue);\n  });\n  var onDelete = function onDelete(index) {\n    if (disabled || !rangeEditable || rawValues.length <= minCount) {\n      return;\n    }\n    var cloneNextValues = _toConsumableArray(rawValues);\n    cloneNextValues.splice(index, 1);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(cloneNextValues));\n    triggerChange(cloneNextValues);\n    var nextFocusIndex = Math.max(0, index - 1);\n    handlesRef.current.hideHelp();\n    handlesRef.current.focus(nextFocusIndex);\n  };\n  var _useDrag = useDrag(containerRef, direction, rawValues, mergedMin, mergedMax, formatValue, triggerChange, finishChange, offsetValues, rangeEditable, minCount),\n    _useDrag2 = _slicedToArray(_useDrag, 5),\n    draggingIndex = _useDrag2[0],\n    draggingValue = _useDrag2[1],\n    draggingDelete = _useDrag2[2],\n    cacheValues = _useDrag2[3],\n    onStartDrag = _useDrag2[4];\n\n  /**\n   * When `rangeEditable` will insert a new value in the values array.\n   * Else it will replace the value in the values array.\n   */\n  var changeToCloseValue = function changeToCloseValue(newValue, e) {\n    if (!disabled) {\n      // Create new values\n      var cloneNextValues = _toConsumableArray(rawValues);\n      var valueIndex = 0;\n      var valueBeforeIndex = 0; // Record the index which value < newValue\n      var valueDist = mergedMax - mergedMin;\n      rawValues.forEach(function (val, index) {\n        var dist = Math.abs(newValue - val);\n        if (dist <= valueDist) {\n          valueDist = dist;\n          valueIndex = index;\n        }\n        if (val < newValue) {\n          valueBeforeIndex = index;\n        }\n      });\n      var focusIndex = valueIndex;\n      if (rangeEditable && valueDist !== 0 && (!maxCount || rawValues.length < maxCount)) {\n        cloneNextValues.splice(valueBeforeIndex + 1, 0, newValue);\n        focusIndex = valueBeforeIndex + 1;\n      } else {\n        cloneNextValues[valueIndex] = newValue;\n      }\n\n      // Fill value to match default 2 (only when `rawValues` is empty)\n      if (rangeEnabled && !rawValues.length && count === undefined) {\n        cloneNextValues.push(newValue);\n      }\n      var nextValue = getTriggerValue(cloneNextValues);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(nextValue);\n      triggerChange(cloneNextValues);\n      if (e) {\n        var _document$activeEleme, _document$activeEleme2;\n        (_document$activeEleme = document.activeElement) === null || _document$activeEleme === void 0 || (_document$activeEleme2 = _document$activeEleme.blur) === null || _document$activeEleme2 === void 0 || _document$activeEleme2.call(_document$activeEleme);\n        handlesRef.current.focus(focusIndex);\n        onStartDrag(e, focusIndex, cloneNextValues);\n      } else {\n        // https://github.com/ant-design/ant-design/issues/49997\n        onAfterChange === null || onAfterChange === void 0 || onAfterChange(nextValue);\n        warning(!onAfterChange, '[rc-slider] `onAfterChange` is deprecated. Please use `onChangeComplete` instead.');\n        onChangeComplete === null || onChangeComplete === void 0 || onChangeComplete(nextValue);\n      }\n    }\n  };\n\n  // ============================ Click =============================\n  var onSliderMouseDown = function onSliderMouseDown(e) {\n    e.preventDefault();\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      width = _containerRef$current.width,\n      height = _containerRef$current.height,\n      left = _containerRef$current.left,\n      top = _containerRef$current.top,\n      bottom = _containerRef$current.bottom,\n      right = _containerRef$current.right;\n    var clientX = e.clientX,\n      clientY = e.clientY;\n    var percent;\n    switch (direction) {\n      case 'btt':\n        percent = (bottom - clientY) / height;\n        break;\n      case 'ttb':\n        percent = (clientY - top) / height;\n        break;\n      case 'rtl':\n        percent = (right - clientX) / width;\n        break;\n      default:\n        percent = (clientX - left) / width;\n    }\n    var nextValue = mergedMin + percent * (mergedMax - mergedMin);\n    changeToCloseValue(formatValue(nextValue), e);\n  };\n\n  // =========================== Keyboard ===========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    keyboardValue = _React$useState2[0],\n    setKeyboardValue = _React$useState2[1];\n  var onHandleOffsetChange = function onHandleOffsetChange(offset, valueIndex) {\n    if (!disabled) {\n      var next = offsetValues(rawValues, offset, valueIndex);\n      onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n      triggerChange(next.values);\n      setKeyboardValue(next.value);\n    }\n  };\n  React.useEffect(function () {\n    if (keyboardValue !== null) {\n      var valueIndex = rawValues.indexOf(keyboardValue);\n      if (valueIndex >= 0) {\n        handlesRef.current.focus(valueIndex);\n      }\n    }\n    setKeyboardValue(null);\n  }, [keyboardValue]);\n\n  // ============================= Drag =============================\n  var mergedDraggableTrack = React.useMemo(function () {\n    if (rangeDraggableTrack && mergedStep === null) {\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, '`draggableTrack` is not supported when `step` is `null`.');\n      }\n      return false;\n    }\n    return rangeDraggableTrack;\n  }, [rangeDraggableTrack, mergedStep]);\n  var onStartMove = useEvent(function (e, valueIndex) {\n    onStartDrag(e, valueIndex);\n    onBeforeChange === null || onBeforeChange === void 0 || onBeforeChange(getTriggerValue(rawValues));\n  });\n\n  // Auto focus for updated handle\n  var dragging = draggingIndex !== -1;\n  React.useEffect(function () {\n    if (!dragging) {\n      var valueIndex = rawValues.lastIndexOf(draggingValue);\n      handlesRef.current.focus(valueIndex);\n    }\n  }, [dragging]);\n\n  // =========================== Included ===========================\n  var sortedCacheValues = React.useMemo(function () {\n    return _toConsumableArray(cacheValues).sort(function (a, b) {\n      return a - b;\n    });\n  }, [cacheValues]);\n\n  // Provide a range values with included [min, max]\n  // Used for Track, Mark & Dot\n  var _React$useMemo = React.useMemo(function () {\n      if (!rangeEnabled) {\n        return [mergedMin, sortedCacheValues[0]];\n      }\n      return [sortedCacheValues[0], sortedCacheValues[sortedCacheValues.length - 1]];\n    }, [sortedCacheValues, rangeEnabled, mergedMin]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    includedStart = _React$useMemo2[0],\n    includedEnd = _React$useMemo2[1];\n\n  // ============================= Refs =============================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus() {\n        handlesRef.current.focus(0);\n      },\n      blur: function blur() {\n        var _containerRef$current2;\n        var _document = document,\n          activeElement = _document.activeElement;\n        if ((_containerRef$current2 = containerRef.current) !== null && _containerRef$current2 !== void 0 && _containerRef$current2.contains(activeElement)) {\n          activeElement === null || activeElement === void 0 || activeElement.blur();\n        }\n      }\n    };\n  });\n\n  // ========================== Auto Focus ==========================\n  React.useEffect(function () {\n    if (autoFocus) {\n      handlesRef.current.focus(0);\n    }\n  }, []);\n\n  // =========================== Context ============================\n  var context = React.useMemo(function () {\n    return {\n      min: mergedMin,\n      max: mergedMax,\n      direction: direction,\n      disabled: disabled,\n      keyboard: keyboard,\n      step: mergedStep,\n      included: included,\n      includedStart: includedStart,\n      includedEnd: includedEnd,\n      range: rangeEnabled,\n      tabIndex: tabIndex,\n      ariaLabelForHandle: ariaLabelForHandle,\n      ariaLabelledByForHandle: ariaLabelledByForHandle,\n      ariaRequired: ariaRequired,\n      ariaValueTextFormatterForHandle: ariaValueTextFormatterForHandle,\n      styles: styles || {},\n      classNames: classNames || {}\n    };\n  }, [mergedMin, mergedMax, direction, disabled, keyboard, mergedStep, included, includedStart, includedEnd, rangeEnabled, tabIndex, ariaLabelForHandle, ariaLabelledByForHandle, ariaRequired, ariaValueTextFormatterForHandle, styles, classNames]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: containerRef,\n    className: cls(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-vertical\"), vertical), \"\".concat(prefixCls, \"-horizontal\"), !vertical), \"\".concat(prefixCls, \"-with-marks\"), markList.length)),\n    style: style,\n    onMouseDown: onSliderMouseDown,\n    id: id\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls(\"\".concat(prefixCls, \"-rail\"), classNames === null || classNames === void 0 ? void 0 : classNames.rail),\n    style: _objectSpread(_objectSpread({}, railStyle), styles === null || styles === void 0 ? void 0 : styles.rail)\n  }), track !== false && /*#__PURE__*/React.createElement(Tracks, {\n    prefixCls: prefixCls,\n    style: trackStyle,\n    values: rawValues,\n    startPoint: startPoint,\n    onStartMove: mergedDraggableTrack ? onStartMove : undefined\n  }), /*#__PURE__*/React.createElement(Steps, {\n    prefixCls: prefixCls,\n    marks: markList,\n    dots: dots,\n    style: dotStyle,\n    activeStyle: activeDotStyle\n  }), /*#__PURE__*/React.createElement(Handles, {\n    ref: handlesRef,\n    prefixCls: prefixCls,\n    style: handleStyle,\n    values: cacheValues,\n    draggingIndex: draggingIndex,\n    draggingDelete: draggingDelete,\n    onStartMove: onStartMove,\n    onOffsetChange: onHandleOffsetChange,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    handleRender: handleRender,\n    activeHandleRender: activeHandleRender,\n    onChangeComplete: finishChange,\n    onDelete: rangeEditable ? onDelete : undefined\n  }), /*#__PURE__*/React.createElement(Marks, {\n    prefixCls: prefixCls,\n    marks: markList,\n    onClick: changeToCloseValue\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Slider.displayName = 'Slider';\n}\nexport default Slider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}