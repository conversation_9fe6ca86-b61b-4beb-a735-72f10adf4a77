{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport WindowsFilledSvg from \"@ant-design/icons-svg/es/asn/WindowsFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar WindowsFilled = function WindowsFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: WindowsFilledSvg\n  }));\n};\n\n/**![windows](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUyMy44IDE5MS40djI4OC45aDM4MlYxMjguMXptMCA2NDIuMmwzODIgNjIuMnYtMzUyaC0zODJ6TTEyMC4xIDQ4MC4ySDQ0M1YyMDEuOWwtMzIyLjkgNTMuNXptMCAyOTAuNEw0NDMgODIzLjJWNTQzLjhIMTIwLjF6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(WindowsFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'WindowsFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}