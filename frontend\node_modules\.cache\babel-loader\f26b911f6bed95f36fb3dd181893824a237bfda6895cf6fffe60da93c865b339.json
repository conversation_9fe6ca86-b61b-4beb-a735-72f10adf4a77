{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport warning from '../_util/warning';\nconst defaultReactRender = (node, container) => {\n  // TODO: Remove in v6\n  // Warning for React 19\n  if (process.env.NODE_ENV !== 'production') {\n    const majorVersion = parseInt(React.version.split('.')[0], 10);\n    const fullKeys = Object.keys(ReactDOM);\n    process.env.NODE_ENV !== \"production\" ? warning(majorVersion < 19 || fullKeys.includes('createRoot'), 'compatible', 'antd v5 support React is 16 ~ 18. see https://u.ant.design/v5-for-19 for compatible.') : void 0;\n  }\n  render(node, container);\n  return () => {\n    return unmount(container);\n  };\n};\nlet unstableRender = defaultReactRender;\n/**\n * @deprecated Set React render function for compatible usage.\n * This is internal usage only compatible with React 19.\n * And will be removed in next major version.\n */\nexport function unstableSetRender(render) {\n  if (render) {\n    unstableRender = render;\n  }\n  return unstableRender;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}