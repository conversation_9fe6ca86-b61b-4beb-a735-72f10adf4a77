{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ProjectFilledSvg from \"@ant-design/icons-svg/es/asn/ProjectFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ProjectFilled = function ProjectFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ProjectFilledSvg\n  }));\n};\n\n/**![project](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNMzY4IDc0NGMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2NDY0em0xOTItMjgwYzAgNC40LTMuNiA4LTggOGgtODBjLTQuNCAwLTgtMy42LTgtOFYyODBjMC00LjQgMy42LTggOC04aDgwYzQuNCAwIDggMy42IDggOHYxODR6bTE5MiA3MmMwIDQuNC0zLjYgOC04IDhoLTgwYy00LjQgMC04LTMuNi04LThWMjgwYzAtNC40IDMuNi04IDgtOGg4MGM0LjQgMCA4IDMuNiA4IDh2MjU2eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ProjectFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ProjectFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}