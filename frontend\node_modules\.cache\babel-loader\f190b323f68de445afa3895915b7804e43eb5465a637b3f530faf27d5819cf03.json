{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\nimport { revert } from '../revert.js';\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase();\n  const definition = state.definitionById.get(id);\n  if (!definition) {\n    return revert(state, node);\n  }\n\n  /** @type {Properties} */\n  const properties = {\n    href: normalizeUri(definition.url || '')\n  };\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}