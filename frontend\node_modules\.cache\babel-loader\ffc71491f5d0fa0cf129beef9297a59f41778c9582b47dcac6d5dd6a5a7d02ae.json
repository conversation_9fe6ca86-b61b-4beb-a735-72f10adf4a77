{"ast": null, "code": "/**\n * @import {Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @typedef {import('micromark-util-types').Options} Options\n */\n\nimport { compile } from './lib/compile.js';\nimport { parse } from './lib/parse.js';\nimport { postprocess } from './lib/postprocess.js';\nimport { preprocess } from './lib/preprocess.js';\nexport { compile } from './lib/compile.js';\nexport { parse } from './lib/parse.js';\nexport { postprocess } from './lib/postprocess.js';\nexport { preprocess } from './lib/preprocess.js';\n\n/**\n * Compile markdown to HTML.\n *\n * > Note: which encodings are supported depends on the engine.\n * > For info on Node.js, see:\n * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n *\n * @overload\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Encoding | null | undefined} encoding\n *   Character encoding to understand `value` as when it’s a `Uint8Array`\n *   (`string`, default: `'utf8'`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n *\n * @overload\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n *\n * @param {Value} value\n *   Markdown to parse (`string` or `Uint8Array`).\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding to understand `value` as when it’s a `Uint8Array`\n *   (`string`, default: `'utf8'`).\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {string}\n *   Compiled HTML.\n */\nexport function micromark(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding;\n    encoding = undefined;\n  }\n  return compile(options)(postprocess(parse(options).document().write(preprocess()(value, encoding, true))));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}