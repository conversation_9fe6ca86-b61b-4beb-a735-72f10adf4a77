{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst SizeContext = /*#__PURE__*/React.createContext(undefined);\nexport const SizeContextProvider = ({\n  children,\n  size\n}) => {\n  const originSize = React.useContext(SizeContext);\n  return /*#__PURE__*/React.createElement(SizeContext.Provider, {\n    value: size || originSize\n  }, children);\n};\nexport default SizeContext;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}