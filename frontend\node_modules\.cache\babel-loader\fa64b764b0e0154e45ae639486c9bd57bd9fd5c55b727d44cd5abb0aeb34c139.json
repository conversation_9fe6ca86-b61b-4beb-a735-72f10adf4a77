{"ast": null, "code": "import * as React from 'react';\nimport { getStyleStr } from './utils';\n/**\n * Base size of the canvas, 1 for parallel layout and 2 for alternate layout\n * Only alternate layout is currently supported\n */\nexport const BaseSize = 2;\nexport const FontGap = 3;\n// Prevent external hidden elements from adding accent styles\nconst emphasizedStyle = {\n  visibility: 'visible !important'\n};\nexport default function useWatermark(markStyle) {\n  const watermarkMap = React.useRef(new Map());\n  const appendWatermark = (base64Url, markWidth, container) => {\n    if (container) {\n      if (!watermarkMap.current.get(container)) {\n        const newWatermarkEle = document.createElement('div');\n        watermarkMap.current.set(container, newWatermarkEle);\n      }\n      const watermarkEle = watermarkMap.current.get(container);\n      watermarkEle.setAttribute('style', getStyleStr(Object.assign(Object.assign(Object.assign({}, markStyle), {\n        backgroundImage: `url('${base64Url}')`,\n        backgroundSize: `${Math.floor(markWidth)}px`\n      }), emphasizedStyle)));\n      // Prevents using the browser `Hide Element` to hide watermarks\n      watermarkEle.removeAttribute('class');\n      watermarkEle.removeAttribute('hidden');\n      if (watermarkEle.parentElement !== container) {\n        container.append(watermarkEle);\n      }\n    }\n    return watermarkMap.current.get(container);\n  };\n  const removeWatermark = container => {\n    const watermarkEle = watermarkMap.current.get(container);\n    if (watermarkEle && container) {\n      container.removeChild(watermarkEle);\n    }\n    watermarkMap.current.delete(container);\n  };\n  const isWatermarkEle = ele => Array.from(watermarkMap.current.values()).includes(ele);\n  return [appendWatermark, removeWatermark, isWatermarkEle];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}