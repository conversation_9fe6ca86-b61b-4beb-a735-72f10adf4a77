{"ast": null, "code": "function compactItemVerticalBorder(token, parentCls) {\n  return {\n    // border collapse\n    [`&-item:not(${parentCls}-last-item)`]: {\n      marginBottom: token.calc(token.lineWidth).mul(-1).equal()\n    },\n    '&-item': {\n      '&:hover,&:focus,&:active': {\n        zIndex: 2\n      },\n      '&[disabled]': {\n        zIndex: 0\n      }\n    }\n  };\n}\nfunction compactItemBorderVerticalRadius(prefixCls, parentCls) {\n  return {\n    [`&-item:not(${parentCls}-first-item):not(${parentCls}-last-item)`]: {\n      borderRadius: 0\n    },\n    [`&-item${parentCls}-first-item:not(${parentCls}-last-item)`]: {\n      [`&, &${prefixCls}-sm, &${prefixCls}-lg`]: {\n        borderEndEndRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`&-item${parentCls}-last-item:not(${parentCls}-first-item)`]: {\n      [`&, &${prefixCls}-sm, &${prefixCls}-lg`]: {\n        borderStartStartRadius: 0,\n        borderStartEndRadius: 0\n      }\n    }\n  };\n}\nexport function genCompactItemVerticalStyle(token) {\n  const compactCls = `${token.componentCls}-compact-vertical`;\n  return {\n    [compactCls]: Object.assign(Object.assign({}, compactItemVerticalBorder(token, compactCls)), compactItemBorderVerticalRadius(token.componentCls, compactCls))\n  };\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}