{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { createTheme, StyleContext as CssInJsStyleContext } from '@ant-design/cssinjs';\nimport IconContext from \"@ant-design/icons/es/components/Context\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport { merge } from \"rc-util/es/utils/set\";\nimport warning, { devUseWarning, WarningContext } from '../_util/warning';\nimport ValidateMessagesContext from '../form/validateMessagesContext';\nimport LocaleProvider, { ANT_MARK } from '../locale';\nimport LocaleContext from '../locale/context';\nimport defaultLocale from '../locale/en_US';\nimport { defaultTheme, DesignTokenContext } from '../theme/context';\nimport defaultSeedToken from '../theme/themes/seed';\nimport { ConfigConsumer, ConfigContext, defaultIconPrefixCls, defaultPrefixCls, Variants } from './context';\nimport { registerTheme } from './cssVariables';\nimport { DisabledContextProvider } from './DisabledContext';\nimport useConfig from './hooks/useConfig';\nimport useTheme from './hooks/useTheme';\nimport MotionWrapper from './MotionWrapper';\nimport PropWarning from './PropWarning';\nimport SizeContext, { SizeContextProvider } from './SizeContext';\nimport useStyle from './style';\nexport { Variants };\n/**\n * Since too many feedback using static method like `Modal.confirm` not getting theme, we record the\n * theme register info here to help developer get warning info.\n */\nlet existThemeConfig = false;\nexport const warnContext = process.env.NODE_ENV !== 'production' ? componentName => {\n  process.env.NODE_ENV !== \"production\" ? warning(!existThemeConfig, componentName, `Static function can not consume context like dynamic theme. Please use 'App' component instead.`) : void 0;\n} : /* istanbul ignore next */\nnull;\nexport { ConfigConsumer, ConfigContext, defaultPrefixCls, defaultIconPrefixCls };\nexport const configConsumerProps = ['getTargetContainer', 'getPopupContainer', 'rootPrefixCls', 'getPrefixCls', 'renderEmpty', 'csp', 'autoInsertSpaceInButton', 'locale'];\n// These props is used by `useContext` directly in sub component\nconst PASSED_PROPS = ['getTargetContainer', 'getPopupContainer', 'renderEmpty', 'input', 'pagination', 'form', 'select', 'button'];\nlet globalPrefixCls;\nlet globalIconPrefixCls;\nlet globalTheme;\nlet globalHolderRender;\nfunction getGlobalPrefixCls() {\n  return globalPrefixCls || defaultPrefixCls;\n}\nfunction getGlobalIconPrefixCls() {\n  return globalIconPrefixCls || defaultIconPrefixCls;\n}\nfunction isLegacyTheme(theme) {\n  return Object.keys(theme).some(key => key.endsWith('Color'));\n}\nconst setGlobalConfig = props => {\n  const {\n    prefixCls,\n    iconPrefixCls,\n    theme,\n    holderRender\n  } = props;\n  if (prefixCls !== undefined) {\n    globalPrefixCls = prefixCls;\n  }\n  if (iconPrefixCls !== undefined) {\n    globalIconPrefixCls = iconPrefixCls;\n  }\n  if ('holderRender' in props) {\n    globalHolderRender = holderRender;\n  }\n  if (theme) {\n    if (isLegacyTheme(theme)) {\n      process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', '`config` of css variable theme is not work in v5. Please use new `theme` config instead.') : void 0;\n      registerTheme(getGlobalPrefixCls(), theme);\n    } else {\n      globalTheme = theme;\n    }\n  }\n};\nexport const globalConfig = () => ({\n  getPrefixCls: (suffixCls, customizePrefixCls) => {\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    return suffixCls ? `${getGlobalPrefixCls()}-${suffixCls}` : getGlobalPrefixCls();\n  },\n  getIconPrefixCls: getGlobalIconPrefixCls,\n  getRootPrefixCls: () => {\n    // If Global prefixCls provided, use this\n    if (globalPrefixCls) {\n      return globalPrefixCls;\n    }\n    // Fallback to default prefixCls\n    return getGlobalPrefixCls();\n  },\n  getTheme: () => globalTheme,\n  holderRender: globalHolderRender\n});\nconst ProviderChildren = props => {\n  const {\n    children,\n    csp: customCsp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    form,\n    locale,\n    componentSize,\n    direction,\n    space,\n    splitter,\n    virtual,\n    dropdownMatchSelectWidth,\n    popupMatchSelectWidth,\n    popupOverflow,\n    legacyLocale,\n    parentContext,\n    iconPrefixCls: customIconPrefixCls,\n    theme,\n    componentDisabled,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    input,\n    textArea,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  } = props;\n  // =================================== Context ===================================\n  const getPrefixCls = React.useCallback((suffixCls, customizePrefixCls) => {\n    const {\n      prefixCls\n    } = props;\n    if (customizePrefixCls) {\n      return customizePrefixCls;\n    }\n    const mergedPrefixCls = prefixCls || parentContext.getPrefixCls('');\n    return suffixCls ? `${mergedPrefixCls}-${suffixCls}` : mergedPrefixCls;\n  }, [parentContext.getPrefixCls, props.prefixCls]);\n  const iconPrefixCls = customIconPrefixCls || parentContext.iconPrefixCls || defaultIconPrefixCls;\n  const csp = customCsp || parentContext.csp;\n  useStyle(iconPrefixCls, csp);\n  const mergedTheme = useTheme(theme, parentContext.theme, {\n    prefixCls: getPrefixCls('')\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    existThemeConfig = existThemeConfig || !!mergedTheme;\n  }\n  const baseConfig = {\n    csp,\n    autoInsertSpaceInButton,\n    alert,\n    anchor,\n    locale: locale || legacyLocale,\n    direction,\n    space,\n    splitter,\n    virtual,\n    popupMatchSelectWidth: popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth,\n    popupOverflow,\n    getPrefixCls,\n    iconPrefixCls,\n    theme: mergedTheme,\n    segmented,\n    statistic,\n    spin,\n    calendar,\n    carousel,\n    cascader,\n    collapse,\n    typography,\n    checkbox,\n    descriptions,\n    divider,\n    drawer,\n    skeleton,\n    steps,\n    image,\n    input,\n    textArea,\n    layout,\n    list,\n    mentions,\n    modal,\n    progress,\n    result,\n    slider,\n    breadcrumb,\n    menu,\n    pagination,\n    empty,\n    badge,\n    radio,\n    rate,\n    switch: SWITCH,\n    transfer,\n    avatar,\n    message,\n    tag,\n    table,\n    card,\n    tabs,\n    timeline,\n    timePicker,\n    upload,\n    notification,\n    tree,\n    colorPicker,\n    datePicker,\n    rangePicker,\n    flex,\n    wave,\n    dropdown,\n    warning: warningConfig,\n    tour,\n    tooltip,\n    popover,\n    popconfirm,\n    floatButtonGroup,\n    variant,\n    inputNumber,\n    treeSelect\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    const warningFn = devUseWarning('ConfigProvider');\n    warningFn(!('autoInsertSpaceInButton' in props), 'deprecated', '`autoInsertSpaceInButton` is deprecated. Please use `{ button: { autoInsertSpace: boolean }}` instead.');\n  }\n  const config = Object.assign({}, parentContext);\n  Object.keys(baseConfig).forEach(key => {\n    if (baseConfig[key] !== undefined) {\n      config[key] = baseConfig[key];\n    }\n  });\n  // Pass the props used by `useContext` directly with child component.\n  // These props should merged into `config`.\n  PASSED_PROPS.forEach(propName => {\n    const propValue = props[propName];\n    if (propValue) {\n      config[propName] = propValue;\n    }\n  });\n  if (typeof autoInsertSpaceInButton !== 'undefined') {\n    // merge deprecated api\n    config.button = Object.assign({\n      autoInsertSpace: autoInsertSpaceInButton\n    }, config.button);\n  }\n  // https://github.com/ant-design/ant-design/issues/27617\n  const memoedConfig = useMemo(() => config, config, (prevConfig, currentConfig) => {\n    const prevKeys = Object.keys(prevConfig);\n    const currentKeys = Object.keys(currentConfig);\n    return prevKeys.length !== currentKeys.length || prevKeys.some(key => prevConfig[key] !== currentConfig[key]);\n  });\n  const {\n    layer\n  } = React.useContext(CssInJsStyleContext);\n  const memoIconContextValue = React.useMemo(() => ({\n    prefixCls: iconPrefixCls,\n    csp,\n    layer: layer ? 'antd' : undefined\n  }), [iconPrefixCls, csp, layer]);\n  let childNode = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(PropWarning, {\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }), children);\n  const validateMessages = React.useMemo(() => {\n    var _a, _b, _c, _d;\n    return merge(((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.defaultValidateMessages) || {}, ((_c = (_b = memoedConfig.locale) === null || _b === void 0 ? void 0 : _b.Form) === null || _c === void 0 ? void 0 : _c.defaultValidateMessages) || {}, ((_d = memoedConfig.form) === null || _d === void 0 ? void 0 : _d.validateMessages) || {}, (form === null || form === void 0 ? void 0 : form.validateMessages) || {});\n  }, [memoedConfig, form === null || form === void 0 ? void 0 : form.validateMessages]);\n  if (Object.keys(validateMessages).length > 0) {\n    childNode = /*#__PURE__*/React.createElement(ValidateMessagesContext.Provider, {\n      value: validateMessages\n    }, childNode);\n  }\n  if (locale) {\n    childNode = /*#__PURE__*/React.createElement(LocaleProvider, {\n      locale: locale,\n      _ANT_MARK__: ANT_MARK\n    }, childNode);\n  }\n  if (iconPrefixCls || csp) {\n    childNode = /*#__PURE__*/React.createElement(IconContext.Provider, {\n      value: memoIconContextValue\n    }, childNode);\n  }\n  if (componentSize) {\n    childNode = /*#__PURE__*/React.createElement(SizeContextProvider, {\n      size: componentSize\n    }, childNode);\n  }\n  // =================================== Motion ===================================\n  childNode = /*#__PURE__*/React.createElement(MotionWrapper, null, childNode);\n  // ================================ Dynamic theme ================================\n  const memoTheme = React.useMemo(() => {\n    const _a = mergedTheme || {},\n      {\n        algorithm,\n        token,\n        components,\n        cssVar\n      } = _a,\n      rest = __rest(_a, [\"algorithm\", \"token\", \"components\", \"cssVar\"]);\n    const themeObj = algorithm && (!Array.isArray(algorithm) || algorithm.length > 0) ? createTheme(algorithm) : defaultTheme;\n    const parsedComponents = {};\n    Object.entries(components || {}).forEach(([componentName, componentToken]) => {\n      const parsedToken = Object.assign({}, componentToken);\n      if ('algorithm' in parsedToken) {\n        if (parsedToken.algorithm === true) {\n          parsedToken.theme = themeObj;\n        } else if (Array.isArray(parsedToken.algorithm) || typeof parsedToken.algorithm === 'function') {\n          parsedToken.theme = createTheme(parsedToken.algorithm);\n        }\n        delete parsedToken.algorithm;\n      }\n      parsedComponents[componentName] = parsedToken;\n    });\n    const mergedToken = Object.assign(Object.assign({}, defaultSeedToken), token);\n    return Object.assign(Object.assign({}, rest), {\n      theme: themeObj,\n      token: mergedToken,\n      components: parsedComponents,\n      override: Object.assign({\n        override: mergedToken\n      }, parsedComponents),\n      cssVar: cssVar\n    });\n  }, [mergedTheme]);\n  if (theme) {\n    childNode = /*#__PURE__*/React.createElement(DesignTokenContext.Provider, {\n      value: memoTheme\n    }, childNode);\n  }\n  // ================================== Warning ===================================\n  if (memoedConfig.warning) {\n    childNode = /*#__PURE__*/React.createElement(WarningContext.Provider, {\n      value: memoedConfig.warning\n    }, childNode);\n  }\n  // =================================== Render ===================================\n  if (componentDisabled !== undefined) {\n    childNode = /*#__PURE__*/React.createElement(DisabledContextProvider, {\n      disabled: componentDisabled\n    }, childNode);\n  }\n  return /*#__PURE__*/React.createElement(ConfigContext.Provider, {\n    value: memoedConfig\n  }, childNode);\n};\nconst ConfigProvider = props => {\n  const context = React.useContext(ConfigContext);\n  const antLocale = React.useContext(LocaleContext);\n  return /*#__PURE__*/React.createElement(ProviderChildren, Object.assign({\n    parentContext: context,\n    legacyLocale: antLocale\n  }, props));\n};\nConfigProvider.ConfigContext = ConfigContext;\nConfigProvider.SizeContext = SizeContext;\nConfigProvider.config = setGlobalConfig;\nConfigProvider.useConfig = useConfig;\nObject.defineProperty(ConfigProvider, 'SizeContext', {\n  get: () => {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'ConfigProvider.SizeContext is deprecated. Please use `ConfigProvider.useConfig().componentSize` instead.') : void 0;\n    return SizeContext;\n  }\n});\nif (process.env.NODE_ENV !== 'production') {\n  ConfigProvider.displayName = 'ConfigProvider';\n}\nexport default ConfigProvider;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}