{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { devUseWarning } from '../_util/warning';\nimport StatisticTimer from './Timer';\nconst Countdown = props => {\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Countdown');\n    warning.deprecated(false, '<Statistic.Countdown />', '<Statistic.Timer type=\"countdown\" />');\n  }\n  return /*#__PURE__*/React.createElement(StatisticTimer, Object.assign({}, props, {\n    type: \"countdown\"\n  }));\n};\nexport default /*#__PURE__*/React.memo(Countdown);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}