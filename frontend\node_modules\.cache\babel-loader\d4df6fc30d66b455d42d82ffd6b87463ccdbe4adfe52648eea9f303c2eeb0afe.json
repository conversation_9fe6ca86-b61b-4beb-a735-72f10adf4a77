{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSummaryStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    tableBorderColor,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${token.lineType} ${tableBorderColor}`;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-summary`]: {\n        position: 'relative',\n        zIndex: token.zIndexTableFixed,\n        background: token.tableBg,\n        '> tr': {\n          '> th, > td': {\n            borderBottom: tableBorder\n          }\n        }\n      },\n      [`div${componentCls}-summary`]: {\n        boxShadow: `0 ${unit(calc(lineWidth).mul(-1).equal())} 0 ${tableBorderColor}`\n      }\n    }\n  };\n};\nexport default genSummaryStyle;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}