{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nexport default function useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return useMemo(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || _objectSpread({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}