{"ast": null, "code": "import { presetPrimaryColors } from '@ant-design/colors';\nexport function validProgress(progress) {\n  if (!progress || progress < 0) {\n    return 0;\n  }\n  if (progress > 100) {\n    return 100;\n  }\n  return progress;\n}\nexport function getSuccessPercent({\n  success,\n  successPercent\n}) {\n  let percent = successPercent;\n  /** @deprecated Use `percent` instead */\n  if (success && 'progress' in success) {\n    percent = success.progress;\n  }\n  if (success && 'percent' in success) {\n    percent = success.percent;\n  }\n  return percent;\n}\nexport const getPercentage = ({\n  percent,\n  success,\n  successPercent\n}) => {\n  const realSuccessPercent = validProgress(getSuccessPercent({\n    success,\n    successPercent\n  }));\n  return [realSuccessPercent, validProgress(validProgress(percent) - realSuccessPercent)];\n};\nexport const getStrokeColor = ({\n  success = {},\n  strokeColor\n}) => {\n  const {\n    strokeColor: successColor\n  } = success;\n  return [successColor || presetPrimaryColors.green, strokeColor || null];\n};\nexport const getSize = (size, type, extra) => {\n  var _a, _b, _c, _d;\n  let width = -1;\n  let height = -1;\n  if (type === 'step') {\n    const steps = extra.steps;\n    const strokeWidth = extra.strokeWidth;\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      width = size === 'small' ? 2 : 14;\n      height = strokeWidth !== null && strokeWidth !== void 0 ? strokeWidth : 8;\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else {\n      [width = 14, height = 8] = Array.isArray(size) ? size : [size.width, size.height];\n    }\n    width *= steps;\n  } else if (type === 'line') {\n    const strokeWidth = extra === null || extra === void 0 ? void 0 : extra.strokeWidth;\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      height = strokeWidth || (size === 'small' ? 6 : 8);\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else {\n      [width = -1, height = 8] = Array.isArray(size) ? size : [size.width, size.height];\n    }\n  } else if (type === 'circle' || type === 'dashboard') {\n    if (typeof size === 'string' || typeof size === 'undefined') {\n      [width, height] = size === 'small' ? [60, 60] : [120, 120];\n    } else if (typeof size === 'number') {\n      [width, height] = [size, size];\n    } else if (Array.isArray(size)) {\n      width = (_b = (_a = size[0]) !== null && _a !== void 0 ? _a : size[1]) !== null && _b !== void 0 ? _b : 120;\n      height = (_d = (_c = size[0]) !== null && _c !== void 0 ? _c : size[1]) !== null && _d !== void 0 ? _d : 120;\n    }\n  }\n  return [width, height];\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}