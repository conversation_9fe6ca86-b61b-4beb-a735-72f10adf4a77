{"ast": null, "code": "import { resetComponent } from '../../style';\nimport { initZoomMotion } from '../../style/motion';\nimport getArrowStyle, { getArrowOffsetToken } from '../../style/placementArrow';\nimport { getArrowToken } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken, PresetColors } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    popoverColor,\n    titleMinWidth,\n    fontWeightStrong,\n    innerPadding,\n    boxShadowSecondary,\n    colorTextHeading,\n    borderRadiusLG,\n    zIndexPopup,\n    titleMarginBottom,\n    colorBgElevated,\n    popoverBg,\n    titleBorderBottom,\n    innerContentPadding,\n    titlePadding\n  } = token;\n  return [{\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: 0,\n      // use `left` to fix https://github.com/ant-design/ant-design/issues/39195\n      left: {\n        _skip_check_: true,\n        value: 0\n      },\n      zIndex: zIndexPopup,\n      fontWeight: 'normal',\n      whiteSpace: 'normal',\n      textAlign: 'start',\n      cursor: 'auto',\n      userSelect: 'text',\n      // When use `autoArrow`, origin will follow the arrow position\n      '--valid-offset-x': 'var(--arrow-offset-horizontal, var(--arrow-x))',\n      transformOrigin: [`var(--valid-offset-x, 50%)`, `var(--arrow-y, 50%)`].join(' '),\n      '--antd-arrow-background-color': colorBgElevated,\n      width: 'max-content',\n      maxWidth: '100vw',\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        position: 'relative'\n      },\n      [`${componentCls}-inner`]: {\n        backgroundColor: popoverBg,\n        backgroundClip: 'padding-box',\n        borderRadius: borderRadiusLG,\n        boxShadow: boxShadowSecondary,\n        padding: innerPadding\n      },\n      [`${componentCls}-title`]: {\n        minWidth: titleMinWidth,\n        marginBottom: titleMarginBottom,\n        color: colorTextHeading,\n        fontWeight: fontWeightStrong,\n        borderBottom: titleBorderBottom,\n        padding: titlePadding\n      },\n      [`${componentCls}-inner-content`]: {\n        color: popoverColor,\n        padding: innerContentPadding\n      }\n    })\n  },\n  // Arrow Style\n  getArrowStyle(token, 'var(--antd-arrow-background-color)'),\n  // Pure Render\n  {\n    [`${componentCls}-pure`]: {\n      position: 'relative',\n      maxWidth: 'none',\n      margin: token.sizePopupArrow,\n      display: 'inline-block',\n      [`${componentCls}-content`]: {\n        display: 'inline-block'\n      }\n    }\n  }];\n};\nconst genColorStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: PresetColors.map(colorKey => {\n      const lightColor = token[`${colorKey}6`];\n      return {\n        [`&${componentCls}-${colorKey}`]: {\n          '--antd-arrow-background-color': lightColor,\n          [`${componentCls}-inner`]: {\n            backgroundColor: lightColor\n          },\n          [`${componentCls}-arrow`]: {\n            background: 'transparent'\n          }\n        }\n      };\n    })\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    lineWidth,\n    controlHeight,\n    fontHeight,\n    padding,\n    wireframe,\n    zIndexPopupBase,\n    borderRadiusLG,\n    marginXS,\n    lineType,\n    colorSplit,\n    paddingSM\n  } = token;\n  const titlePaddingBlockDist = controlHeight - fontHeight;\n  const popoverTitlePaddingBlockTop = titlePaddingBlockDist / 2;\n  const popoverTitlePaddingBlockBottom = titlePaddingBlockDist / 2 - lineWidth;\n  const popoverPaddingHorizontal = padding;\n  return Object.assign(Object.assign(Object.assign({\n    titleMinWidth: 177,\n    zIndexPopup: zIndexPopupBase + 30\n  }, getArrowToken(token)), getArrowOffsetToken({\n    contentRadius: borderRadiusLG,\n    limitVerticalRadius: true\n  })), {\n    // internal\n    innerPadding: wireframe ? 0 : 12,\n    titleMarginBottom: wireframe ? 0 : marginXS,\n    titlePadding: wireframe ? `${popoverTitlePaddingBlockTop}px ${popoverPaddingHorizontal}px ${popoverTitlePaddingBlockBottom}px` : 0,\n    titleBorderBottom: wireframe ? `${lineWidth}px ${lineType} ${colorSplit}` : 'none',\n    innerContentPadding: wireframe ? `${paddingSM}px ${popoverPaddingHorizontal}px` : 0\n  });\n};\nexport default genStyleHooks('Popover', token => {\n  const {\n    colorBgElevated,\n    colorText\n  } = token;\n  const popoverToken = mergeToken(token, {\n    popoverBg: colorBgElevated,\n    popoverColor: colorText\n  });\n  return [genBaseStyle(popoverToken), genColorStyle(popoverToken), initZoomMotion(popoverToken, 'zoom-big')];\n}, prepareComponentToken, {\n  resetStyle: false,\n  deprecatedTokens: [['width', 'titleMinWidth'], ['minWidth', 'titleMinWidth']]\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}