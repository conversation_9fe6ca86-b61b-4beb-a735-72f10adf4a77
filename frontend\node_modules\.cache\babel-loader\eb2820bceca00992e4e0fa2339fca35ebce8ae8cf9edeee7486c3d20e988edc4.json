{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderViewOutlinedSvg from \"@ant-design/icons-svg/es/asn/FolderViewOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderViewOutlined = function FolderViewOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderViewOutlinedSvg\n  }));\n};\n\n/**![folder-view](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMDkuMSA1NTQuM2E0Mi45MiA0Mi45MiAwIDAwMCAzNi40QzM1My4zIDY4NCA0MjEuNiA3MzIgNTEyLjUgNzMyczE1OS4yLTQ4LjEgMjAzLjQtMTQxLjNjNS40LTExLjUgNS40LTI0LjguMS0zNi4zbC0uMS0uMS0uMS0uMUM2NzEuNyA0NjEgNjAzLjQgNDEzIDUxMi41IDQxM3MtMTU5LjIgNDguMS0yMDMuNCAxNDEuM3pNNTEyLjUgNDc3YzYyLjEgMCAxMDcuNCAzMCAxNDEuMSA5NS41QzYyMCA2MzggNTc0LjYgNjY4IDUxMi41IDY2OHMtMTA3LjQtMzAtMTQxLjEtOTUuNWMzMy43LTY1LjUgNzktOTUuNSAxNDEuMS05NS41eiIgLz48cGF0aCBkPSJNNDU3IDU3M2E1NiA1NiAwIDEwMTEyIDAgNTYgNTYgMCAxMC0xMTIgMHoiIC8+PHBhdGggZD0iTTg4MCAyOTguNEg1MjFMNDAzLjcgMTg2LjJhOC4xNSA4LjE1IDAgMDAtNS41LTIuMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTg0MCA3NjhIMTg0VjI1NmgxODguNWwxMTkuNiAxMTQuNEg4NDBWNzY4eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderViewOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderViewOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}