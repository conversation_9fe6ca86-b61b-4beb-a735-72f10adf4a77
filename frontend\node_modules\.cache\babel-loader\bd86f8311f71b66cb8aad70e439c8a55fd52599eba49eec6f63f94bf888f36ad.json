{"ast": null, "code": "// Proxy the dom ref with `{ nativeElement, otherFn }` type\n// ref: https://github.com/ant-design/ant-design/discussions/45242\nimport { useImperativeHandle } from 'react';\nfunction fillProxy(element, handler) {\n  element._antProxy = element._antProxy || {};\n  Object.keys(handler).forEach(key => {\n    if (!(key in element._antProxy)) {\n      const ori = element[key];\n      element._antProxy[key] = ori;\n      element[key] = handler[key];\n    }\n  });\n  return element;\n}\nexport default function useProxyImperativeHandle(ref, init) {\n  return useImperativeHandle(ref, () => {\n    const refObj = init();\n    const {\n      nativeElement\n    } = refObj;\n    if (typeof Proxy !== 'undefined') {\n      return new Proxy(nativeElement, {\n        get(obj, prop) {\n          if (refObj[prop]) {\n            return refObj[prop];\n          }\n          return Reflect.get(obj, prop);\n        }\n      });\n    }\n    // Fallback of IE\n    return fillProxy(nativeElement, refObj);\n  });\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}