{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nexport function getArrowToken(token) {\n  const {\n    sizePopupArrow,\n    borderRadiusXS,\n    borderRadiusOuter\n  } = token;\n  const unitWidth = sizePopupArrow / 2;\n  const ax = 0;\n  const ay = unitWidth;\n  const bx = borderRadiusOuter * 1 / Math.sqrt(2);\n  const by = unitWidth - borderRadiusOuter * (1 - 1 / Math.sqrt(2));\n  const cx = unitWidth - borderRadiusXS * (1 / Math.sqrt(2));\n  const cy = borderRadiusOuter * (Math.sqrt(2) - 1) + borderRadiusXS * (1 / Math.sqrt(2));\n  const dx = 2 * unitWidth - cx;\n  const dy = cy;\n  const ex = 2 * unitWidth - bx;\n  const ey = by;\n  const fx = 2 * unitWidth - ax;\n  const fy = ay;\n  const shadowWidth = unitWidth * Math.sqrt(2) + borderRadiusOuter * (Math.sqrt(2) - 2);\n  const polygonOffset = borderRadiusOuter * (Math.sqrt(2) - 1);\n  const arrowPolygon = `polygon(${polygonOffset}px 100%, 50% ${polygonOffset}px, ${2 * unitWidth - polygonOffset}px 100%, ${polygonOffset}px 100%)`;\n  const arrowPath = `path('M ${ax} ${ay} A ${borderRadiusOuter} ${borderRadiusOuter} 0 0 0 ${bx} ${by} L ${cx} ${cy} A ${borderRadiusXS} ${borderRadiusXS} 0 0 1 ${dx} ${dy} L ${ex} ${ey} A ${borderRadiusOuter} ${borderRadiusOuter} 0 0 0 ${fx} ${fy} Z')`;\n  return {\n    arrowShadowWidth: shadowWidth,\n    arrowPath,\n    arrowPolygon\n  };\n}\nexport const genRoundedArrow = (token, bgColor, boxShadow) => {\n  const {\n    sizePopupArrow,\n    arrowPolygon,\n    arrowPath,\n    arrowShadowWidth,\n    borderRadiusXS,\n    calc\n  } = token;\n  return {\n    pointerEvents: 'none',\n    width: sizePopupArrow,\n    height: sizePopupArrow,\n    overflow: 'hidden',\n    '&::before': {\n      position: 'absolute',\n      bottom: 0,\n      insetInlineStart: 0,\n      width: sizePopupArrow,\n      height: calc(sizePopupArrow).div(2).equal(),\n      background: bgColor,\n      clipPath: {\n        _multi_value_: true,\n        value: [arrowPolygon, arrowPath]\n      },\n      content: '\"\"'\n    },\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      width: arrowShadowWidth,\n      height: arrowShadowWidth,\n      bottom: 0,\n      insetInline: 0,\n      margin: 'auto',\n      borderRadius: {\n        _skip_check_: true,\n        value: `0 0 ${unit(borderRadiusXS)} 0`\n      },\n      transform: 'translateY(50%) rotate(-135deg)',\n      boxShadow,\n      zIndex: 0,\n      background: 'transparent'\n    }\n  };\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}