{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileZipFilledSvg from \"@ant-design/icons-svg/es/asn/FileZipFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileZipFilled = function FileZipFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileZipFilledSvg\n  }));\n};\n\n/**![file-zip](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC43YzYgNiA5LjQgMTQuMSA5LjQgMjIuNlY5MjhjMCAxNy43LTE0LjMgMzItMzIgMzJIMTkyYy0xNy43IDAtMzItMTQuMy0zMi0zMlY5NmMwLTE3LjcgMTQuMy0zMiAzMi0zMmg0MjQuN2M4LjUgMCAxNi43IDMuNCAyMi43IDkuNGwyMTUuMiAyMTUuM3pNNzkwLjIgMzI2TDYwMiAxMzcuOFYzMjZoMTg4LjJ6TTI5NiAxMzZ2NjRoNjR2LTY0aC02NHptNjQgNjR2NjRoNjR2LTY0aC02NHptLTY0IDY0djY0aDY0di02NGgtNjR6bTY0IDY0djY0aDY0di02NGgtNjR6bS02NCA2NHY2NGg2NHYtNjRoLTY0em02NCA2NHY2NGg2NHYtNjRoLTY0em0tNjQgNjR2NjRoNjR2LTY0aC02NHptMCA2NHYxNjBoMTI4VjU4NEgyOTZ6bTQ4IDQ4aDMydjY0aC0zMnYtNjR6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileZipFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileZipFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}