{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Pagination from '../pagination';\nimport ListItem from './ListItem';\nexport const OmitProps = ['handleFilter', 'handleClear', 'checkedKeys'];\nconst parsePagination = pagination => {\n  const defaultPagination = {\n    simple: true,\n    showSizeChanger: false,\n    showLessItems: false\n  };\n  return Object.assign(Object.assign({}, defaultPagination), pagination);\n};\nconst TransferListBody = (props, ref) => {\n  const {\n    prefixCls,\n    filteredRenderItems,\n    selectedKeys,\n    disabled: globalDisabled,\n    showRemove,\n    pagination,\n    onScroll,\n    onItemSelect,\n    onItemRemove\n  } = props;\n  const [current, setCurrent] = React.useState(1);\n  const mergedPagination = React.useMemo(() => {\n    if (!pagination) {\n      return null;\n    }\n    const convertPagination = typeof pagination === 'object' ? pagination : {};\n    return parsePagination(convertPagination);\n  }, [pagination]);\n  const [pageSize, setPageSize] = useMergedState(10, {\n    value: mergedPagination === null || mergedPagination === void 0 ? void 0 : mergedPagination.pageSize\n  });\n  React.useEffect(() => {\n    if (mergedPagination) {\n      const maxPageCount = Math.ceil(filteredRenderItems.length / pageSize);\n      setCurrent(Math.min(current, maxPageCount));\n    }\n  }, [filteredRenderItems, mergedPagination, pageSize]);\n  const onInternalClick = (item, e) => {\n    onItemSelect(item.key, !selectedKeys.includes(item.key), e);\n  };\n  const onRemove = item => {\n    onItemRemove === null || onItemRemove === void 0 ? void 0 : onItemRemove([item.key]);\n  };\n  const onPageChange = cur => {\n    setCurrent(cur);\n  };\n  const onSizeChange = (cur, size) => {\n    setCurrent(cur);\n    setPageSize(size);\n  };\n  const memoizedItems = React.useMemo(() => {\n    const displayItems = mergedPagination ? filteredRenderItems.slice((current - 1) * pageSize, current * pageSize) : filteredRenderItems;\n    return displayItems;\n  }, [current, filteredRenderItems, mergedPagination, pageSize]);\n  React.useImperativeHandle(ref, () => ({\n    items: memoizedItems\n  }));\n  const paginationNode = mergedPagination ? (/*#__PURE__*/React.createElement(Pagination, {\n    size: \"small\",\n    disabled: globalDisabled,\n    simple: mergedPagination.simple,\n    pageSize: pageSize,\n    showLessItems: mergedPagination.showLessItems,\n    showSizeChanger: mergedPagination.showSizeChanger,\n    className: `${prefixCls}-pagination`,\n    total: filteredRenderItems.length,\n    current: current,\n    onChange: onPageChange,\n    onShowSizeChange: onSizeChange\n  })) : null;\n  const cls = classNames(`${prefixCls}-content`, {\n    [`${prefixCls}-content-show-remove`]: showRemove\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"ul\", {\n    className: cls,\n    onScroll: onScroll\n  }, (memoizedItems || []).map(({\n    renderedEl,\n    renderedText,\n    item\n  }) => (/*#__PURE__*/React.createElement(ListItem, {\n    key: item.key,\n    item: item,\n    renderedText: renderedText,\n    renderedEl: renderedEl,\n    prefixCls: prefixCls,\n    showRemove: showRemove,\n    onClick: onInternalClick,\n    onRemove: onRemove,\n    checked: selectedKeys.includes(item.key),\n    disabled: globalDisabled || item.disabled\n  })))), paginationNode);\n};\nif (process.env.NODE_ENV !== 'production') {\n  TransferListBody.displayName = 'TransferListBody';\n}\nexport default /*#__PURE__*/React.forwardRef(TransferListBody);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}