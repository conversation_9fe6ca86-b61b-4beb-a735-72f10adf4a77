{"ast": null, "code": "import * as React from 'react';\nexport default function usePickerRef(ref) {\n  var selectorRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current;\n    return {\n      nativeElement: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.nativeElement,\n      focus: function focus(options) {\n        var _selectorRef$current2;\n        (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 || _selectorRef$current2.focus(options);\n      },\n      blur: function blur() {\n        var _selectorRef$current3;\n        (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 || _selectorRef$current3.blur();\n      }\n    };\n  });\n  return selectorRef;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}