{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport ExclamationCircleFilled from \"@ant-design/icons/es/icons/ExclamationCircleFilled\";\nimport classNames from 'classnames';\nimport ActionButton from '../_util/ActionButton';\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/buttonHelpers';\nimport { ConfigContext } from '../config-provider';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nimport PopoverPurePanel from '../popover/PurePanel';\nimport useStyle from './style';\nexport const Overlay = props => {\n  const {\n    prefixCls,\n    okButtonProps,\n    cancelButtonProps,\n    title,\n    description,\n    cancelText,\n    okText,\n    okType = 'primary',\n    icon = /*#__PURE__*/React.createElement(ExclamationCircleFilled, null),\n    showCancel = true,\n    close,\n    onConfirm,\n    onCancel,\n    onPopupClick\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const [contextLocale] = useLocale('Popconfirm', defaultLocale.Popconfirm);\n  const titleNode = getRenderPropValue(title);\n  const descriptionNode = getRenderPropValue(description);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-inner-content`,\n    onClick: onPopupClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-message`\n  }, icon && /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-message-icon`\n  }, icon), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-message-text`\n  }, titleNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, titleNode), descriptionNode && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-description`\n  }, descriptionNode))), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-buttons`\n  }, showCancel && (/*#__PURE__*/React.createElement(Button, Object.assign({\n    onClick: onCancel,\n    size: \"small\"\n  }, cancelButtonProps), cancelText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.cancelText))), /*#__PURE__*/React.createElement(ActionButton, {\n    buttonProps: Object.assign(Object.assign({\n      size: 'small'\n    }, convertLegacyProps(okType)), okButtonProps),\n    actionFn: onConfirm,\n    close: close,\n    prefixCls: getPrefixCls('btn'),\n    quitOnNullishReturnValue: true,\n    emitEvent: true\n  }, okText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.okText))));\n};\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      placement,\n      className,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"placement\", \"className\", \"style\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);\n  const [wrapCSSVar] = useStyle(prefixCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(PopoverPurePanel, {\n    placement: placement,\n    className: classNames(prefixCls, className),\n    style: style,\n    content: /*#__PURE__*/React.createElement(Overlay, Object.assign({\n      prefixCls: prefixCls\n    }, restProps))\n  }));\n};\nexport default PurePanel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}