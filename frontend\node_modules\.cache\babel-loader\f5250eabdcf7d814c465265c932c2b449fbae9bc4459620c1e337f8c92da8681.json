{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BulbFilledSvg from \"@ant-design/icons-svg/es/asn/BulbFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BulbFilled = function BulbFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BulbFilledSvg\n  }));\n};\n\n/**![bulb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0OCA2NzYuMUMyNTAgNjE5LjQgMTg0IDUxMy40IDE4NCAzOTJjMC0xODEuMSAxNDYuOS0zMjggMzI4LTMyOHMzMjggMTQ2LjkgMzI4IDMyOGMwIDEyMS40LTY2IDIyNy40LTE2NCAyODQuMVY3OTJjMCAxNy43LTE0LjMgMzItMzIgMzJIMzgwYy0xNy43IDAtMzItMTQuMy0zMi0zMlY2NzYuMXpNMzkyIDg4OGgyNDBjNC40IDAgOCAzLjYgOCA4djMyYzAgMTcuNy0xNC4zIDMyLTMyIDMySDQxNmMtMTcuNyAwLTMyLTE0LjMtMzItMzJ2LTMyYzAtNC40IDMuNi04IDgtOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BulbFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BulbFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}