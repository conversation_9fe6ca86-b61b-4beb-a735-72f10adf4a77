{"ast": null, "code": "import React from 'react';\nexport function getPtg(str) {\n  return Number(str.slice(0, -1)) / 100;\n}\nfunction isPtg(itemSize) {\n  return typeof itemSize === 'string' && itemSize.endsWith('%');\n}\n/**\n * Save the size state.\n * Align the size into flex percentage base.\n */\nexport default function useSizes(items, containerSize) {\n  const propSizes = items.map(item => item.size);\n  const itemsCount = items.length;\n  const mergedContainerSize = containerSize || 0;\n  const ptg2px = ptg => ptg * mergedContainerSize;\n  // We do not need care the size state match the `items` length in `useState`.\n  // It will calculate later.\n  const [innerSizes, setInnerSizes] = React.useState(() => items.map(item => item.defaultSize));\n  const sizes = React.useMemo(() => {\n    var _a;\n    const mergedSizes = [];\n    for (let i = 0; i < itemsCount; i += 1) {\n      mergedSizes[i] = (_a = propSizes[i]) !== null && _a !== void 0 ? _a : innerSizes[i];\n    }\n    return mergedSizes;\n  }, [itemsCount, innerSizes, propSizes]);\n  // Post handle the size. Will do:\n  // 1. Convert all the px into percentage if not empty.\n  // 2. Get rest percentage for exist percentage.\n  // 3. Fill the rest percentage into empty item.\n  const postPercentSizes = React.useMemo(() => {\n    let ptgList = [];\n    let emptyCount = 0;\n    // Fill default percentage\n    for (let i = 0; i < itemsCount; i += 1) {\n      const itemSize = sizes[i];\n      if (isPtg(itemSize)) {\n        ptgList[i] = getPtg(itemSize);\n      } else if (itemSize || itemSize === 0) {\n        const num = Number(itemSize);\n        if (!Number.isNaN(num)) {\n          ptgList[i] = num / mergedContainerSize;\n        }\n      } else {\n        emptyCount += 1;\n        ptgList[i] = undefined;\n      }\n    }\n    const totalPtg = ptgList.reduce((acc, ptg) => acc + (ptg || 0), 0);\n    if (totalPtg > 1 || !emptyCount) {\n      // If total percentage is larger than 1, we will scale it down.\n      const scale = 1 / totalPtg;\n      ptgList = ptgList.map(ptg => ptg === undefined ? 0 : ptg * scale);\n    } else {\n      // If total percentage is smaller than 1, we will fill the rest.\n      const avgRest = (1 - totalPtg) / emptyCount;\n      ptgList = ptgList.map(ptg => ptg === undefined ? avgRest : ptg);\n    }\n    return ptgList;\n  }, [sizes, mergedContainerSize]);\n  const postPxSizes = React.useMemo(() => postPercentSizes.map(ptg2px), [postPercentSizes, mergedContainerSize]);\n  const postPercentMinSizes = React.useMemo(() => items.map(item => {\n    if (isPtg(item.min)) {\n      return getPtg(item.min);\n    }\n    return (item.min || 0) / mergedContainerSize;\n  }), [items, mergedContainerSize]);\n  const postPercentMaxSizes = React.useMemo(() => items.map(item => {\n    if (isPtg(item.max)) {\n      return getPtg(item.max);\n    }\n    return (item.max || mergedContainerSize) / mergedContainerSize;\n  }), [items, mergedContainerSize]);\n  // If ssr, we will use the size from developer config first.\n  const panelSizes = React.useMemo(() => containerSize ? postPxSizes : sizes, [postPxSizes, containerSize]);\n  return [panelSizes, postPxSizes, postPercentSizes, postPercentMinSizes, postPercentMaxSizes, setInnerSizes];\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}