{"ast": null, "code": "export function getTargetRect(target) {\n  return target !== window ? target.getBoundingClientRect() : {\n    top: 0,\n    bottom: window.innerHeight\n  };\n}\nexport function getFixedTop(placeholderRect, targetRect, offsetTop) {\n  if (offsetTop !== undefined && Math.round(targetRect.top) > Math.round(placeholderRect.top) - offsetTop) {\n    return offsetTop + targetRect.top;\n  }\n  return undefined;\n}\nexport function getFixedBottom(placeholderRect, targetRect, offsetBottom) {\n  if (offsetBottom !== undefined && Math.round(targetRect.bottom) < Math.round(placeholderRect.bottom) + offsetBottom) {\n    const targetBottomOffset = window.innerHeight - targetRect.bottom;\n    return offsetBottom + targetBottomOffset;\n  }\n  return undefined;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}