{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BuildTwoToneSvg from \"@ant-design/icons-svg/es/asn/BuildTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BuildTwoTone = function BuildTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BuildTwoToneSvg\n  }));\n};\n\n/**![build](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE0NCA1NDZoMjAwdjIwMEgxNDR6bTI2OC0yNjhoMjAwdjIwMEg0MTJ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05MTYgMjEwSDM3NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjM2SDEwOGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjcyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDU0MGMxNy43IDAgMzItMTQuMyAzMi0zMlY1NDZoMjM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjI0MmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNMzQ0IDc0NkgxNDRWNTQ2aDIwMHYyMDB6bTI2OCAwSDQxMlY1NDZoMjAwdjIwMHptMC0yNjhINDEyVjI3OGgyMDB2MjAwem0yNjggMEg2ODBWMjc4aDIwMHYyMDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BuildTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BuildTwoTone';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}