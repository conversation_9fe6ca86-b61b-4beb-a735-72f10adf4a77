{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport RcTooltip from 'rc-tooltip';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport ContextIsolator from '../_util/ContextIsolator';\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport getPlacements from '../_util/placements';\nimport { cloneElement, isFragment } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport zIndexContext from '../_util/zindexContext';\nimport { useComponentConfig } from '../config-provider/context';\nimport { useToken } from '../theme/internal';\nimport PurePanel from './PurePanel';\nimport useStyle from './style';\nimport { parseColor } from './util';\nconst InternalTooltip = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n      prefixCls: customizePrefixCls,\n      openClassName,\n      getTooltipContainer,\n      color,\n      overlayInnerStyle,\n      children,\n      afterOpenChange,\n      afterVisibleChange,\n      destroyTooltipOnHide,\n      destroyOnHidden,\n      arrow = true,\n      title,\n      overlay,\n      builtinPlacements,\n      arrowPointAtCenter = false,\n      autoAdjustOverflow = true,\n      motion,\n      getPopupContainer,\n      placement = 'top',\n      mouseEnterDelay = 0.1,\n      mouseLeaveDelay = 0.1,\n      overlayStyle,\n      rootClassName,\n      overlayClassName,\n      styles,\n      classNames: tooltipClassNames\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"openClassName\", \"getTooltipContainer\", \"color\", \"overlayInnerStyle\", \"children\", \"afterOpenChange\", \"afterVisibleChange\", \"destroyTooltipOnHide\", \"destroyOnHidden\", \"arrow\", \"title\", \"overlay\", \"builtinPlacements\", \"arrowPointAtCenter\", \"autoAdjustOverflow\", \"motion\", \"getPopupContainer\", \"placement\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"rootClassName\", \"overlayClassName\", \"styles\", \"classNames\"]);\n  const mergedShowArrow = !!arrow;\n  const [, token] = useToken();\n  const {\n    getPopupContainer: getContextPopupContainer,\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('tooltip');\n  // ============================== Ref ===============================\n  const warning = devUseWarning('Tooltip');\n  const tooltipRef = React.useRef(null);\n  const forceAlign = () => {\n    var _a;\n    (_a = tooltipRef.current) === null || _a === void 0 ? void 0 : _a.forceAlign();\n  };\n  React.useImperativeHandle(ref, () => {\n    var _a, _b;\n    return {\n      forceAlign,\n      forcePopupAlign: () => {\n        warning.deprecated(false, 'forcePopupAlign', 'forceAlign');\n        forceAlign();\n      },\n      nativeElement: (_a = tooltipRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement,\n      popupElement: (_b = tooltipRef.current) === null || _b === void 0 ? void 0 : _b.popupElement\n    };\n  });\n  // ============================== Warn ==============================\n  if (process.env.NODE_ENV !== 'production') {\n    [['visible', 'open'], ['defaultVisible', 'defaultOpen'], ['onVisibleChange', 'onOpenChange'], ['afterVisibleChange', 'afterOpenChange'], ['destroyTooltipOnHide', 'destroyOnHidden'], ['arrowPointAtCenter', 'arrow={{ pointAtCenter: true }}'], ['overlayStyle', 'styles={{ root: {} }}'], ['overlayInnerStyle', 'styles={{ body: {} }}'], ['overlayClassName', 'classNames={{ root: \"\" }}']].forEach(([deprecatedName, newName]) => {\n      warning.deprecated(!(deprecatedName in props), deprecatedName, newName);\n    });\n    process.env.NODE_ENV !== \"production\" ? warning(!destroyTooltipOnHide || typeof destroyTooltipOnHide === 'boolean', 'usage', '`destroyTooltipOnHide` no need config `keepParent` anymore. Please use `boolean` value directly.') : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(!arrow || typeof arrow === 'boolean' || !('arrowPointAtCenter' in arrow), 'deprecated', '`arrowPointAtCenter` in `arrow` is deprecated. Please use `pointAtCenter` instead.') : void 0;\n  }\n  // ============================== Open ==============================\n  const [open, setOpen] = useMergedState(false, {\n    value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,\n    defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible\n  });\n  const noTitle = !title && !overlay && title !== 0; // overlay for old version compatibility\n  const onOpenChange = vis => {\n    var _a, _b;\n    setOpen(noTitle ? false : vis);\n    if (!noTitle) {\n      (_a = props.onOpenChange) === null || _a === void 0 ? void 0 : _a.call(props, vis);\n      (_b = props.onVisibleChange) === null || _b === void 0 ? void 0 : _b.call(props, vis);\n    }\n  };\n  const tooltipPlacements = React.useMemo(() => {\n    var _a, _b;\n    let mergedArrowPointAtCenter = arrowPointAtCenter;\n    if (typeof arrow === 'object') {\n      mergedArrowPointAtCenter = (_b = (_a = arrow.pointAtCenter) !== null && _a !== void 0 ? _a : arrow.arrowPointAtCenter) !== null && _b !== void 0 ? _b : arrowPointAtCenter;\n    }\n    return builtinPlacements || getPlacements({\n      arrowPointAtCenter: mergedArrowPointAtCenter,\n      autoAdjustOverflow,\n      arrowWidth: mergedShowArrow ? token.sizePopupArrow : 0,\n      borderRadius: token.borderRadius,\n      offset: token.marginXXS,\n      visibleFirst: true\n    });\n  }, [arrowPointAtCenter, arrow, builtinPlacements, token]);\n  const memoOverlay = React.useMemo(() => {\n    if (title === 0) {\n      return title;\n    }\n    return overlay || title || '';\n  }, [overlay, title]);\n  const memoOverlayWrapper = /*#__PURE__*/React.createElement(ContextIsolator, {\n    space: true\n  }, typeof memoOverlay === 'function' ? memoOverlay() : memoOverlay);\n  const prefixCls = getPrefixCls('tooltip', customizePrefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const injectFromPopover = props['data-popover-inject'];\n  let tempOpen = open;\n  // Hide tooltip when there is no title\n  if (!('open' in props) && !('visible' in props) && noTitle) {\n    tempOpen = false;\n  }\n  // ============================= Render =============================\n  const child = /*#__PURE__*/React.isValidElement(children) && !isFragment(children) ? children : /*#__PURE__*/React.createElement(\"span\", null, children);\n  const childProps = child.props;\n  const childCls = !childProps.className || typeof childProps.className === 'string' ? classNames(childProps.className, openClassName || `${prefixCls}-open`) : childProps.className;\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, !injectFromPopover);\n  // Color\n  const colorInfo = parseColor(prefixCls, color);\n  const arrowContentStyle = colorInfo.arrowStyle;\n  const rootClassNames = classNames(overlayClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, colorInfo.className, rootClassName, hashId, cssVarCls, contextClassName, contextClassNames.root, tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.root);\n  const bodyClassNames = classNames(contextClassNames.body, tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.body);\n  // ============================ zIndex ============================\n  const [zIndex, contextZIndex] = useZIndex('Tooltip', restProps.zIndex);\n  const content = /*#__PURE__*/React.createElement(RcTooltip, Object.assign({}, restProps, {\n    zIndex: zIndex,\n    showArrow: mergedShowArrow,\n    placement: placement,\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay,\n    prefixCls: prefixCls,\n    classNames: {\n      root: rootClassNames,\n      body: bodyClassNames\n    },\n    styles: {\n      root: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, arrowContentStyle), contextStyles.root), contextStyle), overlayStyle), styles === null || styles === void 0 ? void 0 : styles.root),\n      body: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.body), overlayInnerStyle), styles === null || styles === void 0 ? void 0 : styles.body), colorInfo.overlayStyle)\n    },\n    getTooltipContainer: getPopupContainer || getTooltipContainer || getContextPopupContainer,\n    ref: tooltipRef,\n    builtinPlacements: tooltipPlacements,\n    overlay: memoOverlayWrapper,\n    visible: tempOpen,\n    onVisibleChange: onOpenChange,\n    afterVisibleChange: afterOpenChange !== null && afterOpenChange !== void 0 ? afterOpenChange : afterVisibleChange,\n    arrowContent: /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-arrow-content`\n    }),\n    motion: {\n      motionName: getTransitionName(rootPrefixCls, 'zoom-big-fast', props.transitionName),\n      motionDeadline: 1000\n    },\n    // TODO: In the future, destroyTooltipOnHide in rc-tooltip needs to be upgrade to destroyOnHidden\n    destroyTooltipOnHide: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : !!destroyTooltipOnHide\n  }), tempOpen ? cloneElement(child, {\n    className: childCls\n  }) : child);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(zIndexContext.Provider, {\n    value: contextZIndex\n  }, content));\n});\nconst Tooltip = InternalTooltip;\nif (process.env.NODE_ENV !== 'production') {\n  Tooltip.displayName = 'Tooltip';\n}\nTooltip._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default Tooltip;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}