{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport SlickCarousel from '@ant-design/react-slick';\nimport classNames from 'classnames';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle, { DotDuration } from './style';\nconst dotsClass = 'slick-dots';\nconst ArrowButton = _a => {\n  var {\n      currentSlide,\n      slideCount\n    } = _a,\n    rest = __rest(_a, [\"currentSlide\", \"slideCount\"]);\n  return /*#__PURE__*/React.createElement(\"button\", Object.assign({\n    type: \"button\"\n  }, rest));\n};\nconst Carousel = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      dots = true,\n      arrows = false,\n      prevArrow = /*#__PURE__*/React.createElement(ArrowButton, {\n        \"aria-label\": \"prev\"\n      }),\n      nextArrow = /*#__PURE__*/React.createElement(ArrowButton, {\n        \"aria-label\": \"next\"\n      }),\n      draggable = false,\n      waitForAnimate = false,\n      dotPosition = 'bottom',\n      vertical = dotPosition === 'left' || dotPosition === 'right',\n      rootClassName,\n      className: customClassName,\n      style,\n      id,\n      autoplay = false,\n      autoplaySpeed = 3000\n    } = props,\n    otherProps = __rest(props, [\"dots\", \"arrows\", \"prevArrow\", \"nextArrow\", \"draggable\", \"waitForAnimate\", \"dotPosition\", \"vertical\", \"rootClassName\", \"className\", \"style\", \"id\", \"autoplay\", \"autoplaySpeed\"]);\n  const {\n    getPrefixCls,\n    direction,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('carousel');\n  const slickRef = React.useRef(null);\n  const goTo = (slide, dontAnimate = false) => {\n    slickRef.current.slickGoTo(slide, dontAnimate);\n  };\n  React.useImperativeHandle(ref, () => ({\n    goTo,\n    autoPlay: slickRef.current.innerSlider.autoPlay,\n    innerSlider: slickRef.current.innerSlider,\n    prev: slickRef.current.slickPrev,\n    next: slickRef.current.slickNext\n  }), [slickRef.current]);\n  const prevCount = React.useRef(React.Children.count(props.children));\n  React.useEffect(() => {\n    if (prevCount.current !== React.Children.count(props.children)) {\n      goTo(props.initialSlide || 0, false);\n      prevCount.current = React.Children.count(props.children);\n    }\n  }, [props.children]);\n  const newProps = Object.assign({\n    vertical,\n    className: classNames(customClassName, contextClassName),\n    style: Object.assign(Object.assign({}, contextStyle), style),\n    autoplay: !!autoplay\n  }, otherProps);\n  if (newProps.effect === 'fade') {\n    newProps.fade = true;\n  }\n  const prefixCls = getPrefixCls('carousel', newProps.prefixCls);\n  const enableDots = !!dots;\n  const dsClass = classNames(dotsClass, `${dotsClass}-${dotPosition}`, typeof dots === 'boolean' ? false : dots === null || dots === void 0 ? void 0 : dots.className);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const className = classNames(prefixCls, {\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-vertical`]: newProps.vertical\n  }, hashId, cssVarCls, rootClassName);\n  const mergedShowDuration = autoplay && (typeof autoplay === 'object' ? autoplay.dotDuration : false);\n  const dotDurationStyle = mergedShowDuration ? {\n    [DotDuration]: `${autoplaySpeed}ms`\n  } : {};\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: className,\n    id: id,\n    style: dotDurationStyle\n  }, /*#__PURE__*/React.createElement(SlickCarousel, Object.assign({\n    ref: slickRef\n  }, newProps, {\n    dots: enableDots,\n    dotsClass: dsClass,\n    arrows: arrows,\n    prevArrow: prevArrow,\n    nextArrow: nextArrow,\n    draggable: draggable,\n    verticalSwiping: vertical,\n    autoplaySpeed: autoplaySpeed,\n    waitForAnimate: waitForAnimate\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Carousel.displayName = 'Carousel';\n}\nexport default Carousel;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}