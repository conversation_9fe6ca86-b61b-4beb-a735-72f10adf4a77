{"ast": null, "code": "import { generate } from '@ant-design/colors';\nimport { FastColor } from '@ant-design/fast-color';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport warning from '../_util/warning';\nconst dynamicStyleMark = `-ant-${Date.now()}-${Math.random()}`;\nexport function getStyle(globalPrefixCls, theme) {\n  const variables = {};\n  const formatColor = (color, updater) => {\n    let clone = color.clone();\n    clone = (updater === null || updater === void 0 ? void 0 : updater(clone)) || clone;\n    return clone.toRgbString();\n  };\n  const fillColor = (colorVal, type) => {\n    const baseColor = new FastColor(colorVal);\n    const colorPalettes = generate(baseColor.toRgbString());\n    variables[`${type}-color`] = formatColor(baseColor);\n    variables[`${type}-color-disabled`] = colorPalettes[1];\n    variables[`${type}-color-hover`] = colorPalettes[4];\n    variables[`${type}-color-active`] = colorPalettes[6];\n    variables[`${type}-color-outline`] = baseColor.clone().setA(0.2).toRgbString();\n    variables[`${type}-color-deprecated-bg`] = colorPalettes[0];\n    variables[`${type}-color-deprecated-border`] = colorPalettes[2];\n  };\n  // ================ Primary Color ================\n  if (theme.primaryColor) {\n    fillColor(theme.primaryColor, 'primary');\n    const primaryColor = new FastColor(theme.primaryColor);\n    const primaryColors = generate(primaryColor.toRgbString());\n    // Legacy - We should use semantic naming standard\n    primaryColors.forEach((color, index) => {\n      variables[`primary-${index + 1}`] = color;\n    });\n    // Deprecated\n    variables['primary-color-deprecated-l-35'] = formatColor(primaryColor, c => c.lighten(35));\n    variables['primary-color-deprecated-l-20'] = formatColor(primaryColor, c => c.lighten(20));\n    variables['primary-color-deprecated-t-20'] = formatColor(primaryColor, c => c.tint(20));\n    variables['primary-color-deprecated-t-50'] = formatColor(primaryColor, c => c.tint(50));\n    variables['primary-color-deprecated-f-12'] = formatColor(primaryColor, c => c.setA(c.a * 0.12));\n    const primaryActiveColor = new FastColor(primaryColors[0]);\n    variables['primary-color-active-deprecated-f-30'] = formatColor(primaryActiveColor, c => c.setA(c.a * 0.3));\n    variables['primary-color-active-deprecated-d-02'] = formatColor(primaryActiveColor, c => c.darken(2));\n  }\n  // ================ Success Color ================\n  if (theme.successColor) {\n    fillColor(theme.successColor, 'success');\n  }\n  // ================ Warning Color ================\n  if (theme.warningColor) {\n    fillColor(theme.warningColor, 'warning');\n  }\n  // ================= Error Color =================\n  if (theme.errorColor) {\n    fillColor(theme.errorColor, 'error');\n  }\n  // ================= Info Color ==================\n  if (theme.infoColor) {\n    fillColor(theme.infoColor, 'info');\n  }\n  // Convert to css variables\n  const cssList = Object.keys(variables).map(key => `--${globalPrefixCls}-${key}: ${variables[key]};`);\n  return `\n  :root {\n    ${cssList.join('\\n')}\n  }\n  `.trim();\n}\nexport function registerTheme(globalPrefixCls, theme) {\n  const style = getStyle(globalPrefixCls, theme);\n  if (canUseDom()) {\n    updateCSS(style, `${dynamicStyleMark}-dynamic-theme`);\n  } else {\n    process.env.NODE_ENV !== \"production\" ? warning(false, 'ConfigProvider', 'SSR do not support dynamic theme with css variables.') : void 0;\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}