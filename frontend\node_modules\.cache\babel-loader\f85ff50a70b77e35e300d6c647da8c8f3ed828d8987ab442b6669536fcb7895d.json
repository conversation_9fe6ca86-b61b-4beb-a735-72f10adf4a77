{"ast": null, "code": "import { trimNumber, num2str } from '@rc-component/mini-decimal';\nexport function getDecupleSteps(step) {\n  var stepStr = typeof step === 'number' ? num2str(step) : trimNumber(step).fullStr;\n  var hasPoint = stepStr.includes('.');\n  if (!hasPoint) {\n    return step + '0';\n  }\n  return trimNumber(stepStr.replace(/(\\d)\\.(\\d)/g, '$1$2.')).fullStr;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}