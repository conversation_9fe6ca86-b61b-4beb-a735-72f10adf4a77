{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\nimport { revert } from '../revert.js';\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase();\n  const definition = state.definitionById.get(id);\n  if (!definition) {\n    return revert(state, node);\n  }\n\n  /** @type {Properties} */\n  const properties = {\n    src: normalizeUri(definition.url || ''),\n    alt: node.alt\n  };\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'img',\n    properties,\n    children: []\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}