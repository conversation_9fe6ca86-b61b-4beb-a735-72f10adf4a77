{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"className\", \"children\", \"direction\", \"type\", \"labelPlacement\", \"iconPrefix\", \"status\", \"size\", \"current\", \"progressDot\", \"stepIcon\", \"initial\", \"icons\", \"onChange\", \"itemRender\", \"items\"];\n/* eslint react/no-did-mount-set-state: 0, react/prop-types: 0 */\nimport classNames from 'classnames';\nimport React from 'react';\nimport Step from \"./Step\";\nfunction Steps(props) {\n  var _classNames;\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-steps' : _props$prefixCls,\n    _props$style = props.style,\n    style = _props$style === void 0 ? {} : _props$style,\n    className = props.className,\n    children = props.children,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'horizontal' : _props$direction,\n    _props$type = props.type,\n    type = _props$type === void 0 ? 'default' : _props$type,\n    _props$labelPlacement = props.labelPlacement,\n    labelPlacement = _props$labelPlacement === void 0 ? 'horizontal' : _props$labelPlacement,\n    _props$iconPrefix = props.iconPrefix,\n    iconPrefix = _props$iconPrefix === void 0 ? 'rc' : _props$iconPrefix,\n    _props$status = props.status,\n    status = _props$status === void 0 ? 'process' : _props$status,\n    size = props.size,\n    _props$current = props.current,\n    current = _props$current === void 0 ? 0 : _props$current,\n    _props$progressDot = props.progressDot,\n    progressDot = _props$progressDot === void 0 ? false : _props$progressDot,\n    stepIcon = props.stepIcon,\n    _props$initial = props.initial,\n    initial = _props$initial === void 0 ? 0 : _props$initial,\n    icons = props.icons,\n    onChange = props.onChange,\n    itemRender = props.itemRender,\n    _props$items = props.items,\n    items = _props$items === void 0 ? [] : _props$items,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var isNav = type === 'navigation';\n  var isInline = type === 'inline';\n\n  // inline type requires fixed progressDot direction size.\n  var mergedProgressDot = isInline || progressDot;\n  var mergedDirection = isInline ? 'horizontal' : direction;\n  var mergedSize = isInline ? undefined : size;\n  var adjustedLabelPlacement = mergedProgressDot ? 'vertical' : labelPlacement;\n  var classString = classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(mergedDirection), className, (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-\").concat(mergedSize), mergedSize), _defineProperty(_classNames, \"\".concat(prefixCls, \"-label-\").concat(adjustedLabelPlacement), mergedDirection === 'horizontal'), _defineProperty(_classNames, \"\".concat(prefixCls, \"-dot\"), !!mergedProgressDot), _defineProperty(_classNames, \"\".concat(prefixCls, \"-navigation\"), isNav), _defineProperty(_classNames, \"\".concat(prefixCls, \"-inline\"), isInline), _classNames));\n  var onStepClick = function onStepClick(next) {\n    if (onChange && current !== next) {\n      onChange(next);\n    }\n  };\n  var renderStep = function renderStep(item, index) {\n    var mergedItem = _objectSpread({}, item);\n    var stepNumber = initial + index;\n    // fix tail color\n    if (status === 'error' && index === current - 1) {\n      mergedItem.className = \"\".concat(prefixCls, \"-next-error\");\n    }\n    if (!mergedItem.status) {\n      if (stepNumber === current) {\n        mergedItem.status = status;\n      } else if (stepNumber < current) {\n        mergedItem.status = 'finish';\n      } else {\n        mergedItem.status = 'wait';\n      }\n    }\n    if (isInline) {\n      mergedItem.icon = undefined;\n      mergedItem.subTitle = undefined;\n    }\n    if (!mergedItem.render && itemRender) {\n      mergedItem.render = function (stepItem) {\n        return itemRender(mergedItem, stepItem);\n      };\n    }\n    return /*#__PURE__*/React.createElement(Step, _extends({}, mergedItem, {\n      active: stepNumber === current,\n      stepNumber: stepNumber + 1,\n      stepIndex: stepNumber,\n      key: stepNumber,\n      prefixCls: prefixCls,\n      iconPrefix: iconPrefix,\n      wrapperStyle: style,\n      progressDot: mergedProgressDot,\n      stepIcon: stepIcon,\n      icons: icons,\n      onStepClick: onChange && onStepClick\n    }));\n  };\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classString,\n    style: style\n  }, restProps), items.filter(function (item) {\n    return item;\n  }).map(renderStep));\n}\nSteps.Step = Step;\nexport default Steps;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}