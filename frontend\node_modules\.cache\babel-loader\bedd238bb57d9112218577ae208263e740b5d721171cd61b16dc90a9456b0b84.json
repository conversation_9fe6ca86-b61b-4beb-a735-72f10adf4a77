{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport EllipsisOutlined from \"@ant-design/icons/es/icons/EllipsisOutlined\";\nimport classNames from 'classnames';\nimport RcMenu from 'rc-menu';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport omit from \"rc-util/es/omit\";\nimport initCollapseMotion from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport MenuContext from './MenuContext';\nimport Divider from './MenuDivider';\nimport MenuItem from './MenuItem';\nimport OverrideContext from './OverrideContext';\nimport useStyle from './style';\nimport SubMenu from './SubMenu';\nfunction isEmptyIcon(icon) {\n  return icon === null || icon === false;\n}\nconst MENU_COMPONENTS = {\n  item: MenuItem,\n  submenu: SubMenu,\n  divider: Divider\n};\nconst InternalMenu = /*#__PURE__*/forwardRef((props, ref) => {\n  var _a;\n  const override = React.useContext(OverrideContext);\n  const overrideObj = override || {};\n  const {\n    getPrefixCls,\n    getPopupContainer,\n    direction,\n    menu\n  } = React.useContext(ConfigContext);\n  const rootPrefixCls = getPrefixCls();\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      style,\n      theme = 'light',\n      expandIcon,\n      _internalDisableMenuItemTitleTooltip,\n      inlineCollapsed,\n      siderCollapsed,\n      rootClassName,\n      mode,\n      selectable,\n      onClick,\n      overflowedIndicatorPopupClassName\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\", \"style\", \"theme\", \"expandIcon\", \"_internalDisableMenuItemTitleTooltip\", \"inlineCollapsed\", \"siderCollapsed\", \"rootClassName\", \"mode\", \"selectable\", \"onClick\", \"overflowedIndicatorPopupClassName\"]);\n  const passedProps = omit(restProps, ['collapsedWidth']);\n  // ======================== Warning ==========================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Menu');\n    process.env.NODE_ENV !== \"production\" ? warning(!('inlineCollapsed' in props && mode !== 'inline'), 'usage', '`inlineCollapsed` should only be used when `mode` is inline.') : void 0;\n    warning.deprecated('items' in props && !props.children, 'children', 'items');\n  }\n  (_a = overrideObj.validator) === null || _a === void 0 ? void 0 : _a.call(overrideObj, {\n    mode\n  });\n  // ========================== Click ==========================\n  // Tell dropdown that item clicked\n  const onItemClick = useEvent((...args) => {\n    var _a;\n    onClick === null || onClick === void 0 ? void 0 : onClick.apply(void 0, args);\n    (_a = overrideObj.onClick) === null || _a === void 0 ? void 0 : _a.call(overrideObj);\n  });\n  // ========================== Mode ===========================\n  const mergedMode = overrideObj.mode || mode;\n  // ======================= Selectable ========================\n  const mergedSelectable = selectable !== null && selectable !== void 0 ? selectable : overrideObj.selectable;\n  // ======================== Collapsed ========================\n  // Inline Collapsed\n  const mergedInlineCollapsed = inlineCollapsed !== null && inlineCollapsed !== void 0 ? inlineCollapsed : siderCollapsed;\n  const defaultMotions = {\n    horizontal: {\n      motionName: `${rootPrefixCls}-slide-up`\n    },\n    inline: initCollapseMotion(rootPrefixCls),\n    other: {\n      motionName: `${rootPrefixCls}-zoom-big`\n    }\n  };\n  const prefixCls = getPrefixCls('menu', customizePrefixCls || overrideObj.prefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls, !override);\n  const menuClassName = classNames(`${prefixCls}-${theme}`, menu === null || menu === void 0 ? void 0 : menu.className, className);\n  // ====================== ExpandIcon ========================\n  const mergedExpandIcon = React.useMemo(() => {\n    var _a, _b;\n    if (typeof expandIcon === 'function' || isEmptyIcon(expandIcon)) {\n      return expandIcon || null;\n    }\n    if (typeof overrideObj.expandIcon === 'function' || isEmptyIcon(overrideObj.expandIcon)) {\n      return overrideObj.expandIcon || null;\n    }\n    if (typeof (menu === null || menu === void 0 ? void 0 : menu.expandIcon) === 'function' || isEmptyIcon(menu === null || menu === void 0 ? void 0 : menu.expandIcon)) {\n      return (menu === null || menu === void 0 ? void 0 : menu.expandIcon) || null;\n    }\n    const mergedIcon = (_a = expandIcon !== null && expandIcon !== void 0 ? expandIcon : overrideObj === null || overrideObj === void 0 ? void 0 : overrideObj.expandIcon) !== null && _a !== void 0 ? _a : menu === null || menu === void 0 ? void 0 : menu.expandIcon;\n    return cloneElement(mergedIcon, {\n      className: classNames(`${prefixCls}-submenu-expand-icon`, /*#__PURE__*/React.isValidElement(mergedIcon) ? (_b = mergedIcon.props) === null || _b === void 0 ? void 0 : _b.className : undefined)\n    });\n  }, [expandIcon, overrideObj === null || overrideObj === void 0 ? void 0 : overrideObj.expandIcon, menu === null || menu === void 0 ? void 0 : menu.expandIcon, prefixCls]);\n  // ======================== Context ==========================\n  const contextValue = React.useMemo(() => ({\n    prefixCls,\n    inlineCollapsed: mergedInlineCollapsed || false,\n    direction,\n    firstLevel: true,\n    theme,\n    mode: mergedMode,\n    disableMenuItemTitleTooltip: _internalDisableMenuItemTitleTooltip\n  }), [prefixCls, mergedInlineCollapsed, direction, _internalDisableMenuItemTitleTooltip, theme]);\n  // ========================= Render ==========================\n  return wrapCSSVar(/*#__PURE__*/React.createElement(OverrideContext.Provider, {\n    value: null\n  }, /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: contextValue\n  }, /*#__PURE__*/React.createElement(RcMenu, Object.assign({\n    getPopupContainer: getPopupContainer,\n    overflowedIndicator: /*#__PURE__*/React.createElement(EllipsisOutlined, null),\n    overflowedIndicatorPopupClassName: classNames(prefixCls, `${prefixCls}-${theme}`, overflowedIndicatorPopupClassName),\n    mode: mergedMode,\n    selectable: mergedSelectable,\n    onClick: onItemClick\n  }, passedProps, {\n    inlineCollapsed: mergedInlineCollapsed,\n    style: Object.assign(Object.assign({}, menu === null || menu === void 0 ? void 0 : menu.style), style),\n    className: menuClassName,\n    prefixCls: prefixCls,\n    direction: direction,\n    defaultMotions: defaultMotions,\n    expandIcon: mergedExpandIcon,\n    ref: ref,\n    rootClassName: classNames(rootClassName, hashId, overrideObj.rootClassName, cssVarCls, rootCls),\n    _internalComponents: MENU_COMPONENTS\n  })))));\n});\nexport default InternalMenu;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}