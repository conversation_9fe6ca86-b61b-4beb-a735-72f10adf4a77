{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport findDOMNode from \"rc-util/es/Dom/findDOMNode\";\nimport { supportRef, useComposeRef, getNodeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { CollectionContext } from \"../Collection\";\nimport { observe, unobserve } from \"../utils/observerUtil\";\nimport DomWrapper from \"./DomWrapper\";\nfunction SingleObserver(props, ref) {\n  var children = props.children,\n    disabled = props.disabled;\n  var elementRef = React.useRef(null);\n  var wrapperRef = React.useRef(null);\n  var onCollectionResize = React.useContext(CollectionContext);\n\n  // =========================== Children ===========================\n  var isRenderProps = typeof children === 'function';\n  var mergedChildren = isRenderProps ? children(elementRef) : children;\n\n  // ============================= Size =============================\n  var sizeRef = React.useRef({\n    width: -1,\n    height: -1,\n    offsetWidth: -1,\n    offsetHeight: -1\n  });\n\n  // ============================= Ref ==============================\n  var canRef = !isRenderProps && /*#__PURE__*/React.isValidElement(mergedChildren) && supportRef(mergedChildren);\n  var originRef = canRef ? getNodeRef(mergedChildren) : null;\n  var mergedRef = useComposeRef(originRef, elementRef);\n  var getDom = function getDom() {\n    var _elementRef$current;\n    return findDOMNode(elementRef.current) || (\n    // Support `nativeElement` format\n    elementRef.current && _typeof(elementRef.current) === 'object' ? findDOMNode((_elementRef$current = elementRef.current) === null || _elementRef$current === void 0 ? void 0 : _elementRef$current.nativeElement) : null) || findDOMNode(wrapperRef.current);\n  };\n  React.useImperativeHandle(ref, function () {\n    return getDom();\n  });\n\n  // =========================== Observe ============================\n  var propsRef = React.useRef(props);\n  propsRef.current = props;\n\n  // Handler\n  var onInternalResize = React.useCallback(function (target) {\n    var _propsRef$current = propsRef.current,\n      onResize = _propsRef$current.onResize,\n      data = _propsRef$current.data;\n    var _target$getBoundingCl = target.getBoundingClientRect(),\n      width = _target$getBoundingCl.width,\n      height = _target$getBoundingCl.height;\n    var offsetWidth = target.offsetWidth,\n      offsetHeight = target.offsetHeight;\n\n    /**\n     * Resize observer trigger when content size changed.\n     * In most case we just care about element size,\n     * let's use `boundary` instead of `contentRect` here to avoid shaking.\n     */\n    var fixedWidth = Math.floor(width);\n    var fixedHeight = Math.floor(height);\n    if (sizeRef.current.width !== fixedWidth || sizeRef.current.height !== fixedHeight || sizeRef.current.offsetWidth !== offsetWidth || sizeRef.current.offsetHeight !== offsetHeight) {\n      var size = {\n        width: fixedWidth,\n        height: fixedHeight,\n        offsetWidth: offsetWidth,\n        offsetHeight: offsetHeight\n      };\n      sizeRef.current = size;\n\n      // IE is strange, right?\n      var mergedOffsetWidth = offsetWidth === Math.round(width) ? width : offsetWidth;\n      var mergedOffsetHeight = offsetHeight === Math.round(height) ? height : offsetHeight;\n      var sizeInfo = _objectSpread(_objectSpread({}, size), {}, {\n        offsetWidth: mergedOffsetWidth,\n        offsetHeight: mergedOffsetHeight\n      });\n\n      // Let collection know what happened\n      onCollectionResize === null || onCollectionResize === void 0 || onCollectionResize(sizeInfo, target, data);\n      if (onResize) {\n        // defer the callback but not defer to next frame\n        Promise.resolve().then(function () {\n          onResize(sizeInfo, target);\n        });\n      }\n    }\n  }, []);\n\n  // Dynamic observe\n  React.useEffect(function () {\n    var currentElement = getDom();\n    if (currentElement && !disabled) {\n      observe(currentElement, onInternalResize);\n    }\n    return function () {\n      return unobserve(currentElement, onInternalResize);\n    };\n  }, [elementRef.current, disabled]);\n\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(DomWrapper, {\n    ref: wrapperRef\n  }, canRef ? /*#__PURE__*/React.cloneElement(mergedChildren, {\n    ref: mergedRef\n  }) : mergedChildren);\n}\nvar RefSingleObserver = /*#__PURE__*/React.forwardRef(SingleObserver);\nif (process.env.NODE_ENV !== 'production') {\n  RefSingleObserver.displayName = 'SingleObserver';\n}\nexport default RefSingleObserver;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}