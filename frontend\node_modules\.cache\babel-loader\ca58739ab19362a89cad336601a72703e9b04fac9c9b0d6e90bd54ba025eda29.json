{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AppleFilledSvg from \"@ant-design/icons-svg/es/asn/AppleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AppleFilled = function AppleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AppleFilledSvg\n  }));\n};\n\n/**![apple](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc0Ny40IDUzNS43Yy0uNC02OC4yIDMwLjUtMTE5LjYgOTIuOS0xNTcuNS0zNC45LTUwLTg3LjctNzcuNS0xNTcuMy04Mi44LTY1LjktNS4yLTEzOCAzOC40LTE2NC40IDM4LjQtMjcuOSAwLTkxLjctMzYuNi0xNDEuOS0zNi42QzI3My4xIDI5OC44IDE2MyAzNzkuOCAxNjMgNTQ0LjZjMCA0OC43IDguOSA5OSAyNi43IDE1MC44IDIzLjggNjguMiAxMDkuNiAyMzUuMyAxOTkuMSAyMzIuNiA0Ni44LTEuMSA3OS45LTMzLjIgMTQwLjgtMzMuMiA1OS4xIDAgODkuNyAzMy4yIDE0MS45IDMzLjIgOTAuMy0xLjMgMTY3LjktMTUzLjIgMTkwLjUtMjIxLjYtMTIxLjEtNTcuMS0xMTQuNi0xNjcuMi0xMTQuNi0xNzAuN3ptLTEwNS4xLTMwNWM1MC43LTYwLjIgNDYuMS0xMTUgNDQuNi0xMzQuNy00NC44IDIuNi05Ni42IDMwLjUtMTI2LjEgNjQuOC0zMi41IDM2LjgtNTEuNiA4Mi4zLTQ3LjUgMTMzLjYgNDguNCAzLjcgOTIuNi0yMS4yIDEyOS02My43eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AppleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AppleFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}