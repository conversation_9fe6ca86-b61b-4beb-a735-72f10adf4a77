{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport NodeCollapseOutlinedSvg from \"@ant-design/icons-svg/es/asn/NodeCollapseOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar NodeCollapseOutlined = function NodeCollapseOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: NodeCollapseOutlinedSvg\n  }));\n};\n\n/**![node-collapse](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NTIgNjEyYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDI5OGE5NS45MiA5NS45MiAwIDAwLTg5LTYwYy01MyAwLTk2IDQzLTk2IDk2czQzIDk2IDk2IDk2YzQwLjMgMCA3NC44LTI0LjggODktNjBoMTUwLjN2MTUyYzAgNTUuMiA0NC44IDEwMCAxMDAgMTAwSDk1MmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOEg1NDguM2MtMTUuNSAwLTI4LTEyLjUtMjgtMjhWNjEySDk1MnpNNDUxLjcgMzEzLjdsMTcyLjUgMTM2LjJjNi4zIDUuMSAxNS44LjUgMTUuOC03LjdWMzQ0aDI2NGM0LjQgMCA4LTMuNiA4LTh2LTYwYzAtNC40LTMuNi04LTgtOEg2NDB2LTk4LjJjMC04LjEtOS40LTEyLjgtMTUuOC03LjdMNDUxLjcgMjk4LjNhOS45IDkuOSAwIDAwMCAxNS40eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(NodeCollapseOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'NodeCollapseOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}