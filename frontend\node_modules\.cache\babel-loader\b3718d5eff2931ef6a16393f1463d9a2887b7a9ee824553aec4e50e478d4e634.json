{"ast": null, "code": "var _jsxFileName = \"D:\\\\pythonproject\\\\checkingsys1\\\\frontend\\\\src\\\\components\\\\RegionManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Tabs, Table, Button, Modal, Form, Input, Select, message, Popconfirm, Space, Typography } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst RegionManagement = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('provinces');\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState(null);\n  const [form] = Form.useForm();\n\n  // 使用现有的API实例，它已经配置了正确的基础URL和认证头\n\n  // 加载省份数据\n  const loadProvinces = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/regions/provinces');\n      setProvinces(response);\n    } catch (error) {\n      message.error('加载省份数据失败');\n      console.error('Error loading provinces:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载城市数据\n  const loadCities = async (provinceId = null) => {\n    setLoading(true);\n    try {\n      const url = provinceId ? `/regions/cities?province_id=${provinceId}` : `/regions/cities`;\n      const response = await api.get(url);\n      setCities(response);\n    } catch (error) {\n      message.error('加载城市数据失败');\n      console.error('Error loading cities:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载区县数据\n  const loadDistricts = async (cityId = null) => {\n    setLoading(true);\n    try {\n      const url = cityId ? `/regions/districts?city_id=${cityId}` : `/regions/districts`;\n      const response = await api.get(url);\n      setDistricts(response);\n    } catch (error) {\n      message.error('加载区县数据失败');\n      console.error('Error loading districts:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    loadProvinces();\n    loadCities();\n    loadDistricts();\n  }, []);\n\n  // 处理添加/编辑\n  const handleAddEdit = (item = null, type) => {\n    setEditingItem(item);\n    setModalVisible(true);\n    if (item) {\n      form.setFieldsValue(item);\n    } else {\n      form.resetFields();\n    }\n  };\n\n  // 处理保存\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      const isEdit = !!editingItem;\n      let url, method, data;\n      if (activeTab === 'provinces') {\n        url = isEdit ? `/regions/provinces/${editingItem.id}` : `/regions/provinces`;\n        method = isEdit ? 'put' : 'post';\n        data = values;\n      } else if (activeTab === 'cities') {\n        url = isEdit ? `/regions/cities/${editingItem.id}` : `/regions/cities`;\n        method = isEdit ? 'put' : 'post';\n        data = values;\n      } else if (activeTab === 'districts') {\n        url = isEdit ? `/regions/districts/${editingItem.id}` : `/regions/districts`;\n        method = isEdit ? 'put' : 'post';\n        data = values;\n      }\n      await api[method](url, data);\n      message.success(isEdit ? '更新成功' : '添加成功');\n      setModalVisible(false);\n      setEditingItem(null);\n      form.resetFields();\n\n      // 重新加载数据\n      if (activeTab === 'provinces') {\n        loadProvinces();\n      } else if (activeTab === 'cities') {\n        loadCities();\n      } else if (activeTab === 'districts') {\n        loadDistricts();\n      }\n    } catch (error) {\n      message.error('操作失败');\n      console.error('Error saving:', error);\n    }\n  };\n\n  // 处理删除\n  const handleDelete = async (id, type) => {\n    try {\n      let url;\n      if (type === 'province') {\n        url = `/regions/provinces/${id}`;\n      } else if (type === 'city') {\n        url = `/regions/cities/${id}`;\n      } else if (type === 'district') {\n        url = `/regions/districts/${id}`;\n      }\n      await api.delete(url);\n      message.success('删除成功');\n\n      // 重新加载数据\n      if (type === 'province') {\n        loadProvinces();\n      } else if (type === 'city') {\n        loadCities();\n      } else if (type === 'district') {\n        loadDistricts();\n      }\n    } catch (error) {\n      message.error('删除失败');\n      console.error('Error deleting:', error);\n    }\n  };\n\n  // 查看城市\n  const handleViewCities = provinceId => {\n    setActiveTab('cities');\n    loadCities(provinceId);\n  };\n\n  // 查看区县\n  const handleViewDistricts = cityId => {\n    setActiveTab('districts');\n    loadDistricts(cityId);\n  };\n\n  // 省份表格列定义\n  const provinceColumns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '省份名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '省份代码',\n    dataIndex: 'code',\n    key: 'code'\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewCities(record.id),\n        children: \"\\u67E5\\u770B\\u57CE\\u5E02\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleAddEdit(record, 'province'),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7701\\u4EFD\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id, 'province'),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 城市表格列定义\n  const cityColumns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '城市名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '城市代码',\n    dataIndex: 'code',\n    key: 'code'\n  }, {\n    title: '所属省份',\n    dataIndex: 'province_id',\n    key: 'province_id',\n    render: provinceId => {\n      const province = provinces.find(p => p.id === provinceId);\n      return province ? province.name : provinceId;\n    }\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewDistricts(record.id),\n        children: \"\\u67E5\\u770B\\u533A\\u53BF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleAddEdit(record, 'city'),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u57CE\\u5E02\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id, 'city'),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 区县表格列定义\n  const districtColumns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '区县名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '区县代码',\n    dataIndex: 'code',\n    key: 'code'\n  }, {\n    title: '所属城市',\n    dataIndex: 'city_id',\n    key: 'city_id',\n    render: cityId => {\n      const city = cities.find(c => c.id === cityId);\n      return city ? city.name : cityId;\n    }\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleAddEdit(record, 'district'),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u533A\\u53BF\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id, 'district'),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5730\\u533A\\u6570\\u636E\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        tabBarExtraContent: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAddEdit(null, activeTab.slice(0, -1)),\n          children: [\"\\u6DFB\\u52A0\", activeTab === 'provinces' ? '省份' : activeTab === 'cities' ? '城市' : '区县']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u7701\\u4EFD\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: provinceColumns,\n            dataSource: provinces,\n            rowKey: \"id\",\n            loading: loading,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 条记录`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, \"provinces\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u57CE\\u5E02\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: cityColumns,\n            dataSource: cities,\n            rowKey: \"id\",\n            loading: loading,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 条记录`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)\n        }, \"cities\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u533A\\u53BF\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: districtColumns,\n            dataSource: districts,\n            rowKey: \"id\",\n            loading: loading,\n            pagination: {\n              pageSize: 10,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 条记录`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)\n        }, \"districts\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `${editingItem ? '编辑' : '添加'}${activeTab === 'provinces' ? '省份' : activeTab === 'cities' ? '城市' : '区县'}`,\n      open: modalVisible,\n      onOk: handleSave,\n      onCancel: () => {\n        setModalVisible(false);\n        setEditingItem(null);\n        form.resetFields();\n      },\n      okText: \"\\u4FDD\\u5B58\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: `${activeTab === 'provinces' ? '省份' : activeTab === 'cities' ? '城市' : '区县'}名称`,\n          rules: [{\n            required: true,\n            message: '请输入名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"code\",\n          label: `${activeTab === 'provinces' ? '省份' : activeTab === 'cities' ? '城市' : '区县'}代码`,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EE3\\u7801\\uFF08\\u53EF\\u9009\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), activeTab === 'cities' && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"province_id\",\n          label: \"\\u6240\\u5C5E\\u7701\\u4EFD\",\n          rules: [{\n            required: true,\n            message: '请选择所属省份'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u7701\\u4EFD\",\n            children: provinces.map(province => /*#__PURE__*/_jsxDEV(Option, {\n              value: province.id,\n              children: province.name\n            }, province.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this), activeTab === 'districts' && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"city_id\",\n          label: \"\\u6240\\u5C5E\\u57CE\\u5E02\",\n          rules: [{\n            required: true,\n            message: '请选择所属城市'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u57CE\\u5E02\",\n            children: cities.map(city => /*#__PURE__*/_jsxDEV(Option, {\n              value: city.id,\n              children: city.name\n            }, city.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 366,\n    columnNumber: 5\n  }, this);\n};\n_s(RegionManagement, \"omR5w39IAE26sWPqNCI8DUSBik0=\", false, function () {\n  return [Form.useForm];\n});\n_c = RegionManagement;\nexport default RegionManagement;\nvar _c;\n$RefreshReg$(_c, \"RegionManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Tabs", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Space", "Typography", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "api", "jsxDEV", "_jsxDEV", "TabPane", "Title", "Option", "RegionManagement", "_s", "activeTab", "setActiveTab", "provinces", "setProvinces", "cities", "setCities", "districts", "setDistricts", "loading", "setLoading", "modalVisible", "setModalVisible", "editingItem", "setEditingItem", "form", "useForm", "loadProvinces", "response", "get", "error", "console", "loadCities", "provinceId", "url", "loadDistricts", "cityId", "handleAddEdit", "item", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetFields", "handleSave", "values", "validateFields", "isEdit", "method", "data", "id", "success", "handleDelete", "delete", "handleViewCities", "handleViewDistricts", "provinceColumns", "title", "dataIndex", "key", "width", "render", "_", "record", "size", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onConfirm", "okText", "cancelText", "danger", "cityColumns", "province", "find", "p", "name", "districtColumns", "city", "c", "style", "padding", "level", "active<PERSON><PERSON>", "onChange", "tabBarExtraContent", "slice", "tab", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onOk", "onCancel", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "map", "value", "_c", "$RefreshReg$"], "sources": ["D:/pythonproject/checkingsys1/frontend/src/components/RegionManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Tabs, \n  Table, \n  Button, \n  Modal, \n  Form, \n  Input, \n  Select, \n  message, \n  Popconfirm,\n  Space,\n  Typography\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';\nimport api from '../utils/api';\n\nconst { TabPane } = Tabs;\nconst { Title } = Typography;\nconst { Option } = Select;\n\nconst RegionManagement = () => {\n  const [activeTab, setActiveTab] = useState('provinces');\n  const [provinces, setProvinces] = useState([]);\n  const [cities, setCities] = useState([]);\n  const [districts, setDistricts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState(null);\n  const [form] = Form.useForm();\n\n  // 使用现有的API实例，它已经配置了正确的基础URL和认证头\n\n  // 加载省份数据\n  const loadProvinces = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/regions/provinces');\n      setProvinces(response);\n    } catch (error) {\n      message.error('加载省份数据失败');\n      console.error('Error loading provinces:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载城市数据\n  const loadCities = async (provinceId = null) => {\n    setLoading(true);\n    try {\n      const url = provinceId\n        ? `/regions/cities?province_id=${provinceId}`\n        : `/regions/cities`;\n      const response = await api.get(url);\n      setCities(response);\n    } catch (error) {\n      message.error('加载城市数据失败');\n      console.error('Error loading cities:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 加载区县数据\n  const loadDistricts = async (cityId = null) => {\n    setLoading(true);\n    try {\n      const url = cityId\n        ? `/regions/districts?city_id=${cityId}`\n        : `/regions/districts`;\n      const response = await api.get(url);\n      setDistricts(response);\n    } catch (error) {\n      message.error('加载区县数据失败');\n      console.error('Error loading districts:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    loadProvinces();\n    loadCities();\n    loadDistricts();\n  }, []);\n\n  // 处理添加/编辑\n  const handleAddEdit = (item = null, type) => {\n    setEditingItem(item);\n    setModalVisible(true);\n    \n    if (item) {\n      form.setFieldsValue(item);\n    } else {\n      form.resetFields();\n    }\n  };\n\n  // 处理保存\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      const isEdit = !!editingItem;\n\n      let url, method, data;\n\n      if (activeTab === 'provinces') {\n        url = isEdit\n          ? `/regions/provinces/${editingItem.id}`\n          : `/regions/provinces`;\n        method = isEdit ? 'put' : 'post';\n        data = values;\n      } else if (activeTab === 'cities') {\n        url = isEdit\n          ? `/regions/cities/${editingItem.id}`\n          : `/regions/cities`;\n        method = isEdit ? 'put' : 'post';\n        data = values;\n      } else if (activeTab === 'districts') {\n        url = isEdit\n          ? `/regions/districts/${editingItem.id}`\n          : `/regions/districts`;\n        method = isEdit ? 'put' : 'post';\n        data = values;\n      }\n\n      await api[method](url, data);\n\n      message.success(isEdit ? '更新成功' : '添加成功');\n      setModalVisible(false);\n      setEditingItem(null);\n      form.resetFields();\n      \n      // 重新加载数据\n      if (activeTab === 'provinces') {\n        loadProvinces();\n      } else if (activeTab === 'cities') {\n        loadCities();\n      } else if (activeTab === 'districts') {\n        loadDistricts();\n      }\n      \n    } catch (error) {\n      message.error('操作失败');\n      console.error('Error saving:', error);\n    }\n  };\n\n  // 处理删除\n  const handleDelete = async (id, type) => {\n    try {\n      let url;\n      if (type === 'province') {\n        url = `/regions/provinces/${id}`;\n      } else if (type === 'city') {\n        url = `/regions/cities/${id}`;\n      } else if (type === 'district') {\n        url = `/regions/districts/${id}`;\n      }\n\n      await api.delete(url);\n\n      message.success('删除成功');\n      \n      // 重新加载数据\n      if (type === 'province') {\n        loadProvinces();\n      } else if (type === 'city') {\n        loadCities();\n      } else if (type === 'district') {\n        loadDistricts();\n      }\n      \n    } catch (error) {\n      message.error('删除失败');\n      console.error('Error deleting:', error);\n    }\n  };\n\n  // 查看城市\n  const handleViewCities = (provinceId) => {\n    setActiveTab('cities');\n    loadCities(provinceId);\n  };\n\n  // 查看区县\n  const handleViewDistricts = (cityId) => {\n    setActiveTab('districts');\n    loadDistricts(cityId);\n  };\n\n  // 省份表格列定义\n  const provinceColumns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '省份名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '省份代码',\n      dataIndex: 'code',\n      key: 'code',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button \n            type=\"link\" \n            icon={<EyeOutlined />} \n            onClick={() => handleViewCities(record.id)}\n          >\n            查看城市\n          </Button>\n          <Button \n            type=\"link\" \n            icon={<EditOutlined />} \n            onClick={() => handleAddEdit(record, 'province')}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个省份吗？\"\n            onConfirm={() => handleDelete(record.id, 'province')}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  // 城市表格列定义\n  const cityColumns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '城市名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '城市代码',\n      dataIndex: 'code',\n      key: 'code',\n    },\n    {\n      title: '所属省份',\n      dataIndex: 'province_id',\n      key: 'province_id',\n      render: (provinceId) => {\n        const province = provinces.find(p => p.id === provinceId);\n        return province ? province.name : provinceId;\n      }\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button \n            type=\"link\" \n            icon={<EyeOutlined />} \n            onClick={() => handleViewDistricts(record.id)}\n          >\n            查看区县\n          </Button>\n          <Button \n            type=\"link\" \n            icon={<EditOutlined />} \n            onClick={() => handleAddEdit(record, 'city')}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个城市吗？\"\n            onConfirm={() => handleDelete(record.id, 'city')}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  // 区县表格列定义\n  const districtColumns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '区县名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '区县代码',\n      dataIndex: 'code',\n      key: 'code',\n    },\n    {\n      title: '所属城市',\n      dataIndex: 'city_id',\n      key: 'city_id',\n      render: (cityId) => {\n        const city = cities.find(c => c.id === cityId);\n        return city ? city.name : cityId;\n      }\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button \n            type=\"link\" \n            icon={<EditOutlined />} \n            onClick={() => handleAddEdit(record, 'district')}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个区县吗？\"\n            onConfirm={() => handleDelete(record.id, 'district')}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>地区数据管理</Title>\n      \n      <Card>\n        <Tabs \n          activeKey={activeTab} \n          onChange={setActiveTab}\n          tabBarExtraContent={\n            <Button \n              type=\"primary\" \n              icon={<PlusOutlined />}\n              onClick={() => handleAddEdit(null, activeTab.slice(0, -1))}\n            >\n              添加{activeTab === 'provinces' ? '省份' : activeTab === 'cities' ? '城市' : '区县'}\n            </Button>\n          }\n        >\n          <TabPane tab=\"省份管理\" key=\"provinces\">\n            <Table\n              columns={provinceColumns}\n              dataSource={provinces}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条记录`\n              }}\n            />\n          </TabPane>\n          \n          <TabPane tab=\"城市管理\" key=\"cities\">\n            <Table\n              columns={cityColumns}\n              dataSource={cities}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条记录`\n              }}\n            />\n          </TabPane>\n          \n          <TabPane tab=\"区县管理\" key=\"districts\">\n            <Table\n              columns={districtColumns}\n              dataSource={districts}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{\n                pageSize: 10,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 条记录`\n              }}\n            />\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 添加/编辑模态框 */}\n      <Modal\n        title={`${editingItem ? '编辑' : '添加'}${\n          activeTab === 'provinces' ? '省份' : \n          activeTab === 'cities' ? '城市' : '区县'\n        }`}\n        open={modalVisible}\n        onOk={handleSave}\n        onCancel={() => {\n          setModalVisible(false);\n          setEditingItem(null);\n          form.resetFields();\n        }}\n        okText=\"保存\"\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"name\"\n            label={`${\n              activeTab === 'provinces' ? '省份' : \n              activeTab === 'cities' ? '城市' : '区县'\n            }名称`}\n            rules={[{ required: true, message: '请输入名称' }]}\n          >\n            <Input placeholder=\"请输入名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"code\"\n            label={`${\n              activeTab === 'provinces' ? '省份' : \n              activeTab === 'cities' ? '城市' : '区县'\n            }代码`}\n          >\n            <Input placeholder=\"请输入代码（可选）\" />\n          </Form.Item>\n          \n          {activeTab === 'cities' && (\n            <Form.Item\n              name=\"province_id\"\n              label=\"所属省份\"\n              rules={[{ required: true, message: '请选择所属省份' }]}\n            >\n              <Select placeholder=\"请选择省份\">\n                {provinces.map(province => (\n                  <Option key={province.id} value={province.id}>\n                    {province.name}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          )}\n          \n          {activeTab === 'districts' && (\n            <Form.Item\n              name=\"city_id\"\n              label=\"所属城市\"\n              rules={[{ required: true, message: '请选择所属城市' }]}\n            >\n              <Select placeholder=\"请选择城市\">\n                {cities.map(city => (\n                  <Option key={city.id} value={city.id}>\n                    {city.name}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          )}\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default RegionManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,UAAU,QACL,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAC3F,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAM;EAAEC;AAAQ,CAAC,GAAGlB,IAAI;AACxB,MAAM;EAAEmB;AAAM,CAAC,GAAGT,UAAU;AAC5B,MAAM;EAAEU;AAAO,CAAC,GAAGd,MAAM;AAEzB,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwC,IAAI,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;;EAE7B;;EAEA;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAAC,oBAAoB,CAAC;MACpDf,YAAY,CAACc,QAAQ,CAAC;IACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,UAAU,CAAC;MACzBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMY,UAAU,GAAG,MAAAA,CAAOC,UAAU,GAAG,IAAI,KAAK;IAC9Cb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,GAAG,GAAGD,UAAU,GAClB,+BAA+BA,UAAU,EAAE,GAC3C,iBAAiB;MACrB,MAAML,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAACK,GAAG,CAAC;MACnClB,SAAS,CAACY,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,UAAU,CAAC;MACzBC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAG,MAAAA,CAAOC,MAAM,GAAG,IAAI,KAAK;IAC7ChB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,GAAG,GAAGE,MAAM,GACd,8BAA8BA,MAAM,EAAE,GACtC,oBAAoB;MACxB,MAAMR,QAAQ,GAAG,MAAMzB,GAAG,CAAC0B,GAAG,CAACK,GAAG,CAAC;MACnChB,YAAY,CAACU,QAAQ,CAAC;IACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,UAAU,CAAC;MACzBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAlC,SAAS,CAAC,MAAM;IACdyC,aAAa,CAAC,CAAC;IACfK,UAAU,CAAC,CAAC;IACZG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,aAAa,GAAGA,CAACC,IAAI,GAAG,IAAI,EAAEC,IAAI,KAAK;IAC3Cf,cAAc,CAACc,IAAI,CAAC;IACpBhB,eAAe,CAAC,IAAI,CAAC;IAErB,IAAIgB,IAAI,EAAE;MACRb,IAAI,CAACe,cAAc,CAACF,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLb,IAAI,CAACgB,WAAW,CAAC,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMlB,IAAI,CAACmB,cAAc,CAAC,CAAC;MAC1C,MAAMC,MAAM,GAAG,CAAC,CAACtB,WAAW;MAE5B,IAAIW,GAAG,EAAEY,MAAM,EAAEC,IAAI;MAErB,IAAIpC,SAAS,KAAK,WAAW,EAAE;QAC7BuB,GAAG,GAAGW,MAAM,GACR,sBAAsBtB,WAAW,CAACyB,EAAE,EAAE,GACtC,oBAAoB;QACxBF,MAAM,GAAGD,MAAM,GAAG,KAAK,GAAG,MAAM;QAChCE,IAAI,GAAGJ,MAAM;MACf,CAAC,MAAM,IAAIhC,SAAS,KAAK,QAAQ,EAAE;QACjCuB,GAAG,GAAGW,MAAM,GACR,mBAAmBtB,WAAW,CAACyB,EAAE,EAAE,GACnC,iBAAiB;QACrBF,MAAM,GAAGD,MAAM,GAAG,KAAK,GAAG,MAAM;QAChCE,IAAI,GAAGJ,MAAM;MACf,CAAC,MAAM,IAAIhC,SAAS,KAAK,WAAW,EAAE;QACpCuB,GAAG,GAAGW,MAAM,GACR,sBAAsBtB,WAAW,CAACyB,EAAE,EAAE,GACtC,oBAAoB;QACxBF,MAAM,GAAGD,MAAM,GAAG,KAAK,GAAG,MAAM;QAChCE,IAAI,GAAGJ,MAAM;MACf;MAEA,MAAMxC,GAAG,CAAC2C,MAAM,CAAC,CAACZ,GAAG,EAAEa,IAAI,CAAC;MAE5BpD,OAAO,CAACsD,OAAO,CAACJ,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;MACzCvB,eAAe,CAAC,KAAK,CAAC;MACtBE,cAAc,CAAC,IAAI,CAAC;MACpBC,IAAI,CAACgB,WAAW,CAAC,CAAC;;MAElB;MACA,IAAI9B,SAAS,KAAK,WAAW,EAAE;QAC7BgB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIhB,SAAS,KAAK,QAAQ,EAAE;QACjCqB,UAAU,CAAC,CAAC;MACd,CAAC,MAAM,IAAIrB,SAAS,KAAK,WAAW,EAAE;QACpCwB,aAAa,CAAC,CAAC;MACjB;IAEF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,MAAM,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAG,MAAAA,CAAOF,EAAE,EAAET,IAAI,KAAK;IACvC,IAAI;MACF,IAAIL,GAAG;MACP,IAAIK,IAAI,KAAK,UAAU,EAAE;QACvBL,GAAG,GAAG,sBAAsBc,EAAE,EAAE;MAClC,CAAC,MAAM,IAAIT,IAAI,KAAK,MAAM,EAAE;QAC1BL,GAAG,GAAG,mBAAmBc,EAAE,EAAE;MAC/B,CAAC,MAAM,IAAIT,IAAI,KAAK,UAAU,EAAE;QAC9BL,GAAG,GAAG,sBAAsBc,EAAE,EAAE;MAClC;MAEA,MAAM7C,GAAG,CAACgD,MAAM,CAACjB,GAAG,CAAC;MAErBvC,OAAO,CAACsD,OAAO,CAAC,MAAM,CAAC;;MAEvB;MACA,IAAIV,IAAI,KAAK,UAAU,EAAE;QACvBZ,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM,IAAIY,IAAI,KAAK,MAAM,EAAE;QAC1BP,UAAU,CAAC,CAAC;MACd,CAAC,MAAM,IAAIO,IAAI,KAAK,UAAU,EAAE;QAC9BJ,aAAa,CAAC,CAAC;MACjB;IAEF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,MAAM,CAAC;MACrBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAInB,UAAU,IAAK;IACvCrB,YAAY,CAAC,QAAQ,CAAC;IACtBoB,UAAU,CAACC,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMoB,mBAAmB,GAAIjB,MAAM,IAAK;IACtCxB,YAAY,CAAC,WAAW,CAAC;IACzBuB,aAAa,CAACC,MAAM,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkB,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBxD,OAAA,CAACR,KAAK;MAACiE,IAAI,EAAC,OAAO;MAAAC,QAAA,gBACjB1D,OAAA,CAACf,MAAM;QACLiD,IAAI,EAAC,MAAM;QACXyB,IAAI,eAAE3D,OAAA,CAACH,WAAW;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBC,OAAO,EAAEA,CAAA,KAAMjB,gBAAgB,CAACS,MAAM,CAACb,EAAE,CAAE;QAAAe,QAAA,EAC5C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA,CAACf,MAAM;QACLiD,IAAI,EAAC,MAAM;QACXyB,IAAI,eAAE3D,OAAA,CAACL,YAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAACwB,MAAM,EAAE,UAAU,CAAE;QAAAE,QAAA,EAClD;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA,CAACT,UAAU;QACT2D,KAAK,EAAC,oEAAa;QACnBe,SAAS,EAAEA,CAAA,KAAMpB,YAAY,CAACW,MAAM,CAACb,EAAE,EAAE,UAAU,CAAE;QACrDuB,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAT,QAAA,eAEf1D,OAAA,CAACf,MAAM;UAACiD,IAAI,EAAC,MAAM;UAACkC,MAAM;UAACT,IAAI,eAAE3D,OAAA,CAACJ,cAAc;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMM,WAAW,GAAG,CAClB;IACEnB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBE,MAAM,EAAG1B,UAAU,IAAK;MACtB,MAAM0C,QAAQ,GAAG9D,SAAS,CAAC+D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAKf,UAAU,CAAC;MACzD,OAAO0C,QAAQ,GAAGA,QAAQ,CAACG,IAAI,GAAG7C,UAAU;IAC9C;EACF,CAAC,EACD;IACEsB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBxD,OAAA,CAACR,KAAK;MAACiE,IAAI,EAAC,OAAO;MAAAC,QAAA,gBACjB1D,OAAA,CAACf,MAAM;QACLiD,IAAI,EAAC,MAAM;QACXyB,IAAI,eAAE3D,OAAA,CAACH,WAAW;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBC,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAACQ,MAAM,CAACb,EAAE,CAAE;QAAAe,QAAA,EAC/C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA,CAACf,MAAM;QACLiD,IAAI,EAAC,MAAM;QACXyB,IAAI,eAAE3D,OAAA,CAACL,YAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAACwB,MAAM,EAAE,MAAM,CAAE;QAAAE,QAAA,EAC9C;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA,CAACT,UAAU;QACT2D,KAAK,EAAC,oEAAa;QACnBe,SAAS,EAAEA,CAAA,KAAMpB,YAAY,CAACW,MAAM,CAACb,EAAE,EAAE,MAAM,CAAE;QACjDuB,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAT,QAAA,eAEf1D,OAAA,CAACf,MAAM;UAACiD,IAAI,EAAC,MAAM;UAACkC,MAAM;UAACT,IAAI,eAAE3D,OAAA,CAACJ,cAAc;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMW,eAAe,GAAG,CACtB;IACExB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdE,MAAM,EAAGvB,MAAM,IAAK;MAClB,MAAM4C,IAAI,GAAGjE,MAAM,CAAC6D,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKZ,MAAM,CAAC;MAC9C,OAAO4C,IAAI,GAAGA,IAAI,CAACF,IAAI,GAAG1C,MAAM;IAClC;EACF,CAAC,EACD;IACEmB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBxD,OAAA,CAACR,KAAK;MAACiE,IAAI,EAAC,OAAO;MAAAC,QAAA,gBACjB1D,OAAA,CAACf,MAAM;QACLiD,IAAI,EAAC,MAAM;QACXyB,IAAI,eAAE3D,OAAA,CAACL,YAAY;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAACwB,MAAM,EAAE,UAAU,CAAE;QAAAE,QAAA,EAClD;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/D,OAAA,CAACT,UAAU;QACT2D,KAAK,EAAC,oEAAa;QACnBe,SAAS,EAAEA,CAAA,KAAMpB,YAAY,CAACW,MAAM,CAACb,EAAE,EAAE,UAAU,CAAE;QACrDuB,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAT,QAAA,eAEf1D,OAAA,CAACf,MAAM;UAACiD,IAAI,EAAC,MAAM;UAACkC,MAAM;UAACT,IAAI,eAAE3D,OAAA,CAACJ,cAAc;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAL,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE/D,OAAA;IAAK6E,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAApB,QAAA,gBAC9B1D,OAAA,CAACE,KAAK;MAAC6E,KAAK,EAAE,CAAE;MAAArB,QAAA,EAAC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE/B/D,OAAA,CAAClB,IAAI;MAAA4E,QAAA,eACH1D,OAAA,CAACjB,IAAI;QACHiG,SAAS,EAAE1E,SAAU;QACrB2E,QAAQ,EAAE1E,YAAa;QACvB2E,kBAAkB,eAChBlF,OAAA,CAACf,MAAM;UACLiD,IAAI,EAAC,SAAS;UACdyB,IAAI,eAAE3D,OAAA,CAACN,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAAC,IAAI,EAAE1B,SAAS,CAAC6E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;UAAAzB,QAAA,GAC5D,cACG,EAACpD,SAAS,KAAK,WAAW,GAAG,IAAI,GAAGA,SAAS,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CACT;QAAAL,QAAA,gBAED1D,OAAA,CAACC,OAAO;UAACmF,GAAG,EAAC,0BAAM;UAAA1B,QAAA,eACjB1D,OAAA,CAAChB,KAAK;YACJqG,OAAO,EAAEpC,eAAgB;YACzBqC,UAAU,EAAE9E,SAAU;YACtB+E,MAAM,EAAC,IAAI;YACXzE,OAAO,EAAEA,OAAQ;YACjB0E,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAZoB,WAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAa1B,CAAC,eAEV/D,OAAA,CAACC,OAAO;UAACmF,GAAG,EAAC,0BAAM;UAAA1B,QAAA,eACjB1D,OAAA,CAAChB,KAAK;YACJqG,OAAO,EAAEhB,WAAY;YACrBiB,UAAU,EAAE5E,MAAO;YACnB6E,MAAM,EAAC,IAAI;YACXzE,OAAO,EAAEA,OAAQ;YACjB0E,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAZoB,QAAQ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAavB,CAAC,eAEV/D,OAAA,CAACC,OAAO;UAACmF,GAAG,EAAC,0BAAM;UAAA1B,QAAA,eACjB1D,OAAA,CAAChB,KAAK;YACJqG,OAAO,EAAEX,eAAgB;YACzBY,UAAU,EAAE1E,SAAU;YACtB2E,MAAM,EAAC,IAAI;YACXzE,OAAO,EAAEA,OAAQ;YACjB0E,UAAU,EAAE;cACVC,QAAQ,EAAE,EAAE;cACZC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAZoB,WAAW;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAa1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP/D,OAAA,CAACd,KAAK;MACJgE,KAAK,EAAE,GAAGhC,WAAW,GAAG,IAAI,GAAG,IAAI,GACjCZ,SAAS,KAAK,WAAW,GAAG,IAAI,GAChCA,SAAS,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,EACnC;MACHwF,IAAI,EAAE9E,YAAa;MACnB+E,IAAI,EAAE1D,UAAW;MACjB2D,QAAQ,EAAEA,CAAA,KAAM;QACd/E,eAAe,CAAC,KAAK,CAAC;QACtBE,cAAc,CAAC,IAAI,CAAC;QACpBC,IAAI,CAACgB,WAAW,CAAC,CAAC;MACpB,CAAE;MACF8B,MAAM,EAAC,cAAI;MACXC,UAAU,EAAC,cAAI;MAAAT,QAAA,eAEf1D,OAAA,CAACb,IAAI;QAACiC,IAAI,EAAEA,IAAK;QAAC6E,MAAM,EAAC,UAAU;QAAAvC,QAAA,gBACjC1D,OAAA,CAACb,IAAI,CAAC+G,IAAI;UACRzB,IAAI,EAAC,MAAM;UACX0B,KAAK,EAAE,GACL7F,SAAS,KAAK,WAAW,GAAG,IAAI,GAChCA,SAAS,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,IACjC;UACL8F,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/G,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAoE,QAAA,eAE9C1D,OAAA,CAACZ,KAAK;YAACkH,WAAW,EAAC;UAAO;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZ/D,OAAA,CAACb,IAAI,CAAC+G,IAAI;UACRzB,IAAI,EAAC,MAAM;UACX0B,KAAK,EAAE,GACL7F,SAAS,KAAK,WAAW,GAAG,IAAI,GAChCA,SAAS,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,IACjC;UAAAoD,QAAA,eAEL1D,OAAA,CAACZ,KAAK;YAACkH,WAAW,EAAC;UAAW;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EAEXzD,SAAS,KAAK,QAAQ,iBACrBN,OAAA,CAACb,IAAI,CAAC+G,IAAI;UACRzB,IAAI,EAAC,aAAa;UAClB0B,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoE,QAAA,eAEhD1D,OAAA,CAACX,MAAM;YAACiH,WAAW,EAAC,gCAAO;YAAA5C,QAAA,EACxBlD,SAAS,CAAC+F,GAAG,CAACjC,QAAQ,iBACrBtE,OAAA,CAACG,MAAM;cAAmBqG,KAAK,EAAElC,QAAQ,CAAC3B,EAAG;cAAAe,QAAA,EAC1CY,QAAQ,CAACG;YAAI,GADHH,QAAQ,CAAC3B,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ,EAEAzD,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAACb,IAAI,CAAC+G,IAAI;UACRzB,IAAI,EAAC,SAAS;UACd0B,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAoE,QAAA,eAEhD1D,OAAA,CAACX,MAAM;YAACiH,WAAW,EAAC,gCAAO;YAAA5C,QAAA,EACxBhD,MAAM,CAAC6F,GAAG,CAAC5B,IAAI,iBACd3E,OAAA,CAACG,MAAM;cAAeqG,KAAK,EAAE7B,IAAI,CAAChC,EAAG;cAAAe,QAAA,EAClCiB,IAAI,CAACF;YAAI,GADCE,IAAI,CAAChC,EAAE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAheID,gBAAgB;EAAA,QAQLjB,IAAI,CAACkC,OAAO;AAAA;AAAAoF,EAAA,GARvBrG,gBAAgB;AAketB,eAAeA,gBAAgB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}