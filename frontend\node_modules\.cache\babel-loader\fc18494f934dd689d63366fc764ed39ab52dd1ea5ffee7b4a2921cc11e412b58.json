{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport StepForwardFilledSvg from \"@ant-design/icons-svg/es/asn/StepForwardFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar StepForwardFilled = function StepForwardFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: StepForwardFilledSvg\n  }));\n};\n\n/**![step-forward](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY3Ni40IDUyOC45NUwyOTMuMiA4MjkuOTdjLTE0LjI1IDExLjItMzUuMiAxLjEtMzUuMi0xNi45NVYyMTAuOTdjMC0xOC4wNSAyMC45NS0yOC4xNCAzNS4yLTE2Ljk0bDM4My4yIDMwMS4wMmEyMS41MyAyMS41MyAwIDAxMCAzMy45TTY5NCA4NjRoNjRhOCA4IDAgMDA4LThWMTY4YTggOCAwIDAwLTgtOGgtNjRhOCA4IDAgMDAtOCA4djY4OGE4IDggMCAwMDggOCIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(StepForwardFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StepForwardFilled';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}