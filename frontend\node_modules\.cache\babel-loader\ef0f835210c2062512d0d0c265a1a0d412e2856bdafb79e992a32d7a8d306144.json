{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { getSize } from './utils';\nconst Steps = props => {\n  const {\n    size,\n    steps,\n    rounding: customRounding = Math.round,\n    percent = 0,\n    strokeWidth = 8,\n    strokeColor,\n    trailColor = null,\n    prefixCls,\n    children\n  } = props;\n  const current = customRounding(steps * (percent / 100));\n  const stepWidth = size === 'small' ? 2 : 14;\n  const mergedSize = size !== null && size !== void 0 ? size : [stepWidth, strokeWidth];\n  const [width, height] = getSize(mergedSize, 'step', {\n    steps,\n    strokeWidth\n  });\n  const unitWidth = width / steps;\n  const styledSteps = Array.from({\n    length: steps\n  });\n  for (let i = 0; i < steps; i++) {\n    const color = Array.isArray(strokeColor) ? strokeColor[i] : strokeColor;\n    styledSteps[i] = /*#__PURE__*/React.createElement(\"div\", {\n      key: i,\n      className: classNames(`${prefixCls}-steps-item`, {\n        [`${prefixCls}-steps-item-active`]: i <= current - 1\n      }),\n      style: {\n        backgroundColor: i <= current - 1 ? color : trailColor,\n        width: unitWidth,\n        height\n      }\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-steps-outer`\n  }, styledSteps, children);\n};\nexport default Steps;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}