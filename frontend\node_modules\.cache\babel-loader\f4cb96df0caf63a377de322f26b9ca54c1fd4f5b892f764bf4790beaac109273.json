{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CommentOutlinedSvg from \"@ant-design/icons-svg/es/asn/CommentOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CommentOutlined = function CommentOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CommentOutlinedSvg\n  }));\n};\n\n/**![comment](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik01NzMgNDIxYy0yMy4xIDAtNDEgMTcuOS00MSA0MHMxNy45IDQwIDQxIDQwYzIxLjEgMCAzOS0xNy45IDM5LTQwcy0xNy45LTQwLTM5LTQwem0tMjgwIDBjLTIzLjEgMC00MSAxNy45LTQxIDQwczE3LjkgNDAgNDEgNDBjMjEuMSAwIDM5LTE3LjkgMzktNDBzLTE3LjktNDAtMzktNDB6IiAvPjxwYXRoIGQ9Ik04OTQgMzQ1YTM0My45MiAzNDMuOTIgMCAwMC0xODktMTMwdi4xYy0xNy4xLTE5LTM2LjQtMzYuNS01OC01Mi4xLTE2My43LTExOS0zOTMuNS04Mi43LTUxMyA4MS05Ni4zIDEzMy05Mi4yIDMxMS45IDYgNDM5bC44IDEzMi42YzAgMy4yLjUgNi40IDEuNSA5LjRhMzEuOTUgMzEuOTUgMCAwMDQwLjEgMjAuOUwzMDkgODA2YzMzLjUgMTEuOSA2OC4xIDE4LjcgMTAyLjUgMjAuNmwtLjUuNGM4OS4xIDY0LjkgMjA1LjkgODQuNCAzMTMgNDlsMTI3LjEgNDEuNGMzLjIgMSA2LjUgMS42IDkuOSAxLjYgMTcuNyAwIDMyLTE0LjMgMzItMzJWNzUzYzg4LjEtMTE5LjYgOTAuNC0yODQuOSAxLTQwOHpNMzIzIDczNWwtMTItNS05OSAzMS0xLTEwNC04LTljLTg0LjYtMTAzLjItOTAuMi0yNTEuOS0xMS0zNjEgOTYuNC0xMzIuMiAyODEuMi0xNjEuNCA0MTMtNjYgMTMyLjIgOTYuMSAxNjEuNSAyODAuNiA2NiA0MTItODAuMSAxMDkuOS0yMjMuNSAxNTAuNS0zNDggMTAyem01MDUtMTdsLTggMTAgMSAxMDQtOTgtMzMtMTIgNWMtNTYgMjAuOC0xMTUuNyAyMi41LTE3MSA3bC0uMi0uMUEzNjcuMzEgMzY3LjMxIDAgMDA3MjkgNjc2Yzc2LjQtMTA1LjMgODguOC0yMzcuNiA0NC40LTM1MC40bC42LjRjMjMgMTYuNSA0NC4xIDM3LjEgNjIgNjIgNzIuNiA5OS42IDY4LjUgMjM1LjItOCAzMzB6IiAvPjxwYXRoIGQ9Ik00MzMgNDIxYy0yMy4xIDAtNDEgMTcuOS00MSA0MHMxNy45IDQwIDQxIDQwYzIxLjEgMCAzOS0xNy45IDM5LTQwcy0xNy45LTQwLTM5LTQweiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CommentOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CommentOutlined';\n}\nexport default RefIcon;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}