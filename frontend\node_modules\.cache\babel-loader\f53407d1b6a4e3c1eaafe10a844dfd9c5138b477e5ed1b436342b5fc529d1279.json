{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport cls from 'classnames';\nimport RcCascader from 'rc-cascader';\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { getTransitionName } from '../_util/motion';\nimport genPurePanel from '../_util/PurePanel';\nimport { getMergedStatus, getStatusClassNames } from '../_util/statusUtils';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport { useComponentConfig } from '../config-provider/context';\nimport DefaultRenderEmpty from '../config-provider/defaultRenderEmpty';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport { FormItemInputContext } from '../form/context';\nimport useVariant from '../form/hooks/useVariants';\nimport mergedBuiltinPlacements from '../select/mergedBuiltinPlacements';\nimport useSelectStyle from '../select/style';\nimport useIcons from '../select/useIcons';\nimport useShowArrow from '../select/useShowArrow';\nimport { useCompactItemContext } from '../space/Compact';\nimport useBase from './hooks/useBase';\nimport useCheckable from './hooks/useCheckable';\nimport useColumnIcons from './hooks/useColumnIcons';\nimport CascaderPanel from './Panel';\nimport useStyle from './style';\nconst {\n  SHOW_CHILD,\n  SHOW_PARENT\n} = RcCascader;\nfunction highlightKeyword(str, lowerKeyword, prefixCls) {\n  const cells = str.toLowerCase().split(lowerKeyword).reduce((list, cur, index) => index === 0 ? [cur] : [].concat(_toConsumableArray(list), [lowerKeyword, cur]), []);\n  const fillCells = [];\n  let start = 0;\n  cells.forEach((cell, index) => {\n    const end = start + cell.length;\n    let originWorld = str.slice(start, end);\n    start = end;\n    if (index % 2 === 1) {\n      originWorld = /*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(\"span\", {\n        className: `${prefixCls}-menu-item-keyword`,\n        key: `separator-${index}`\n      }, originWorld);\n    }\n    fillCells.push(originWorld);\n  });\n  return fillCells;\n}\nconst defaultSearchRender = (inputValue, path, prefixCls, fieldNames) => {\n  const optionList = [];\n  // We do lower here to save perf\n  const lower = inputValue.toLowerCase();\n  path.forEach((node, index) => {\n    if (index !== 0) {\n      optionList.push(' / ');\n    }\n    let label = node[fieldNames.label];\n    const type = typeof label;\n    if (type === 'string' || type === 'number') {\n      label = highlightKeyword(String(label), lower, prefixCls);\n    }\n    optionList.push(label);\n  });\n  return optionList;\n};\nconst Cascader = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b, _c, _d;\n  const {\n      prefixCls: customizePrefixCls,\n      size: customizeSize,\n      disabled: customDisabled,\n      className,\n      rootClassName,\n      multiple,\n      bordered = true,\n      transitionName,\n      choiceTransitionName = '',\n      popupClassName,\n      dropdownClassName,\n      expandIcon,\n      placement,\n      showSearch,\n      allowClear = true,\n      notFoundContent,\n      direction,\n      getPopupContainer,\n      status: customStatus,\n      showArrow,\n      builtinPlacements,\n      style,\n      variant: customVariant,\n      dropdownRender,\n      onDropdownVisibleChange,\n      dropdownMenuColumnStyle,\n      popupRender,\n      dropdownStyle,\n      popupMenuColumnStyle,\n      onOpenChange,\n      styles,\n      classNames\n    } = props,\n    rest = __rest(props, [\"prefixCls\", \"size\", \"disabled\", \"className\", \"rootClassName\", \"multiple\", \"bordered\", \"transitionName\", \"choiceTransitionName\", \"popupClassName\", \"dropdownClassName\", \"expandIcon\", \"placement\", \"showSearch\", \"allowClear\", \"notFoundContent\", \"direction\", \"getPopupContainer\", \"status\", \"showArrow\", \"builtinPlacements\", \"style\", \"variant\", \"dropdownRender\", \"onDropdownVisibleChange\", \"dropdownMenuColumnStyle\", \"popupRender\", \"dropdownStyle\", \"popupMenuColumnStyle\", \"onOpenChange\", \"styles\", \"classNames\"]);\n  const restProps = omit(rest, ['suffixIcon']);\n  const {\n    getPrefixCls,\n    getPopupContainer: getContextPopupContainer,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('cascader');\n  const {\n    popupOverflow\n  } = React.useContext(ConfigContext);\n  // =================== Form =====================\n  const {\n    status: contextStatus,\n    hasFeedback,\n    isFormItemInput,\n    feedbackIcon\n  } = React.useContext(FormItemInputContext);\n  const mergedStatus = getMergedStatus(contextStatus, customStatus);\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Cascader');\n    // v5 deprecated dropdown api\n    const deprecatedProps = {\n      dropdownClassName: 'classNames.popup.root',\n      dropdownStyle: 'styles.popup.root',\n      dropdownRender: 'popupRender',\n      dropdownMenuColumnStyle: 'popupMenuColumnStyle',\n      onDropdownVisibleChange: 'onOpenChange',\n      bordered: 'variant'\n    };\n    Object.entries(deprecatedProps).forEach(([oldProp, newProp]) => {\n      warning.deprecated(!(oldProp in props), oldProp, newProp);\n    });\n    process.env.NODE_ENV !== \"production\" ? warning(!('showArrow' in props), 'deprecated', '`showArrow` is deprecated which will be removed in next major version. It will be a default behavior, you can hide it by setting `suffixIcon` to null.') : void 0;\n  }\n  // ==================== Prefix =====================\n  const [prefixCls, cascaderPrefixCls, mergedDirection, renderEmpty] = useBase(customizePrefixCls, direction);\n  const isRtl = mergedDirection === 'rtl';\n  const rootPrefixCls = getPrefixCls();\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapSelectCSSVar, hashId, cssVarCls] = useSelectStyle(prefixCls, rootCls);\n  const cascaderRootCls = useCSSVarCls(cascaderPrefixCls);\n  const [wrapCascaderCSSVar] = useStyle(cascaderPrefixCls, cascaderRootCls);\n  const {\n    compactSize,\n    compactItemClassnames\n  } = useCompactItemContext(prefixCls, direction);\n  const [variant, enableVariantCls] = useVariant('cascader', customVariant, bordered);\n  // =================== No Found ====================\n  const mergedNotFoundContent = notFoundContent || (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Cascader')) || (/*#__PURE__*/React.createElement(DefaultRenderEmpty, {\n    componentName: \"Cascader\"\n  }));\n  // =================== Dropdown ====================\n  const mergedPopupClassName = cls(((_a = classNames === null || classNames === void 0 ? void 0 : classNames.popup) === null || _a === void 0 ? void 0 : _a.root) || ((_b = contextClassNames.popup) === null || _b === void 0 ? void 0 : _b.root) || popupClassName || dropdownClassName, `${cascaderPrefixCls}-dropdown`, {\n    [`${cascaderPrefixCls}-dropdown-rtl`]: mergedDirection === 'rtl'\n  }, rootClassName, rootCls, contextClassNames.root, classNames === null || classNames === void 0 ? void 0 : classNames.root, cascaderRootCls, hashId, cssVarCls);\n  const mergedPopupRender = popupRender || dropdownRender;\n  const mergedPopupMenuColumnStyle = popupMenuColumnStyle || dropdownMenuColumnStyle;\n  const mergedOnOpenChange = onOpenChange || onDropdownVisibleChange;\n  const mergedPopupStyle = ((_c = styles === null || styles === void 0 ? void 0 : styles.popup) === null || _c === void 0 ? void 0 : _c.root) || ((_d = contextStyles.popup) === null || _d === void 0 ? void 0 : _d.root) || dropdownStyle;\n  // ==================== Search =====================\n  const mergedShowSearch = React.useMemo(() => {\n    if (!showSearch) {\n      return showSearch;\n    }\n    let searchConfig = {\n      render: defaultSearchRender\n    };\n    if (typeof showSearch === 'object') {\n      searchConfig = Object.assign(Object.assign({}, searchConfig), showSearch);\n    }\n    return searchConfig;\n  }, [showSearch]);\n  // ===================== Size ======================\n  const mergedSize = useSize(ctx => {\n    var _a;\n    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : compactSize) !== null && _a !== void 0 ? _a : ctx;\n  });\n  // ===================== Disabled =====================\n  const disabled = React.useContext(DisabledContext);\n  const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;\n  // ===================== Icon ======================\n  const [mergedExpandIcon, loadingIcon] = useColumnIcons(prefixCls, isRtl, expandIcon);\n  // =================== Multiple ====================\n  const checkable = useCheckable(cascaderPrefixCls, multiple);\n  // ===================== Icons =====================\n  const showSuffixIcon = useShowArrow(props.suffixIcon, showArrow);\n  const {\n    suffixIcon,\n    removeIcon,\n    clearIcon\n  } = useIcons(Object.assign(Object.assign({}, props), {\n    hasFeedback,\n    feedbackIcon,\n    showSuffixIcon,\n    multiple,\n    prefixCls,\n    componentName: 'Cascader'\n  }));\n  // ===================== Placement =====================\n  const memoPlacement = React.useMemo(() => {\n    if (placement !== undefined) {\n      return placement;\n    }\n    return isRtl ? 'bottomRight' : 'bottomLeft';\n  }, [placement, isRtl]);\n  const mergedAllowClear = allowClear === true ? {\n    clearIcon\n  } : allowClear;\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('SelectLike', mergedPopupStyle === null || mergedPopupStyle === void 0 ? void 0 : mergedPopupStyle.zIndex);\n  // ==================== Render =====================\n  const renderNode = /*#__PURE__*/React.createElement(RcCascader, Object.assign({\n    prefixCls: prefixCls,\n    className: cls(!customizePrefixCls && cascaderPrefixCls, {\n      [`${prefixCls}-lg`]: mergedSize === 'large',\n      [`${prefixCls}-sm`]: mergedSize === 'small',\n      [`${prefixCls}-rtl`]: isRtl,\n      [`${prefixCls}-${variant}`]: enableVariantCls,\n      [`${prefixCls}-in-form-item`]: isFormItemInput\n    }, getStatusClassNames(prefixCls, mergedStatus, hasFeedback), compactItemClassnames, contextClassName, className, rootClassName, classNames === null || classNames === void 0 ? void 0 : classNames.root, contextClassNames.root, rootCls, cascaderRootCls, hashId, cssVarCls),\n    disabled: mergedDisabled,\n    style: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), styles === null || styles === void 0 ? void 0 : styles.root), contextStyle), style)\n  }, restProps, {\n    builtinPlacements: mergedBuiltinPlacements(builtinPlacements, popupOverflow),\n    direction: mergedDirection,\n    placement: memoPlacement,\n    notFoundContent: mergedNotFoundContent,\n    allowClear: mergedAllowClear,\n    showSearch: mergedShowSearch,\n    expandIcon: mergedExpandIcon,\n    suffixIcon: suffixIcon,\n    removeIcon: removeIcon,\n    loadingIcon: loadingIcon,\n    checkable: checkable,\n    dropdownClassName: mergedPopupClassName,\n    dropdownPrefixCls: customizePrefixCls || cascaderPrefixCls,\n    dropdownStyle: Object.assign(Object.assign({}, mergedPopupStyle), {\n      zIndex\n    }),\n    dropdownRender: mergedPopupRender,\n    dropdownMenuColumnStyle: mergedPopupMenuColumnStyle,\n    onOpenChange: mergedOnOpenChange,\n    choiceTransitionName: getTransitionName(rootPrefixCls, '', choiceTransitionName),\n    transitionName: getTransitionName(rootPrefixCls, 'slide-up', transitionName),\n    getPopupContainer: getPopupContainer || getContextPopupContainer,\n    ref: ref\n  }));\n  return wrapCascaderCSSVar(wrapSelectCSSVar(renderNode));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Cascader.displayName = 'Cascader';\n}\n// We don't care debug panel\n/* istanbul ignore next */\nconst PurePanel = genPurePanel(Cascader, 'dropdownAlign', props => omit(props, ['visible']));\nCascader.SHOW_PARENT = SHOW_PARENT;\nCascader.SHOW_CHILD = SHOW_CHILD;\nCascader.Panel = CascaderPanel;\nCascader._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default Cascader;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}