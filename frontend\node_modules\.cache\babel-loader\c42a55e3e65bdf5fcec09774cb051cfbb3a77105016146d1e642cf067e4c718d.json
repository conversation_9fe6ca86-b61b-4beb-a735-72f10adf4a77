{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { toPathOptions } from \"../utils/treeUtil\";\nimport * as React from 'react';\nimport { toPathKey } from \"../utils/commonUtil\";\nexport default (function (rawValues, options, fieldNames, multiple, displayRender) {\n  return React.useMemo(function () {\n    var mergedDisplayRender = displayRender ||\n    // Default displayRender\n    function (labels) {\n      var mergedLabels = multiple ? labels.slice(-1) : labels;\n      var SPLIT = ' / ';\n      if (mergedLabels.every(function (label) {\n        return ['string', 'number'].includes(_typeof(label));\n      })) {\n        return mergedLabels.join(SPLIT);\n      }\n\n      // If exist non-string value, use ReactNode instead\n      return mergedLabels.reduce(function (list, label, index) {\n        var keyedLabel = /*#__PURE__*/React.isValidElement(label) ? /*#__PURE__*/React.cloneElement(label, {\n          key: index\n        }) : label;\n        if (index === 0) {\n          return [keyedLabel];\n        }\n        return [].concat(_toConsumableArray(list), [SPLIT, keyedLabel]);\n      }, []);\n    };\n    return rawValues.map(function (valueCells) {\n      var _valueOptions;\n      var valueOptions = toPathOptions(valueCells, options, fieldNames);\n      var label = mergedDisplayRender(valueOptions.map(function (_ref) {\n        var _option$fieldNames$la;\n        var option = _ref.option,\n          value = _ref.value;\n        return (_option$fieldNames$la = option === null || option === void 0 ? void 0 : option[fieldNames.label]) !== null && _option$fieldNames$la !== void 0 ? _option$fieldNames$la : value;\n      }), valueOptions.map(function (_ref2) {\n        var option = _ref2.option;\n        return option;\n      }));\n      var value = toPathKey(valueCells);\n      return {\n        label: label,\n        value: value,\n        key: value,\n        valueCells: valueCells,\n        disabled: (_valueOptions = valueOptions[valueOptions.length - 1]) === null || _valueOptions === void 0 || (_valueOptions = _valueOptions.option) === null || _valueOptions === void 0 ? void 0 : _valueOptions.disabled\n      };\n    });\n  }, [rawValues, options, fieldNames, displayRender, multiple]);\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}